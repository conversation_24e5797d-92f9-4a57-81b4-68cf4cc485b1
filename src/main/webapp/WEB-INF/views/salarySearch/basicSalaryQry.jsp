<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">


<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>工资查询</title>
</head>
<body>
    <div id="basicSalaryQry-dialog-toolbar" class="dialog-toolbar">
        <a id="basicSalaryQry-add" href="javascript:void(0)" onclick="salaryQry.search();" class="easyui-linkbutton" data-options="plain:true">查询</a> |
        <a id="basicSalaryQry-export" href="javascript:void(0)" onclick="salaryQry.search();" class="easyui-linkbutton" data-options="plain:true">导出</a>
    </div>
    <div id="basicSalaryQry-dialog-parambar" class="dialog-toolbar" style="padding-left:50px;">
                查询年度&nbsp;&nbsp;<select id="year" class="easyui-combobox" name="year" style="width:80px;" editable="false"></select>
                &nbsp;&nbsp;开始月份&nbsp;&nbsp;<select id="smonth" class="easyui-combobox" name="smonth" style="width:80px;" editable="false"></select>
        &nbsp;-&nbsp;结束月份&nbsp;&nbsp;<select id="emonth" class="easyui-combobox" name="emonth" style="width:80px;" editable="false"></select>
    </div>
    <div id="basicSalaryQry-easyui-tabs" class="easyui-tabs" data-options="tools:'#basicSalaryQry-tab-tools'" style="width:100%;">
        <div title="卡片" style="padding:10px">
            <div id="cardDiv" style="width:100%"></div>
        </div>
        <div title="表格" style="padding:10px">
            <div id="tableDiv" style="width:100%">
                <table id="tableSalary" class="easyui-datagrid" style="width:100%;height:500px"
                            data-options="
                                singleSelect: true,
                                loadMsg : '正在加载数据，请稍等...',
                                rownumbers: true,
                                striped:true,
                                showFooter: true,
                                rowStyler: function(index,row){
                    if ((index % 2)==0){
                        return '';
                    }else{
                      return 'background-color:#E0ECFF;';
                    }
                }
                            ">
                        <thead>
                            <tr>
                                <th data-options="field:'deptCode',width:80,align:'center'">部门代码</th>
                                <th data-options="field:'deptName',width:80,align:'center'">部门名称</th>
                                <th data-options="field:'employeeCode',width:80,align:'center'">职员代码</th>
                                <th data-options="field:'employeeName',width:80,align:'center'">职员名称</th>
                                <th data-options="field:'employeeTypeName',width:80,align:'center'">职员类型</th>
                                <th data-options="field:'salaryMonth',width:80,align:'center'">发放月份</th>
                                <th data-options="field:'rankSalary',width:80,align:'center'">薪级工资</th>
                                <th data-options="field:'jobSalary',width:80,align:'center'">岗位工资</th>
                                <th data-options="field:'lifeSubsidy',width:80,align:'center'">生活性补贴</th>
                                <th data-options="field:'jobSubsidy',width:80,align:'center'">岗位津贴</th>
                                <th data-options="field:'monthAssess',width:80,align:'center'">月度考核奖</th>
                                <th data-options="field:'workYearSubsidy',width:80,align:'center'">工龄补贴</th>
                                <th data-options="field:'medicalSubsidy',width:80,align:'center'">医疗补贴</th>
                                <th data-options="field:'rentSubsidy',width:80,align:'center'">住房提租补贴</th>
                                <th data-options="field:'tempSubsidy',width:80,align:'center'">临时性补贴</th>
                                <th data-options="field:'otSubsidy',width:80,align:'center'">其他补贴</th>
                                <th data-options="field:'trialSalary',width:80,align:'center'">试用期工资</th>
                                <th data-options="field:'payableSalary',width:80,align:'center',styler: function (value, row, index) {
                                    return 'background-color:write;color:red;';
                                    }">财政应发工资</th>
                                <th data-options="field:'housFund',width:80,align:'center'">住房公积金</th>
                                <th data-options="field:'medicare',width:80,align:'center'">医疗保险</th>
                                <th data-options="field:'endowmentInsurance',width:80,align:'center'">养老保险</th>
                                <th data-options="field:'unemployInsurance',width:80,align:'center'">失业保险</th>
                                <th data-options="field:'otDeduc',width:80,align:'center'">其它扣款</th>
                                <th data-options="field:'subSalary',width:80,align:'center'">减发工资</th>
                                <th data-options="field:'occupationalPension',width:80,align:'center'">职业年金</th>
                                <th data-options="field:'deducTotal',width:80,align:'center'">扣款合计</th>
                                <th data-options="field:'backPay',width:50,align:'center'">补发工资</th>
                                <th data-options="field:'netPayroll',width:80,align:'center',styler: function (value, row, index) {
                                    return 'background-color:write;color:red;';
                                    }">财政实发工资</th>
                            </tr>
                        </thead>
                    </table>
            </div>
        </div>
    </div>
    
</body>
</html>