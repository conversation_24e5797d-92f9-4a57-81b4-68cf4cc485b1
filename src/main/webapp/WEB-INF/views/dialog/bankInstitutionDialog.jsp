<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>

<div id="bankInstitutionDialog" class="easyui-dialog" title="选择银行机构" style="width:530px;height:400px;padding:10px;"
    data-options="toolbar: '#bankInstitutionDialog-toolbar'" closed="true">
    <input id="rowIndex" type="hidden" />
    <table id="bankInstitutionTab" style="width: 100%; height: 304px;">
        <thead>
            <tr>
                <th data-options="field:'bankInstitutionCode',width:120">代码</th>
                <th data-options="field:'bankInstitutionName',width:280">名称</th>
            </tr>
        </thead>
    </table>
</div>

<div id="bankInstitutionDialog-toolbar">
    <table cellpadding="0" cellspacing="0" style="width: 100%">
        <tr>
            <td>
                <form action="#">
                    <span style="margin-left: 20px;">过滤</span>&nbsp;&nbsp;
                    <input id="bankInstitution-searchText" name="search" class="easyui-textbox" />&nbsp;&nbsp;
                    <a id="qryBankInstitution" href="javascript:void(0);" onclick="qryBankInstitution.query();" class="easyui-linkbutton" style="width: 80px">查询</a>&nbsp;&nbsp;
                    <a href="javascript:void(0);" onclick="qryBankInstitution.ok();" class="easyui-linkbutton" style="width: 80px">确认</a>
                </form>
            </td>
        </tr>
    </table>
</div>

<script type="text/javascript" src="assets/js/modules/dialog/bankInstitutionDialog.js"></script>