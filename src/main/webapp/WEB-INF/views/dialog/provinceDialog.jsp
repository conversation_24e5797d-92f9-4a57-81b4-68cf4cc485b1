<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>

<div id="provinceDialog" class="easyui-dialog" title="选择省份" style="width:530px;height:400px;padding:10px;"
    data-options="toolbar: '#provinceDialog-toolbar'" closed="true">
    <input id="rowIndex" type="hidden" />
    <table id="provinceTab" style="width: 100%; height: 304px;">
        <thead>
            <tr>
                <th data-options="field:'provinceId',width:120">代码</th>
                <th data-options="field:'provinceName',width:280">名称</th>
            </tr>
        </thead>
    </table>
</div>

<div id="provinceDialog-toolbar">
    <table cellpadding="0" cellspacing="0" style="width: 100%">
        <tr>
            <td>
                <form action="#">
                    <span style="margin-left: 20px;">过滤</span>&nbsp;&nbsp;
                    <input id="province-searchText" name="search" class="easyui-textbox" />&nbsp;&nbsp;
                    <a id="qryProvince" href="javascript:void(0);" onclick="qryProvince.query();" class="easyui-linkbutton" style="width: 80px">查询</a>&nbsp;&nbsp;
                    <a href="javascript:void(0);" onclick="qryProvince.ok();" class="easyui-linkbutton" style="width: 80px">确认</a>
                </form>
            </td>
        </tr>
    </table>
</div>

<script type="text/javascript" src="assets/js/modules/dialog/provinceDialog.js"></script>