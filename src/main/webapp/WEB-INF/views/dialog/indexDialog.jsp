<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!-- closed="true" -->
<div id="indexDialog" class="easyui-dialog" title="选择指标"
	style="width: 800px; height: 400px;"
	data-options="toolbar: '#indexDialog-toolbar'" closed="true">
    <input id="rowIndex" type="hidden" />
	<table id="indexTab" style="width: 100%; height: 324px;">
		<thead>
			<tr>
				<th data-options="field:'indexID',width:60">指标ID</th>
				<th data-options="field:'deptCode',width:60">部门代码</th>
				<th data-options="field:'deptName',width:60">部门名称</th>
				<th data-options="field:'residual',width:60">可用金额</th>
				<th data-options="field:'usedAmt',width:60">已用金额</th>
				<th data-options="field:'projectCode',width:60">项目代码</th>
			</tr>
		</thead>
	</table>
</div>

<div id="indexDialog-toolbar">
	<table cellpadding="0" cellspacing="0" style="width: 100%">
		<tr>
			<td>
				<form action="#">
					<span style="margin-left: 30px; margin-right: 10px;">请输入部门名称或项目名称或指标ID
						<input id="paraVal" name="search" class="easyui-textbox" />
					</span> 显示可用金额为零的指标 <input name="isShowZeroIndex" type="checkbox" />&nbsp;&nbsp;
					<a id="qryIndex" href="javascript:void(0);" onclick="qryIndex.query();" class="easyui-linkbutton" style="width: 80px">查询</a>&nbsp;&nbsp;
					<a href="javascript:void(0);" onclick="qryIndex.ok();" class="easyui-linkbutton" style="width: 80px">确认</a>
				</form>
			</td>
		</tr>
	</table>
</div>

<script type="text/javascript" src="assets/js/modules/dialog/indexDialog.js"></script>