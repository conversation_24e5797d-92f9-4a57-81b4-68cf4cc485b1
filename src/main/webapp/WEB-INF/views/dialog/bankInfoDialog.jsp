<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>

<div id="bankInfoDialog" class="easyui-dialog" title="选择银行行号" style="width:530px;height:400px;padding:10px;"
    data-options="toolbar: '#bankInfoDialog-toolbar'" closed="true">
    <input id="rowIndex" type="hidden" />
    <table id="bankInfoTab" style="width: 100%; height: 304px;">
        <thead>
            <tr>
                <th data-options="field:'bankCode',width:120">代码</th>
                <th data-options="field:'bankName',width:280">名称</th>
            </tr>
        </thead>
    </table>
</div>

<div id="bankInfoDialog-toolbar">
    <table cellpadding="0" cellspacing="0" style="width: 100%">
        <tr>
            <td>
                <form action="#">
                    <span style="margin-left: 20px;">过滤</span>&nbsp;&nbsp;
                    <input id="bankInfo-searchText" name="search" class="easyui-textbox" />&nbsp;&nbsp;
                    <a id="qryBankInfo" href="javascript:void(0);" onclick="qryBankInfo.query();" class="easyui-linkbutton" style="width: 80px">查询</a>&nbsp;&nbsp;
                    <a href="javascript:void(0);" onclick="qryBankInfo.ok();" class="easyui-linkbutton" style="width: 80px">确认</a>
                </form>
            </td>
        </tr>
    </table>
</div>

<script type="text/javascript" src="assets/js/modules/dialog/bankInfoDialog.js"></script>