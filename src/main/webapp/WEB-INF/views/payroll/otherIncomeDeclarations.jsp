<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>其它收入申报</title>
</head>
<body>
    <div id="otherIncomeDeclarations-bill" style="height:100%">
        <div style="height:25%;background:#F4F4F4;">
            <div id="otherIncomeDeclarations-dialog-toolbar" class="dialog-toolbar">
                <a id="otherIncomeDeclarations-print" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">打印</a> |
                <a id="otherIncomeDeclarations-pdfprint" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">PDF打印</a> |
                <a id="otherIncomeDeclarations-add" href="javascript:void(0)" onclick="billStateSwitchOver.edit();" class="easyui-linkbutton" data-options="plain:true">新增</a> |
                <a id="otherIncomeDeclarations-update" href="javascript:void(0)" onclick="cashLoan.update();" class="easyui-linkbutton" data-options="plain:true">修改</a> |
                <a id="otherIncomeDeclarations-save" href="javascript:void(0)" onclick="cashLoan.save();" class="easyui-linkbutton" data-options="plain:true">保存</a> |
                <a id="otherIncomeDeclarations-cancel" href="javascript:void(0)" onclick="cashLoan.cancel();" class="easyui-linkbutton" data-options="plain:true">取消</a> |
                <a id="otherIncomeDeclarations-del" href="javascript:void(0)" onclick="cashLoan.del();" class="easyui-linkbutton" data-options="plain:true">删除</a> |
                <a id="otherIncomeDeclarations-budgetIndex" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">预算指标</a> |
                <a id="otherIncomeDeclarations-reverse" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">冲销借款单</a> |
                <a id="otherIncomeDeclarations-submit" href="javascript:void(0)" onclick="cashLoan.submit();" class="easyui-linkbutton" data-options="plain:true">提交</a> |
                <a id="otherIncomeDeclarations-recycle" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">收回</a> |
                <a id="otherIncomeDeclarations-audit" href="javascript:void(0)" onclick="$('#auditRecordDialog').dialog('open');" class="easyui-linkbutton" data-options="plain:true">审核记录</a> |
                <a id="otherIncomeDeclarations-import" href="javascript:void(0)" onclick="$('#attachDialog').dialog('open');" class="easyui-linkbutton" data-options="plain:true">导入</a>
            </div>
            
            <form id="otherIncomeDeclarations-form">
                <input type="hidden" name="otherIncomeDeclarationsId" value="" />
                <table style="width:100%;">
                    <tr>
                        <td class="w8p text-right">上报部门<i class="color-red">*</i></td>
                        <td class="w12p">
                            <input id="otherIncomeDeclarations-dept" name="dept" class="easyui-searchbox" style="width:100%;" />
                        </td>
                        <td class="w8p text-right">单据编号：</td>
                        <td class="w12p">
                            <input id="otherIncomeDeclarations-billno" name="billNo" class="easyui-textbox" style="width:100%;" />
                        </td>
                        <td class="w60p">
                        </td>
                    </tr>
                </table>
                <table style="width:100%">
                    <tr>
                        <td class="w8p text-right">工资项<i class="color-red">*</i></td>
                        <td class="w12p">
                            <input name="salaryItem" class="easyui-combobox" data-options="valueField:'id',textField:'text'" style="width:100%;" />
                        </td>
                        <td class="w8p text-right">明细项<i class="color-red">*</i></td>
                        <td class="w12p">
                            <input name="detailItem" class="easyui-combobox" data-options="valueField:'id',textField:'text'" style="width:100%;">
                        </td>
                        <td class="w60p">
                        </td>
                    </tr>
                </table>
                
                <table style="width:100%">
                    <tr>
                        <td class="w8p text-right">摘要<i class="color-red">*</i></td>
                        <td class="w32p">
                            <input name="remark" class="easyui-textbox" style="width:100%;" />
                        </td>
                        <td class="w60p">
                        </td>
                    </tr>
                </table>
                <p style="margin-left:48px;line-height:25px;">注意：个税只是临时计算，因存在合并计税等情况，最终以财务审核后计算为准。 </p>
            </div>
            <div style="height:75%">
                <table id="otherIncomeDeclarations-easyui-datagrid" class="easyui-datagrid"
                    data-options="rownumbers:true,fit:true,singleSelect:true,toolbar:'#otherIncomeDeclarations-tb'" style="height:100%">
                    <thead>
                        <tr>
                            <th data-options="field:'ck',checkbox:true,align:'center'" rowspan="2"></th>
                            <th data-options="field:'itemid',align:'center'" width="36%" colspan="4">人员信息</th>
                            <th data-options="field:'productid',align:'center'" width="10%">帐号信息</th>
                            <th data-options="field:'listprice',align:'center'" width="10%">其他收入</th>
                            <th data-options="field:'unitcost',align:'center'" width="20%" rowspan="2">备注*</th>
                        </tr>
                        <tr>
                            <th data-options="field:'itemid2',align:'center'" width="10%">部门</th>
                            <th data-options="field:'itemid3',align:'center'" width="8%">工号*</th>
                            <th data-options="field:'itemid4',align:'center'" width="8%">姓名*</th>
                            <th data-options="field:'itemid5',align:'center'" width="10%">身份证号*</th>
                            <th data-options="field:'itemid6',align:'center'" width="10%">银行卡号*</th>
                            <th data-options="field:'itemid7',align:'center'" width="10%">金额*</th>
                        </tr>
                    </thead>
                </table>
                
                <div id="otherIncomeDeclarations-tb">
                    <div style="float:right;">
                        <a href="#" class="easyui-linkbutton" plain="true">追加行</a> |
                        <a href="#" class="easyui-linkbutton" plain="true">删除行</a> |
                        <a href="#" class="easyui-linkbutton" plain="true">批量追加</a>
                    </div>
                    <div style="clear:both;"></div>
                </div>
            </div>
        </form>
    </div>
    
     
    <script type="text/javascript" src="assets/js/modules/payroll/otherIncomeDeclarations.js"></script>
     
    
    <jsp:include page="../dialog/resourceTreeDialog.jsp"></jsp:include>
</body>
</html>