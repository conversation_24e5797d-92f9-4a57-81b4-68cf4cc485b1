<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>出差审批（事后）报销单</title>
</head>
<body>
	<div id="travelApproval-dialog-toolbar" class="dialog-toolbar">
		<a id="travelApproval-print" href="javascript:void(0)"
			class="easyui-linkbutton" data-options="plain:true">打印</a> | <a
			id="travelApproval-pdfprint" href="javascript:void(0)"
			class="easyui-linkbutton" data-options="plain:true">PDF打印</a> | <a
			id="travelApproval-add" href="javascript:void(0)"
			onclick="travelApproval.add();" class="easyui-linkbutton"
			data-options="plain:true">新增</a> | <a id="travelApproval-update"
			href="javascript:void(0)" onclick="travelApproval.update();"
			class="easyui-linkbutton" data-options="plain:true">修改</a> | <a
			id="travelApproval-save" href="javascript:void(0)"
			onclick="travelApproval.save();" class="easyui-linkbutton"
			data-options="plain:true">保存</a> | <a id="travelApproval-cancel"
			href="javascript:void(0)" onclick="travelApproval.cancel();"
			class="easyui-linkbutton" data-options="plain:true">取消</a> | <a
			id="travelApproval-del" href="javascript:void(0)"
			onclick="travelApproval.del();" class="easyui-linkbutton"
			data-options="plain:true">删除</a> | <a id="travelApproval-budgetIndex"
			href="javascript:void(0)" class="easyui-linkbutton"
			data-options="plain:true">预算指标</a> | <a id="travelApproval-reverse"
			href="javascript:void(0)" class="easyui-linkbutton"
			data-options="plain:true">冲销借款单</a> | <a id="travelApproval-submit"
			href="javascript:void(0)" onclick="travelApproval.submit();"
			class="easyui-linkbutton" data-options="plain:true">提交</a> | <a
			id="travelApproval-recycle" href="javascript:void(0)"
			class="easyui-linkbutton" data-options="plain:true">收回</a> | <a
			id="travelApproval-audit" href="javascript:void(0)"
			onclick="$('#auditRecordDialog').dialog('open');"
			class="easyui-linkbutton" data-options="plain:true">审核记录</a> | <a
			id="travelApproval-attach" href="javascript:void(0)"
			onclick="$('#attachDialog').dialog('open');"
			class="easyui-linkbutton" data-options="plain:true">附件</a>
	</div>

	<div id="travelApproval-bill" class="bill">
		<p style="text-align: center; font-size: 18px; color: #39f;">出差审批（事后）报销单</p>
		<form id="travelApprovalForm_part1">
			<input type="hidden" id="travelApprovalId" name="id" value="" />
			<div class="row">
				<div class="w8p text-right">
					<label name="billNo">单据号</label>
				</div>
				<div class="w12p">
					<input id="travelApproval_billNo" name="billNo"
						class="easyui-textbox" style="width: 100%;" />
				</div>
				<div class="w8p text-right">
					<label name="billDate">单据日期<i class="color-red">*</i></label>
				</div>
				<div class="w12p">
					<input id="travelApproval_billDate" name="billDate"
						class="easyui-datebox"
						data-options="formatter:myformatter,parser:myparser"
						style="width: 100%;" />
				</div>
				<div class="w8p text-right">
					<label name="applyPerson">申请人<i class="color-red">*</i></label>
				</div>
				<div class="w12p">
					<input id="travelApproval-applyPerson" name="applyPerson"
						class="easyui-searchbox" style="width: 100%;" />
				</div>
				<div class="w8p text-right">
					<label name="dept">部门<i class="color-red">*</i></label>
				</div>
				<div class="w12p">
					<input id="travelApproval-dept" name="dept"
						class="easyui-searchbox" style="width: 100%;" />
				</div>
				<div class="w8p text-right">
					<label name="contact">联系电话</label>
				</div>
				<div class="w12p">
					<input id="contact" name="contact" class="easyui-textbox"
						style="width: 100%;" />
				</div>
			</div>

			<div class="row">
				<div class="w8p text-right">
					<label name="remark">事由<i class="color-red">*</i></label>
				</div>
				<div class="w52p">
					<input name="remark" class="easyui-textbox" style="width: 100%;" />
				</div>
				<div class="w8p text-right">
					<label name="attachNum">附件数</label>
				</div>
				<div class="w12p">
					<input name="attachNum" class="easyui-textbox" style="width: 100%;" />
				</div>
				<div class="w8p text-right">
					<label name="economicSubject">经济科目(选填)</label>
				</div>
				<div class="w12p">
					<input id="economicSubject" name="economicSubject"
						class="easyui-searchbox" style="width: 100%;" />
				</div>
			</div>
		</form>

		<div id="travelApproval-easyui-tabs" class="easyui-tabs"
			data-options="tools:'#travelApproval-tab-tools'"
			style="width: 100%; margin-top: 40px;">
			<div title="交通费用" style="padding: 10px">
				<table id="carfare-dg" class="easyui-datagrid"
					style="width: 100%; height: 220px"
					data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
					<thead>
						<tr>
							<th
								data-options="field:'departureDate',width:150,editor:{type:'datebox',options:{formatter:myformatter,parser:myparser}}">出发时间*</th>
							<th
								data-options="field:'departureAddress',width:150,editor:'textbox'">出发地点*</th>
							<th
								data-options="field:'arrivalDate',width:150,editor:{type:'datebox',options:{formatter:myformatter,parser:myparser}}">到达时间*</th>
							<th
								data-options="field:'arrivalAddress',width:150,editor:'textbox'">到达地点*</th>
							<th data-options="field:'vehicle',width:150,editor:'searchbox'">交通工具*</th>
							<th data-options="field:'amount',width:150,editor:'textbox'">金额*</th>
							<th
								data-options="field:'expenseRemark',width:150,editor:'textbox'">费用说明</th>
						</tr>
					</thead>
				</table>
			</div>
			<div title="伙食补助费用" style="padding: 10px">
				<table id="foodSubsidy-dg" class="easyui-datagrid"
					style="width: 100%; height: 220px"
					data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
					<thead>
						<tr>
							<th
								data-options="field:'foodSubsidyType',width:150,editor:'searchbox'">伙食补贴类型*</th>
							<th
								data-options="field:'standardFoodSubsidy',width:150,editor:'textbox'">伙食补贴标准*</th>
							<th
								data-options="field:'peopleNumber',width:150,editor:'textbox'">人数*</th>
							<th data-options="field:'times',width:150,editor:'textbox'">次数*</th>
							<th data-options="field:'amount',width:150,editor:'textbox'">金额*</th>
							<th
								data-options="field:'expenseRemark',width:150,editor:'textbox'">费用说明</th>
						</tr>
					</thead>
				</table>
			</div>
			<div title="公杂费用" style="padding: 10px">
				<table id="publicFees-dg" class="easyui-datagrid"
					style="width: 100%; height: 220px"
					data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
					<thead>
						<tr>
							<th
								data-options="field:'publicExpenseType',width:150,editor:'searchbox'">公杂费用类别*</th>
							<th
								data-options="field:'standardPublicExpense',width:150,editor:'textbox'">公杂费用标准*</th>
							<th
								data-options="field:'peopleNumber',width:150,editor:'textbox'">人数*</th>
							<th data-options="field:'days',width:150,editor:'textbox'">天数*</th>
							<th data-options="field:'amount',width:150,editor:'textbox'">公杂费金额*</th>
							<th
								data-options="field:'expenseRemark',width:150,editor:'textbox'">费用说明</th>
						</tr>
					</thead>
				</table>
			</div>
			<div title="住宿费用" style="padding: 10px">
				<table id="hotelExpense-dg" class="easyui-datagrid"
					style="width: 100%; height: 220px"
					data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
					<thead>
						<tr>
							<th data-options="field:'stayPeriod',width:150,editor:'textbox'">住宿天数*</th>
							<th data-options="field:'roomNumber',width:150,editor:'textbox'">房间数量*</th>
							<th data-options="field:'amount',width:150,editor:'textbox'">住宿金额*</th>
							<th
								data-options="field:'houseFeeUnitPrice',width:150,editor:'textbox'">房费单价（自动测算）*</th>
							<th
								data-options="field:'expenseRemark',width:150,editor:'textbox'">费用说明</th>
						</tr>
					</thead>
				</table>
			</div>
			<div title="其它费用" style="padding: 10px">
				<table id="otherFees-dg" class="easyui-datagrid"
					style="width: 100%; height: 220px"
					data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
					<thead>
						<tr>
							<th
								data-options="field:'expenseRemark',width:150,editor:'textbox'">费用说明</th>
							<th data-options="field:'amount',width:150,editor:'textbox'">费用金额*</th>
						</tr>
					</thead>
				</table>
			</div>
			<div title="支付方式" style="padding: 10px">
				<table id="travelApproval-payway-dg" class="easyui-datagrid"
					style="width: 100%; height: 220px"
					data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
					<thead>
						<tr>
							<th data-options="field:'payWay',width:150,editor:'searchbox'">结算方式*</th>
							<th data-options="field:'subject',width:150,editor:'searchbox'">科目*</th>
							<th data-options="field:'payee',width:120,editor:'searchbox'">收款人</th>
							<th
								data-options="field:'depositBank',width:120,editor:'searchbox'">开户银行</th>
							<th data-options="field:'bankAccount',width:120,editor:'textbox'">银行帐号</th>
							<th
								data-options="field:'officialCardNumber',width:120,editor:'textbox'">公务卡号</th>
							<th
								data-options="field:'chequeNumber',width:120,editor:'textbox'">支票号</th>
							<th data-options="field:'amount',width:120,editor:'textbox'">金额*</th>
							<th
								data-options="field:'bankInstitution',width:120,editor:'textbox'">银行机构</th>
							<th data-options="field:'bankCode',width:120,editor:'textbox'">银行行号</th>
							<th data-options="field:'province',width:120,editor:'textbox'">省份</th>
							<th data-options="field:'city',width:120,editor:'textbox'">城市</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
		<div id="travelApproval-tab-tools">
			<a href="javascript:void(0)" class="easyui-linkbutton"
				data-options="plain:true" onclick="travelApproval.append();">增加</a>
			| <a href="javascript:void(0)" class="easyui-linkbutton"
				data-options="plain:true" onclick="travelApproval.remove();">删除</a>
		</div>
	</div>

	<form id="travelApprovalForm_part2">
		<div class="row">
			<div class="w8p text-right">
				<label name="amount">金额<i class="color-red">*</i></label>
			</div>
			<div class="w12p">
				<input name="amount" class="easyui-textbox" style="width: 100%;"
					value="0.00" />
			</div>
			<div class="w8p text-right">
				<label name="amountUpper">金额（大写）<i class="color-red">*</i></label>
			</div>
			<div class="w32p">
				<input name="amountUpper" class="easyui-textbox"
					style="width: 100%;" value="0.00" />
			</div>
		</div>
	</form>
	</div>

	<script type="text/javascript"
		src="assets/js/modules/reimbursement/travelApproval.js"></script>

	<jsp:include page="../dialog/indexDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/applyPersonDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/deptDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/projectDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/accountTitleDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/economicSubjectDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/outlayDeptDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/executeWayDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/payWayDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/auditRecordDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/attachDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/vehicleDialog.jsp"></jsp:include>
	<jsp:include page="../dialog/foodAllowance.jsp"></jsp:include>
</body>
</html>