<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>领款（报销）单</title>
</head>
<body>
        <div id="drawMoney-dialog-toolbar" class="dialog-toolbar">
            <a id="drawMoney-print" href="#" class="easyui-linkbutton" data-options="plain:true">打印</a> |
            <a id="drawMoney-pdfprint" href="#" class="easyui-linkbutton" data-options="plain:true">PDF打印</a> |
            <a id="drawMoney-add" href="javascript:void(0)" onclick="drawMoney.add();" class="easyui-linkbutton" data-options="plain:true">新增</a> |
            <a id="drawMoney-update" href="javascript:void(0)" onclick="drawMoney.update();" class="easyui-linkbutton" data-options="plain:true">修改</a> |
            <a id="drawMoney-save" href="javascript:void(0)" onclick="drawMoney.save();" class="easyui-linkbutton" data-options="plain:true">保存</a> |
            <a id="drawMoney-cancel" href="javascript:void(0)" onclick="drawMoney.cancel();" class="easyui-linkbutton" data-options="plain:true">取消</a> |
            <a id="drawMoney-del" href="javascript:void(0)" onclick="drawMoney.del();" class="easyui-linkbutton" data-options="plain:true">删除</a> |
            <a id="drawMoney-reverse" href="#" class="easyui-linkbutton" data-options="plain:true">冲销贷款单</a> |
            <a id="drawMoney-submit" href="javascript:void(0)" onclick="drawMoney.submit();" class="easyui-linkbutton" data-options="plain:true">提交</a> |
            <a id="drawMoney-recycle" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">收回</a> |
            <a id="drawMoney-audit" href="javascript:void(0)" onclick="$('#auditRecordDialog').dialog('open');" class="easyui-linkbutton" data-options="plain:true">审核记录</a> |
            <a id="drawMoney-attach" href="javascript:void(0)" onclick="$('#attachDialog').dialog('open');" class="easyui-linkbutton" data-options="plain:true">附件</a>
        </div>
        
        <div id="drawMoney-bill" class="bill">
            <p style="text-align:center;font-size:18px;color:#39f;">领款（报销）单</p>
            <form id="drawMoneyForm_part1">
                <input type="hidden" id="drawMoneyId" name="id" value="" />
                <div class="row">
                    <div class="w8p text-right">
                        <label name="billNo">单据号</label>
                    </div>
                    <div class="w12p">
                        <input id="billNo" name="billNo" class="easyui-textbox" style="width:100%;" />
                    </div>
                    <div class="w8p text-right">
                        <label name="billDate">单据日期<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input id="billDate" name="billDate" class="easyui-datebox" data-options="formatter:myformatter,parser:myparser" style="width:100%;" />
                    </div>
                    <div class="w8p text-right">
                        <label name="applyPerson">申请人<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input id="drawMoney-applyPerson" name="applyPerson" class="easyui-searchbox" style="width:100%;" />
                    </div>
                    <div class="w8p text-right">
                        <label name="dept">部门<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input id="drawMoney-dept" name="dept" class="easyui-searchbox" style="width:100%;" />
                    </div>
                    <div class="w8p text-right">
                        <label name="contact">联系电话</label>
                    </div>
                    <div class="w12p">
                        <input id="contact" name="contact" class="easyui-textbox" style="width:100%;" />
                    </div>
                </div>
                
                <div class="row">
                    <div class="w8p text-right">
                        <label name="remark">报销说明<i class="color-red">*</i></label>
                    </div>
                    <div class="w52p">
                        <input name="remark" class="easyui-textbox" style="width:100%;" />
                    </div>
                    <div class="w8p text-right">
                        <label name="attachNum">附件数</label>
                    </div>
                    <div class="w12p">
                        <input name="attachNum" class="easyui-textbox" style="width:100%;" />
                    </div>
                </div>
            </form>
            <div id="drawMoney-easyui-tabs" class="easyui-tabs" data-options="tools:'#drawMoney-tab-tools'" style="width:100%;margin-top:40px;">
                <div title="支出明细" style="padding:10px">
                    <table id="paydetail-dg" class="easyui-datagrid" style="width:100%;height:220px"
                            data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
                        <thead>
                            <tr>
                                <th data-options="field:'index',width:150,editor:'searchbox'">指标*</th>
                                <th data-options="field:'project',width:150,editor:'searchbox'">项目*</th>
                                <th data-options="field:'accountTitle',width:150,editor:'searchbox'">会计科目（选填）</th>
                                <th data-options="field:'economicSubject',width:150,editor:'searchbox'">经济科目（选填）</th>
                                <th data-options="field:'amount',width:150,editor:'textbox'">金额*</th>
                                <th data-options="field:'expenseRemark',width:150,editor:'textbox'">费用说明</th>
                                <th data-options="field:'outlayDept',width:150,editor:'searchbox'">经费归口部门</th>
                                <th data-options="field:'executeWay',width:150,editor:'searchbox'">执行方式*</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div title="支付方式" style="padding:10px">
                    <table id="drawMoney-payway-dg" class="easyui-datagrid" style="width:100%;height:220px"
                            data-options="
                                singleSelect: true,
                                rownumbers: true,
                                showFooter: true
                            ">
                        <thead>
                            <tr>
                                <th data-options="field:'payWay',width:150,editor:'searchbox'">结算方式*</th>
                                <th data-options="field:'subject',width:150,editor:'searchbox'">科目</th>
                                <th data-options="field:'payee',width:150,editor:'searchbox'">收款人</th>
                                <th data-options="field:'personnelType',width:120,editor:{type:'combobox',options:{data:[{'value':'1','text':'经办人'},{'value':'2','text':'负责人'}],valueField:'value',textField:'text'}}">人员类型</th>
                                <th data-options="field:'depositBank',width:120,editor:'textbox'">开户银行</th>
                                <th data-options="field:'bankAccount',width:120,editor:'textbox'">银行帐号</th>
                                <th data-options="field:'officialCardNumber',width:120,editor:'textbox'">公务卡号</th>
                                <th data-options="field:'officialCardDissipate',width:120,editor:{type:'datebox',options:{formatter:myformatter,parser:myparser}}">公务卡消费时间</th>
                                <th data-options="field:'chequeNumber',width:120,editor:'textbox'">支票号</th>
                                <th data-options="field:'amount',width:120,editor:'textbox'">金额*</th>
                                <th data-options="field:'province',width:150,editor:'searchbox'">省份</th>
                                <th data-options="field:'area',width:150,editor:'searchbox'">城市</th>
                                <th data-options="field:'bankInstitution',width:150,editor:'searchbox'">银行机构</th>
                                <th data-options="field:'bankInfo',width:150,editor:'searchbox'">银行行号</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div id="drawMoney-tab-tools">
                <a href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true" onclick="drawMoney.append()">增加</a> |
                <a href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true" onclick="drawMoney.remove()">删除</a>
            </div>
            
            <form id="drawMoneyForm_part2">
                <div class="row">
                    <div class="w8p text-right">
                        <label name="preLoanAmount">预借金额<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input name="preLoanAmount" class="easyui-textbox" style="width:100%;" value="0.00" />
                    </div>
                    <div class="w8p text-right">
                        <label name="backAmount">退还金额<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input name="backAmount" class="easyui-textbox" style="width:100%;" value="0.00" />
                    </div>
                    <div class="w8p text-right">
                        <label name="replacementAmount">补领金额<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input name="replacementAmount" class="easyui-textbox" style="width:100%;" value="0.00" />
                    </div>
                </div>
                
                <div class="row">
                    <div class="w8p text-right">
                        <label name="amount">金额<i class="color-red">*</i></label>
                    </div>
                    <div class="w12p">
                        <input id="drawMoney-amount" name="amount" class="easyui-textbox" style="width:100%;" value="0.00" />
                    </div>
                    <div class="w8p text-right">
                        <label name="amountUpper">金额（大写）<i class="color-red">*</i></label>
                    </div>
                    <div class="w32p">
                        <input id="drawMoney-amountUpper" name="amountUpper" class="easyui-textbox" style="width:100%;" value="0.00" />
                    </div>
                </div>
            </form>
        </div>
        
        <script type="text/javascript" src="assets/js/modules/reimbursement/drawMoney.js"></script>
        
        <jsp:include page="../dialog/indexDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/applyPersonDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/deptDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/projectDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/accountTitleDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/economicSubjectDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/outlayDeptDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/executeWayDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/payWayDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/auditRecordDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/attachDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/payeeDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/provinceDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/areaDialog.jsp"></jsp:include>
        <!-- 
        <jsp:include page="../dialog/bankInstitutionDialog.jsp"></jsp:include>
        <jsp:include page="../dialog/bankInfoDialog.jsp"></jsp:include>
         -->
</body>
</html>