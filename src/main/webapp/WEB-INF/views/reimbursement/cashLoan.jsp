<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>现金借款（对公暂付）单</title>
</head>
<body>

    <div id="cashLoan-dialog-toolbar" class="dialog-toolbar">
        <a id="cashLoan-print" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">打印</a> |
        <a id="cashLoan-pdfprint" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">PDF打印</a> |
        <a id="cashLoan-add" href="javascript:void(0)" onclick="cashLoan.add();" class="easyui-linkbutton" data-options="plain:true">新增</a> |
        <a id="cashLoan-update" href="javascript:void(0)" onclick="cashLoan.update();" class="easyui-linkbutton" data-options="plain:true">修改</a> |
        <a id="cashLoan-save" href="javascript:void(0)" onclick="cashLoan.save();" class="easyui-linkbutton" data-options="plain:true">保存</a> |
        <a id="cashLoan-cancel" href="javascript:void(0)" onclick="cashLoan.cancel();" class="easyui-linkbutton" data-options="plain:true">取消</a> |
        <a id="cashLoan-del" href="javascript:void(0)" onclick="cashLoan.del();" class="easyui-linkbutton" data-options="plain:true">删除</a> |
        <a id="cashLoan-budgetIndex" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">预算指标</a> |
        <a id="cashLoan-submit" href="javascript:void(0)" onclick="cashLoan.submit();" class="easyui-linkbutton" data-options="plain:true">提交</a> |
        <a id="cashLoan-recycle" href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true">收回</a> |
        <a id="cashLoan-audit" href="javascript:void(0)" onclick="$('#auditRecordDialog').dialog('open');" class="easyui-linkbutton" data-options="plain:true">审核记录</a> |
        <a id="cashLoan-attach" href="javascript:void(0)" onclick="$('#attachDialog').dialog('open');" class="easyui-linkbutton" data-options="plain:true">附件</a>
    </div>
    
    <div id="cashLoan-bill" class="bill">
        <p style="text-align:center;font-size:18px;color:#39f;">现金借款（对公暂付）单</p>
        <form id="cashLoanForm_part1">
            <input type="hidden" id="cashLoanId" name="id" value="" />
            <div class="row">
                <div class="w8p text-right">
                    <label name="billNo">单据号</label>
                </div>
                <div class="w17p">
                    <input id="billNo" name="billNo" class="easyui-textbox" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="billDate">单据日期<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input id="billDate" name="billDate" class="easyui-datebox" data-options="formatter:myformatter,parser:myparser" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="repaymentDate">还款日期<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input name="repaymentDate" class="easyui-datebox" data-options="formatter:myformatter,parser:myparser" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="attachNum">附件数<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input name="attachNum" class="easyui-textbox" style="width:100%;" />
                </div>
            </div>
            
            <div class="row">
                <div class="w8p text-right">
                    <label name="borrower">借款人<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input id="borrower" name="billNo" class="easyui-textbox" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="dept">部门<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input id="dept" name="billDate" class="easyui-textbox" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="contact">联系电话<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input id="contact" name="applyPerson" class="easyui-textbox" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="debitEntry">借方科目<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input id="debitEntry" name="dept" class="easyui-searchbox" style="width:100%;" />
                </div>
            </div>
            
            <div class="row">
                <div class="w8p text-right">
                    <label name="usageLoan">借款用途<i class="color-red">*</i></label>
                </div>
                <div class="w42p">
                    <input name="usageLoan" class="easyui-textbox" style="width:100%;" />
                </div>
                <div class="w8p text-right">
                    <label name="amount">金额<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input name="amount" class="easyui-textbox" style="width:100%;" value="0.00" />
                </div>
                <div class="w8p text-right">
                    <label name="amountUpper">金额（大写）<i class="color-red">*</i></label>
                </div>
                <div class="w17p">
                    <input name="amountUpper" class="easyui-textbox" style="width:100%;" value="0.00" />
                </div>
            </div>
        </form>
        
        <div id="cashLoan-easyui-tabs" class="easyui-tabs" data-options="tools:'#cashLoan-tab-tools'" style="width:100%;margin-top:40px;">
            <div title="支付方式" style="padding:10px">
                <table id="cashLoan-payway-dg" class="easyui-datagrid" style="width:100%;height:220px"
                        data-options="
                            singleSelect: true,
                            rownumbers: true,
                            showFooter: true
                        ">
                    <thead>
                        <tr>
                                <th data-options="field:'payWay',width:150,editor:'searchbox'">结算方式*</th>
                                <th data-options="field:'subject',width:150,editor:'searchbox'">科目*</th>
                                <th data-options="field:'payee',width:120,editor:'searchbox'">收款人</th>
                                <th data-options="field:'depositBank',width:120,editor:'searchbox'">开户银行</th>
                                <th data-options="field:'bankAccount',width:120,editor:'textbox'">银行帐号</th>
                                <th data-options="field:'officialCardNumber',width:120,editor:'textbox'">公务卡号</th>
                                <th data-options="field:'chequeNumber',width:120,editor:'textbox'">支票号</th>
                                <th data-options="field:'amount',width:120,editor:'textbox'">金额*</th>
                                <th data-options="field:'bankInstitution',width:120,editor:'textbox'">银行机构</th>
                                <th data-options="field:'bankCode',width:120,editor:'textbox'">银行行号</th>
                                <th data-options="field:'province',width:120,editor:'textbox'">省份</th>
                                <th data-options="field:'city',width:120,editor:'textbox'">城市</th>
                            </tr>
                    </thead>
                </table>
            </div>
        </div>
        
        <div id="cashLoan-tab-tools">
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true" onclick="cashLoan.append()">增加</a> |
            <a href="javascript:void(0)" class="easyui-linkbutton" data-options="plain:true" onclick="cashLoan.remove()">删除</a>
        </div>
        
        <!-- 经办人 -->
        <form id="cashLoanForm_part2">
            <div class="row2">
                <div class="text-center" style="display:margin:0px auto;">
                    <label name="operator">经办人<i class="color-red">*</i></label>
                    <input name="operator" class="easyui-textbox" style="width:205px" />
                </div>
            </div>
        </form>
    </div>
    
    <script type="text/javascript" src="assets/js/modules/reimbursement/cashLoan.js"></script>
    
    <jsp:include page="../dialog/resourceTreeDialog.jsp"></jsp:include>
    <jsp:include page="../dialog/payWayDialog.jsp"></jsp:include>
    <jsp:include page="../dialog/auditRecordDialog.jsp"></jsp:include>
    <jsp:include page="../dialog/attachDialog.jsp"></jsp:include>
</body>
</html>