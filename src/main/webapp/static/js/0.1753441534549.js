webpackJsonp([0],{2821:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t){r.d(e,"AElement",function(){return te}),r.d(e,"AnimateColorElement",function(){return Gt}),r.d(e,"AnimateElement",function(){return qt}),r.d(e,"AnimateTransformElement",function(){return Qt}),r.d(e,"BoundingBox",function(){return At}),r.d(e,"CB1",function(){return Q}),r.d(e,"CB2",function(){return $}),r.d(e,"CB3",function(){return Z}),r.d(e,"CB4",function(){return K}),r.d(e,"Canvg",function(){return _e}),r.d(e,"CircleElement",function(){return It}),r.d(e,"ClipPathElement",function(){return ye}),r.d(e,"DefsElement",function(){return zt}),r.d(e,"DescElement",function(){return Ae}),r.d(e,"Document",function(){return Ee}),r.d(e,"Element",function(){return bt}),r.d(e,"EllipseElement",function(){return kt}),r.d(e,"FeColorMatrixElement",function(){return pe}),r.d(e,"FeCompositeElement",function(){return Se}),r.d(e,"FeDropShadowElement",function(){return xe}),r.d(e,"FeGaussianBlurElement",function(){return we}),r.d(e,"FeMorphologyElement",function(){return be}),r.d(e,"FilterElement",function(){return me}),r.d(e,"Font",function(){return Tt}),r.d(e,"FontElement",function(){return $t}),r.d(e,"FontFaceElement",function(){return Zt}),r.d(e,"GElement",function(){return Ut}),r.d(e,"GlyphElement",function(){return Et}),r.d(e,"GradientElement",function(){return Ht}),r.d(e,"ImageElement",function(){return ae}),r.d(e,"LineElement",function(){return Lt}),r.d(e,"LinearGradientElement",function(){return Xt}),r.d(e,"MarkerElement",function(){return Ft}),r.d(e,"MaskElement",function(){return de}),r.d(e,"Matrix",function(){return dt}),r.d(e,"MissingGlyphElement",function(){return Kt}),r.d(e,"Mouse",function(){return at}),r.d(e,"PSEUDO_ZERO",function(){return Y}),r.d(e,"Parser",function(){return lt}),r.d(e,"PathElement",function(){return Pt}),r.d(e,"PathParser",function(){return Ot}),r.d(e,"PatternElement",function(){return Bt}),r.d(e,"Point",function(){return nt}),r.d(e,"PolygonElement",function(){return jt}),r.d(e,"PolylineElement",function(){return Dt}),r.d(e,"Property",function(){return rt}),r.d(e,"QB1",function(){return J}),r.d(e,"QB2",function(){return tt}),r.d(e,"QB3",function(){return et}),r.d(e,"RadialGradientElement",function(){return Yt}),r.d(e,"RectElement",function(){return Vt}),r.d(e,"RenderedElement",function(){return Ct}),r.d(e,"Rotate",function(){return gt}),r.d(e,"SVGElement",function(){return Rt}),r.d(e,"SVGFontLoader",function(){return oe}),r.d(e,"Scale",function(){return pt}),r.d(e,"Screen",function(){return ut}),r.d(e,"Skew",function(){return vt}),r.d(e,"SkewX",function(){return yt}),r.d(e,"SkewY",function(){return mt}),r.d(e,"StopElement",function(){return Wt}),r.d(e,"StyleElement",function(){return ue}),r.d(e,"SymbolElement",function(){return se}),r.d(e,"TRefElement",function(){return Jt}),r.d(e,"TSpanElement",function(){return Nt}),r.d(e,"TextElement",function(){return Mt}),r.d(e,"TextPathElement",function(){return ie}),r.d(e,"TitleElement",function(){return Te}),r.d(e,"Transform",function(){return xt}),r.d(e,"Translate",function(){return ft}),r.d(e,"UnknownElement",function(){return St}),r.d(e,"UseElement",function(){return he}),r.d(e,"ViewPort",function(){return it}),r.d(e,"compressSpaces",function(){return E}),r.d(e,"default",function(){return _e}),r.d(e,"getSelectorSpecificity",function(){return X}),r.d(e,"normalizeAttributeName",function(){return V}),r.d(e,"normalizeColor",function(){return k}),r.d(e,"parseExternalUrl",function(){return I}),r.d(e,"presets",function(){return P}),r.d(e,"toNumbers",function(){return _}),r.d(e,"trimLeft",function(){return M}),r.d(e,"trimRight",function(){return N}),r.d(e,"vectorMagnitude",function(){return W}),r.d(e,"vectorsAngle",function(){return G}),r.d(e,"vectorsRatio",function(){return q});var i=r(2976),n=(r.n(i),r(722)),a=r.n(n),s=r(2984),o=(r.n(s),r(2985)),u=(r.n(o),r(2987)),h=(r.n(u),r(2919)),c=(r.n(h),r(2989)),l=(r.n(c),r(99)),f=r.n(l),g=r(2971),p=(r.n(g),r(2982)),d=(r.n(p),r(2986)),v=(r.n(d),r(2993)),y=r.n(v),m=r(2988),x=(r.n(m),r(2994)),b=r.n(x),S=r(2970),w=(r.n(S),r(2983)),T=(r.n(w),r(2972)),A=(r.n(T),r(2996)),O=r(2981),C=(r.n(O),r(2995));var P=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>a()(function*(){var e=yield(yield fetch(t)).blob();return yield createImageBitmap(e)})()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function E(t){return t.replace(/(?!\u3000)\s+/gm," ")}function M(t){return t.replace(/^[\n \t]+/,"")}function N(t){return t.replace(/[\n \t]+$/,"")}function _(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var R=/^[A-Z-]+$/;function V(t){return R.test(t)?t.toLowerCase():t}function I(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function k(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t)}var L=/(\[[^\]]+\])/g,D=/(#[^\s+>~.[:]+)/g,j=/(\.[^\s+>~.[:]+)/g,B=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,F=/(:[\w-]+\([^)]*\))/gi,z=/(:[^\s+>~.[:]+)/g,U=/([^\s+>~.[:]+)/g;function H(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function X(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=H(r,L),e[1]+=i,[r,i]=H(r,D),e[0]+=i,[r,i]=H(r,j),e[1]+=i,[r,i]=H(r,B),e[2]+=i,[r,i]=H(r,F),e[1]+=i,[r,i]=H(r,z),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=H(r,U),e[2]+=i,e.join("")}var Y=1e-8;function W(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function q(t,e){return(t[0]*e[0]+t[1]*e[1])/(W(t)*W(e))}function G(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(q(t,e))}function Q(t){return t*t*t}function $(t){return 3*t*t*(1-t)}function Z(t){return 3*t*(1-t)*(1-t)}function K(t){return(1-t)*(1-t)*(1-t)}function J(t){return t*t}function tt(t){return 2*t*(1-t)}function et(t){return(1-t)*(1-t)}class rt{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new rt(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return E(this.getString()).trim().split(t).map(t=>new rt(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor?e:(this.isNormalizedColor=!0,e=k(e),this.value=e,e)}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*n.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?rt.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var a=new b.a(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new rt(this.document,this.name,e)}}rt.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class it{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class nt{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=_(t);return new nt(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=_(t);return new nt(r,i)}static parsePath(t){for(var e=_(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new nt(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class at{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(a,s)&&(i[n]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInBox(a,s)&&(i[n]=t)})}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new nt(t,e),a=i.canvas;a;)n.x-=a.offsetLeft,n.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var st="undefined"!=typeof window?window:null,ot="undefined"!=typeof fetch?fetch.bind(void 0):null;class ut{constructor(t){var{fetch:e=ot,window:r=st}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new it,this.mouse=new at(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:a,height:s,desiredHeight:o,minX:u=0,minY:h=0,refX:c,refY:l,clip:f=!1,clipX:g=0,clipY:p=0}=t,d=E(i).replace(/^defer\s/,""),[v,y]=d.split(" "),m=v||"xMidYMid",x=y||"meet",b=n/a,S=s/o,w=Math.min(b,S),T=Math.max(b,S),A=a,O=o;"meet"===x&&(A*=w,O*=w),"slice"===x&&(A*=T,O*=T);var C=new rt(e,"refX",c),P=new rt(e,"refY",l),M=C.hasValue()&&P.hasValue();if(M&&r.translate(-w*C.getPixels("x"),-w*P.getPixels("y")),f){var N=w*g,_=w*p;r.beginPath(),r.moveTo(N,_),r.lineTo(n,_),r.lineTo(n,s),r.lineTo(N,s),r.closePath(),r.clip()}if(!M){var R="meet"===x&&w===S,V="slice"===x&&T===S,I="meet"===x&&w===b,k="slice"===x&&T===b;m.startsWith("xMid")&&(R||V)&&r.translate(n/2-A/2,0),m.endsWith("YMid")&&(I||k)&&r.translate(0,s/2-O/2),m.startsWith("xMax")&&(R||V)&&r.translate(n-A,0),m.endsWith("YMax")&&(I||k)&&r.translate(0,s-O)}switch(!0){case"none"===m:r.scale(b,S);break;case"meet"===x:r.scale(w,w);break;case"slice"===x:r.scale(T,T)}r.translate(-u,-h)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:a=!1,forceRedraw:s,scaleWidth:o,scaleHeight:u,offsetX:h,offsetY:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:l,mouse:f}=this,g=1e3/l;if(this.frameDuration=g,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,n,a,o,u,h,c),e){var p=Date.now(),d=p,v=0,m=()=>{p=Date.now(),(v=p-d)>=g&&(d=p-v%g,this.shouldUpdate(i,s)&&(this.render(t,n,a,o,u,h,c),f.runEvents())),this.intervalId=y()(m)};r||f.start(),this.intervalId=y()(m)}}stop(){this.intervalId&&(y.a.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce((t,e)=>e.update(r)||t,!1))return!0}return!("function"!=typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,i,n,a,s){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:h,ctx:c,isFirstRender:l}=this,f=c.canvas;h.clear(),f.width&&f.height?h.setCurrent(f.width,f.height):h.setCurrent(o,u);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(l||"number"!=typeof i&&"number"!=typeof n)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),v=p.getPixels("y")),h.setCurrent(d,v),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof s&&t.getAttribute("y",!0).setValue(s),"number"==typeof i||"number"==typeof n){var y=_(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof i){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/i:isNaN(y[2])||(m=y[2]/i)}if("number"==typeof n){var S=t.getStyle("height");S.hasValue()?x=S.getPixels("y")/n:isNaN(y[3])||(x=y[3]/n)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var w=t.getStyle("transform",!0,!0);w.setValue("".concat(w.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||c.clearRect(0,0,d,v),t.render(c),l&&(this.isFirstRender=!1)}}ut.defaultWindow=st,ut.defaultFetch=ot;var{defaultFetch:ht}=ut,ct="undefined"!=typeof DOMParser?DOMParser:null;class lt{constructor(){var{fetch:t=ht,DOMParser:e=ct}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return a()(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return a()(function*(){var r=yield(yield e.fetch(t)).text();return e.parseFromString(r)})()}}class ft{constructor(t,e){this.type="translate",this.point=null,this.point=nt.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class gt{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=_(e);this.angle=new rt(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(a.getRadians()),t.translate(-s,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(-1*a.getRadians()),t.translate(-s,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class pt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=nt.parseScale(e);0!==i.x&&0!==i.y||(i.x=Y,i.y=Y),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(e,r||e),t.translate(-a,-s)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(1/e,1/r||e),t.translate(-a,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class dt{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=_(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),a=r.getPixels("y");t.translate(n,a),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-a)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],a=i[2],s=i[4],o=i[1],u=i[3],h=i[5],c=1/(n*(1*u-0*h)-a*(1*o-0*h)+s*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(c*(1*u-0*h),c*(0*h-1*o),c*(0*s-1*a),c*(1*n-0*s),c*(a*h-s*u),c*(s*o-n*h)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class vt extends dt{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new rt(t,"angle",e)}}class yt extends vt{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class mt extends vt{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class xt{constructor(t,e,r){this.document=t,this.transforms=[],function(t){return E(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e).forEach(t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=xt.transformTypes[e];void 0!==n&&this.transforms.push(new n(this.document,i,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),a=[i,n];return r.hasValue()?new xt(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}xt.transformTypes={translate:ft,rotate:gt,scale:pt,matrix:dt,skewX:yt,skewY:mt};class bt{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach(e=>{var r=V(e.nodeName);this.attributes[r]=new rt(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue())this.getAttribute("style").getString().split(";").map(t=>t.trim()).forEach(e=>{if(e){var[r,i]=e.split(":").map(t=>t.trim());this.styles[r]=new rt(t,r,i)}});var{definitions:i}=t,n=this.getAttribute("id");n.hasValue()&&(i[n.getString()]||(i[n.getString()]=this)),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}})}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new rt(this.document,t,"");return this.attributes[t]=i,i}return r||rt.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return rt.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!==n&&void 0!==n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:a}=this;if(a){var s=a.getStyle(t);if(null!==s&&void 0!==s&&s.hasValue())return s}}if(e){var o=new rt(this.document,t,"");return this.styles[t]=o,o}return i||rt.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=xt.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof bt?t:this.document.createElement(t);e.parent=this,bt.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var a in i){var s=this.stylesSpecificity[a];void 0===s&&(s="000"),n>=s&&(this.styles[a]=i[a],this.stylesSpecificity[a]=n)}}}removeStyles(t,e){return e.reduce((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]},[])}restoreStyles(t,e){e.forEach(e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)})}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}bt.ignoreChildTypes=["title"];class St extends bt{constructor(t,e,r){super(t,e,r)}}function wt(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}class Tt{constructor(t,e,r,i,n,a){var s=a?"string"==typeof a?Tt.parse(a):a:{};this.fontFamily=n||s.fontFamily,this.fontSize=i||s.fontSize,this.fontStyle=t||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=e||s.fontVariant}static parse(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,r="",i="",n="",a="",s="",o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return E(t).trim().split(" ").forEach(t=>{switch(!0){case!o.fontStyle&&Tt.styles.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0;break;case!o.fontVariant&&Tt.variants.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&Tt.weights.includes(t):"inherit"!==t&&(n=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([a]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(s+=t)}}),new Tt(r,i,n,a,s,e)}toString(){return[function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}(this.fontStyle),this.fontVariant,function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}(this.fontWeight),this.fontSize,(e=this.fontFamily,void 0===t?e:e.trim().split(",").map(wt).join(","))].join(" ").trim();var e}}Tt.styles="normal|italic|oblique|inherit",Tt.variants="normal|small-caps|inherit",Tt.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class At{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var a=6*e-12*r+6*i,s=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==s){var u=Math.pow(a,2)-4*o*s;if(!(u<0)){var h=(-a+Math.sqrt(u))/(2*s);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,i,n)):this.addY(this.sumCubic(h,e,r,i,n)));var c=(-a-Math.sqrt(u))/(2*s);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)))}}else{if(0===a)return;var l=-o/a;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,a,s,o){this.addPoint(t,e),this.addPoint(s,o),this.bezierCurveAdd(!0,t,r,n,s),this.bezierCurveAdd(!1,e,i,a,o)}addQuadraticCurve(t,e,r,i,n,a){var s=t+2/3*(r-t),o=e+2/3*(i-e),u=s+1/3*(n-t),h=o+1/3*(a-e);this.addBezierCurve(t,e,s,u,o,h,n,a)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:a}=this;return r<=t&&t<=n&&i<=e&&e<=a}}class Ot extends A.a{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new nt(0,0),this.control=new nt(0,0),this.current=new nt(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new nt(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==A.a.CURVE_TO&&t!==A.a.SMOOTH_CURVE_TO&&t!==A.a.QUAD_TO&&t!==A.a.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new nt(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class Ct extends bt{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,i);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var u=new rt(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=u}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,a);h&&(t.strokeStyle=h)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var c=n.getString();"inherit"!==c&&(t.strokeStyle="none"===c?"rgba(0,0,0,0)":c)}if(a.hasValue()){var l=new rt(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var g=f.getPixels();t.lineWidth=g||Y}var p=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),y=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),d.hasValue()&&(t.lineJoin=d.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),y.hasValue()&&"none"!==y.getString()){var x=_(y.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var S=this.getStyle("font"),w=this.getStyle("font-style"),T=this.getStyle("font-variant"),A=this.getStyle("font-weight"),O=this.getStyle("font-size"),C=this.getStyle("font-family"),P=new Tt(w.getString(),T.getString(),A.getString(),O.hasValue()?"".concat(O.getPixels(!0),"px"):"",C.getString(),Tt.parse(S.getString(),t.font));w.setValue(P.fontStyle),T.setValue(P.fontVariant),A.setValue(P.fontWeight),O.setValue(P.fontSize),C.setValue(P.fontFamily),t.font=P.toString(),O.isPixels()&&(this.document.emSize=O.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class Pt extends Ct{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new Ot(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new At;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case Ot.MOVE_TO:this.pathM(t,r);break;case Ot.LINE_TO:this.pathL(t,r);break;case Ot.HORIZ_LINE_TO:this.pathH(t,r);break;case Ot.VERT_LINE_TO:this.pathV(t,r);break;case Ot.CURVE_TO:this.pathC(t,r);break;case Ot.SMOOTH_CURVE_TO:this.pathS(t,r);break;case Ot.QUAD_TO:this.pathQ(t,r);break;case Ot.SMOOTH_QUAD_TO:this.pathT(t,r);break;case Ot.ARC:this.pathA(t,r);break;case Ot.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((t,e)=>[t,r[e]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[u,h]=r[0];o.render(t,u,h)}if(a.isUrlDefinition())for(var c=a.getDefinition(),l=1;l<i;l++){var[f,g]=r[l];c.render(t,f,g)}if(s.isUrlDefinition()){var p=s.getDefinition(),[d,v]=r[i];p.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=Pt.pathM(r),{x:n,y:a}=i;r.addMarker(i),e.addPoint(n,a),t&&t.moveTo(n,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=Pt.pathL(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathH(t){var{current:e,command:r}=t,i=new nt((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=Pt.pathH(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathV(t){var{current:e,command:r}=t,i=new nt(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=Pt.pathV(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=Pt.pathC(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=Pt.pathS(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=Pt.pathQ(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=Pt.pathT(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:a,lArcFlag:s,sweepFlag:o}=r,u=a*(Math.PI/180),h=t.getAsCurrentPoint(),c=new nt(Math.cos(u)*(e.x-h.x)/2+Math.sin(u)*(e.y-h.y)/2,-Math.sin(u)*(e.x-h.x)/2+Math.cos(u)*(e.y-h.y)/2),l=Math.pow(c.x,2)/Math.pow(i,2)+Math.pow(c.y,2)/Math.pow(n,2);l>1&&(i*=Math.sqrt(l),n*=Math.sqrt(l));var f=(s===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(c.y,2)-Math.pow(n,2)*Math.pow(c.x,2))/(Math.pow(i,2)*Math.pow(c.y,2)+Math.pow(n,2)*Math.pow(c.x,2)));isNaN(f)&&(f=0);var g=new nt(f*i*c.y/n,f*-n*c.x/i),p=new nt((e.x+h.x)/2+Math.cos(u)*g.x-Math.sin(u)*g.y,(e.y+h.y)/2+Math.sin(u)*g.x+Math.cos(u)*g.y),d=G([1,0],[(c.x-g.x)/i,(c.y-g.y)/n]),v=[(c.x-g.x)/i,(c.y-g.y)/n],y=[(-c.x-g.x)/i,(-c.y-g.y)/n],m=G(v,y);return q(v,y)<=-1&&(m=Math.PI),q(v,y)>=1&&(m=0),{currentPoint:h,rX:i,rY:n,sweepFlag:o,xAxisRotation:u,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:a,sweepFlag:s,xAxisRotation:o,centp:u,a1:h,ad:c}=Pt.pathA(r),l=1-s?1:-1,f=h+l*(c/2),g=new nt(u.x+n*Math.cos(f),u.y+a*Math.sin(f));if(r.addMarkerAngle(g,f-l*Math.PI/2),r.addMarkerAngle(i,f-l*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(h)&&!isNaN(c)){var p=n>a?n:a,d=n>a?1:n/a,v=n>a?a/n:1;t.translate(u.x,u.y),t.rotate(o),t.scale(d,v),t.arc(0,0,p,h,h+c,Boolean(1-s)),t.scale(1/d,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){Pt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class Et extends Pt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class Mt extends Ct{constructor(t,e,r){super(t,e,new.target===Mt||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n}),e}getFontSize(){var{document:t,parent:e}=this,r=Tt.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new At(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var a=e.length,s=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===s)&&r<a-1&&" "!==o&&(u="terminal"),r>0&&" "!==s&&r<a-1&&" "!==o&&(u="medial"),r>0&&" "!==s&&(r===a-1||" "===o)&&(u="initial"),void 0!==t.glyphs[i]){var h=t.glyphs[i];n=h instanceof Et?h:h[u]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,a=E(e.textContent||"");return 0===i&&(a=M(a)),i===n&&(a=N(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:a}=n.fontFace,s=Tt.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(s.fontSize),u=r.getStyle("font-style").getString(s.fontStyle),h=o/a,c=n.isRTL?i.split("").reverse().join(""):i,l=_(r.getAttribute("dx").getString()),f=c.length,g=0;g<f;g++){var p=this.getGlyph(n,c,g);t.translate(this.x,this.y),t.scale(h,-h);var d=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===u&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||n.horizAdvX)/a,void 0===l[g]||isNaN(l[g])||(this.x+=l[g])}else{var{x:v,y:y}=this;t.fillStyle&&t.fillText(i,v,y),t.strokeStyle&&t.strokeText(i,v,y)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)}):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!=typeof n.measureText)return n;t.save(),n.setContext(t,!0);var a=n.getAttribute("x"),s=n.getAttribute("y"),o=n.getAttribute("dx"),u=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),c=Boolean(h)&&h.isRTL;0===i&&(a.hasValue()||a.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),u.hasValue()||u.setValue(n.getInheritedAttribute("dy")));var l=n.measureText(t);return c&&(e.x-=l),a.hasValue()?(e.applyAnchoring(),n.x=a.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,c||(e.x+=l),s.hasValue()?(n.y=s.getPixels("y"),u.hasValue()&&(n.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+l),e.maxX=Math.max(e.maxX,n.x,n.x+l),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!=typeof n.getBoundingBox)return null;var a=n.getBoundingBox(t);return a?(n.children.forEach((r,i)=>{var s=e.getChildBoundingBox(t,e,n,i);a.addBoundingBox(s)}),a):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach((r,i)=>{e.renderChild(t,e,n,i)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),a=i.isRTL?e.split("").reverse().join(""):e,s=_(r.getAttribute("dx").getString()),o=a.length,u=0,h=0;h<o;h++){u+=(this.getGlyph(i,a,h).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===s[h]||isNaN(s[h])||(u+=s[h])}return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:c}=t.measureText(e);return this.clearContext(t),t.restore(),c}getInheritedAttribute(t){for(var e=this;e instanceof Mt&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Nt extends Mt{constructor(t,e,r){super(t,e,new.target===Nt||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class _t extends Nt{constructor(){super(...arguments),this.type="textNode"}}class Rt extends Ct{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,a=t.canvas;if(i.setDefaults(t),a.style&&void 0!==t.font&&n&&void 0!==n.getComputedStyle){t.font=n.getComputedStyle(a).getPropertyValue("font");var s=new rt(r,"fontSize",Tt.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),c=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?_(l.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,v=0,y=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=p,y=d,p=0,d=0)),i.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:u,minX:p,minY:d,refX:h.getValue(),refY:c.getValue(),clip:g,clipX:v,clipY:y}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),s=this.getAttribute("style"),o=i.getNumber(0),u=n.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(u||e)),s.hasValue()){var c=this.getStyle("width"),l=this.getStyle("height");c.hasValue()&&c.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class Vt extends Pt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),s=this.getAttribute("ry"),o=a.getPixels("x"),u=s.getPixels("y");if(a.hasValue()&&!s.hasValue()&&(u=o),s.hasValue()&&!a.hasValue()&&(o=u),o=Math.min(o,i/2),u=Math.min(u,n/2),t){var h=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+h*o,r,e+i,r+u-h*u,e+i,r+u),t.lineTo(e+i,r+n-u),t.bezierCurveTo(e+i,r+n-u+h*u,e+i-o+h*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-h*o,r+n,e,r+n-u+h*u,e,r+n-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-h*u,e+o-h*o,r,e+o,r),t.closePath())}return new At(e,r,e+i,r+n)}getMarkers(){return null}}class It extends Pt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new At(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class kt extends Pt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,a),t.bezierCurveTo(n+r,a+e*i,n+e*r,a+i,n,a+i),t.bezierCurveTo(n-e*r,a+i,n-r,a+e*i,n-r,a),t.bezierCurveTo(n-r,a-e*i,n-e*r,a-i,n,a-i),t.bezierCurveTo(n+e*r,a-i,n+r,a-e*i,n+r,a),t.closePath()),new At(n-r,a-i,n+r,a+i)}getMarkers(){return null}}class Lt extends Pt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new nt(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new nt(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new At(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Dt extends Pt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=nt.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new At(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach(e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)}),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class jt extends Dt{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class Bt extends bt{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),a=new Rt(this.document,null);a.attributes.viewBox=new rt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new rt(this.document,"width","".concat(i,"px")),a.attributes.height=new rt(this.document,"height","".concat(n,"px")),a.attributes.transform=new rt(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var s=this.document.createCanvas(i,n),o=s.getContext("2d"),u=this.getAttribute("x"),h=this.getAttribute("y");u.hasValue()&&h.hasValue()&&o.translate(u.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var c=-1;c<=1;c++)for(var l=-1;l<=1;l++)o.save(),a.attributes.x=new rt(this.document,"x",c*s.width),a.attributes.y=new rt(this.document,"y",l*s.height),a.render(o),o.restore();return t.createPattern(s,"repeat")}}class Ft extends bt{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,a=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===a&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new Rt(this.document,null);o.type=this.type,o.attributes.viewBox=new rt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new rt(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new rt(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new rt(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new rt(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new rt(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new rt(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new rt(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-i,-n)}}}class zt extends bt{constructor(){super(...arguments),this.type="defs"}render(){}}class Ut extends Ct{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new At;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class Ht extends bt{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(t=>{"stop"===t.type&&i.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=s.screen,[h]=u.viewPorts,c=new Vt(s,null);c.attributes.x=new rt(s,"x",-o/3),c.attributes.y=new rt(s,"y",-o/3),c.attributes.width=new rt(s,"width",o),c.attributes.height=new rt(s,"height",o);var l=new Ut(s,null);l.attributes.transform=new rt(s,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[c];var f=new Rt(s,null);f.attributes.x=new rt(s,"x",0),f.attributes.y=new rt(s,"y",0),f.attributes.width=new rt(s,"width",h.width),f.attributes.height=new rt(s,"height",h.height),f.children=[l];var g=s.createCanvas(h.width,h.height),p=g.getContext("2d");return p.fillStyle=a,f.render(p),p.createPattern(g,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new rt(this.document,"color",e).addOpacity(t).getColor():e}}class Xt extends Ht{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&a===o?null:t.createLinearGradient(n,a,s,o)}}class Yt extends Ht{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,o=a;this.getAttribute("fx").hasValue()&&(s=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,o,h,n,a,u)}}class Wt extends bt{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),n.hasValue()&&(a=a.addOpacity(n)),this.offset=i,this.color=a.getColor()}}class qt extends bt{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new rt(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var a=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var o=s.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),a=Math.ceil(i);r.from=new rt(t,"from",parseFloat(e.getValue()[n])),r.to=new rt(t,"to",parseFloat(e.getValue()[a])),r.progress=(i-n)/(a-n)}else r.from=this.from,r.to=this.to;return r}}class Gt extends qt{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new b.a(e.getColor()),n=new b.a(r.getColor());if(i.ok&&n.ok){var a=i.r+(n.r-i.r)*t,s=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(s),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class Qt extends qt{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=_(e.getString()),n=_(r.getString());return i.map((e,r)=>{return e+(n[r]-e)*t}).join(" ")}}class $t extends bt{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var a of n)switch(a.type){case"font-face":this.fontFace=a;var s=a.getStyle("font-family");s.hasValue()&&(i[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]={}),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class Zt extends bt{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class Kt extends Pt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class Jt extends Mt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class te extends Mt{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],a=i.length>0&&Array.from(i).every(t=>3===t.nodeType);this.hasText=a,this.text=a?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,a=new rt(e,"fontSize",Tt.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new At(r,i-a.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var s=new Ut(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function ee(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function re(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(r),!0).forEach(function(e){f()(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}class ie extends Mt{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:i}=e;switch(r){case Ot.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case Ot.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case Ot.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case Ot.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case Ot.ARC:var[n,a,s,o,u,h,c,l]=i,f=s>o?s:o,g=s>o?1:s/o,p=s>o?o/s:1;t&&(t.translate(n,a),t.rotate(c),t.scale(g,p),t.arc(0,0,f,u,u+h,Boolean(1-l)),t.scale(1/g,1/p),t.rotate(-c),t.translate(-n,-a));break;case Ot.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach((i,n)=>{var{p0:a,p1:s,rotation:o,text:u}=i;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(a.x,a.y+r/8),t.lineTo(s.x,s.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,a,s,o,u){var h=a,c=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(c+=(i-r)/n),u>-1&&(h+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(h,l,0),g=this.getEquidistantPointOnPath(h+c,l,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(s){var v=Math.cos(Math.PI/2+d)*s,y=Math.cos(-d)*s;p.p0=re(re({},f),{},{x:f.x+v,y:f.y+y}),p.p1=re(re({},g),{},{x:g.x+v,y:g.y+y})}return{offset:h+=c,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),a=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),h=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(h=o.getPixels()):h=u.getPixels();var c=[],l=e.length;this.letterSpacingCache=c;for(var f=0;f<l;f++)c.push(void 0!==n[f]?n[f]:h);var g=c.reduce((t,e,r)=>0===r?0:t+e||0,0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==s&&"center"!==s||(m=-d/2),"end"!==s&&"right"!==s||(m=-d),m+=y,r.forEach((e,n)=>{var{offset:o,segment:u,rotation:h}=this.findSegmentToFitChar(t,s,d,v,i,m,a,e,n);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[n],p0:u.p0,p1:u.p1,rotation:h})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,a=i?i.y:0,s=r.next(),o=s.type,u=[];switch(s.type){case Ot.MOVE_TO:this.pathM(r,u);break;case Ot.LINE_TO:o=this.pathL(r,u);break;case Ot.HORIZ_LINE_TO:o=this.pathH(r,u);break;case Ot.VERT_LINE_TO:o=this.pathV(r,u);break;case Ot.CURVE_TO:this.pathC(r,u);break;case Ot.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case Ot.QUAD_TO:this.pathQ(r,u);break;case Ot.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case Ot.ARC:u=this.pathA(r);break;case Ot.CLOSE_PATH:Pt.pathZ(r)}s.type!==Ot.CLOSE_PATH?e.push({type:o,points:u,start:{x:n,y:a},pathLength:this.calcLength(n,a,o,u)}):e.push({type:Ot.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=Pt.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=Pt.pathL(t).point;return e.push(r,i),Ot.LINE_TO}pathH(t,e){var{x:r,y:i}=Pt.pathH(t).point;return e.push(r,i),Ot.LINE_TO}pathV(t,e){var{x:r,y:i}=Pt.pathV(t).point;return e.push(r,i),Ot.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=Pt.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=Pt.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),Ot.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=Pt.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=Pt.pathT(t);return e.push(r.x,r.y,i.x,i.y),Ot.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:a,a1:s,ad:o}=Pt.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,s,o,n,i]}calcLength(t,e,r,i){var n=0,a=null,s=null,o=0;switch(r){case Ot.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case Ot.CURVE_TO:for(n=0,a=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)s=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case Ot.QUAD_TO:for(n=0,a=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)s=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case Ot.ARC:n=0;var u=i[4],h=i[5],c=i[4]+h,l=Math.PI/180;if(Math.abs(u-c)<l&&(l=Math.abs(u-c)),a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),h<0)for(o=u-l;o>c;o-=l)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;else for(o=u+l;o<c;o+=l)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],c,0),n+=this.getLineLength(a.x,a.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,i,n){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+Y),u=Math.sqrt(t*t/(1+o*o));i<e&&(u*=-1);var h=o*u,c=null;if(i===e)c={x:a,y:s+h};else if((s-r)/(a-e+Y)===o)c={x:a+u,y:s+h};else{var l,f,g=this.getLineLength(e,r,i,n);if(g<Y)return null;var p=(a-e)*(i-e)+(s-r)*(n-r);l=e+(p/=g*g)*(i-e),f=r+p*(n-r);var d=this.getLineLength(a,s,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),i<e&&(u*=-1),c={x:l+u,y:f+(h=o*u)}}return c}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var a of n){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var s=t-r,o=0;switch(a.type){case Ot.LINE_TO:i=this.getPointOnLine(s,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case Ot.ARC:var u=a.points[4],h=a.points[5],c=a.points[4]+h;if(o=u+s/a.pathLength*h,h<0&&o<c||h>=0&&o>c)break;i=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case Ot.CURVE_TO:(o=s/a.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case Ot.QUAD_TO:(o=s/a.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(i)return i;break}r+=a.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,a,s,o,u){return{x:o*Q(t)+a*$(t)+i*Z(t)+e*K(t),y:u*Q(t)+s*$(t)+n*Z(t)+r*K(t)}}getPointOnQuadraticBezier(t,e,r,i,n,a,s){return{x:a*J(t)+i*tt(t)+e*et(t),y:s*J(t)+n*tt(t)+r*et(t)}}getPointOnEllipticalArc(t,e,r,i,n,a){var s=Math.cos(a),o=Math.sin(a),u=r*Math.cos(n),h=i*Math.sin(n);return{x:t+(u*s-h*o),y:e+(u*o+h*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var a=0,s=0;s<=r;s+=i){var o=this.getPointOnPath(s),u=this.getPointOnPath(s+i);o&&u&&((a+=this.getLineLength(o.x,o.y,u.x,u.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:s}),a-=n))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var ne=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class ae extends Ct{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return a()(function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0})()}loadSvg(t){var e=this;return a()(function*(){var r=ne.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield(yield e.document.fetch(t)).text();e.image=n}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0})()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&s&&o){if(t.save(),t.translate(n,a),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var h=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:o,desiredHeight:h.height}),this.loaded&&(void 0===h.complete||h.complete)&&t.drawImage(h,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new At(t,e,t+r,e+i)}}class se extends Ct{constructor(){super(...arguments),this.type="symbol"}render(t){}}class oe{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return a()(function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach(e=>{var r=i.createElement(e);i.definitions[t]=r})}catch(t){console.error('Error while loading font "'.concat(e,'":'),t)}r.loaded=!0})()}}class ue extends bt{constructor(t,e,r){super(t,e,r),this.type="style",E(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),a=i[1].split(";");n.forEach(e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(a.forEach(e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();n&&a&&(i[n]=new rt(t,n,a))}),t.styles[r]=i,t.stylesSpecificity[r]=X(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var r=I(e);r&&new oe(t).load(n,r)}})}}})}})}}ue.parseExternalUrl=I;class he extends Ct{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new Rt(e,null)).attributes.viewBox=new rt(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new rt(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new rt(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new rt(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new rt(e,"width",n.getString())),a.hasValue()&&(i.attributes.height=new rt(e,"height",a.getString()))}var s=i.parent;i.parent=this,i.render(t),i.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return xt.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function ce(t,e,r,i,n,a){return t[r*i*4+4*e+a]}function le(t,e,r,i,n,a,s){t[r*i*4+4*e+a]=s}function fe(t,e,r){return t[e]*r}function ge(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class pe extends bt{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=_(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=i[0]*Math.PI/180;i=[ge(a,.213,.787,-.213),ge(a,.715,-.715,-.715),ge(a,.072,-.072,.928),0,0,ge(a,.213,-.213,.143),ge(a,.715,.285,.14),ge(a,.072,-.072,-.283),0,0,ge(a,.213,-.213,-.787),ge(a,.715,-.715,.715),ge(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:a,matrix:s}=this,o=t.getImageData(0,0,i,n),u=0;u<n;u++)for(var h=0;h<i;h++){var c=ce(o.data,h,u,i,0,0),l=ce(o.data,h,u,i,0,1),f=ce(o.data,h,u,i,0,2),g=ce(o.data,h,u,i,0,3),p=fe(s,0,c)+fe(s,1,l)+fe(s,2,f)+fe(s,3,g)+fe(s,4,1),d=fe(s,5,c)+fe(s,6,l)+fe(s,7,f)+fe(s,8,g)+fe(s,9,1),v=fe(s,10,c)+fe(s,11,l)+fe(s,12,f)+fe(s,13,g)+fe(s,14,1),y=fe(s,15,c)+fe(s,16,l)+fe(s,17,f)+fe(s,18,g)+fe(s,19,1);a&&(p=0,d=0,v=0,y*=g/255),le(o.data,h,u,i,0,0,p),le(o.data,h,u,i,0,1,d),le(o.data,h,u,i,0,2,v),le(o.data,h,u,i,0,3,y)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class de extends bt{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!a&&!s){var o=new At;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),i=Math.floor(o.x1),n=Math.floor(o.y1),a=Math.floor(o.width),s=Math.floor(o.height)}var u=this.removeStyles(e,de.ignoreStyles),h=r.createCanvas(i+a,n+s),c=h.getContext("2d");r.screen.setDefaults(c),this.renderChildren(c),new pe(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(c,0,0,i+a,n+s);var l=r.createCanvas(i+a,n+s),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=c.createPattern(h,"no-repeat"),f.fillRect(0,0,i+a,n+s),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,i+a,n+s),this.restoreStyles(e,u)}render(t){}}de.ignoreStyles=["mask","transform","clip-path"];var ve=()=>{};class ye extends bt{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=ve,r.closePath=ve),Reflect.apply(i,t,[]),this.children.forEach(i=>{if(void 0!==i.path){var a=void 0!==i.elementTransform?i.elementTransform():null;a||(a=xt.fromElement(e,i)),a&&a.apply(t),i.path(t),r&&(r.closePath=n),a&&a.unapply(t)}}),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class me extends bt{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var a=0,s=0;i.forEach(t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),s=Math.max(s,e)});var o=Math.floor(n.width),u=Math.floor(n.height),h=o+2*a,c=u+2*s;if(!(h<1||c<1)){var l=Math.floor(n.x),f=Math.floor(n.y),g=this.removeStyles(e,me.ignoreStyles),p=r.createCanvas(h,c),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-l+a,-f+s),e.render(d),i.forEach(t=>{"function"==typeof t.apply&&t.apply(d,0,0,h,c)}),t.drawImage(p,0,0,h,c,l-a,f-s,h,c),this.restoreStyles(e,g)}}}render(t){}}me.ignoreStyles=["filter","transform","clip-path"];class xe extends bt{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class be extends bt{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class Se extends bt{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class we extends bt{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,i,n,a){var{document:s,blurRadius:o}=this,u=s.window?s.window.document.body:null,h=t.canvas;h.id=s.getUniqueId(),u&&(h.style.display="none",u.appendChild(h)),r.i(C.a)(h,e,i,n,a,o),u&&u.removeChild(h)}}class Te extends bt{constructor(){super(...arguments),this.type="title"}}class Ae extends bt{constructor(){super(...arguments),this.type="desc"}}var Oe={svg:Rt,rect:Vt,circle:It,ellipse:kt,line:Lt,polyline:Dt,polygon:jt,path:Pt,pattern:Bt,marker:Ft,defs:zt,linearGradient:Xt,radialGradient:Yt,stop:Wt,animate:qt,animateColor:Gt,animateTransform:Qt,font:$t,"font-face":Zt,"missing-glyph":Kt,glyph:Et,text:Mt,tspan:Nt,tref:Jt,a:te,textPath:ie,image:ae,g:Ut,symbol:se,style:ue,use:he,mask:de,clipPath:ye,filter:me,feDropShadow:xe,feMorphology:be,feComposite:Se,feColorMatrix:pe,feGaussianBlur:we,title:Te,desc:Ae};function Ce(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function Pe(){return(Pe=a()(function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,i)=>{r.onload=(()=>{e(r)}),r.onerror=((t,e,r,n,a)=>{i(a)}),r.src=t})})).apply(this,arguments)}class Ee{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=Ee.createCanvas,createImage:n=Ee.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=Ee.elementTypes[e];return void 0!==r?new r(this,t):new St(this,t)}createTextNode(t){return new _t(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ce(Object(r),!0).forEach(function(e){f()(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ce(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({document:this},t))}}function Me(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function Ne(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Me(Object(r),!0).forEach(function(e){f()(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Me(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}Ee.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},Ee.createImage=function(t){return Pe.apply(this,arguments)},Ee.elementTypes=Oe;class _e{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new lt(r),this.screen=new ut(t,r),this.options=r;var i=new Ee(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return a()(function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=yield new lt(i).parse(e);return new _e(t,n,i)})()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new lt(r).parseFromString(e);return new _e(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return _e.from(t,e,Ne(Ne({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return _e.fromString(t,e,Ne(Ne({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return a()(function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(Ne({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,Ne(Ne({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}}.call(e,r(303))},2826:function(t,e,r){var i=r(2891),n=i.all;t.exports=i.IS_HTMLDDA?function(t){return"function"==typeof t||t===n}:function(t){return"function"==typeof t}},2827:function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},2828:function(t,e,r){var i=r(2860),n=Function.prototype,a=n.call,s=i&&n.bind.bind(a,a);t.exports=i?s:function(t){return function(){return a.apply(t,arguments)}}},2829:function(t,e,r){(function(e){var r=function(t){return t&&t.Math==Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||function(){return this}()||Function("return this")()}).call(e,r(126))},2830:function(t,e,r){var i=r(2829),n=r(2887),a=r(2834),s=r(2915),o=r(2912),u=r(2916),h=i.Symbol,c=n("wks"),l=u?h.for||h:h&&h.withoutSetter||s;t.exports=function(t){return a(c,t)||(c[t]=o&&a(h,t)?h[t]:l("Symbol."+t)),c[t]}},2831:function(t,e,r){var i=r(2860),n=Function.prototype.call;t.exports=i?n.bind(n):function(){return n.apply(n,arguments)}},2832:function(t,e,r){var i=r(2840),n=String,a=TypeError;t.exports=function(t){if(i(t))return t;throw a(n(t)+" is not an object")}},2833:function(t,e,r){var i=r(2829),n=r(2853).f,a=r(2848),s=r(2843),o=r(2873),u=r(2929),h=r(2898);t.exports=function(t,e){var r,c,l,f,g,p=t.target,d=t.global,v=t.stat;if(r=d?i:v?i[p]||o(p,{}):(i[p]||{}).prototype)for(c in e){if(f=e[c],l=t.dontCallGetSet?(g=n(r,c))&&g.value:r[c],!h(d?c:p+(v?".":"#")+c,t.forced)&&void 0!==l){if(typeof f==typeof l)continue;u(f,l)}(t.sham||l&&l.sham)&&a(f,"sham",!0),s(r,c,f,t)}}},2834:function(t,e,r){var i=r(2828),n=r(2868),a=i({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return a(n(t),e)}},2835:function(t,e,r){var i=r(2827);t.exports=!i(function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})},2836:function(t,e){t.exports=!1},2837:function(t,e,r){var i=r(2845),n=TypeError;t.exports=function(t){if(i(t))throw n("Can't call method on "+t);return t}},2838:function(t,e,r){var i=r(2871),n=String;t.exports=function(t){if("Symbol"===i(t))throw TypeError("Cannot convert a Symbol value to a string");return n(t)}},2839:function(t,e,r){var i=r(2826),n=r(2869),a=TypeError;t.exports=function(t){if(i(t))return t;throw a(n(t)+" is not a function")}},2840:function(t,e,r){var i=r(2826),n=r(2891),a=n.all;t.exports=n.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:i(t)||t===a}:function(t){return"object"==typeof t?null!==t:i(t)}},2841:function(t,e,r){var i=r(2835),n=r(2896),a=r(2917),s=r(2832),o=r(2888),u=TypeError,h=Object.defineProperty,c=Object.getOwnPropertyDescriptor;e.f=i?a?function(t,e,r){if(s(t),e=o(e),s(r),"function"==typeof t&&"prototype"===e&&"value"in r&&"writable"in r&&!r.writable){var i=c(t,e);i&&i.writable&&(t[e]=r.value,r={configurable:"configurable"in r?r.configurable:i.configurable,enumerable:"enumerable"in r?r.enumerable:i.enumerable,writable:!1})}return h(t,e,r)}:h:function(t,e,r){if(s(t),e=o(e),s(r),n)try{return h(t,e,r)}catch(t){}if("get"in r||"set"in r)throw u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},2842:function(t,e,r){var i=r(2828),n=i({}.toString),a=i("".slice);t.exports=function(t){return a(n(t),8,-1)}},2843:function(t,e,r){var i=r(2826),n=r(2841),a=r(2903),s=r(2873);t.exports=function(t,e,r,o){o||(o={});var u=o.enumerable,h=void 0!==o.name?o.name:e;if(i(r)&&a(r,h,o),o.global)u?t[e]=r:s(e,r);else{try{o.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:n.f(t,e,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},2844:function(t,e,r){var i=r(2829),n=r(2826);t.exports=function(t,e){return arguments.length<2?(r=i[t],n(r)?r:void 0):i[t]&&i[t][e];var r}},2845:function(t,e){t.exports=function(t){return null===t||void 0===t}},2846:function(t,e,r){var i=r(2839),n=r(2845);t.exports=function(t,e){var r=t[e];return n(r)?void 0:i(r)}},2847:function(t,e,r){var i=r(2867),n=Math.min;t.exports=function(t){return t>0?n(i(t),9007199254740991):0}},2848:function(t,e,r){var i=r(2835),n=r(2841),a=r(2857);t.exports=i?function(t,e,r){return n.f(t,e,a(1,r))}:function(t,e,r){return t[e]=r,t}},2849:function(t,e,r){(function(e){var i=r(2842);t.exports=void 0!==e&&"process"==i(e)}).call(e,r(303))},2850:function(t,e,r){var i=r(2842),n=r(2828);t.exports=function(t){if("Function"===i(t))return n(t)}},2851:function(t,e){t.exports={}},2852:function(t,e,r){"use strict";var i=r(2839),n=TypeError;t.exports.f=function(t){return new function(t){var e,r;this.promise=new t(function(t,i){if(void 0!==e||void 0!==r)throw n("Bad Promise constructor");e=t,r=i}),this.resolve=i(e),this.reject=i(r)}(t)}},2853:function(t,e,r){var i=r(2835),n=r(2831),a=r(2955),s=r(2857),o=r(2856),u=r(2888),h=r(2834),c=r(2896),l=Object.getOwnPropertyDescriptor;e.f=i?l:function(t,e){if(t=o(t),e=u(e),c)try{return l(t,e)}catch(t){}if(h(t,e))return s(!n(a.f,t,e),t[e])}},2854:function(t,e,r){var i=r(2829),n=r(2855),a=r(2826),s=r(2898),o=r(2880),u=r(2830),h=r(2936),c=r(2892),l=r(2836),f=r(2874),g=n&&n.prototype,p=u("species"),d=!1,v=a(i.PromiseRejectionEvent),y=s("Promise",function(){var t=o(n),e=t!==String(n);if(!e&&66===f)return!0;if(l&&(!g.catch||!g.finally))return!0;if(!f||f<51||!/native code/.test(t)){var r=new n(function(t){t(1)}),i=function(t){t(function(){},function(){})};if((r.constructor={})[p]=i,!(d=r.then(function(){})instanceof i))return!0}return!e&&(h||c)&&!v});t.exports={CONSTRUCTOR:y,REJECTION_EVENT:v,SUBCLASSING:d}},2855:function(t,e,r){var i=r(2829);t.exports=i.Promise},2856:function(t,e,r){var i=r(2897),n=r(2837);t.exports=function(t){return i(n(t))}},2857:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},2858:function(t,e,r){var i=r(2829),n=r(2840),a=i.document,s=n(a)&&n(a.createElement);t.exports=function(t){return s?a.createElement(t):{}}},2859:function(t,e){t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},2860:function(t,e,r){var i=r(2827);t.exports=!i(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},2861:function(t,e,r){var i=r(2835),n=r(2834),a=Function.prototype,s=i&&Object.getOwnPropertyDescriptor,o=n(a,"name"),u=o&&"something"===function(){}.name,h=o&&(!i||i&&s(a,"name").configurable);t.exports={EXISTS:o,PROPER:u,CONFIGURABLE:h}},2862:function(t,e,r){var i,n,a,s=r(2969),o=r(2829),u=r(2840),h=r(2848),c=r(2834),l=r(2886),f=r(2885),g=r(2879),p=o.TypeError,d=o.WeakMap;if(s||l.state){var v=l.state||(l.state=new d);v.get=v.get,v.has=v.has,v.set=v.set,i=function(t,e){if(v.has(t))throw p("Object already initialized");return e.facade=t,v.set(t,e),e},n=function(t){return v.get(t)||{}},a=function(t){return v.has(t)}}else{var y=f("state");g[y]=!0,i=function(t,e){if(c(t,y))throw p("Object already initialized");return e.facade=t,h(t,y,e),e},n=function(t){return c(t,y)?t[y]:{}},a=function(t){return c(t,y)}}t.exports={set:i,get:n,has:a,enforce:function(t){return a(t)?n(t):i(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=n(e)).type!==t)throw p("Incompatible receiver, "+t+" required");return r}}}},2863:function(t,e,r){var i=r(2847);t.exports=function(t){return i(t.length)}},2864:function(t,e,r){var i,n=r(2832),a=r(2951),s=r(2875),o=r(2879),u=r(2895),h=r(2858),c=r(2885)("IE_PROTO"),l=function(){},f=function(t){return"<script>"+t+"<\/script>"},g=function(t){t.write(f("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}var t,e;p="undefined"!=typeof document?document.domain&&i?g(i):((e=h("iframe")).style.display="none",u.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(f("document.F=Object")),t.close(),t.F):g(i);for(var r=s.length;r--;)delete p.prototype[s[r]];return p()};o[c]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(l.prototype=n(t),r=new l,l.prototype=null,r[c]=t):r=p(),void 0===e?r:a.f(r,e)}},2865:function(t,e,r){var i=r(2828);t.exports=i({}.isPrototypeOf)},2866:function(t,e,r){"use strict";var i,n,a=r(2831),s=r(2828),o=r(2838),u=r(2909),h=r(2910),c=r(2887),l=r(2864),f=r(2862).get,g=r(2960),p=r(2961),d=c("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,y=v,m=s("".charAt),x=s("".indexOf),b=s("".replace),S=s("".slice),w=(n=/b*/g,a(v,i=/a/,"a"),a(v,n,"a"),0!==i.lastIndex||0!==n.lastIndex),T=h.BROKEN_CARET,A=void 0!==/()??/.exec("")[1];(w||A||T||g||p)&&(y=function(t){var e,r,i,n,s,h,c,g=this,p=f(g),O=o(t),C=p.raw;if(C)return C.lastIndex=g.lastIndex,e=a(y,C,O),g.lastIndex=C.lastIndex,e;var P=p.groups,E=T&&g.sticky,M=a(u,g),N=g.source,_=0,R=O;if(E&&(M=b(M,"y",""),-1===x(M,"g")&&(M+="g"),R=S(O,g.lastIndex),g.lastIndex>0&&(!g.multiline||g.multiline&&"\n"!==m(O,g.lastIndex-1))&&(N="(?: "+N+")",R=" "+R,_++),r=new RegExp("^(?:"+N+")",M)),A&&(r=new RegExp("^"+N+"$(?!\\s)",M)),w&&(i=g.lastIndex),n=a(v,E?r:g,R),E?n?(n.input=S(n.input,_),n[0]=S(n[0],_),n.index=g.lastIndex,g.lastIndex+=n[0].length):g.lastIndex=0:w&&n&&(g.lastIndex=g.global?n.index+n[0].length:i),A&&n&&n.length>1&&a(d,n[0],r,function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(n[s]=void 0)}),n&&P)for(n.groups=h=l(null),s=0;s<P.length;s++)h[(c=P[s])[0]]=n[c[1]];return n}),t.exports=y},2867:function(t,e,r){var i=r(2949);t.exports=function(t){var e=+t;return e!=e||0===e?0:i(e)}},2868:function(t,e,r){var i=r(2837),n=Object;t.exports=function(t){return n(i(t))}},2869:function(t,e){var r=String;t.exports=function(t){try{return r(t)}catch(t){return"Object"}}},2870:function(t,e,r){"use strict";var i=r(2963).charAt;t.exports=function(t,e,r){return e+(r?i(t,e).length:1)}},2871:function(t,e,r){var i=r(2967),n=r(2826),a=r(2842),s=r(2830)("toStringTag"),o=Object,u="Arguments"==a(function(){return arguments}());t.exports=i?a:function(t){var e,r,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=o(t),s))?r:u?a(e):"Object"==(i=a(e))&&n(e.callee)?"Arguments":i}},2872:function(t,e,r){var i=r(2830)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[i]=!1,"/./"[t](e)}catch(t){}}return!1}},2873:function(t,e,r){var i=r(2829),n=Object.defineProperty;t.exports=function(t,e){try{n(i,t,{value:e,configurable:!0,writable:!0})}catch(r){i[t]=e}return e}},2874:function(t,e,r){var i,n,a=r(2829),s=r(2859),o=a.process,u=a.Deno,h=o&&o.versions||u&&u.version,c=h&&h.v8;c&&(n=(i=c.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!n&&s&&(!(i=s.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=s.match(/Chrome\/(\d+)/))&&(n=+i[1]),t.exports=n},2875:function(t,e){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2876:function(t,e,r){"use strict";r(2980);var i=r(2850),n=r(2843),a=r(2866),s=r(2827),o=r(2830),u=r(2848),h=o("species"),c=RegExp.prototype;t.exports=function(t,e,r,l){var f=o(t),g=!s(function(){var e={};return e[f]=function(){return 7},7!=""[t](e)}),p=g&&!s(function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[h]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return e=!0,null},r[f](""),!e});if(!g||!p||r){var d=i(/./[f]),v=e(f,""[t],function(t,e,r,n,s){var o=i(t),u=e.exec;return u===a||u===c.exec?g&&!s?{done:!0,value:d(e,r,n)}:{done:!0,value:o(r,e,n)}:{done:!1}});n(String.prototype,t,v[0]),n(c,f,v[1])}l&&u(c[f],"sham",!0)}},2877:function(t,e,r){var i=r(2860),n=Function.prototype,a=n.apply,s=n.call;t.exports="object"==typeof Reflect&&Reflect.apply||(i?s.bind(a):function(){return s.apply(a,arguments)})},2878:function(t,e,r){var i=r(2850),n=r(2839),a=r(2860),s=i(i.bind);t.exports=function(t,e){return n(t),void 0===e?t:a?s(t,e):function(){return t.apply(e,arguments)}}},2879:function(t,e){t.exports={}},2880:function(t,e,r){var i=r(2828),n=r(2826),a=r(2886),s=i(Function.toString);n(a.inspectSource)||(a.inspectSource=function(t){return s(t)}),t.exports=a.inspectSource},2881:function(t,e,r){var i=r(2899),n=TypeError;t.exports=function(t){if(i(t))throw n("The method doesn't accept regular expressions");return t}},2882:function(t,e){t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},2883:function(t,e,r){var i=r(2831),n=r(2832),a=r(2826),s=r(2842),o=r(2866),u=TypeError;t.exports=function(t,e){var r=t.exec;if(a(r)){var h=i(r,t,e);return null!==h&&n(h),h}if("RegExp"===s(t))return i(o,t,e);throw u("RegExp#exec called on incompatible receiver")}},2884:function(t,e,r){var i=r(2841).f,n=r(2834),a=r(2830)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!n(t,a)&&i(t,a,{configurable:!0,value:e})}},2885:function(t,e,r){var i=r(2887),n=r(2915),a=i("keys");t.exports=function(t){return a[t]||(a[t]=n(t))}},2886:function(t,e,r){var i=r(2829),n=r(2873),a=i["__core-js_shared__"]||n("__core-js_shared__",{});t.exports=a},2887:function(t,e,r){var i=r(2836),n=r(2886);(t.exports=function(t,e){return n[t]||(n[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.29.1",mode:i?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.29.1/LICENSE",source:"https://github.com/zloirock/core-js"})},2888:function(t,e,r){var i=r(2966),n=r(2900);t.exports=function(t){var e=i(t,"string");return n(e)?e:e+""}},2889:function(t,e,r){var i=r(2856),n=r(2914),a=r(2863),s=function(t){return function(e,r,s){var o,u=i(e),h=a(u),c=n(s,h);if(t&&r!=r){for(;h>c;)if((o=u[c++])!=o)return!0}else for(;h>c;c++)if((t||c in u)&&u[c]===r)return t||c||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},2890:function(t,e,r){"use strict";var i=r(2827);t.exports=function(t,e){var r=[][t];return!!r&&i(function(){r.call(null,e||function(){return 1},1)})}},2891:function(t,e){var r="object"==typeof document&&document.all,i=void 0===r&&void 0!==r;t.exports={all:r,IS_HTMLDDA:i}},2892:function(t,e){t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},2893:function(t,e,r){var i=r(2859);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(i)},2894:function(t,e,r){var i=r(2871),n=r(2846),a=r(2845),s=r(2851),o=r(2830)("iterator");t.exports=function(t){if(!a(t))return n(t,o)||n(t,"@@iterator")||s[i(t)]}},2895:function(t,e,r){var i=r(2844);t.exports=i("document","documentElement")},2896:function(t,e,r){var i=r(2835),n=r(2827),a=r(2858);t.exports=!i&&!n(function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a})},2897:function(t,e,r){var i=r(2828),n=r(2827),a=r(2842),s=Object,o=i("".split);t.exports=n(function(){return!s("z").propertyIsEnumerable(0)})?function(t){return"String"==a(t)?o(t,""):s(t)}:s},2898:function(t,e,r){var i=r(2827),n=r(2826),a=/#|\.prototype\./,s=function(t,e){var r=u[o(t)];return r==c||r!=h&&(n(e)?i(e):!!e)},o=s.normalize=function(t){return String(t).replace(a,".").toLowerCase()},u=s.data={},h=s.NATIVE="N",c=s.POLYFILL="P";t.exports=s},2899:function(t,e,r){var i=r(2840),n=r(2842),a=r(2830)("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[a])?!!e:"RegExp"==n(t))}},2900:function(t,e,r){var i=r(2844),n=r(2826),a=r(2865),s=r(2916),o=Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=i("Symbol");return n(e)&&a(e.prototype,o(t))}},2901:function(t,e,r){var i=r(2878),n=r(2831),a=r(2832),s=r(2869),o=r(2943),u=r(2863),h=r(2865),c=r(2940),l=r(2894),f=r(2946),g=TypeError,p=function(t,e){this.stopped=t,this.result=e},d=p.prototype;t.exports=function(t,e,r){var v,y,m,x,b,S,w,T=r&&r.that,A=!(!r||!r.AS_ENTRIES),O=!(!r||!r.IS_RECORD),C=!(!r||!r.IS_ITERATOR),P=!(!r||!r.INTERRUPTED),E=i(e,T),M=function(t){return v&&f(v,"normal",t),new p(!0,t)},N=function(t){return A?(a(t),P?E(t[0],t[1],M):E(t[0],t[1])):P?E(t,M):E(t)};if(O)v=t.iterator;else if(C)v=t;else{if(!(y=l(t)))throw g(s(t)+" is not iterable");if(o(y)){for(m=0,x=u(t);x>m;m++)if((b=N(t[m]))&&h(d,b))return b;return new p(!1)}v=c(t,y)}for(S=O?t.next:v.next;!(w=n(S,v)).done;){try{b=N(w.value)}catch(t){f(v,"throw",t)}if("object"==typeof b&&b&&h(d,b))return b}return new p(!1)}},2902:function(t,e,r){"use strict";var i,n,a,s=r(2827),o=r(2826),u=r(2840),h=r(2864),c=r(2904),l=r(2843),f=r(2830),g=r(2836),p=f("iterator"),d=!1;[].keys&&("next"in(a=[].keys())?(n=c(c(a)))!==Object.prototype&&(i=n):d=!0),!u(i)||s(function(){var t={};return i[p].call(t)!==t})?i={}:g&&(i=h(i)),o(i[p])||l(i,p,function(){return this}),t.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:d}},2903:function(t,e,r){var i=r(2828),n=r(2827),a=r(2826),s=r(2834),o=r(2835),u=r(2861).CONFIGURABLE,h=r(2880),c=r(2862),l=c.enforce,f=c.get,g=String,p=Object.defineProperty,d=i("".slice),v=i("".replace),y=i([].join),m=o&&!n(function(){return 8!==p(function(){},"length",{value:8}).length}),x=String(String).split("String"),b=t.exports=function(t,e,r){"Symbol("===d(g(e),0,7)&&(e="["+v(g(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!s(t,"name")||u&&t.name!==e)&&(o?p(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&s(r,"arity")&&t.length!==r.arity&&p(t,"length",{value:r.arity});try{r&&s(r,"constructor")&&r.constructor?o&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var i=l(t);return s(i,"source")||(i.source=y(x,"string"==typeof e?e:"")),t};Function.prototype.toString=b(function(){return a(this)&&f(this).source||h(this)},"toString")},2904:function(t,e,r){var i=r(2834),n=r(2826),a=r(2868),s=r(2885),o=r(2930),u=s("IE_PROTO"),h=Object,c=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=a(t);if(i(e,u))return e[u];var r=e.constructor;return n(r)&&e instanceof r?r.prototype:e instanceof h?c:null}},2905:function(t,e,r){var i=r(2828),n=r(2834),a=r(2856),s=r(2889).indexOf,o=r(2879),u=i([].push);t.exports=function(t,e){var r,i=a(t),h=0,c=[];for(r in i)!n(o,r)&&n(i,r)&&u(c,r);for(;e.length>h;)n(i,r=e[h++])&&(~s(c,r)||u(c,r));return c}},2906:function(t,e,r){var i=r(2939),n=r(2832),a=r(2922);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=i(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,i){return n(r),a(i),e?t(r,i):r.__proto__=i,r}}():void 0)},2907:function(t,e,r){var i=r(2855),n=r(2928),a=r(2854).CONSTRUCTOR;t.exports=a||!n(function(t){i.all(t).then(void 0,function(){})})},2908:function(t,e){var r=function(){this.head=null,this.tail=null};r.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=r},2909:function(t,e,r){"use strict";var i=r(2832);t.exports=function(){var t=i(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},2910:function(t,e,r){var i=r(2827),n=r(2829).RegExp,a=i(function(){var t=n("a","y");return t.lastIndex=2,null!=t.exec("abcd")}),s=a||i(function(){return!n("a","y").sticky}),o=a||i(function(){var t=n("^r","gy");return t.lastIndex=2,null!=t.exec("str")});t.exports={BROKEN_CARET:o,MISSED_STICKY:s,UNSUPPORTED_Y:a}},2911:function(t,e,r){var i=r(2832),n=r(2921),a=r(2845),s=r(2830)("species");t.exports=function(t,e){var r,o=i(t).constructor;return void 0===o||a(r=i(o)[s])?e:n(r)}},2912:function(t,e,r){var i=r(2874),n=r(2827);t.exports=!!Object.getOwnPropertySymbols&&!n(function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&i&&i<41})},2913:function(t,e,r){var i,n,a,s,o=r(2829),u=r(2877),h=r(2878),c=r(2826),l=r(2834),f=r(2827),g=r(2895),p=r(2927),d=r(2858),v=r(2968),y=r(2893),m=r(2849),x=o.setImmediate,b=o.clearImmediate,S=o.process,w=o.Dispatch,T=o.Function,A=o.MessageChannel,O=o.String,C=0,P={};f(function(){i=o.location});var E=function(t){if(l(P,t)){var e=P[t];delete P[t],e()}},M=function(t){return function(){E(t)}},N=function(t){E(t.data)},_=function(t){o.postMessage(O(t),i.protocol+"//"+i.host)};x&&b||(x=function(t){v(arguments.length,1);var e=c(t)?t:T(t),r=p(arguments,1);return P[++C]=function(){u(e,void 0,r)},n(C),C},b=function(t){delete P[t]},m?n=function(t){S.nextTick(M(t))}:w&&w.now?n=function(t){w.now(M(t))}:A&&!y?(s=(a=new A).port2,a.port1.onmessage=N,n=h(s.postMessage,s)):o.addEventListener&&c(o.postMessage)&&!o.importScripts&&i&&"file:"!==i.protocol&&!f(_)?(n=_,o.addEventListener("message",N,!1)):n="onreadystatechange"in d("script")?function(t){g.appendChild(d("script")).onreadystatechange=function(){g.removeChild(this),E(t)}}:function(t){setTimeout(M(t),0)}),t.exports={set:x,clear:b}},2914:function(t,e,r){var i=r(2867),n=Math.max,a=Math.min;t.exports=function(t,e){var r=i(t);return r<0?n(r+e,0):a(r,e)}},2915:function(t,e,r){var i=r(2828),n=0,a=Math.random(),s=i(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+s(++n+a,36)}},2916:function(t,e,r){var i=r(2912);t.exports=i&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},2917:function(t,e,r){var i=r(2835),n=r(2827);t.exports=i&&n(function(){return 42!=Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},2918:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},2919:function(t,e,r){"use strict";var i=r(2856),n=r(2923),a=r(2851),s=r(2862),o=r(2841).f,u=r(2948),h=r(2931),c=r(2836),l=r(2835),f=s.set,g=s.getterFor("Array Iterator");t.exports=u(Array,"Array",function(t,e){f(this,{type:"Array Iterator",target:i(t),index:0,kind:e})},function(){var t=g(this),e=t.target,r=t.kind,i=t.index++;return!e||i>=e.length?(t.target=void 0,h(void 0,!0)):h("keys"==r?i:"values"==r?e[i]:[i,e[i]],!1)},"values");var p=a.Arguments=a.Array;if(n("keys"),n("values"),n("entries"),!c&&l&&"values"!==p.name)try{o(p,"name",{value:"values"})}catch(t){}},2921:function(t,e,r){var i=r(2945),n=r(2869),a=TypeError;t.exports=function(t){if(i(t))return t;throw a(n(t)+" is not a constructor")}},2922:function(t,e,r){var i=r(2826),n=String,a=TypeError;t.exports=function(t){if("object"==typeof t||i(t))return t;throw a("Can't set "+n(t)+" as a prototype")}},2923:function(t,e,r){var i=r(2830),n=r(2864),a=r(2841).f,s=i("unscopables"),o=Array.prototype;void 0==o[s]&&a(o,s,{configurable:!0,value:n(null)}),t.exports=function(t){o[s][t]=!0}},2924:function(t,e,r){var i=r(2865),n=TypeError;t.exports=function(t,e){if(i(e,t))return t;throw n("Incorrect invocation")}},2925:function(t,e,r){var i=r(2839),n=r(2868),a=r(2897),s=r(2863),o=TypeError,u=function(t){return function(e,r,u,h){i(r);var c=n(e),l=a(c),f=s(c),g=t?f-1:0,p=t?-1:1;if(u<2)for(;;){if(g in l){h=l[g],g+=p;break}if(g+=p,t?g<0:f<=g)throw o("Reduce of empty array with no initial value")}for(;t?g>=0:f>g;g+=p)g in l&&(h=r(h,l[g],g,c));return h}};t.exports={left:u(!1),right:u(!0)}},2926:function(t,e,r){var i=r(2914),n=r(2863),a=r(2932),s=Array,o=Math.max;t.exports=function(t,e,r){for(var u=n(t),h=i(e,u),c=i(void 0===r?u:r,u),l=s(o(c-h,0)),f=0;h<c;h++,f++)a(l,f,t[h]);return l.length=f,l}},2927:function(t,e,r){var i=r(2828);t.exports=i([].slice)},2928:function(t,e,r){var i=r(2830)("iterator"),n=!1;try{var a=0,s={next:function(){return{done:!!a++}},return:function(){n=!0}};s[i]=function(){return this},Array.from(s,function(){throw 2})}catch(t){}t.exports=function(t,e){if(!e&&!n)return!1;var r=!1;try{var a={};a[i]=function(){return{next:function(){return{done:r=!0}}}},t(a)}catch(t){}return r}},2929:function(t,e,r){var i=r(2834),n=r(2957),a=r(2853),s=r(2841);t.exports=function(t,e,r){for(var o=n(e),u=s.f,h=a.f,c=0;c<o.length;c++){var l=o[c];i(t,l)||r&&i(r,l)||u(t,l,h(e,l))}}},2930:function(t,e,r){var i=r(2827);t.exports=!i(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},2931:function(t,e){t.exports=function(t,e){return{value:t,done:e}}},2932:function(t,e,r){"use strict";var i=r(2888),n=r(2841),a=r(2857);t.exports=function(t,e,r){var s=i(e);s in t?n.f(t,s,a(0,r)):t[s]=r}},2933:function(t,e,r){var i=r(2903),n=r(2841);t.exports=function(t,e,r){return r.get&&i(r.get,e,{getter:!0}),r.set&&i(r.set,e,{setter:!0}),n.f(t,e,r)}},2934:function(t,e){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},2935:function(t,e,r){var i=r(2858)("span").classList,n=i&&i.constructor&&i.constructor.prototype;t.exports=n===Object.prototype?void 0:n},2936:function(t,e,r){var i=r(2892),n=r(2849);t.exports=!i&&!n&&"object"==typeof window&&"object"==typeof document},2937:function(t,e,r){var i=r(2859);t.exports=/ipad|iphone|ipod/i.test(i)&&"undefined"!=typeof Pebble},2938:function(t,e,r){var i=r(2859);t.exports=/web0s(?!.*chrome)/i.test(i)},2939:function(t,e,r){var i=r(2828),n=r(2839);t.exports=function(t,e,r){try{return i(n(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},2940:function(t,e,r){var i=r(2831),n=r(2839),a=r(2832),s=r(2869),o=r(2894),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?o(t):e;if(n(r))return a(i(r,t));throw u(s(t)+" is not iterable")}},2941:function(t,e,r){var i=r(2828),n=r(2868),a=Math.floor,s=i("".charAt),o=i("".replace),u=i("".slice),h=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,c=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,i,l,f){var g=r+t.length,p=i.length,d=c;return void 0!==l&&(l=n(l),d=h),o(f,d,function(n,o){var h;switch(s(o,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,r);case"'":return u(e,g);case"<":h=l[u(o,1,-1)];break;default:var c=+o;if(0===c)return n;if(c>p){var f=a(c/10);return 0===f?n:f<=p?void 0===i[f-1]?s(o,1):i[f-1]+s(o,1):n}h=i[c-1]}return void 0===h?"":h})}},2942:function(t,e){t.exports=function(t,e){try{1==arguments.length?console.error(t):console.error(t,e)}catch(t){}}},2943:function(t,e,r){var i=r(2830),n=r(2851),a=i("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||s[a]===t)}},2944:function(t,e,r){var i=r(2842);t.exports=Array.isArray||function(t){return"Array"==i(t)}},2945:function(t,e,r){var i=r(2828),n=r(2827),a=r(2826),s=r(2871),o=r(2844),u=r(2880),h=function(){},c=[],l=o("Reflect","construct"),f=/^\s*(?:class|function)\b/,g=i(f.exec),p=!f.exec(h),d=function(t){if(!a(t))return!1;try{return l(h,c,t),!0}catch(t){return!1}},v=function(t){if(!a(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!g(f,u(t))}catch(t){return!0}};v.sham=!0,t.exports=!l||n(function(){var t;return d(d.call)||!d(Object)||!d(function(){t=!0})||t})?v:d},2946:function(t,e,r){var i=r(2831),n=r(2832),a=r(2846);t.exports=function(t,e,r){var s,o;n(t);try{if(!(s=a(t,"return"))){if("throw"===e)throw r;return r}s=i(s,t)}catch(t){o=!0,s=t}if("throw"===e)throw r;if(o)throw s;return n(s),r}},2947:function(t,e,r){"use strict";var i=r(2902).IteratorPrototype,n=r(2864),a=r(2857),s=r(2884),o=r(2851),u=function(){return this};t.exports=function(t,e,r,h){var c=e+" Iterator";return t.prototype=n(i,{next:a(+!h,r)}),s(t,c,!1,!0),o[c]=u,t}},2948:function(t,e,r){"use strict";var i=r(2833),n=r(2831),a=r(2836),s=r(2861),o=r(2826),u=r(2947),h=r(2904),c=r(2906),l=r(2884),f=r(2848),g=r(2843),p=r(2830),d=r(2851),v=r(2902),y=s.PROPER,m=s.CONFIGURABLE,x=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,S=p("iterator"),w=function(){return this};t.exports=function(t,e,r,s,p,v,T){u(r,e,s);var A,O,C,P=function(t){if(t===p&&R)return R;if(!b&&t in N)return N[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},E=e+" Iterator",M=!1,N=t.prototype,_=N[S]||N["@@iterator"]||p&&N[p],R=!b&&_||P(p),V="Array"==e&&N.entries||_;if(V&&(A=h(V.call(new t)))!==Object.prototype&&A.next&&(a||h(A)===x||(c?c(A,x):o(A[S])||g(A,S,w)),l(A,E,!0,!0),a&&(d[E]=w)),y&&"values"==p&&_&&"values"!==_.name&&(!a&&m?f(N,"name","values"):(M=!0,R=function(){return n(_,this)})),p)if(O={values:P("values"),keys:v?R:P("keys"),entries:P("entries")},T)for(C in O)!b&&!M&&C in N||g(N,C,O[C]);else i({target:e,proto:!0,forced:b||M},O);return a&&!T||N[S]===R||g(N,S,R,{name:p}),d[e]=R,O}},2949:function(t,e){var r=Math.ceil,i=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?i:r)(e)}},2950:function(t,e,r){var i,n,a,s,o,u=r(2829),h=r(2878),c=r(2853).f,l=r(2913).set,f=r(2908),g=r(2893),p=r(2937),d=r(2938),v=r(2849),y=u.MutationObserver||u.WebKitMutationObserver,m=u.document,x=u.process,b=u.Promise,S=c(u,"queueMicrotask"),w=S&&S.value;if(!w){var T=new f,A=function(){var t,e;for(v&&(t=x.domain)&&t.exit();e=T.get();)try{e()}catch(t){throw T.head&&i(),t}t&&t.enter()};g||v||d||!y||!m?!p&&b&&b.resolve?((s=b.resolve(void 0)).constructor=b,o=h(s.then,s),i=function(){o(A)}):v?i=function(){x.nextTick(A)}:(l=h(l,u),i=function(){l(A)}):(n=!0,a=m.createTextNode(""),new y(A).observe(a,{characterData:!0}),i=function(){a.data=n=!n}),w=function(t){T.head||i(),T.add(t)}}t.exports=w},2951:function(t,e,r){var i=r(2835),n=r(2917),a=r(2841),s=r(2832),o=r(2856),u=r(2954);e.f=i&&!n?Object.defineProperties:function(t,e){s(t);for(var r,i=o(e),n=u(e),h=n.length,c=0;h>c;)a.f(t,r=n[c++],i[r]);return t}},2952:function(t,e,r){var i=r(2905),n=r(2875).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,n)}},2953:function(t,e){e.f=Object.getOwnPropertySymbols},2954:function(t,e,r){var i=r(2905),n=r(2875);t.exports=Object.keys||function(t){return i(t,n)}},2955:function(t,e,r){"use strict";var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,a=n&&!i.call({1:2},1);e.f=a?function(t){var e=n(this,t);return!!e&&e.enumerable}:i},2956:function(t,e,r){var i=r(2831),n=r(2826),a=r(2840),s=TypeError;t.exports=function(t,e){var r,o;if("string"===e&&n(r=t.toString)&&!a(o=i(r,t)))return o;if(n(r=t.valueOf)&&!a(o=i(r,t)))return o;if("string"!==e&&n(r=t.toString)&&!a(o=i(r,t)))return o;throw s("Can't convert object to primitive value")}},2957:function(t,e,r){var i=r(2844),n=r(2828),a=r(2952),s=r(2953),o=r(2832),u=n([].concat);t.exports=i("Reflect","ownKeys")||function(t){var e=a.f(o(t)),r=s.f;return r?u(e,r(t)):e}},2958:function(t,e,r){var i=r(2832),n=r(2840),a=r(2852);t.exports=function(t,e){if(i(t),n(e)&&e.constructor===t)return e;var r=a.f(t);return(0,r.resolve)(e),r.promise}},2959:function(t,e,r){var i=r(2831),n=r(2834),a=r(2865),s=r(2909),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||n(t,"flags")||!a(o,t)?e:i(s,t)}},2960:function(t,e,r){var i=r(2827),n=r(2829).RegExp;t.exports=i(function(){var t=n(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})},2961:function(t,e,r){var i=r(2827),n=r(2829).RegExp;t.exports=i(function(){var t=n("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})},2962:function(t,e,r){"use strict";var i=r(2844),n=r(2933),a=r(2830),s=r(2835),o=a("species");t.exports=function(t){var e=i(t);s&&e&&!e[o]&&n(e,o,{configurable:!0,get:function(){return this}})}},2963:function(t,e,r){var i=r(2828),n=r(2867),a=r(2838),s=r(2837),o=i("".charAt),u=i("".charCodeAt),h=i("".slice),c=function(t){return function(e,r){var i,c,l=a(s(e)),f=n(r),g=l.length;return f<0||f>=g?t?"":void 0:(i=u(l,f))<55296||i>56319||f+1===g||(c=u(l,f+1))<56320||c>57343?t?o(l,f):i:t?h(l,f,f+2):c-56320+(i-55296<<10)+65536}};t.exports={codeAt:c(!1),charAt:c(!0)}},2964:function(t,e,r){var i=r(2861).PROPER,n=r(2827),a=r(2918);t.exports=function(t){return n(function(){return!!a[t]()||"​᠎"!=="​᠎"[t]()||i&&a[t].name!==t})}},2965:function(t,e,r){var i=r(2828),n=r(2837),a=r(2838),s=r(2918),o=i("".replace),u=RegExp("^["+s+"]+"),h=RegExp("(^|[^"+s+"])["+s+"]+$"),c=function(t){return function(e){var r=a(n(e));return 1&t&&(r=o(r,u,"")),2&t&&(r=o(r,h,"$1")),r}};t.exports={start:c(1),end:c(2),trim:c(3)}},2966:function(t,e,r){var i=r(2831),n=r(2840),a=r(2900),s=r(2846),o=r(2956),u=r(2830),h=TypeError,c=u("toPrimitive");t.exports=function(t,e){if(!n(t)||a(t))return t;var r,u=s(t,c);if(u){if(void 0===e&&(e="default"),r=i(u,t,e),!n(r)||a(r))return r;throw h("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},2967:function(t,e,r){var i={};i[r(2830)("toStringTag")]="z",t.exports="[object z]"===String(i)},2968:function(t,e){var r=TypeError;t.exports=function(t,e){if(t<e)throw r("Not enough arguments");return t}},2969:function(t,e,r){var i=r(2829),n=r(2826),a=i.WeakMap;t.exports=n(a)&&/native code/.test(String(a))},2970:function(t,e,r){"use strict";var i=r(2833),n=r(2850),a=r(2889).indexOf,s=r(2890),o=n([].indexOf),u=!!o&&1/o([1],1,-0)<0;i({target:"Array",proto:!0,forced:u||!s("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?o(this,t,e)||0:a(this,t,e)}})},2971:function(t,e,r){"use strict";var i=r(2833),n=r(2925).left,a=r(2890),s=r(2874);i({target:"Array",proto:!0,forced:!r(2849)&&s>79&&s<83||!a("reduce")},{reduce:function(t){var e=arguments.length;return n(this,t,e,e>1?arguments[1]:void 0)}})},2972:function(t,e,r){"use strict";var i=r(2833),n=r(2828),a=r(2944),s=n([].reverse),o=[1,2];i({target:"Array",proto:!0,forced:String(o)===String(o.reverse())},{reverse:function(){return a(this)&&(this.length=this.length),s(this)}})},2973:function(t,e,r){"use strict";var i=r(2833),n=r(2831),a=r(2839),s=r(2852),o=r(2882),u=r(2901);i({target:"Promise",stat:!0,forced:r(2907)},{all:function(t){var e=this,r=s.f(e),i=r.resolve,h=r.reject,c=o(function(){var r=a(e.resolve),s=[],o=0,c=1;u(t,function(t){var a=o++,u=!1;c++,n(r,e,t).then(function(t){u||(u=!0,s[a]=t,--c||i(s))},h)}),--c||i(s)});return c.error&&h(c.value),r.promise}})},2974:function(t,e,r){"use strict";var i=r(2833),n=r(2836),a=r(2854).CONSTRUCTOR,s=r(2855),o=r(2844),u=r(2826),h=r(2843),c=s&&s.prototype;if(i({target:"Promise",proto:!0,forced:a,real:!0},{catch:function(t){return this.then(void 0,t)}}),!n&&u(s)){var l=o("Promise").prototype.catch;c.catch!==l&&h(c,"catch",l,{unsafe:!0})}},2975:function(t,e,r){"use strict";var i,n,a,s=r(2833),o=r(2836),u=r(2849),h=r(2829),c=r(2831),l=r(2843),f=r(2906),g=r(2884),p=r(2962),d=r(2839),v=r(2826),y=r(2840),m=r(2924),x=r(2911),b=r(2913).set,S=r(2950),w=r(2942),T=r(2882),A=r(2908),O=r(2862),C=r(2855),P=r(2854),E=r(2852),M=P.CONSTRUCTOR,N=P.REJECTION_EVENT,_=P.SUBCLASSING,R=O.getterFor("Promise"),V=O.set,I=C&&C.prototype,k=C,L=I,D=h.TypeError,j=h.document,B=h.process,F=E.f,z=F,U=!!(j&&j.createEvent&&h.dispatchEvent),H=function(t){var e;return!(!y(t)||!v(e=t.then))&&e},X=function(t,e){var r,i,n,a=e.value,s=1==e.state,o=s?t.ok:t.fail,u=t.resolve,h=t.reject,l=t.domain;try{o?(s||(2===e.rejection&&Q(e),e.rejection=1),!0===o?r=a:(l&&l.enter(),r=o(a),l&&(l.exit(),n=!0)),r===t.promise?h(D("Promise-chain cycle")):(i=H(r))?c(i,r,u,h):u(r)):h(a)}catch(t){l&&!n&&l.exit(),h(t)}},Y=function(t,e){t.notified||(t.notified=!0,S(function(){for(var r,i=t.reactions;r=i.get();)X(r,t);t.notified=!1,e&&!t.rejection&&q(t)}))},W=function(t,e,r){var i,n;U?((i=j.createEvent("Event")).promise=e,i.reason=r,i.initEvent(t,!1,!0),h.dispatchEvent(i)):i={promise:e,reason:r},!N&&(n=h["on"+t])?n(i):"unhandledrejection"===t&&w("Unhandled promise rejection",r)},q=function(t){c(b,h,function(){var e,r=t.facade,i=t.value;if(G(t)&&(e=T(function(){u?B.emit("unhandledRejection",i,r):W("unhandledrejection",r,i)}),t.rejection=u||G(t)?2:1,e.error))throw e.value})},G=function(t){return 1!==t.rejection&&!t.parent},Q=function(t){c(b,h,function(){var e=t.facade;u?B.emit("rejectionHandled",e):W("rejectionhandled",e,t.value)})},$=function(t,e,r){return function(i){t(e,i,r)}},Z=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,Y(t,!0))},K=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw D("Promise can't be resolved itself");var i=H(e);i?S(function(){var r={done:!1};try{c(i,e,$(K,r,t),$(Z,r,t))}catch(e){Z(r,e,t)}}):(t.value=e,t.state=1,Y(t,!1))}catch(e){Z({done:!1},e,t)}}};if(M&&(L=(k=function(t){m(this,L),d(t),c(i,this);var e=R(this);try{t($(K,e),$(Z,e))}catch(t){Z(e,t)}}).prototype,(i=function(t){V(this,{type:"Promise",done:!1,notified:!1,parent:!1,reactions:new A,rejection:!1,state:0,value:void 0})}).prototype=l(L,"then",function(t,e){var r=R(this),i=F(x(this,k));return r.parent=!0,i.ok=!v(t)||t,i.fail=v(e)&&e,i.domain=u?B.domain:void 0,0==r.state?r.reactions.add(i):S(function(){X(i,r)}),i.promise}),n=function(){var t=new i,e=R(t);this.promise=t,this.resolve=$(K,e),this.reject=$(Z,e)},E.f=F=function(t){return t===k||void 0===t?new n(t):z(t)},!o&&v(C)&&I!==Object.prototype)){a=I.then,_||l(I,"then",function(t,e){var r=this;return new k(function(t,e){c(a,r,t,e)}).then(t,e)},{unsafe:!0});try{delete I.constructor}catch(t){}f&&f(I,L)}s({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:k}),g(k,"Promise",!1,!0),p("Promise")},2976:function(t,e,r){r(2975),r(2973),r(2974),r(2977),r(2978),r(2979)},2977:function(t,e,r){"use strict";var i=r(2833),n=r(2831),a=r(2839),s=r(2852),o=r(2882),u=r(2901);i({target:"Promise",stat:!0,forced:r(2907)},{race:function(t){var e=this,r=s.f(e),i=r.reject,h=o(function(){var s=a(e.resolve);u(t,function(t){n(s,e,t).then(r.resolve,i)})});return h.error&&i(h.value),r.promise}})},2978:function(t,e,r){"use strict";var i=r(2833),n=r(2831),a=r(2852);i({target:"Promise",stat:!0,forced:r(2854).CONSTRUCTOR},{reject:function(t){var e=a.f(this);return n(e.reject,void 0,t),e.promise}})},2979:function(t,e,r){"use strict";var i=r(2833),n=r(2844),a=r(2836),s=r(2855),o=r(2854).CONSTRUCTOR,u=r(2958),h=n("Promise"),c=a&&!o;i({target:"Promise",stat:!0,forced:a||o},{resolve:function(t){return u(c&&this===h?s:this,t)}})},2980:function(t,e,r){"use strict";var i=r(2833),n=r(2866);i({target:"RegExp",proto:!0,forced:/./.exec!==n},{exec:n})},2981:function(t,e,r){"use strict";var i=r(2861).PROPER,n=r(2843),a=r(2832),s=r(2838),o=r(2827),u=r(2959),h=RegExp.prototype.toString,c=o(function(){return"/a/b"!=h.call({source:"a",flags:"b"})}),l=i&&"toString"!=h.name;(c||l)&&n(RegExp.prototype,"toString",function(){var t=a(this);return"/"+s(t.source)+"/"+s(u(t))},{unsafe:!0})},2982:function(t,e,r){"use strict";var i,n=r(2833),a=r(2850),s=r(2853).f,o=r(2847),u=r(2838),h=r(2881),c=r(2837),l=r(2872),f=r(2836),g=a("".endsWith),p=a("".slice),d=Math.min,v=l("endsWith");n({target:"String",proto:!0,forced:!!(f||v||(i=s(String.prototype,"endsWith"),!i||i.writable))&&!v},{endsWith:function(t){var e=u(c(this));h(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===r?i:d(o(r),i),a=u(t);return g?g(e,a,n):p(e,n-a.length,n)===a}})},2983:function(t,e,r){"use strict";var i=r(2833),n=r(2828),a=r(2881),s=r(2837),o=r(2838),u=r(2872),h=n("".indexOf);i({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~h(o(s(this)),o(a(t)),arguments.length>1?arguments[1]:void 0)}})},2984:function(t,e,r){"use strict";var i=r(2831),n=r(2876),a=r(2832),s=r(2845),o=r(2847),u=r(2838),h=r(2837),c=r(2846),l=r(2870),f=r(2883);n("match",function(t,e,r){return[function(e){var r=h(this),n=s(e)?void 0:c(e,t);return n?i(n,e,r):new RegExp(e)[t](u(r))},function(t){var i=a(this),n=u(t),s=r(e,i,n);if(s.done)return s.value;if(!i.global)return f(i,n);var h=i.unicode;i.lastIndex=0;for(var c,g=[],p=0;null!==(c=f(i,n));){var d=u(c[0]);g[p]=d,""===d&&(i.lastIndex=l(n,o(i.lastIndex),h)),p++}return 0===p?null:g}]})},2985:function(t,e,r){"use strict";var i=r(2877),n=r(2831),a=r(2828),s=r(2876),o=r(2827),u=r(2832),h=r(2826),c=r(2845),l=r(2867),f=r(2847),g=r(2838),p=r(2837),d=r(2870),v=r(2846),y=r(2941),m=r(2883),x=r(2830)("replace"),b=Math.max,S=Math.min,w=a([].concat),T=a([].push),A=a("".indexOf),O=a("".slice),C="$0"==="a".replace(/./,"$0"),P=!!/./[x]&&""===/./[x]("a","$0");s("replace",function(t,e,r){var a=P?"$":"$0";return[function(t,r){var i=p(this),a=c(t)?void 0:v(t,x);return a?n(a,t,i,r):n(e,g(i),t,r)},function(t,n){var s=u(this),o=g(t);if("string"==typeof n&&-1===A(n,a)&&-1===A(n,"$<")){var c=r(e,s,o,n);if(c.done)return c.value}var p=h(n);p||(n=g(n));var v=s.global;if(v){var x=s.unicode;s.lastIndex=0}for(var C=[];;){var P=m(s,o);if(null===P)break;if(T(C,P),!v)break;""===g(P[0])&&(s.lastIndex=d(o,f(s.lastIndex),x))}for(var E,M="",N=0,_=0;_<C.length;_++){for(var R=g((P=C[_])[0]),V=b(S(l(P.index),o.length),0),I=[],k=1;k<P.length;k++)T(I,void 0===(E=P[k])?E:String(E));var L=P.groups;if(p){var D=w([R],I,V,o);void 0!==L&&T(D,L);var j=g(i(n,void 0,D))}else j=y(R,o,V,I,L,n);V>=N&&(M+=O(o,N,V)+j,N=V+R.length)}return M+O(o,N)}]},!!o(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})||!C||P)},2986:function(t,e,r){"use strict";var i=r(2877),n=r(2831),a=r(2828),s=r(2876),o=r(2832),u=r(2845),h=r(2899),c=r(2837),l=r(2911),f=r(2870),g=r(2847),p=r(2838),d=r(2846),v=r(2926),y=r(2883),m=r(2866),x=r(2910),b=r(2827),S=x.UNSUPPORTED_Y,w=Math.min,T=[].push,A=a(/./.exec),O=a(T),C=a("".slice);s("split",function(t,e,r){var a;return a="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var a=p(c(this)),s=void 0===r?4294967295:r>>>0;if(0===s)return[];if(void 0===t)return[a];if(!h(t))return n(e,a,t,s);for(var o,u,l,f=[],g=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,y=new RegExp(t.source,g+"g");(o=n(m,y,a))&&!((u=y.lastIndex)>d&&(O(f,C(a,d,o.index)),o.length>1&&o.index<a.length&&i(T,f,v(o,1)),l=o[0].length,d=u,f.length>=s));)y.lastIndex===o.index&&y.lastIndex++;return d===a.length?!l&&A(y,"")||O(f,""):O(f,C(a,d)),f.length>s?v(f,0,s):f}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:n(e,this,t,r)}:e,[function(e,r){var i=c(this),s=u(e)?void 0:d(e,t);return s?n(s,e,i,r):n(a,p(i),e,r)},function(t,i){var n=o(this),s=p(t),u=r(a,n,s,i,a!==e);if(u.done)return u.value;var h=l(n,RegExp),c=n.unicode,d=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.unicode?"u":"")+(S?"g":"y"),v=new h(S?"^(?:"+n.source+")":n,d),m=void 0===i?4294967295:i>>>0;if(0===m)return[];if(0===s.length)return null===y(v,s)?[s]:[];for(var x=0,b=0,T=[];b<s.length;){v.lastIndex=S?0:b;var A,P=y(v,S?C(s,b):s);if(null===P||(A=w(g(v.lastIndex+(S?b:0)),s.length))===x)b=f(s,b,c);else{if(O(T,C(s,x,b)),T.length===m)return T;for(var E=1;E<=P.length-1;E++)if(O(T,P[E]),T.length===m)return T;b=x=A}}return O(T,C(s,x)),T}]},!!b(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]}),S)},2987:function(t,e,r){"use strict";var i,n=r(2833),a=r(2850),s=r(2853).f,o=r(2847),u=r(2838),h=r(2881),c=r(2837),l=r(2872),f=r(2836),g=a("".startsWith),p=a("".slice),d=Math.min,v=l("startsWith");n({target:"String",proto:!0,forced:!!(f||v||(i=s(String.prototype,"startsWith"),!i||i.writable))&&!v},{startsWith:function(t){var e=u(c(this));h(t);var r=o(d(arguments.length>1?arguments[1]:void 0,e.length)),i=u(t);return g?g(e,i,r):p(e,r,r+i.length)===i}})},2988:function(t,e,r){"use strict";var i=r(2833),n=r(2965).trim;i({target:"String",proto:!0,forced:r(2964)("trim")},{trim:function(){return n(this)}})},2989:function(t,e,r){var i=r(2829),n=r(2934),a=r(2935),s=r(2919),o=r(2848),u=r(2830),h=u("iterator"),c=u("toStringTag"),l=s.values,f=function(t,e){if(t){if(t[h]!==l)try{o(t,h,l)}catch(e){t[h]=l}if(t[c]||o(t,c,e),n[e])for(var r in s)if(t[r]!==s[r])try{o(t,r,s[r])}catch(e){t[r]=s[r]}}};for(var g in n)f(i[g]&&i[g].prototype,g);f(a,"DOMTokenList")},2992:function(t,e,r){(function(e){(function(){var r,i,n,a,s,o;"undefined"!=typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:void 0!==e&&null!==e&&e.hrtime?(t.exports=function(){return(r()-s)/1e6},i=e.hrtime,a=(r=function(){var t;return 1e9*(t=i())[0]+t[1]})(),o=1e9*e.uptime(),s=a-o):Date.now?(t.exports=function(){return Date.now()-n},n=Date.now()):(t.exports=function(){return(new Date).getTime()-n},n=(new Date).getTime())}).call(this)}).call(e,r(303))},2993:function(t,e,r){(function(e){for(var i=r(2992),n="undefined"==typeof window?e:window,a=["moz","webkit"],s="AnimationFrame",o=n["request"+s],u=n["cancel"+s]||n["cancelRequest"+s],h=0;!o&&h<a.length;h++)o=n[a[h]+"Request"+s],u=n[a[h]+"Cancel"+s]||n[a[h]+"CancelRequest"+s];if(!o||!u){var c=0,l=0,f=[];o=function(t){if(0===f.length){var e=i(),r=Math.max(0,1e3/60-(e-c));c=r+e,setTimeout(function(){var t=f.slice(0);f.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(c)}catch(t){setTimeout(function(){throw t},0)}},Math.round(r))}return f.push({handle:++l,callback:t,cancelled:!1}),l},u=function(t){for(var e=0;e<f.length;e++)f[e].handle===t&&(f[e].cancelled=!0)}}t.exports=function(t){return o.call(n,t)},t.exports.cancel=function(){u.apply(n,arguments)},t.exports.polyfill=function(t){t||(t=n),t.requestAnimationFrame=o,t.cancelAnimationFrame=u}}).call(e,r(126))},2994:function(t,e){t.exports=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,a=r[i].process,s=n.exec(t);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var n=r[i].example,a=0;a<n.length;a++)t[t.length]=n[a];for(var s in e)t[t.length]=s;var o=document.createElement("ul");o.setAttribute("id","rgbcolor-examples");for(i=0;i<t.length;i++)try{var u=document.createElement("li"),h=new RGBColor(t[i]),c=document.createElement("div");c.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),c.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[i]+" -> "+h.toRGB()+" -> "+h.toHex());u.appendChild(c),u.appendChild(l),o.appendChild(u)}catch(t){}return o}}},2995:function(t,e,r){"use strict";function i(t){"@babel/helpers - typeof";return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.d(e,"a",function(){return o});var n=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],a=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function s(t,e,r,n,a){if("string"==typeof t&&(t=document.getElementById(t)),!(t&&"object"===i(t)&&"getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var s=t.getContext("2d");try{return s.getImageData(e,r,n,a)}catch(t){throw new Error("unable to access image data: "+t)}}function o(t,e,r,i,o,h){if(!(isNaN(h)||h<1)){h|=0;var c=s(t,e,r,i,o);c=function(t,e,r,i,s,o){for(var h,c=t.data,l=2*o+1,f=i-1,g=s-1,p=o+1,d=p*(p+1)/2,v=new u,y=v,m=1;m<l;m++)y=y.next=new u,m===p&&(h=y);y.next=v;for(var x=null,b=null,S=0,w=0,T=n[o],A=a[o],O=0;O<s;O++){y=v;for(var C=c[w],P=c[w+1],E=c[w+2],M=c[w+3],N=0;N<p;N++)y.r=C,y.g=P,y.b=E,y.a=M,y=y.next;for(var _=0,R=0,V=0,I=0,k=p*C,L=p*P,D=p*E,j=p*M,B=d*C,F=d*P,z=d*E,U=d*M,H=1;H<p;H++){var X=w+((f<H?f:H)<<2),Y=c[X],W=c[X+1],q=c[X+2],G=c[X+3],Q=p-H;B+=(y.r=Y)*Q,F+=(y.g=W)*Q,z+=(y.b=q)*Q,U+=(y.a=G)*Q,_+=Y,R+=W,V+=q,I+=G,y=y.next}x=v,b=h;for(var $=0;$<i;$++){var Z=U*T>>A;if(c[w+3]=Z,0!==Z){var K=255/Z;c[w]=(B*T>>A)*K,c[w+1]=(F*T>>A)*K,c[w+2]=(z*T>>A)*K}else c[w]=c[w+1]=c[w+2]=0;B-=k,F-=L,z-=D,U-=j,k-=x.r,L-=x.g,D-=x.b,j-=x.a;var J=$+o+1;J=S+(J<f?J:f)<<2,_+=x.r=c[J],R+=x.g=c[J+1],V+=x.b=c[J+2],I+=x.a=c[J+3],B+=_,F+=R,z+=V,U+=I,x=x.next;var tt=b,et=tt.r,rt=tt.g,it=tt.b,nt=tt.a;k+=et,L+=rt,D+=it,j+=nt,_-=et,R-=rt,V-=it,I-=nt,b=b.next,w+=4}S+=i}for(var at=0;at<i;at++){var st=c[w=at<<2],ot=c[w+1],ut=c[w+2],ht=c[w+3],ct=p*st,lt=p*ot,ft=p*ut,gt=p*ht,pt=d*st,dt=d*ot,vt=d*ut,yt=d*ht;y=v;for(var mt=0;mt<p;mt++)y.r=st,y.g=ot,y.b=ut,y.a=ht,y=y.next;for(var xt=i,bt=0,St=0,wt=0,Tt=0,At=1;At<=o;At++){w=xt+at<<2;var Ot=p-At;pt+=(y.r=st=c[w])*Ot,dt+=(y.g=ot=c[w+1])*Ot,vt+=(y.b=ut=c[w+2])*Ot,yt+=(y.a=ht=c[w+3])*Ot,Tt+=st,bt+=ot,St+=ut,wt+=ht,y=y.next,At<g&&(xt+=i)}w=at,x=v,b=h;for(var Ct=0;Ct<s;Ct++){var Pt=w<<2;c[Pt+3]=ht=yt*T>>A,ht>0?(ht=255/ht,c[Pt]=(pt*T>>A)*ht,c[Pt+1]=(dt*T>>A)*ht,c[Pt+2]=(vt*T>>A)*ht):c[Pt]=c[Pt+1]=c[Pt+2]=0,pt-=ct,dt-=lt,vt-=ft,yt-=gt,ct-=x.r,lt-=x.g,ft-=x.b,gt-=x.a,Pt=at+((Pt=Ct+p)<g?Pt:g)*i<<2,pt+=Tt+=x.r=c[Pt],dt+=bt+=x.g=c[Pt+1],vt+=St+=x.b=c[Pt+2],yt+=wt+=x.a=c[Pt+3],x=x.next,ct+=st=b.r,lt+=ot=b.g,ft+=ut=b.b,gt+=ht=b.a,Tt-=st,bt-=ot,St-=ut,wt-=ht,b=b.next,w+=i}}return t}(c,0,0,i,o,h),t.getContext("2d").putImageData(c,e,r)}}var u=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}},2996:function(t,e,r){"use strict";r.d(e,"a",function(){return S});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function n(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function a(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function s(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var o=Math.PI;function u(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,n=t.rY,s=t.x,u=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var h=a([(e-s)/2,(r-u)/2],-t.xRot/180*o),c=h[0],l=h[1],f=Math.pow(c,2)/Math.pow(i,2)+Math.pow(l,2)/Math.pow(n,2);1<f&&(i*=Math.sqrt(f),n*=Math.sqrt(f)),t.rX=i,t.rY=n;var g=Math.pow(i,2)*Math.pow(l,2)+Math.pow(n,2)*Math.pow(c,2),p=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-g)/g)),d=i*l/n*p,v=-n*c/i*p,y=a([d,v],t.xRot/180*o);t.cX=y[0]+(e+s)/2,t.cY=y[1]+(r+u)/2,t.phi1=Math.atan2((l-v)/n,(c-d)/i),t.phi2=Math.atan2((-l-v)/n,(-c-d)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*o),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*o),t.phi1*=180/o,t.phi2*=180/o}function h(t,e,r){s(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var c,l=Math.PI/180;function f(t,e,r){return(1-r)*t+r*e}function g(t,e,r,i){return t+Math.cos(i/180*o)*e+Math.sin(i/180*o)*r}function p(t,e,r,i){var n=e-t,a=r-e,s=3*n+3*(i-r)-6*a,o=6*(a-n),u=3*n;return Math.abs(s)<1e-6?[-u/o]:function(t,e,r){void 0===r&&(r=1e-6);var i=t*t/4-u/s;if(i<-r)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(o/s,0,1e-6)}function d(t,e,r,i,n){var a=1-n;return t*(a*a*a)+e*(3*a*a*n)+r*(3*a*n*n)+i*(n*n*n)}!function(t){function e(){return n(function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t})}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n(function(n,a,s){return n.type&S.SMOOTH_CURVE_TO&&(n.type=S.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?s:e,n.x1=n.relative?a-t:2*a-t,n.y1=n.relative?s-e:2*s-e),n.type&S.CURVE_TO?(t=n.relative?a+n.x2:n.x2,e=n.relative?s+n.y2:n.y2):(t=NaN,e=NaN),n.type&S.SMOOTH_QUAD_TO&&(n.type=S.QUAD_TO,r=isNaN(r)?a:r,i=isNaN(i)?s:i,n.x1=n.relative?a-r:2*a-r,n.y1=n.relative?s-i:2*s-i),n.type&S.QUAD_TO?(r=n.relative?a+n.x1:n.x1,i=n.relative?s+n.y1:n.y1):(r=NaN,i=NaN),n})}function i(){var t=NaN,e=NaN;return n(function(r,i,n){if(r.type&S.SMOOTH_QUAD_TO&&(r.type=S.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&S.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var a=r.x1,s=r.y1;r.type=S.CURVE_TO,r.x1=((r.relative?0:i)+2*a)/3,r.y1=((r.relative?0:n)+2*s)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*s)/3}else t=NaN,e=NaN;return r})}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(a){if(isNaN(i)&&!(a.type&S.MOVE_TO))throw new Error("path must start with moveto");var s=t(a,e,r,i,n);return a.type&S.CLOSE_PATH&&(e=i,r=n),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&S.MOVE_TO&&(i=e,n=r),s}}function o(t,e,r,i,a,o){return s(t,e,r,i,a,o),n(function(n,s,u,h){var c=n.x1,l=n.x2,f=n.relative&&!isNaN(h),g=void 0!==n.x?n.x:f?0:s,p=void 0!==n.y?n.y:f?0:u;function d(t){return t*t}n.type&S.HORIZ_LINE_TO&&0!==e&&(n.type=S.LINE_TO,n.y=n.relative?0:u),n.type&S.VERT_LINE_TO&&0!==r&&(n.type=S.LINE_TO,n.x=n.relative?0:s),void 0!==n.x&&(n.x=n.x*t+p*r+(f?0:a)),void 0!==n.y&&(n.y=g*e+n.y*i+(f?0:o)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(f?0:a)),void 0!==n.y1&&(n.y1=c*e+n.y1*i+(f?0:o)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(f?0:a)),void 0!==n.y2&&(n.y2=l*e+n.y2*i+(f?0:o));var v=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===v)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=S.LINE_TO;else{var y=n.xRot*Math.PI/180,m=Math.sin(y),x=Math.cos(y),b=1/d(n.rX),w=1/d(n.rY),T=d(x)*b+d(m)*w,A=2*m*x*(b-w),O=d(m)*b+d(x)*w,C=T*i*i-A*e*i+O*e*e,P=A*(t*i+e*r)-2*(T*r*i+O*t*e),E=T*r*r-A*t*r+O*t*t,M=(Math.atan2(P,C-E)+Math.PI)%Math.PI/2,N=Math.sin(M),_=Math.cos(M);n.rX=Math.abs(v)/Math.sqrt(C*d(_)+P*N*_+E*d(N)),n.rY=Math.abs(v)/Math.sqrt(C*d(N)-P*N*_+E*d(_)),n.xRot=180*M/Math.PI}return void 0!==n.sweepFlag&&0>v&&(n.sweepFlag=+!n.sweepFlag),n})}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),s(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n(function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t})},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n(function(i,n,a,s,o){if(isNaN(s)&&!(i.type&S.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&S.HORIZ_LINE_TO&&(i.type=S.LINE_TO,i.y=i.relative?0:a),r&&i.type&S.VERT_LINE_TO&&(i.type=S.LINE_TO,i.x=i.relative?0:n),t&&i.type&S.CLOSE_PATH&&(i.type=S.LINE_TO,i.x=i.relative?s-n:s,i.y=i.relative?o-a:o),i.type&S.ARC&&(0===i.rX||0===i.rY)&&(i.type=S.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i})},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),s(t);var e=NaN,r=NaN,i=NaN,a=NaN;return n(function(n,s,o,u,h){var c=Math.abs,l=!1,f=0,g=0;if(n.type&S.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:s-e,g=isNaN(r)?0:o-r),n.type&(S.CURVE_TO|S.SMOOTH_CURVE_TO)?(e=n.relative?s+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&S.SMOOTH_QUAD_TO?(i=isNaN(i)?s:2*s-i,a=isNaN(a)?o:2*o-a):n.type&S.QUAD_TO?(i=n.relative?s+n.x1:n.x1,a=n.relative?o+n.y1:n.y2):(i=NaN,a=NaN),n.type&S.LINE_COMMANDS||n.type&S.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&S.CURVE_TO||n.type&S.SMOOTH_CURVE_TO||n.type&S.QUAD_TO||n.type&S.SMOOTH_QUAD_TO){var p=void 0===n.x?0:n.relative?n.x:n.x-s,d=void 0===n.y?0:n.relative?n.y:n.y-o;f=isNaN(i)?void 0===n.x1?f:n.relative?n.x:n.x1-s:i-s,g=isNaN(a)?void 0===n.y1?g:n.relative?n.y:n.y1-o:a-o;var v=void 0===n.x2?0:n.relative?n.x:n.x2-s,y=void 0===n.y2?0:n.relative?n.y:n.y2-o;c(p)<=t&&c(d)<=t&&c(f)<=t&&c(g)<=t&&c(v)<=t&&c(y)<=t&&(l=!0)}return n.type&S.CLOSE_PATH&&c(s-u)<=t&&c(o-h)<=t&&(l=!0),l?[]:n})},t.MATRIX=o,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),s(t,e,r);var i=Math.sin(t),n=Math.cos(t);return o(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),s(t,e),o(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),s(t,e),o(t,0,0,e,0,0)},t.SKEW_X=function(t){return s(t),o(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return s(t),o(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),s(t),o(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),s(t),o(1,0,0,-1,0,t)},t.A_TO_C=function(){return n(function(t,e,r){return S.ARC===t.type?function(t,e,r){var i,n,s,o;t.cX||u(t,e,r);for(var h=Math.min(t.phi1,t.phi2),c=Math.max(t.phi1,t.phi2)-h,g=Math.ceil(c/90),p=new Array(g),d=e,v=r,y=0;y<g;y++){var m=f(t.phi1,t.phi2,y/g),x=f(t.phi1,t.phi2,(y+1)/g),b=x-m,w=4/3*Math.tan(b*l/4),T=[Math.cos(m*l)-w*Math.sin(m*l),Math.sin(m*l)+w*Math.cos(m*l)],A=T[0],O=T[1],C=[Math.cos(x*l),Math.sin(x*l)],P=C[0],E=C[1],M=[P+w*Math.sin(x*l),E-w*Math.cos(x*l)],N=M[0],_=M[1];p[y]={relative:t.relative,type:S.CURVE_TO};var R=function(e,r){var i=a([e*t.rX,r*t.rY],t.xRot),n=i[0],s=i[1];return[t.cX+n,t.cY+s]};i=R(A,O),p[y].x1=i[0],p[y].y1=i[1],n=R(N,_),p[y].x2=n[0],p[y].y2=n[1],s=R(P,E),p[y].x=s[0],p[y].y=s[1],t.relative&&(p[y].x1-=d,p[y].y1-=v,p[y].x2-=d,p[y].y2-=v,p[y].x-=d,p[y].y-=v),d=(o=[p[y].x,p[y].y])[0],v=o[1]}return p}(t,t.relative?0:e,t.relative?0:r):t})},t.ANNOTATE_ARCS=function(){return n(function(t,e,r){return t.relative&&(e=0,r=0),S.ARC===t.type&&u(t,e,r),t})},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),a=i(),s=r(),o=n(function(e,r,i){var n=s(a(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function c(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function l(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(n.type&S.DRAWING_COMMANDS&&(c(r),l(i)),n.type&S.HORIZ_LINE_TO&&c(n.x),n.type&S.VERT_LINE_TO&&l(n.y),n.type&S.LINE_TO&&(c(n.x),l(n.y)),n.type&S.CURVE_TO){c(n.x),l(n.y);for(var f=0,v=p(r,n.x1,n.x2,n.x);f<v.length;f++)0<(V=v[f])&&1>V&&c(d(r,n.x1,n.x2,n.x,V));for(var y=0,m=p(i,n.y1,n.y2,n.y);y<m.length;y++)0<(V=m[y])&&1>V&&l(d(i,n.y1,n.y2,n.y,V))}if(n.type&S.ARC){c(n.x),l(n.y),u(n,r,i);for(var x=n.xRot/180*Math.PI,b=Math.cos(x)*n.rX,w=Math.sin(x)*n.rX,T=-Math.sin(x)*n.rY,A=Math.cos(x)*n.rY,O=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],C=O[0],P=O[1],E=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<C?i+360:i},M=0,N=h(T,-b,0).map(E);M<N.length;M++)(V=N[M])>C&&V<P&&c(g(n.cX,b,T,V));for(var _=0,R=h(A,-w,0).map(E);_<R.length;_++){var V;(V=R[_])>C&&V<P&&l(g(n.cY,w,A,V))}}return e});return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(c||(c={}));var v,y=function(){function t(){}return t.prototype.round=function(t){return this.transform(c.ROUND(t))},t.prototype.toAbs=function(){return this.transform(c.TO_ABS())},t.prototype.toRel=function(){return this.transform(c.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(c.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(c.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(c.QT_TO_C())},t.prototype.aToC=function(){return this.transform(c.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(c.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(c.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(c.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(c.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,a){return this.transform(c.MATRIX(t,e,r,i,n,a))},t.prototype.skewX=function(t){return this.transform(c.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(c.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(c.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(c.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(c.ANNOTATE_ARCS())},t}(),m=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},x=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},b=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return n(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var a=t[n],s=!(this.curCommandType!==S.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=x(a)&&("0"===this.curNumber&&"0"===a||s);if(!x(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||s){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError("Invalid number ending at "+n);if(this.curCommandType===S.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got "'+u+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"');this.curArgs.push(u),this.curArgs.length===w[this.curCommandType]&&(S.HORIZ_LINE_TO===this.curCommandType?i({type:S.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):S.VERT_LINE_TO===this.curCommandType?i({type:S.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===S.MOVE_TO||this.curCommandType===S.LINE_TO||this.curCommandType===S.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),S.MOVE_TO===this.curCommandType&&(this.curCommandType=S.LINE_TO)):this.curCommandType===S.CURVE_TO?i({type:S.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===S.SMOOTH_CURVE_TO?i({type:S.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===S.QUAD_TO?i({type:S.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===S.ARC&&i({type:S.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!m(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=S.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=S.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=S.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=S.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=S.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=S.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=S.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=S.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+".");this.curCommandType=S.ARC,this.curCommandRelative="a"===a}else e.push({type:S.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var a=n[i],s=t(a);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(y),S=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return n(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=c.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===S.CLOSE_PATH)e+="z";else if(i.type===S.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===S.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===S.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===S.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===S.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===S.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===S.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===S.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==S.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new b,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(y),w=((v={})[S.MOVE_TO]=2,v[S.LINE_TO]=2,v[S.HORIZ_LINE_TO]=1,v[S.VERT_LINE_TO]=1,v[S.CLOSE_PATH]=0,v[S.QUAD_TO]=4,v[S.SMOOTH_QUAD_TO]=2,v[S.CURVE_TO]=6,v[S.SMOOTH_CURVE_TO]=4,v[S.ARC]=7,v)}});