/*-------------------------------------------------------
 All Rights Reserved, Copyright (C) 2013, Wuyeguo, Ltd.
-------------------------------------------------------*/

.icon-blank{ background:url('icons/blank.gif') no-repeat center center;}

.icon-add{ background:url('icons/edit_add.png') no-repeat center center;}

.icon-edit{ background:url('icons/pencil.png') no-repeat center center;}

.icon-remove{ background:url('icons/edit_remove.png') no-repeat center center;}

.icon-save{ background:url('icons/filesave.png') no-repeat center center;}

.icon-cut{ background:url('icons/cut.png') no-repeat center center;}

.icon-ok{ background:url('icons/ok.png') no-repeat center center;}

.icon-no{ background:url('icons/no.png') no-repeat center center;}

.icon-cancel{ background:url('icons/cancel.png') no-repeat center center;}

.icon-reload{ background:url('icons/reload.png') no-repeat center center;}

.icon-search{ background:url('icons/search.png') no-repeat center center;}

.icon-print{ background:url('icons/print.png') no-repeat center center;}

.icon-help{ background:url('icons/help.png') no-repeat center center;}

.icon-undo{ background:url('icons/undo.png') no-repeat center center;}

.icon-redo{ background:url('icons/redo.png') no-repeat center center;}

.icon-back{ background:url('icons/back.png') no-repeat center center;}

.icon-sum{ background:url('icons/sum.png') no-repeat center center;}

.icon-tip{ background:url('icons/tip.png') no-repeat center center;}

.icon-mini-add{ background:url('icons/mini_add.png') no-repeat center center;}

.icon-mini-edit{ background:url('icons/mini_edit.png') no-repeat center center;}

.icon-mini-refresh{ background:url('icons/mini_refresh.png') no-repeat center center;}

.icon-accept { background:url('icons/accept.png') no-repeat center center;}

.icon-add { background:url('icons/add.png') no-repeat center center;}

.icon-add1 { background:url('icons/add1.png') no-repeat center center;}

.icon-advancedsettings { background:url('icons/advancedsettings.png') no-repeat center center;}

.icon-advancedsettings2 { background:url('icons/advancedsettings2.png') no-repeat center center;}

.icon-anchor { background:url('icons/anchor.png') no-repeat center center;}

.icon-application { background:url('icons/application.png') no-repeat center center;}

.icon-application-add { background:url('icons/application_add.png') no-repeat center center;}

.icon-application-cascade { background:url('icons/application_cascade.png') no-repeat center center;}

.icon-application-delete { background:url('icons/application_delete.png') no-repeat center center;}

.icon-application-double { background:url('icons/application_double.png') no-repeat center center;}

.icon-application-edit { background:url('icons/application_edit.png') no-repeat center center;}

.icon-application-error { background:url('icons/application_error.png') no-repeat center center;}

.icon-application-form { background:url('icons/application_form.png') no-repeat center center;}

.icon-application-form-add { background:url('icons/application_form_add.png') no-repeat center center;}

.icon-application-form-delete { background:url('icons/application_form_delete.png') no-repeat center center;}

.icon-application-form-edit { background:url('icons/application_form_edit.png') no-repeat center center;}

.icon-application-form-magnify { background:url('icons/application_form_magnify.png') no-repeat center center;}

.icon-application-get { background:url('icons/application_get.png') no-repeat center center;}

.icon-application-go { background:url('icons/application_go.png') no-repeat center center;}

.icon-application-home { background:url('icons/application_home.png') no-repeat center center;}

.icon-application-key { background:url('icons/application_key.png') no-repeat center center;}

.icon-application-lightning { background:url('icons/application_lightning.png') no-repeat center center;}

.icon-application-link { background:url('icons/application_link.png') no-repeat center center;}

.icon-application-osx { background:url('icons/application_osx.png') no-repeat center center;}

.icon-application-osx-add { background:url('icons/application_osx_add.png') no-repeat center center;}

.icon-application-osx-cascade { background:url('icons/application_osx_cascade.png') no-repeat center center;}

.icon-application-osx-delete { background:url('icons/application_osx_delete.png') no-repeat center center;}

.icon-application-osx-double { background:url('icons/application_osx_double.png') no-repeat center center;}

.icon-application-osx-error { background:url('icons/application_osx_error.png') no-repeat center center;}

.icon-application-osx-get { background:url('icons/application_osx_get.png') no-repeat center center;}

.icon-application-osx-go { background:url('icons/application_osx_go.png') no-repeat center center;}

.icon-application-osx-home { background:url('icons/application_osx_home.png') no-repeat center center;}

.icon-application-osx-key { background:url('icons/application_osx_key.png') no-repeat center center;}

.icon-application-osx-lightning { background:url('icons/application_osx_lightning.png') no-repeat center center;}

.icon-application-osx-link { background:url('icons/application_osx_link.png') no-repeat center center;}

.icon-application-osx-split { background:url('icons/application_osx_split.png') no-repeat center center;}

.icon-application-osx-start { background:url('icons/application_osx_start.png') no-repeat center center;}

.icon-application-osx-stop { background:url('icons/application_osx_stop.png') no-repeat center center;}

.icon-application-osx-terminal { background:url('icons/application_osx_terminal.png') no-repeat center center;}

.icon-application-put { background:url('icons/application_put.png') no-repeat center center;}

.icon-application-side-boxes { background:url('icons/application_side_boxes.png') no-repeat center center;}

.icon-application-side-contract { background:url('icons/application_side_contract.png') no-repeat center center;}

.icon-application-side-expand { background:url('icons/application_side_expand.png') no-repeat center center;}

.icon-application-side-list { background:url('icons/application_side_list.png') no-repeat center center;}

.icon-application-side-tree { background:url('icons/application_side_tree.png') no-repeat center center;}

.icon-application-split { background:url('icons/application_split.png') no-repeat center center;}

.icon-application-start { background:url('icons/application_start.png') no-repeat center center;}

.icon-application-stop { background:url('icons/application_stop.png') no-repeat center center;}

.icon-application-tile-horizontal { background:url('icons/application_tile_horizontal.png') no-repeat center center;}

.icon-application-tile-vertical { background:url('icons/application_tile_vertical.png') no-repeat center center;}

.icon-application-view-columns { background:url('icons/application_view_columns.png') no-repeat center center;}

.icon-application-view-detail { background:url('icons/application_view_detail.png') no-repeat center center;}

.icon-application-view-gallery { background:url('icons/application_view_gallery.png') no-repeat center center;}

.icon-application-view-icons { background:url('icons/application_view_icons.png') no-repeat center center;}

.icon-application-view-list { background:url('icons/application_view_list.png') no-repeat center center;}

.icon-application-view-tile { background:url('icons/application_view_tile.png') no-repeat center center;}

.icon-application-xp { background:url('icons/application_xp.png') no-repeat center center;}

.icon-application-xp-terminal { background:url('icons/application_xp_terminal.png') no-repeat center center;}

.icon-arrow-branch { background:url('icons/arrow_branch.png') no-repeat center center;}

.icon-arrow-divide { background:url('icons/arrow_divide.png') no-repeat center center;}

.icon-arrow-down { background:url('icons/arrow_down.png') no-repeat center center;}

.icon-arrow-ew { background:url('icons/arrow_ew.png') no-repeat center center;}

.icon-arrow-in { background:url('icons/arrow_in.png') no-repeat center center;}

.icon-arrow-inout { background:url('icons/arrow_inout.png') no-repeat center center;}

.icon-arrow-in-longer { background:url('icons/arrow_in_longer.png') no-repeat center center;}

.icon-arrow-join { background:url('icons/arrow_join.png') no-repeat center center;}

.icon-arrow-left { background:url('icons/arrow_left.png') no-repeat center center;}

.icon-arrow-merge { background:url('icons/arrow_merge.png') no-repeat center center;}

.icon-arrow-ne { background:url('icons/arrow_ne.png') no-repeat center center;}

.icon-arrow-ns { background:url('icons/arrow_ns.png') no-repeat center center;}

.icon-arrow-nsew { background:url('icons/arrow_nsew.png') no-repeat center center;}

.icon-arrow-nw { background:url('icons/arrow_nw.png') no-repeat center center;}

.icon-arrow-nw-ne-sw-se { background:url('icons/arrow_nw_ne_sw_se.png') no-repeat center center;}

.icon-arrow-nw-se { background:url('icons/arrow_nw_se.png') no-repeat center center;}

.icon-arrow-out { background:url('icons/arrow_out.png') no-repeat center center;}

.icon-arrow-out-longer { background:url('icons/arrow_out_longer.png') no-repeat center center;}

.icon-arrow-redo { background:url('icons/arrow_redo.png') no-repeat center center;}

.icon-arrow-refresh { background:url('icons/arrow_refresh.png') no-repeat center center;}

.icon-arrow-refresh-small { background:url('icons/arrow_refresh_small.png') no-repeat center center;}

.icon-arrow-right { background:url('icons/arrow_right.png') no-repeat center center;}

.icon-arrow-right-16 { background:url('icons/arrow_right_16.png') no-repeat center center;}

.icon-arrow-rotate-anticlockwise { background:url('icons/arrow_rotate_anticlockwise.png') no-repeat center center;}

.icon-arrow-rotate-clockwise { background:url('icons/arrow_rotate_clockwise.png') no-repeat center center;}

.icon-arrow-se { background:url('icons/arrow_se.png') no-repeat center center;}

.icon-arrow-sw { background:url('icons/arrow_sw.png') no-repeat center center;}

.icon-arrow-switch { background:url('icons/arrow_switch.png') no-repeat center center;}

.icon-arrow-switch-bluegreen { background:url('icons/arrow_switch_bluegreen.png') no-repeat center center;}

.icon-arrow-sw-ne { background:url('icons/arrow_sw_ne.png') no-repeat center center;}

.icon-arrow-turn-left { background:url('icons/arrow_turn_left.png') no-repeat center center;}

.icon-arrow-turn-right { background:url('icons/arrow_turn_right.png') no-repeat center center;}

.icon-arrow-undo { background:url('icons/arrow_undo.png') no-repeat center center;}

.icon-arrow-up { background:url('icons/arrow_up.png') no-repeat center center;}

.icon-asterisk-orange { background:url('icons/asterisk_orange.png') no-repeat center center;}

.icon-asterisk-red { background:url('icons/asterisk_red.png') no-repeat center center;}

.icon-asterisk-yellow { background:url('icons/asterisk_yellow.png') no-repeat center center;}

.icon-attach { background:url('icons/attach.png') no-repeat center center;}

.icon-award-star-add { background:url('icons/award_star_add.png') no-repeat center center;}

.icon-award-star-bronze-1 { background:url('icons/award_star_bronze_1.png') no-repeat center center;}

.icon-award-star-bronze-2 { background:url('icons/award_star_bronze_2.png') no-repeat center center;}

.icon-award-star-bronze-3 { background:url('icons/award_star_bronze_3.png') no-repeat center center;}

.icon-award-star-delete { background:url('icons/award_star_delete.png') no-repeat center center;}

.icon-award-star-gold-1 { background:url('icons/award_star_gold_1.png') no-repeat center center;}

.icon-award-star-gold-2 { background:url('icons/award_star_gold_2.png') no-repeat center center;}

.icon-award-star-gold-3 { background:url('icons/award_star_gold_3.png') no-repeat center center;}

.icon-award-star-silver-1 { background:url('icons/award_star_silver_1.png') no-repeat center center;}

.icon-award-star-silver-2 { background:url('icons/award_star_silver_2.png') no-repeat center center;}

.icon-award-star-silver-3 { background:url('icons/award_star_silver_3.png') no-repeat center center;}

.icon-basket { background:url('icons/basket.png') no-repeat center center;}

.icon-basket-add { background:url('icons/basket_add.png') no-repeat center center;}

.icon-basket-delete { background:url('icons/basket_delete.png') no-repeat center center;}

.icon-basket-edit { background:url('icons/basket_edit.png') no-repeat center center;}

.icon-basket-error { background:url('icons/basket_error.png') no-repeat center center;}

.icon-basket-go { background:url('icons/basket_go.png') no-repeat center center;}

.icon-basket-put { background:url('icons/basket_put.png') no-repeat center center;}

.icon-basket-remove { background:url('icons/basket_remove.png') no-repeat center center;}

.icon-bell { background:url('icons/bell.png') no-repeat center center;}

.icon-bell-add { background:url('icons/bell_add.png') no-repeat center center;}

.icon-bell-delete { background:url('icons/bell_delete.png') no-repeat center center;}

.icon-bell-error { background:url('icons/bell_error.png') no-repeat center center;}

.icon-bell-go { background:url('icons/bell_go.png') no-repeat center center;}

.icon-bell-link { background:url('icons/bell_link.png') no-repeat center center;}

.icon-bell-silver { background:url('icons/bell_silver.png') no-repeat center center;}

.icon-bell-silver-start { background:url('icons/bell_silver_start.png') no-repeat center center;}

.icon-bell-silver-stop { background:url('icons/bell_silver_stop.png') no-repeat center center;}

.icon-bell-start { background:url('icons/bell_start.png') no-repeat center center;}

.icon-bell-stop { background:url('icons/bell_stop.png') no-repeat center center;}

.icon-bin { background:url('icons/bin.png') no-repeat center center;}

.icon-bin-closed { background:url('icons/bin_closed.png') no-repeat center center;}

.icon-bin-empty { background:url('icons/bin_empty.png') no-repeat center center;}

.icon-bomb { background:url('icons/bomb.png') no-repeat center center;}

.icon-book { background:url('icons/book.png') no-repeat center center;}

.icon-bookmark { background:url('icons/bookmark.png') no-repeat center center;}

.icon-bookmark-add { background:url('icons/bookmark_add.png') no-repeat center center;}

.icon-bookmark-delete { background:url('icons/bookmark_delete.png') no-repeat center center;}

.icon-bookmark-edit { background:url('icons/bookmark_edit.png') no-repeat center center;}

.icon-bookmark-error { background:url('icons/bookmark_error.png') no-repeat center center;}

.icon-bookmark-go { background:url('icons/bookmark_go.png') no-repeat center center;}

.icon-book-add { background:url('icons/book_add.png') no-repeat center center;}

.icon-book-addresses { background:url('icons/book_addresses.png') no-repeat center center;}

.icon-book-addresses-add { background:url('icons/book_addresses_add.png') no-repeat center center;}

.icon-book-addresses-delete { background:url('icons/book_addresses_delete.png') no-repeat center center;}

.icon-book-addresses-edit { background:url('icons/book_addresses_edit.png') no-repeat center center;}

.icon-book-addresses-error { background:url('icons/book_addresses_error.png') no-repeat center center;}

.icon-book-addresses-key { background:url('icons/book_addresses_key.png') no-repeat center center;}

.icon-book-delete { background:url('icons/book_delete.png') no-repeat center center;}

.icon-book-edit { background:url('icons/book_edit.png') no-repeat center center;}

.icon-book-error { background:url('icons/book_error.png') no-repeat center center;}

.icon-book-go { background:url('icons/book_go.png') no-repeat center center;}

.icon-book-key { background:url('icons/book_key.png') no-repeat center center;}

.icon-book-link { background:url('icons/book_link.png') no-repeat center center;}

.icon-book-magnify { background:url('icons/book_magnify.png') no-repeat center center;}

.icon-book-next { background:url('icons/book_next.png') no-repeat center center;}

.icon-book-open { background:url('icons/book_open.png') no-repeat center center;}

.icon-book-open-mark { background:url('icons/book_open_mark.png') no-repeat center center;}

.icon-book-previous { background:url('icons/book_previous.png') no-repeat center center;}

.icon-book-red { background:url('icons/book_red.png') no-repeat center center;}

.icon-book-tabs { background:url('icons/book_tabs.png') no-repeat center center;}

.icon-border-all { background:url('icons/border_all.png') no-repeat center center;}

.icon-border-bottom { background:url('icons/border_bottom.png') no-repeat center center;}

.icon-border-draw { background:url('icons/border_draw.png') no-repeat center center;}

.icon-border-inner { background:url('icons/border_inner.png') no-repeat center center;}

.icon-border-inner-horizontal { background:url('icons/border_inner_horizontal.png') no-repeat center center;}

.icon-border-inner-vertical { background:url('icons/border_inner_vertical.png') no-repeat center center;}

.icon-border-left { background:url('icons/border_left.png') no-repeat center center;}

.icon-border-none { background:url('icons/border_none.png') no-repeat center center;}

.icon-border-outer { background:url('icons/border_outer.png') no-repeat center center;}

.icon-border-right { background:url('icons/border_right.png') no-repeat center center;}

.icon-border-top { background:url('icons/border_top.png') no-repeat center center;}

.icon-box { background:url('icons/box.png') no-repeat center center;}

.icon-box-error { background:url('icons/box_error.png') no-repeat center center;}

.icon-box-picture { background:url('icons/box_picture.png') no-repeat center center;}

.icon-box-world { background:url('icons/box_world.png') no-repeat center center;}

.icon-brick { background:url('icons/brick.png') no-repeat center center;}

.icon-bricks { background:url('icons/bricks.png') no-repeat center center;}

.icon-brick-add { background:url('icons/brick_add.png') no-repeat center center;}

.icon-brick-delete { background:url('icons/brick_delete.png') no-repeat center center;}

.icon-brick-edit { background:url('icons/brick_edit.png') no-repeat center center;}

.icon-brick-error { background:url('icons/brick_error.png') no-repeat center center;}

.icon-brick-go { background:url('icons/brick_go.png') no-repeat center center;}

.icon-brick-link { background:url('icons/brick_link.png') no-repeat center center;}

.icon-brick-magnify { background:url('icons/brick_magnify.png') no-repeat center center;}

.icon-briefcase { background:url('icons/briefcase.png') no-repeat center center;}

.icon-bug { background:url('icons/bug.png') no-repeat center center;}

.icon-bug-add { background:url('icons/bug_add.png') no-repeat center center;}

.icon-bug-delete { background:url('icons/bug_delete.png') no-repeat center center;}

.icon-bug-edit { background:url('icons/bug_edit.png') no-repeat center center;}

.icon-bug-error { background:url('icons/bug_error.png') no-repeat center center;}

.icon-bug-fix { background:url('icons/bug_fix.png') no-repeat center center;}

.icon-bug-go { background:url('icons/bug_go.png') no-repeat center center;}

.icon-bug-link { background:url('icons/bug_link.png') no-repeat center center;}

.icon-bug-magnify { background:url('icons/bug_magnify.png') no-repeat center center;}

.icon-build { background:url('icons/build.png') no-repeat center center;}

.icon-building { background:url('icons/building.png') no-repeat center center;}

.icon-building-add { background:url('icons/building_add.png') no-repeat center center;}

.icon-building-delete { background:url('icons/building_delete.png') no-repeat center center;}

.icon-building-edit { background:url('icons/building_edit.png') no-repeat center center;}

.icon-building-error { background:url('icons/building_error.png') no-repeat center center;}

.icon-building-go { background:url('icons/building_go.png') no-repeat center center;}

.icon-building-key { background:url('icons/building_key.png') no-repeat center center;}

.icon-building-link { background:url('icons/building_link.png') no-repeat center center;}

.icon-build-cancel { background:url('icons/build_cancel.png') no-repeat center center;}

.icon-bullet-add { background:url('icons/bullet_add.png') no-repeat center center;}

.icon-bullet-arrow-bottom { background:url('icons/bullet_arrow_bottom.png') no-repeat center center;}

.icon-bullet-arrow-down { background:url('icons/bullet_arrow_down.png') no-repeat center center;}

.icon-bullet-arrow-top { background:url('icons/bullet_arrow_top.png') no-repeat center center;}

.icon-bullet-arrow-up { background:url('icons/bullet_arrow_up.png') no-repeat center center;}

.icon-bullet-black { background:url('icons/bullet_black.png') no-repeat center center;}

.icon-bullet-blue { background:url('icons/bullet_blue.png') no-repeat center center;}

.icon-bullet-connect { background:url('icons/bullet_connect.png') no-repeat center center;}

.icon-bullet-cross { background:url('icons/bullet_cross.png') no-repeat center center;}

.icon-bullet-database { background:url('icons/bullet_database.png') no-repeat center center;}

.icon-bullet-database-yellow { background:url('icons/bullet_database_yellow.png') no-repeat center center;}

.icon-bullet-delete { background:url('icons/bullet_delete.png') no-repeat center center;}

.icon-bullet-disk { background:url('icons/bullet_disk.png') no-repeat center center;}

.icon-bullet-earth { background:url('icons/bullet_earth.png') no-repeat center center;}

.icon-bullet-edit { background:url('icons/bullet_edit.png') no-repeat center center;}

.icon-bullet-eject { background:url('icons/bullet_eject.png') no-repeat center center;}

.icon-bullet-error { background:url('icons/bullet_error.png') no-repeat center center;}

.icon-bullet-feed { background:url('icons/bullet_feed.png') no-repeat center center;}

.icon-bullet-get { background:url('icons/bullet_get.png') no-repeat center center;}

.icon-bullet-go { background:url('icons/bullet_go.png') no-repeat center center;}

.icon-bullet-green { background:url('icons/bullet_green.png') no-repeat center center;}

.icon-bullet-home { background:url('icons/bullet_home.png') no-repeat center center;}

.icon-bullet-key { background:url('icons/bullet_key.png') no-repeat center center;}

.icon-bullet-left { background:url('icons/bullet_left.png') no-repeat center center;}

.icon-bullet-lightning { background:url('icons/bullet_lightning.png') no-repeat center center;}

.icon-bullet-magnify { background:url('icons/bullet_magnify.png') no-repeat center center;}

.icon-bullet-minus { background:url('icons/bullet_minus.png') no-repeat center center;}

.icon-bullet-orange { background:url('icons/bullet_orange.png') no-repeat center center;}

.icon-bullet-page-white { background:url('icons/bullet_page_white.png') no-repeat center center;}

.icon-bullet-picture { background:url('icons/bullet_picture.png') no-repeat center center;}

.icon-bullet-pink { background:url('icons/bullet_pink.png') no-repeat center center;}

.icon-bullet-plus { background:url('icons/bullet_plus.png') no-repeat center center;}

.icon-bullet-purple { background:url('icons/bullet_purple.png') no-repeat center center;}

.icon-bullet-red { background:url('icons/bullet_red.png') no-repeat center center;}

.icon-bullet-right { background:url('icons/bullet_right.png') no-repeat center center;}

.icon-bullet-shape { background:url('icons/bullet_shape.png') no-repeat center center;}

.icon-bullet-sparkle { background:url('icons/bullet_sparkle.png') no-repeat center center;}

.icon-bullet-star { background:url('icons/bullet_star.png') no-repeat center center;}

.icon-bullet-start { background:url('icons/bullet_start.png') no-repeat center center;}

.icon-bullet-stop { background:url('icons/bullet_stop.png') no-repeat center center;}

.icon-bullet-stop-alt { background:url('icons/bullet_stop_alt.png') no-repeat center center;}

.icon-bullet-tick { background:url('icons/bullet_tick.png') no-repeat center center;}

.icon-bullet-toggle-minus { background:url('icons/bullet_toggle_minus.png') no-repeat center center;}

.icon-bullet-toggle-plus { background:url('icons/bullet_toggle_plus.png') no-repeat center center;}

.icon-bullet-white { background:url('icons/bullet_white.png') no-repeat center center;}

.icon-bullet-wrench { background:url('icons/bullet_wrench.png') no-repeat center center;}

.icon-bullet-wrench-red { background:url('icons/bullet_wrench_red.png') no-repeat center center;}

.icon-bullet-yellow { background:url('icons/bullet_yellow.png') no-repeat center center;}

.icon-button { background:url('icons/button.png') no-repeat center center;}

.icon-cake { background:url('icons/cake.png') no-repeat center center;}

.icon-cake-out { background:url('icons/cake_out.png') no-repeat center center;}

.icon-cake-sliced { background:url('icons/cake_sliced.png') no-repeat center center;}

.icon-calculator { background:url('icons/calculator.png') no-repeat center center;}

.icon-calculator-add { background:url('icons/calculator_add.png') no-repeat center center;}

.icon-calculator-delete { background:url('icons/calculator_delete.png') no-repeat center center;}

.icon-calculator-edit { background:url('icons/calculator_edit.png') no-repeat center center;}

.icon-calculator-error { background:url('icons/calculator_error.png') no-repeat center center;}

.icon-calculator-link { background:url('icons/calculator_link.png') no-repeat center center;}

.icon-calendar { background:url('icons/calendar.png') no-repeat center center;}

.icon-calendar-add { background:url('icons/calendar_add.png') no-repeat center center;}

.icon-calendar-delete { background:url('icons/calendar_delete.png') no-repeat center center;}

.icon-calendar-edit { background:url('icons/calendar_edit.png') no-repeat center center;}

.icon-calendar-link { background:url('icons/calendar_link.png') no-repeat center center;}

.icon-calendar-select-day { background:url('icons/calendar_select_day.png') no-repeat center center;}

.icon-calendar-select-none { background:url('icons/calendar_select_none.png') no-repeat center center;}

.icon-calendar-select-week { background:url('icons/calendar_select_week.png') no-repeat center center;}

.icon-calendar-star { background:url('icons/calendar_star.png') no-repeat center center;}

.icon-calendar-view-day { background:url('icons/calendar_view_day.png') no-repeat center center;}

.icon-calendar-view-month { background:url('icons/calendar_view_month.png') no-repeat center center;}

.icon-calendar-view-week { background:url('icons/calendar_view_week.png') no-repeat center center;}

.icon-camera { background:url('icons/camera.png') no-repeat center center;}

.icon-camera-add { background:url('icons/camera_add.png') no-repeat center center;}

.icon-camera-connect { background:url('icons/camera_connect.png') no-repeat center center;}

.icon-camera-delete { background:url('icons/camera_delete.png') no-repeat center center;}

.icon-camera-edit { background:url('icons/camera_edit.png') no-repeat center center;}

.icon-camera-error { background:url('icons/camera_error.png') no-repeat center center;}

.icon-camera-go { background:url('icons/camera_go.png') no-repeat center center;}

.icon-camera-link { background:url('icons/camera_link.png') no-repeat center center;}

.icon-camera-magnify { background:url('icons/camera_magnify.png') no-repeat center center;}

.icon-camera-picture { background:url('icons/camera_picture.png') no-repeat center center;}

.icon-camera-small { background:url('icons/camera_small.png') no-repeat center center;}

.icon-camera-start { background:url('icons/camera_start.png') no-repeat center center;}

.icon-camera-stop { background:url('icons/camera_stop.png') no-repeat center center;}

.icon-cancel { background:url('icons/cancel.png') no-repeat center center;}

.icon-car { background:url('icons/car.png') no-repeat center center;}

.icon-cart { background:url('icons/cart.png') no-repeat center center;}

.icon-cart-add { background:url('icons/cart_add.png') no-repeat center center;}

.icon-cart-delete { background:url('icons/cart_delete.png') no-repeat center center;}

.icon-cart-edit { background:url('icons/cart_edit.png') no-repeat center center;}

.icon-cart-error { background:url('icons/cart_error.png') no-repeat center center;}

.icon-cart-full { background:url('icons/cart_full.png') no-repeat center center;}

.icon-cart-go { background:url('icons/cart_go.png') no-repeat center center;}

.icon-cart-magnify { background:url('icons/cart_magnify.png') no-repeat center center;}

.icon-cart-put { background:url('icons/cart_put.png') no-repeat center center;}

.icon-cart-remove { background:url('icons/cart_remove.png') no-repeat center center;}

.icon-car-add { background:url('icons/car_add.png') no-repeat center center;}

.icon-car-delete { background:url('icons/car_delete.png') no-repeat center center;}

.icon-car-error { background:url('icons/car_error.png') no-repeat center center;}

.icon-car-red { background:url('icons/car_red.png') no-repeat center center;}

.icon-car-start { background:url('icons/car_start.png') no-repeat center center;}

.icon-car-stop { background:url('icons/car_stop.png') no-repeat center center;}

.icon-cd { background:url('icons/cd.png') no-repeat center center;}

.icon-cdr { background:url('icons/cdr.png') no-repeat center center;}

.icon-cdr-add { background:url('icons/cdr_add.png') no-repeat center center;}

.icon-cdr-burn { background:url('icons/cdr_burn.png') no-repeat center center;}

.icon-cdr-cross { background:url('icons/cdr_cross.png') no-repeat center center;}

.icon-cdr-delete { background:url('icons/cdr_delete.png') no-repeat center center;}

.icon-cdr-edit { background:url('icons/cdr_edit.png') no-repeat center center;}

.icon-cdr-eject { background:url('icons/cdr_eject.png') no-repeat center center;}

.icon-cdr-error { background:url('icons/cdr_error.png') no-repeat center center;}

.icon-cdr-go { background:url('icons/cdr_go.png') no-repeat center center;}

.icon-cdr-magnify { background:url('icons/cdr_magnify.png') no-repeat center center;}

.icon-cdr-play { background:url('icons/cdr_play.png') no-repeat center center;}

.icon-cdr-start { background:url('icons/cdr_start.png') no-repeat center center;}

.icon-cdr-stop { background:url('icons/cdr_stop.png') no-repeat center center;}

.icon-cdr-stop-alt { background:url('icons/cdr_stop_alt.png') no-repeat center center;}

.icon-cdr-tick { background:url('icons/cdr_tick.png') no-repeat center center;}

.icon-cd-add { background:url('icons/cd_add.png') no-repeat center center;}

.icon-cd-burn { background:url('icons/cd_burn.png') no-repeat center center;}

.icon-cd-delete { background:url('icons/cd_delete.png') no-repeat center center;}

.icon-cd-edit { background:url('icons/cd_edit.png') no-repeat center center;}

.icon-cd-eject { background:url('icons/cd_eject.png') no-repeat center center;}

.icon-cd-go { background:url('icons/cd_go.png') no-repeat center center;}

.icon-cd-magnify { background:url('icons/cd_magnify.png') no-repeat center center;}

.icon-cd-play { background:url('icons/cd_play.png') no-repeat center center;}

.icon-cd-stop { background:url('icons/cd_stop.png') no-repeat center center;}

.icon-cd-stop-alt { background:url('icons/cd_stop_alt.png') no-repeat center center;}

.icon-cd-tick { background:url('icons/cd_tick.png') no-repeat center center;}

.icon-chart-bar { background:url('icons/chart_bar.png') no-repeat center center;}

.icon-chart-bar-add { background:url('icons/chart_bar_add.png') no-repeat center center;}

.icon-chart-bar-delete { background:url('icons/chart_bar_delete.png') no-repeat center center;}

.icon-chart-bar-edit { background:url('icons/chart_bar_edit.png') no-repeat center center;}

.icon-chart-bar-error { background:url('icons/chart_bar_error.png') no-repeat center center;}

.icon-chart-bar-link { background:url('icons/chart_bar_link.png') no-repeat center center;}

.icon-chart-curve { background:url('icons/chart_curve.png') no-repeat center center;}

.icon-chart-curve-add { background:url('icons/chart_curve_add.png') no-repeat center center;}

.icon-chart-curve-delete { background:url('icons/chart_curve_delete.png') no-repeat center center;}

.icon-chart-curve-edit { background:url('icons/chart_curve_edit.png') no-repeat center center;}

.icon-chart-curve-error { background:url('icons/chart_curve_error.png') no-repeat center center;}

.icon-chart-curve-go { background:url('icons/chart_curve_go.png') no-repeat center center;}

.icon-chart-curve-link { background:url('icons/chart_curve_link.png') no-repeat center center;}

.icon-chart-line { background:url('icons/chart_line.png') no-repeat center center;}

.icon-chart-line-add { background:url('icons/chart_line_add.png') no-repeat center center;}

.icon-chart-line-delete { background:url('icons/chart_line_delete.png') no-repeat center center;}

.icon-chart-line-edit { background:url('icons/chart_line_edit.png') no-repeat center center;}

.icon-chart-line-error { background:url('icons/chart_line_error.png') no-repeat center center;}

.icon-chart-line-link { background:url('icons/chart_line_link.png') no-repeat center center;}

.icon-chart-organisation { background:url('icons/chart_organisation.png') no-repeat center center;}

.icon-chart-organisation-add { background:url('icons/chart_organisation_add.png') no-repeat center center;}

.icon-chart-organisation-delete { background:url('icons/chart_organisation_delete.png') no-repeat center center;}

.icon-chart-org-inverted { background:url('icons/chart_org_inverted.png') no-repeat center center;}

.icon-chart-pie { background:url('icons/chart_pie.png') no-repeat center center;}

.icon-chart-pie-add { background:url('icons/chart_pie_add.png') no-repeat center center;}

.icon-chart-pie-delete { background:url('icons/chart_pie_delete.png') no-repeat center center;}

.icon-chart-pie-edit { background:url('icons/chart_pie_edit.png') no-repeat center center;}

.icon-chart-pie-error { background:url('icons/chart_pie_error.png') no-repeat center center;}

.icon-chart-pie-lightning { background:url('icons/chart_pie_lightning.png') no-repeat center center;}

.icon-chart-pie-link { background:url('icons/chart_pie_link.png') no-repeat center center;}

.icon-check-error { background:url('icons/check_error.png') no-repeat center center;}

.icon-chk-checked { background:url('icons/chk_checked.gif') no-repeat center center;}

.icon-chk-unchecked { background:url('icons/chk_unchecked.gif') no-repeat center center;}

.icon-clipboard { background:url('icons/clipboard.png') no-repeat center center;}

.icon-clock { background:url('icons/clock.png') no-repeat center center;}

.icon-clock-add { background:url('icons/clock_add.png') no-repeat center center;}

.icon-clock-delete { background:url('icons/clock_delete.png') no-repeat center center;}

.icon-clock-edit { background:url('icons/clock_edit.png') no-repeat center center;}

.icon-clock-error { background:url('icons/clock_error.png') no-repeat center center;}

.icon-clock-go { background:url('icons/clock_go.png') no-repeat center center;}

.icon-clock-link { background:url('icons/clock_link.png') no-repeat center center;}

.icon-clock-pause { background:url('icons/clock_pause.png') no-repeat center center;}

.icon-clock-play { background:url('icons/clock_play.png') no-repeat center center;}

.icon-clock-red { background:url('icons/clock_red.png') no-repeat center center;}

.icon-clock-start { background:url('icons/clock_start.png') no-repeat center center;}

.icon-clock-stop { background:url('icons/clock_stop.png') no-repeat center center;}

.icon-cmy { background:url('icons/cmy.png') no-repeat center center;}

.icon-cog { background:url('icons/cog.png') no-repeat center center;}

.icon-cog-add { background:url('icons/cog_add.png') no-repeat center center;}

.icon-cog-delete { background:url('icons/cog_delete.png') no-repeat center center;}

.icon-cog-edit { background:url('icons/cog_edit.png') no-repeat center center;}

.icon-cog-error { background:url('icons/cog_error.png') no-repeat center center;}

.icon-cog-go { background:url('icons/cog_go.png') no-repeat center center;}

.icon-cog-start { background:url('icons/cog_start.png') no-repeat center center;}

.icon-cog-stop { background:url('icons/cog_stop.png') no-repeat center center;}

.icon-coins { background:url('icons/coins.png') no-repeat center center;}

.icon-coins-add { background:url('icons/coins_add.png') no-repeat center center;}

.icon-coins-delete { background:url('icons/coins_delete.png') no-repeat center center;}

.icon-collapse-all { background:url('icons/collapse-all.gif') no-repeat center center;}

.icon-color { background:url('icons/color.png') no-repeat center center;}

.icon-color-swatch { background:url('icons/color_swatch.png') no-repeat center center;}

.icon-color-wheel { background:url('icons/color_wheel.png') no-repeat center center;}

.icon-comment { background:url('icons/comment.png') no-repeat center center;}

.icon-comments { background:url('icons/comments.png') no-repeat center center;}

.icon-comments-add { background:url('icons/comments_add.png') no-repeat center center;}

.icon-comments-delete { background:url('icons/comments_delete.png') no-repeat center center;}

.icon-comment-add { background:url('icons/comment_add.png') no-repeat center center;}

.icon-comment-delete { background:url('icons/comment_delete.png') no-repeat center center;}

.icon-comment-dull { background:url('icons/comment_dull.png') no-repeat center center;}

.icon-comment-edit { background:url('icons/comment_edit.png') no-repeat center center;}

.icon-comment-play { background:url('icons/comment_play.png') no-repeat center center;}

.icon-comment-record { background:url('icons/comment_record.png') no-repeat center center;}

.icon-compass { background:url('icons/compass.png') no-repeat center center;}

.icon-compress { background:url('icons/compress.png') no-repeat center center;}

.icon-computer { background:url('icons/computer.png') no-repeat center center;}

.icon-computer-add { background:url('icons/computer_add.png') no-repeat center center;}

.icon-computer-connect { background:url('icons/computer_connect.png') no-repeat center center;}

.icon-computer-delete { background:url('icons/computer_delete.png') no-repeat center center;}

.icon-computer-edit { background:url('icons/computer_edit.png') no-repeat center center;}

.icon-computer-error { background:url('icons/computer_error.png') no-repeat center center;}

.icon-computer-go { background:url('icons/computer_go.png') no-repeat center center;}

.icon-computer-key { background:url('icons/computer_key.png') no-repeat center center;}

.icon-computer-link { background:url('icons/computer_link.png') no-repeat center center;}

.icon-computer-magnify { background:url('icons/computer_magnify.png') no-repeat center center;}

.icon-computer-off { background:url('icons/computer_off.png') no-repeat center center;}

.icon-computer-start { background:url('icons/computer_start.png') no-repeat center center;}

.icon-computer-stop { background:url('icons/computer_stop.png') no-repeat center center;}

.icon-computer-wrench { background:url('icons/computer_wrench.png') no-repeat center center;}

.icon-connect { background:url('icons/connect.png') no-repeat center center;}

.icon-contrast { background:url('icons/contrast.png') no-repeat center center;}

.icon-contrast-decrease { background:url('icons/contrast_decrease.png') no-repeat center center;}

.icon-contrast-high { background:url('icons/contrast_high.png') no-repeat center center;}

.icon-contrast-increase { background:url('icons/contrast_increase.png') no-repeat center center;}

.icon-contrast-low { background:url('icons/contrast_low.png') no-repeat center center;}

.icon-controller { background:url('icons/controller.png') no-repeat center center;}

.icon-controller-add { background:url('icons/controller_add.png') no-repeat center center;}

.icon-controller-delete { background:url('icons/controller_delete.png') no-repeat center center;}

.icon-controller-error { background:url('icons/controller_error.png') no-repeat center center;}

.icon-control-add { background:url('icons/control_add.png') no-repeat center center;}

.icon-control-add-blue { background:url('icons/control_add_blue.png') no-repeat center center;}

.icon-control-blank { background:url('icons/control_blank.png') no-repeat center center;}

.icon-control-blank-blue { background:url('icons/control_blank_blue.png') no-repeat center center;}

.icon-control-eject { background:url('icons/control_eject.png') no-repeat center center;}

.icon-control-eject-blue { background:url('icons/control_eject_blue.png') no-repeat center center;}

.icon-control-end { background:url('icons/control_end.png') no-repeat center center;}

.icon-control-end-blue { background:url('icons/control_end_blue.png') no-repeat center center;}

.icon-control-equalizer { background:url('icons/control_equalizer.png') no-repeat center center;}

.icon-control-equalizer-blue { background:url('icons/control_equalizer_blue.png') no-repeat center center;}

.icon-control-fastforward { background:url('icons/control_fastforward.png') no-repeat center center;}

.icon-control-fastforward-blue { background:url('icons/control_fastforward_blue.png') no-repeat center center;}

.icon-control-pause { background:url('icons/control_pause.png') no-repeat center center;}

.icon-control-pause-blue { background:url('icons/control_pause_blue.png') no-repeat center center;}

.icon-control-play { background:url('icons/control_play.png') no-repeat center center;}

.icon-control-play-blue { background:url('icons/control_play_blue.png') no-repeat center center;}

.icon-control-power { background:url('icons/control_power.png') no-repeat center center;}

.icon-control-power-blue { background:url('icons/control_power_blue.png') no-repeat center center;}

.icon-control-record { background:url('icons/control_record.png') no-repeat center center;}

.icon-control-record-blue { background:url('icons/control_record_blue.png') no-repeat center center;}

.icon-control-remove { background:url('icons/control_remove.png') no-repeat center center;}

.icon-control-remove-blue { background:url('icons/control_remove_blue.png') no-repeat center center;}

.icon-control-repeat { background:url('icons/control_repeat.png') no-repeat center center;}

.icon-control-repeat-blue { background:url('icons/control_repeat_blue.png') no-repeat center center;}

.icon-control-rewind { background:url('icons/control_rewind.png') no-repeat center center;}

.icon-control-rewind-blue { background:url('icons/control_rewind_blue.png') no-repeat center center;}

.icon-control-start { background:url('icons/control_start.png') no-repeat center center;}

.icon-control-start-blue { background:url('icons/control_start_blue.png') no-repeat center center;}

.icon-control-stop { background:url('icons/control_stop.png') no-repeat center center;}

.icon-control-stop-blue { background:url('icons/control_stop_blue.png') no-repeat center center;}

.icon-creditcards { background:url('icons/creditcards.png') no-repeat center center;}

.icon-cross { background:url('icons/cross.png') no-repeat center center;}

.icon-cross-octagon { background:url('icons/cross_octagon.png') no-repeat center center;}

.icon-css { background:url('icons/css.png') no-repeat center center;}

.icon-css-add { background:url('icons/css_add.png') no-repeat center center;}

.icon-css-delete { background:url('icons/css_delete.png') no-repeat center center;}

.icon-css-error { background:url('icons/css_error.png') no-repeat center center;}

.icon-css-go { background:url('icons/css_go.png') no-repeat center center;}

.icon-css-valid { background:url('icons/css_valid.png') no-repeat center center;}

.icon-cup { background:url('icons/cup.png') no-repeat center center;}

.icon-cup-add { background:url('icons/cup_add.png') no-repeat center center;}

.icon-cup-black { background:url('icons/cup_black.png') no-repeat center center;}

.icon-cup-delete { background:url('icons/cup_delete.png') no-repeat center center;}

.icon-cup-edit { background:url('icons/cup_edit.png') no-repeat center center;}

.icon-cup-error { background:url('icons/cup_error.png') no-repeat center center;}

.icon-cup-go { background:url('icons/cup_go.png') no-repeat center center;}

.icon-cup-green { background:url('icons/cup_green.png') no-repeat center center;}

.icon-cup-key { background:url('icons/cup_key.png') no-repeat center center;}

.icon-cup-link { background:url('icons/cup_link.png') no-repeat center center;}

.icon-cup-tea { background:url('icons/cup_tea.png') no-repeat center center;}

.icon-cursor { background:url('icons/cursor.png') no-repeat center center;}

.icon-cursor-small { background:url('icons/cursor_small.png') no-repeat center center;}

.icon-cut { background:url('icons/cut.png') no-repeat center center;}

.icon-cut-red { background:url('icons/cut_red.png') no-repeat center center;}

.icon-database { background:url('icons/database.png') no-repeat center center;}

.icon-database-add { background:url('icons/database_add.png') no-repeat center center;}

.icon-database-connect { background:url('icons/database_connect.png') no-repeat center center;}

.icon-database-copy { background:url('icons/database_copy.png') no-repeat center center;}

.icon-database-delete { background:url('icons/database_delete.png') no-repeat center center;}

.icon-database-edit { background:url('icons/database_edit.png') no-repeat center center;}

.icon-database-error { background:url('icons/database_error.png') no-repeat center center;}

.icon-database-gear { background:url('icons/database_gear.png') no-repeat center center;}

.icon-database-go { background:url('icons/database_go.png') no-repeat center center;}

.icon-database-key { background:url('icons/database_key.png') no-repeat center center;}

.icon-database-lightning { background:url('icons/database_lightning.png') no-repeat center center;}

.icon-database-link { background:url('icons/database_link.png') no-repeat center center;}

.icon-database-refresh { background:url('icons/database_refresh.png') no-repeat center center;}

.icon-database-save { background:url('icons/database_save.png') no-repeat center center;}

.icon-database-start { background:url('icons/database_start.png') no-repeat center center;}

.icon-database-stop { background:url('icons/database_stop.png') no-repeat center center;}

.icon-database-table { background:url('icons/database_table.png') no-repeat center center;}

.icon-database-wrench { background:url('icons/database_wrench.png') no-repeat center center;}

.icon-database-yellow { background:url('icons/database_yellow.png') no-repeat center center;}

.icon-database-yellow-start { background:url('icons/database_yellow_start.png') no-repeat center center;}

.icon-database-yellow-stop { background:url('icons/database_yellow_stop.png') no-repeat center center;}

.icon-date { background:url('icons/date.png') no-repeat center center;}

.icon-date-add { background:url('icons/date_add.png') no-repeat center center;}

.icon-date-delete { background:url('icons/date_delete.png') no-repeat center center;}

.icon-date-edit { background:url('icons/date_edit.png') no-repeat center center;}

.icon-date-error { background:url('icons/date_error.png') no-repeat center center;}

.icon-date-go { background:url('icons/date_go.png') no-repeat center center;}

.icon-date-link { background:url('icons/date_link.png') no-repeat center center;}

.icon-date-magnify { background:url('icons/date_magnify.png') no-repeat center center;}

.icon-date-next { background:url('icons/date_next.png') no-repeat center center;}

.icon-date-previous { background:url('icons/date_previous.png') no-repeat center center;}

.icon-decline { background:url('icons/decline.png') no-repeat center center;}

.icon-delete { background:url('icons/delete.gif') no-repeat center center;}

.icon-delete { background:url('icons/delete.png') no-repeat center center;}

.icon-delete3 { background:url('icons/delete3.png') no-repeat center center;}

.icon-DeleteRed { background:url('icons/DeleteRed.png') no-repeat center center;}

.icon-device-stylus { background:url('icons/device_stylus.png') no-repeat center center;}

.icon-disconnect { background:url('icons/disconnect.png') no-repeat center center;}

.icon-disk { background:url('icons/disk.png') no-repeat center center;}

.icon-disk-black { background:url('icons/disk_black.png') no-repeat center center;}

.icon-disk-black-error { background:url('icons/disk_black_error.png') no-repeat center center;}

.icon-disk-black-magnify { background:url('icons/disk_black_magnify.png') no-repeat center center;}

.icon-disk-download { background:url('icons/disk_download.png') no-repeat center center;}

.icon-disk-edit { background:url('icons/disk_edit.png') no-repeat center center;}

.icon-disk-error { background:url('icons/disk_error.png') no-repeat center center;}

.icon-disk-magnify { background:url('icons/disk_magnify.png') no-repeat center center;}

.icon-disk-multiple { background:url('icons/disk_multiple.png') no-repeat center center;}

.icon-disk-upload { background:url('icons/disk_upload.png') no-repeat center center;}

.icon-door { background:url('icons/door.png') no-repeat center center;}

.icon-door-error { background:url('icons/door_error.png') no-repeat center center;}

.icon-door-in { background:url('icons/door_in.png') no-repeat center center;}

.icon-door-open { background:url('icons/door_open.png') no-repeat center center;}

.icon-door-out { background:url('icons/door_out.png') no-repeat center center;}

.icon-download { background:url('icons/download.gif') no-repeat center center;}

.icon-download { background:url('icons/download.png') no-repeat center center;}

.icon-drink { background:url('icons/drink.png') no-repeat center center;}

.icon-drink-empty { background:url('icons/drink_empty.png') no-repeat center center;}

.icon-drink-red { background:url('icons/drink_red.png') no-repeat center center;}

.icon-drive { background:url('icons/drive.png') no-repeat center center;}

.icon-drive-add { background:url('icons/drive_add.png') no-repeat center center;}

.icon-drive-burn { background:url('icons/drive_burn.png') no-repeat center center;}

.icon-drive-cd { background:url('icons/drive_cd.png') no-repeat center center;}

.icon-drive-cdr { background:url('icons/drive_cdr.png') no-repeat center center;}

.icon-drive-cd-empty { background:url('icons/drive_cd_empty.png') no-repeat center center;}

.icon-drive-delete { background:url('icons/drive_delete.png') no-repeat center center;}

.icon-drive-disk { background:url('icons/drive_disk.png') no-repeat center center;}

.icon-drive-edit { background:url('icons/drive_edit.png') no-repeat center center;}

.icon-drive-error { background:url('icons/drive_error.png') no-repeat center center;}

.icon-drive-go { background:url('icons/drive_go.png') no-repeat center center;}

.icon-drive-key { background:url('icons/drive_key.png') no-repeat center center;}

.icon-drive-link { background:url('icons/drive_link.png') no-repeat center center;}

.icon-drive-magnify { background:url('icons/drive_magnify.png') no-repeat center center;}

.icon-drive-network { background:url('icons/drive_network.png') no-repeat center center;}

.icon-drive-network-error { background:url('icons/drive_network_error.png') no-repeat center center;}

.icon-drive-network-stop { background:url('icons/drive_network_stop.png') no-repeat center center;}

.icon-drive-rename { background:url('icons/drive_rename.png') no-repeat center center;}

.icon-drive-user { background:url('icons/drive_user.png') no-repeat center center;}

.icon-drive-web { background:url('icons/drive_web.png') no-repeat center center;}

.icon-dvd { background:url('icons/dvd.png') no-repeat center center;}

.icon-dvd-add { background:url('icons/dvd_add.png') no-repeat center center;}

.icon-dvd-delete { background:url('icons/dvd_delete.png') no-repeat center center;}

.icon-dvd-edit { background:url('icons/dvd_edit.png') no-repeat center center;}

.icon-dvd-error { background:url('icons/dvd_error.png') no-repeat center center;}

.icon-dvd-go { background:url('icons/dvd_go.png') no-repeat center center;}

.icon-dvd-key { background:url('icons/dvd_key.png') no-repeat center center;}

.icon-dvd-link { background:url('icons/dvd_link.png') no-repeat center center;}

.icon-dvd-start { background:url('icons/dvd_start.png') no-repeat center center;}

.icon-dvd-stop { background:url('icons/dvd_stop.png') no-repeat center center;}

.icon-edit-clear { background:url('icons/edit-clear.png') no-repeat center center;}

.icon-edit { background:url('icons/edit.gif') no-repeat center center;}

.icon-edit-remove { background:url('icons/edit_remove.png') no-repeat center center;}

.icon-eject-blue { background:url('icons/eject_blue.png') no-repeat center center;}

.icon-eject-green { background:url('icons/eject_green.png') no-repeat center center;}

.icon-email { background:url('icons/email.png') no-repeat center center;}

.icon-email-add { background:url('icons/email_add.png') no-repeat center center;}

.icon-email-attach { background:url('icons/email_attach.png') no-repeat center center;}

.icon-email-delete { background:url('icons/email_delete.png') no-repeat center center;}

.icon-email-edit { background:url('icons/email_edit.png') no-repeat center center;}

.icon-email-error { background:url('icons/email_error.png') no-repeat center center;}

.icon-email-go { background:url('icons/email_go.png') no-repeat center center;}

.icon-email-link { background:url('icons/email_link.png') no-repeat center center;}

.icon-email-magnify { background:url('icons/email_magnify.png') no-repeat center center;}

.icon-email-open { background:url('icons/email_open.png') no-repeat center center;}

.icon-email-open-image { background:url('icons/email_open_image.png') no-repeat center center;}

.icon-email-star { background:url('icons/email_star.png') no-repeat center center;}

.icon-email-start { background:url('icons/email_start.png') no-repeat center center;}

.icon-email-stop { background:url('icons/email_stop.png') no-repeat center center;}

.icon-email-transfer { background:url('icons/email_transfer.png') no-repeat center center;}

.icon-emoticon-evilgrin { background:url('icons/emoticon_evilgrin.png') no-repeat center center;}

.icon-emoticon-grin { background:url('icons/emoticon_grin.png') no-repeat center center;}

.icon-emoticon-happy { background:url('icons/emoticon_happy.png') no-repeat center center;}

.icon-emoticon-smile { background:url('icons/emoticon_smile.png') no-repeat center center;}

.icon-emoticon-surprised { background:url('icons/emoticon_surprised.png') no-repeat center center;}

.icon-emoticon-tongue { background:url('icons/emoticon_tongue.png') no-repeat center center;}

.icon-emoticon-unhappy { background:url('icons/emoticon_unhappy.png') no-repeat center center;}

.icon-emoticon-waii { background:url('icons/emoticon_waii.png') no-repeat center center;}

.icon-emoticon-wink { background:url('icons/emoticon_wink.png') no-repeat center center;}

.icon-erase { background:url('icons/erase.png') no-repeat center center;}

.icon-error { background:url('icons/error.png') no-repeat center center;}

.icon-error-add { background:url('icons/error_add.png') no-repeat center center;}

.icon-error-delete { background:url('icons/error_delete.png') no-repeat center center;}

.icon-error-go { background:url('icons/error_go.png') no-repeat center center;}

.icon-exclamation { background:url('icons/exclamation.png') no-repeat center center;}

.icon-expand-all { background:url('icons/expand-all.gif') no-repeat center center;}

.icon-export { background:url('icons/export.png') no-repeat center center;}

.icon-eye { background:url('icons/eye.png') no-repeat center center;}

.icon-eyes { background:url('icons/eyes.png') no-repeat center center;}

.icon-feed { background:url('icons/feed.png') no-repeat center center;}

.icon-feed-add { background:url('icons/feed_add.png') no-repeat center center;}

.icon-feed-delete { background:url('icons/feed_delete.png') no-repeat center center;}

.icon-feed-disk { background:url('icons/feed_disk.png') no-repeat center center;}

.icon-feed-edit { background:url('icons/feed_edit.png') no-repeat center center;}

.icon-feed-error { background:url('icons/feed_error.png') no-repeat center center;}

.icon-feed-go { background:url('icons/feed_go.png') no-repeat center center;}

.icon-feed-key { background:url('icons/feed_key.png') no-repeat center center;}

.icon-feed-link { background:url('icons/feed_link.png') no-repeat center center;}

.icon-feed-magnify { background:url('icons/feed_magnify.png') no-repeat center center;}

.icon-feed-star { background:url('icons/feed_star.png') no-repeat center center;}

.icon-female { background:url('icons/female.png') no-repeat center center;}

.icon-film { background:url('icons/film.png') no-repeat center center;}

.icon-film-add { background:url('icons/film_add.png') no-repeat center center;}

.icon-film-delete { background:url('icons/film_delete.png') no-repeat center center;}

.icon-film-edit { background:url('icons/film_edit.png') no-repeat center center;}

.icon-film-eject { background:url('icons/film_eject.png') no-repeat center center;}

.icon-film-error { background:url('icons/film_error.png') no-repeat center center;}

.icon-film-go { background:url('icons/film_go.png') no-repeat center center;}

.icon-film-key { background:url('icons/film_key.png') no-repeat center center;}

.icon-film-link { background:url('icons/film_link.png') no-repeat center center;}

.icon-film-magnify { background:url('icons/film_magnify.png') no-repeat center center;}

.icon-film-save { background:url('icons/film_save.png') no-repeat center center;}

.icon-film-star { background:url('icons/film_star.png') no-repeat center center;}

.icon-film-start { background:url('icons/film_start.png') no-repeat center center;}

.icon-film-stop { background:url('icons/film_stop.png') no-repeat center center;}

.icon-find { background:url('icons/find.png') no-repeat center center;}

.icon-finger-point { background:url('icons/finger_point.png') no-repeat center center;}

.icon-flag-black { background:url('icons/flag_black.png') no-repeat center center;}

.icon-flag-blue { background:url('icons/flag_blue.png') no-repeat center center;}

.icon-flag-checked { background:url('icons/flag_checked.png') no-repeat center center;}

.icon-flag-france { background:url('icons/flag_france.png') no-repeat center center;}

.icon-flag-green { background:url('icons/flag_green.png') no-repeat center center;}

.icon-flag-grey { background:url('icons/flag_grey.png') no-repeat center center;}

.icon-flag-orange { background:url('icons/flag_orange.png') no-repeat center center;}

.icon-flag-pink { background:url('icons/flag_pink.png') no-repeat center center;}

.icon-flag-purple { background:url('icons/flag_purple.png') no-repeat center center;}

.icon-flag-red { background:url('icons/flag_red.png') no-repeat center center;}

.icon-flag-white { background:url('icons/flag_white.png') no-repeat center center;}

.icon-flag-yellow { background:url('icons/flag_yellow.png') no-repeat center center;}

.icon-flower-daisy { background:url('icons/flower_daisy.png') no-repeat center center;}

.icon-folder { background:url('icons/folder.png') no-repeat center center;}

.icon-folder-add { background:url('icons/folder_add.png') no-repeat center center;}

.icon-folder-bell { background:url('icons/folder_bell.png') no-repeat center center;}

.icon-folder-bookmark { background:url('icons/folder_bookmark.png') no-repeat center center;}

.icon-folder-brick { background:url('icons/folder_brick.png') no-repeat center center;}

.icon-folder-bug { background:url('icons/folder_bug.png') no-repeat center center;}

.icon-folder-camera { background:url('icons/folder_camera.png') no-repeat center center;}

.icon-folder-connect { background:url('icons/folder_connect.png') no-repeat center center;}

.icon-folder-database { background:url('icons/folder_database.png') no-repeat center center;}

.icon-folder-delete { background:url('icons/folder_delete.png') no-repeat center center;}

.icon-folder-edit { background:url('icons/folder_edit.png') no-repeat center center;}

.icon-folder-error { background:url('icons/folder_error.png') no-repeat center center;}

.icon-folder-explore { background:url('icons/folder_explore.png') no-repeat center center;}

.icon-folder-feed { background:url('icons/folder_feed.png') no-repeat center center;}

.icon-folder-film { background:url('icons/folder_film.png') no-repeat center center;}

.icon-folder-find { background:url('icons/folder_find.png') no-repeat center center;}

.icon-folder-font { background:url('icons/folder_font.png') no-repeat center center;}

.icon-folder-go { background:url('icons/folder_go.png') no-repeat center center;}

.icon-folder-heart { background:url('icons/folder_heart.png') no-repeat center center;}

.icon-folder-home { background:url('icons/folder_home.png') no-repeat center center;}

.icon-folder-image { background:url('icons/folder_image.png') no-repeat center center;}

.icon-folder-key { background:url('icons/folder_key.png') no-repeat center center;}

.icon-folder-lightbulb { background:url('icons/folder_lightbulb.png') no-repeat center center;}

.icon-folder-link { background:url('icons/folder_link.png') no-repeat center center;}

.icon-folder-magnify { background:url('icons/folder_magnify.png') no-repeat center center;}

.icon-folder-page { background:url('icons/folder_page.png') no-repeat center center;}

.icon-folder-page-white { background:url('icons/folder_page_white.png') no-repeat center center;}

.icon-folder-palette { background:url('icons/folder_palette.png') no-repeat center center;}

.icon-folder-picture { background:url('icons/folder_picture.png') no-repeat center center;}

.icon-folder-star { background:url('icons/folder_star.png') no-repeat center center;}

.icon-folder-table { background:url('icons/folder_table.png') no-repeat center center;}

.icon-folder-up { background:url('icons/folder_up.png') no-repeat center center;}

.icon-folder-user { background:url('icons/folder_user.png') no-repeat center center;}

.icon-folder-wrench { background:url('icons/folder_wrench.png') no-repeat center center;}

.icon-font { background:url('icons/font.png') no-repeat center center;}

.icon-font-add { background:url('icons/font_add.png') no-repeat center center;}

.icon-font-color { background:url('icons/font_color.png') no-repeat center center;}

.icon-font-delete { background:url('icons/font_delete.png') no-repeat center center;}

.icon-font-go { background:url('icons/font_go.png') no-repeat center center;}

.icon-font-larger { background:url('icons/font_larger.png') no-repeat center center;}

.icon-font-smaller { background:url('icons/font_smaller.png') no-repeat center center;}

.icon-forward-blue { background:url('icons/forward_blue.png') no-repeat center center;}

.icon-forward-green { background:url('icons/forward_green.png') no-repeat center center;}

.icon-group { background:url('icons/group.png') no-repeat center center;}

.icon-group32 { background:url('icons/group32.png') no-repeat center center;}

.icon-group-add { background:url('icons/group_add.png') no-repeat center center;}

.icon-group-delete { background:url('icons/group_delete.png') no-repeat center center;}

.icon-group-edit { background:url('icons/group_edit.png') no-repeat center center;}

.icon-group-error { background:url('icons/group_error.png') no-repeat center center;}

.icon-group-gear { background:url('icons/group_gear.png') no-repeat center center;}

.icon-group-go { background:url('icons/group_go.png') no-repeat center center;}

.icon-group-key { background:url('icons/group_key.png') no-repeat center center;}

.icon-group-link { background:url('icons/group_link.png') no-repeat center center;}

.icon-heart { background:url('icons/heart.png') no-repeat center center;}

.icon-heart-add { background:url('icons/heart_add.png') no-repeat center center;}

.icon-heart-broken { background:url('icons/heart_broken.png') no-repeat center center;}

.icon-heart-connect { background:url('icons/heart_connect.png') no-repeat center center;}

.icon-heart-delete { background:url('icons/heart_delete.png') no-repeat center center;}

.icon-help { background:url('icons/help.png') no-repeat center center;}

.icon-hourglass { background:url('icons/hourglass.png') no-repeat center center;}

.icon-hourglass-add { background:url('icons/hourglass_add.png') no-repeat center center;}

.icon-hourglass-delete { background:url('icons/hourglass_delete.png') no-repeat center center;}

.icon-hourglass-go { background:url('icons/hourglass_go.png') no-repeat center center;}

.icon-hourglass-link { background:url('icons/hourglass_link.png') no-repeat center center;}

.icon-house { background:url('icons/house.png') no-repeat center center;}

.icon-house-connect { background:url('icons/house_connect.png') no-repeat center center;}

.icon-house-go { background:url('icons/house_go.png') no-repeat center center;}

.icon-house-in { background:url('icons/house_in.png') no-repeat center center;}

.icon-house-key { background:url('icons/house_key.png') no-repeat center center;}

.icon-house-link { background:url('icons/house_link.png') no-repeat center center;}

.icon-house-star { background:url('icons/house_star.png') no-repeat center center;}

.icon-html { background:url('icons/html.png') no-repeat center center;}

.icon-html-add { background:url('icons/html_add.png') no-repeat center center;}

.icon-html-delete { background:url('icons/html_delete.png') no-repeat center center;}

.icon-html-error { background:url('icons/html_error.png') no-repeat center center;}

.icon-html-go { background:url('icons/html_go.png') no-repeat center center;}

.icon-html-valid { background:url('icons/html_valid.png') no-repeat center center;}

.icon-image { background:url('icons/image.png') no-repeat center center;}

.icon-images { background:url('icons/images.png') no-repeat center center;}

.icon-image-add { background:url('icons/image_add.png') no-repeat center center;}

.icon-image-delete { background:url('icons/image_delete.png') no-repeat center center;}

.icon-image-edit { background:url('icons/image_edit.png') no-repeat center center;}

.icon-image-link { background:url('icons/image_link.png') no-repeat center center;}

.icon-image-magnify { background:url('icons/image_magnify.png') no-repeat center center;}

.icon-image-star { background:url('icons/image_star.png') no-repeat center center;}

.icon-information { background:url('icons/information.png') no-repeat center center;}

.icon-ipod { background:url('icons/ipod.png') no-repeat center center;}

.icon-ipod-cast { background:url('icons/ipod_cast.png') no-repeat center center;}

.icon-ipod-cast-add { background:url('icons/ipod_cast_add.png') no-repeat center center;}

.icon-ipod-cast-delete { background:url('icons/ipod_cast_delete.png') no-repeat center center;}

.icon-ipod-connect { background:url('icons/ipod_connect.png') no-repeat center center;}

.icon-ipod-nano { background:url('icons/ipod_nano.png') no-repeat center center;}

.icon-ipod-nano-connect { background:url('icons/ipod_nano_connect.png') no-repeat center center;}

.icon-ipod-sound { background:url('icons/ipod_sound.png') no-repeat center center;}

.icon-joystick { background:url('icons/joystick.png') no-repeat center center;}

.icon-joystick-add { background:url('icons/joystick_add.png') no-repeat center center;}

.icon-joystick-connect { background:url('icons/joystick_connect.png') no-repeat center center;}

.icon-joystick-delete { background:url('icons/joystick_delete.png') no-repeat center center;}

.icon-joystick-error { background:url('icons/joystick_error.png') no-repeat center center;}

.icon-key { background:url('icons/key.png') no-repeat center center;}

.icon-keyboard { background:url('icons/keyboard.png') no-repeat center center;}

.icon-keyboard-add { background:url('icons/keyboard_add.png') no-repeat center center;}

.icon-keyboard-connect { background:url('icons/keyboard_connect.png') no-repeat center center;}

.icon-keyboard-delete { background:url('icons/keyboard_delete.png') no-repeat center center;}

.icon-keyboard-magnify { background:url('icons/keyboard_magnify.png') no-repeat center center;}

.icon-key-add { background:url('icons/key_add.png') no-repeat center center;}

.icon-key-delete { background:url('icons/key_delete.png') no-repeat center center;}

.icon-key-go { background:url('icons/key_go.png') no-repeat center center;}

.icon-key-start { background:url('icons/key_start.png') no-repeat center center;}

.icon-key-stop { background:url('icons/key_stop.png') no-repeat center center;}

.icon-laptop { background:url('icons/laptop.png') no-repeat center center;}

.icon-laptop-add { background:url('icons/laptop_add.png') no-repeat center center;}

.icon-laptop-connect { background:url('icons/laptop_connect.png') no-repeat center center;}

.icon-laptop-delete { background:url('icons/laptop_delete.png') no-repeat center center;}

.icon-laptop-disk { background:url('icons/laptop_disk.png') no-repeat center center;}

.icon-laptop-edit { background:url('icons/laptop_edit.png') no-repeat center center;}

.icon-laptop-error { background:url('icons/laptop_error.png') no-repeat center center;}

.icon-laptop-go { background:url('icons/laptop_go.png') no-repeat center center;}

.icon-laptop-key { background:url('icons/laptop_key.png') no-repeat center center;}

.icon-laptop-link { background:url('icons/laptop_link.png') no-repeat center center;}

.icon-laptop-magnify { background:url('icons/laptop_magnify.png') no-repeat center center;}

.icon-laptop-start { background:url('icons/laptop_start.png') no-repeat center center;}

.icon-laptop-stop { background:url('icons/laptop_stop.png') no-repeat center center;}

.icon-laptop-wrench { background:url('icons/laptop_wrench.png') no-repeat center center;}

.icon-layers { background:url('icons/layers.png') no-repeat center center;}

.icon-layout { background:url('icons/layout.png') no-repeat center center;}

.icon-layout-add { background:url('icons/layout_add.png') no-repeat center center;}

.icon-layout-content { background:url('icons/layout_content.png') no-repeat center center;}

.icon-layout-delete { background:url('icons/layout_delete.png') no-repeat center center;}

.icon-layout-edit { background:url('icons/layout_edit.png') no-repeat center center;}

.icon-layout-error { background:url('icons/layout_error.png') no-repeat center center;}

.icon-layout-header { background:url('icons/layout_header.png') no-repeat center center;}

.icon-layout-key { background:url('icons/layout_key.png') no-repeat center center;}

.icon-layout-lightning { background:url('icons/layout_lightning.png') no-repeat center center;}

.icon-layout-link { background:url('icons/layout_link.png') no-repeat center center;}

.icon-layout-sidebar { background:url('icons/layout_sidebar.png') no-repeat center center;}

.icon-lightbulb { background:url('icons/lightbulb.png') no-repeat center center;}

.icon-lightbulb-add { background:url('icons/lightbulb_add.png') no-repeat center center;}

.icon-lightbulb-delete { background:url('icons/lightbulb_delete.png') no-repeat center center;}

.icon-lightbulb-off { background:url('icons/lightbulb_off.png') no-repeat center center;}

.icon-lightning { background:url('icons/lightning.png') no-repeat center center;}

.icon-lightning-add { background:url('icons/lightning_add.png') no-repeat center center;}

.icon-lightning-delete { background:url('icons/lightning_delete.png') no-repeat center center;}

.icon-lightning-go { background:url('icons/lightning_go.png') no-repeat center center;}

.icon-link { background:url('icons/link.png') no-repeat center center;}

.icon-link-add { background:url('icons/link_add.png') no-repeat center center;}

.icon-link-break { background:url('icons/link_break.png') no-repeat center center;}

.icon-link-delete { background:url('icons/link_delete.png') no-repeat center center;}

.icon-link-edit { background:url('icons/link_edit.png') no-repeat center center;}

.icon-link-error { background:url('icons/link_error.png') no-repeat center center;}

.icon-link-go { background:url('icons/link_go.png') no-repeat center center;}

.icon-lock { background:url('icons/lock.png') no-repeat center center;}

.icon-lock-add { background:url('icons/lock_add.png') no-repeat center center;}

.icon-lock-break { background:url('icons/lock_break.png') no-repeat center center;}

.icon-lock-delete { background:url('icons/lock_delete.png') no-repeat center center;}

.icon-lock-edit { background:url('icons/lock_edit.png') no-repeat center center;}

.icon-lock-go { background:url('icons/lock_go.png') no-repeat center center;}

.icon-lock-key { background:url('icons/lock_key.png') no-repeat center center;}

.icon-lock-open { background:url('icons/lock_open.png') no-repeat center center;}

.icon-lock-start { background:url('icons/lock_start.png') no-repeat center center;}

.icon-lock-stop { background:url('icons/lock_stop.png') no-repeat center center;}

.icon-lorry { background:url('icons/lorry.png') no-repeat center center;}

.icon-lorry-add { background:url('icons/lorry_add.png') no-repeat center center;}

.icon-lorry-delete { background:url('icons/lorry_delete.png') no-repeat center center;}

.icon-lorry-error { background:url('icons/lorry_error.png') no-repeat center center;}

.icon-lorry-flatbed { background:url('icons/lorry_flatbed.png') no-repeat center center;}

.icon-lorry-go { background:url('icons/lorry_go.png') no-repeat center center;}

.icon-lorry-link { background:url('icons/lorry_link.png') no-repeat center center;}

.icon-lorry-start { background:url('icons/lorry_start.png') no-repeat center center;}

.icon-lorry-stop { background:url('icons/lorry_stop.png') no-repeat center center;}

.icon-magifier-zoom-out { background:url('icons/magifier_zoom_out.png') no-repeat center center;}

.icon-magnifier { background:url('icons/magnifier.png') no-repeat center center;}

.icon-magnifier-zoom-in { background:url('icons/magnifier_zoom_in.png') no-repeat center center;}

.icon-mail { background:url('icons/mail.png') no-repeat center center;}

.icon-male { background:url('icons/male.png') no-repeat center center;}

.icon-map { background:url('icons/map.png') no-repeat center center;}

.icon-map-add { background:url('icons/map_add.png') no-repeat center center;}

.icon-map-clipboard { background:url('icons/map_clipboard.png') no-repeat center center;}

.icon-map-cursor { background:url('icons/map_cursor.png') no-repeat center center;}

.icon-map-delete { background:url('icons/map_delete.png') no-repeat center center;}

.icon-map-edit { background:url('icons/map_edit.png') no-repeat center center;}

.icon-map-error { background:url('icons/map_error.png') no-repeat center center;}

.icon-map-go { background:url('icons/map_go.png') no-repeat center center;}

.icon-map-link { background:url('icons/map_link.png') no-repeat center center;}

.icon-map-magnify { background:url('icons/map_magnify.png') no-repeat center center;}

.icon-map-start { background:url('icons/map_start.png') no-repeat center center;}

.icon-map-stop { background:url('icons/map_stop.png') no-repeat center center;}

.icon-medal-bronze-1 { background:url('icons/medal_bronze_1.png') no-repeat center center;}

.icon-medal-bronze-2 { background:url('icons/medal_bronze_2.png') no-repeat center center;}

.icon-medal-bronze-3 { background:url('icons/medal_bronze_3.png') no-repeat center center;}

.icon-medal-bronze-add { background:url('icons/medal_bronze_add.png') no-repeat center center;}

.icon-medal-bronze-delete { background:url('icons/medal_bronze_delete.png') no-repeat center center;}

.icon-medal-gold-1 { background:url('icons/medal_gold_1.png') no-repeat center center;}

.icon-medal-gold-2 { background:url('icons/medal_gold_2.png') no-repeat center center;}

.icon-medal-gold-3 { background:url('icons/medal_gold_3.png') no-repeat center center;}

.icon-medal-gold-add { background:url('icons/medal_gold_add.png') no-repeat center center;}

.icon-medal-gold-delete { background:url('icons/medal_gold_delete.png') no-repeat center center;}

.icon-medal-silver-1 { background:url('icons/medal_silver_1.png') no-repeat center center;}

.icon-medal-silver-2 { background:url('icons/medal_silver_2.png') no-repeat center center;}

.icon-medal-silver-3 { background:url('icons/medal_silver_3.png') no-repeat center center;}

.icon-medal-silver-add { background:url('icons/medal_silver_add.png') no-repeat center center;}

.icon-medal-silver-delete { background:url('icons/medal_silver_delete.png') no-repeat center center;}

.icon-menu-rightarrow { background:url('icons/menu_rightarrow.png') no-repeat center center;}

.icon-money { background:url('icons/money.png') no-repeat center center;}

.icon-money-add { background:url('icons/money_add.png') no-repeat center center;}

.icon-money-delete { background:url('icons/money_delete.png') no-repeat center center;}

.icon-money-dollar { background:url('icons/money_dollar.png') no-repeat center center;}

.icon-money-euro { background:url('icons/money_euro.png') no-repeat center center;}

.icon-money-pound { background:url('icons/money_pound.png') no-repeat center center;}

.icon-money-yen { background:url('icons/money_yen.png') no-repeat center center;}

.icon-monitor { background:url('icons/monitor.png') no-repeat center center;}

.icon-monitor-add { background:url('icons/monitor_add.png') no-repeat center center;}

.icon-monitor-delete { background:url('icons/monitor_delete.png') no-repeat center center;}

.icon-monitor-edit { background:url('icons/monitor_edit.png') no-repeat center center;}

.icon-monitor-error { background:url('icons/monitor_error.png') no-repeat center center;}

.icon-monitor-go { background:url('icons/monitor_go.png') no-repeat center center;}

.icon-monitor-key { background:url('icons/monitor_key.png') no-repeat center center;}

.icon-monitor-lightning { background:url('icons/monitor_lightning.png') no-repeat center center;}

.icon-monitor-link { background:url('icons/monitor_link.png') no-repeat center center;}

.icon-moon-full { background:url('icons/moon_full.png') no-repeat center center;}

.icon-mouse { background:url('icons/mouse.png') no-repeat center center;}

.icon-mouse-add { background:url('icons/mouse_add.png') no-repeat center center;}

.icon-mouse-delete { background:url('icons/mouse_delete.png') no-repeat center center;}

.icon-mouse-error { background:url('icons/mouse_error.png') no-repeat center center;}

.icon-music { background:url('icons/music.png') no-repeat center center;}

.icon-music-note { background:url('icons/music_note.png') no-repeat center center;}

.icon-neighbourhood { background:url('icons/neighbourhood.png') no-repeat center center;}

.icon-new { background:url('icons/new.png') no-repeat center center;}

.icon-newspaper { background:url('icons/newspaper.png') no-repeat center center;}

.icon-newspaper-add { background:url('icons/newspaper_add.png') no-repeat center center;}

.icon-newspaper-delete { background:url('icons/newspaper_delete.png') no-repeat center center;}

.icon-newspaper-go { background:url('icons/newspaper_go.png') no-repeat center center;}

.icon-newspaper-link { background:url('icons/newspaper_link.png') no-repeat center center;}

.icon-new-blue { background:url('icons/new_blue.png') no-repeat center center;}

.icon-new-red { background:url('icons/new_red.png') no-repeat center center;}

.icon-next-green { background:url('icons/next-green.png') no-repeat center center;}

.icon-next { background:url('icons/next.png') no-repeat center center;}

.icon-next-blue { background:url('icons/next_blue.png') no-repeat center center;}

.icon-next-green { background:url('icons/next_green.png') no-repeat center center;}

.icon-node-tree16 { background:url('icons/node_tree16.png') no-repeat center center;}

.icon-node-tree32 { background:url('icons/node_tree32.png') no-repeat center center;}

.icon-note { background:url('icons/note.png') no-repeat center center;}

.icon-note-add { background:url('icons/note_add.png') no-repeat center center;}

.icon-note-delete { background:url('icons/note_delete.png') no-repeat center center;}

.icon-note-edit { background:url('icons/note_edit.png') no-repeat center center;}

.icon-note-error { background:url('icons/note_error.png') no-repeat center center;}

.icon-note-go { background:url('icons/note_go.png') no-repeat center center;}

.icon-ok { background:url('icons/ok.png') no-repeat center center;}

.icon-org32 { background:url('icons/org32.png') no-repeat center center;}

.icon-organization { background:url('icons/organization.png') no-repeat center center;}

.icon-outline { background:url('icons/outline.png') no-repeat center center;}

.icon-overlays { background:url('icons/overlays.png') no-repeat center center;}

.icon-package { background:url('icons/package.png') no-repeat center center;}

.icon-package-add { background:url('icons/package_add.png') no-repeat center center;}

.icon-package-delete { background:url('icons/package_delete.png') no-repeat center center;}

.icon-package-down { background:url('icons/package_down.png') no-repeat center center;}

.icon-package-go { background:url('icons/package_go.png') no-repeat center center;}

.icon-package-green { background:url('icons/package_green.png') no-repeat center center;}

.icon-package-in { background:url('icons/package_in.png') no-repeat center center;}

.icon-package-link { background:url('icons/package_link.png') no-repeat center center;}

.icon-package-se { background:url('icons/package_se.png') no-repeat center center;}

.icon-package-start { background:url('icons/package_start.png') no-repeat center center;}

.icon-package-stop { background:url('icons/package_stop.png') no-repeat center center;}

.icon-package-system { background:url('icons/package_system.png') no-repeat center center;}

.icon-package-white { background:url('icons/package_white.png') no-repeat center center;}

.icon-page { background:url('icons/page.png') no-repeat center center;}

.icon-page-add { background:url('icons/page_add.png') no-repeat center center;}

.icon-page-attach { background:url('icons/page_attach.png') no-repeat center center;}

.icon-page-back { background:url('icons/page_back.png') no-repeat center center;}

.icon-page-break { background:url('icons/page_break.png') no-repeat center center;}

.icon-page-break-insert { background:url('icons/page_break_insert.png') no-repeat center center;}

.icon-page-cancel { background:url('icons/page_cancel.png') no-repeat center center;}

.icon-page-code { background:url('icons/page_code.png') no-repeat center center;}

.icon-page-copy { background:url('icons/page_copy.png') no-repeat center center;}

.icon-page-delete { background:url('icons/page_delete.png') no-repeat center center;}

.icon-page-edit { background:url('icons/page_edit.png') no-repeat center center;}

.icon-page-error { background:url('icons/page_error.png') no-repeat center center;}

.icon-page-excel { background:url('icons/page_excel.png') no-repeat center center;}

.icon-page-find { background:url('icons/page_find.png') no-repeat center center;}

.icon-page-forward { background:url('icons/page_forward.png') no-repeat center center;}

.icon-page-gear { background:url('icons/page_gear.png') no-repeat center center;}

.icon-page-go { background:url('icons/page_go.png') no-repeat center center;}

.icon-page-green { background:url('icons/page_green.png') no-repeat center center;}

.icon-page-header-footer { background:url('icons/page_header_footer.png') no-repeat center center;}

.icon-page-key { background:url('icons/page_key.png') no-repeat center center;}

.icon-page-landscape { background:url('icons/page_landscape.png') no-repeat center center;}

.icon-page-landscape-shot { background:url('icons/page_landscape_shot.png') no-repeat center center;}

.icon-page-lightning { background:url('icons/page_lightning.png') no-repeat center center;}

.icon-page-link { background:url('icons/page_link.png') no-repeat center center;}

.icon-page-magnify { background:url('icons/page_magnify.png') no-repeat center center;}

.icon-page-paintbrush { background:url('icons/page_paintbrush.png') no-repeat center center;}

.icon-page-paste { background:url('icons/page_paste.png') no-repeat center center;}

.icon-page-portrait { background:url('icons/page_portrait.png') no-repeat center center;}

.icon-page-portrait-shot { background:url('icons/page_portrait_shot.png') no-repeat center center;}

.icon-page-red { background:url('icons/page_red.png') no-repeat center center;}

.icon-page-refresh { background:url('icons/page_refresh.png') no-repeat center center;}

.icon-page-save { background:url('icons/page_save.png') no-repeat center center;}

.icon-page-white { background:url('icons/page_white.png') no-repeat center center;}

.icon-page-white-acrobat { background:url('icons/page_white_acrobat.png') no-repeat center center;}

.icon-page-white-actionscript { background:url('icons/page_white_actionscript.png') no-repeat center center;}

.icon-page-white-add { background:url('icons/page_white_add.png') no-repeat center center;}

.icon-page-white-break { background:url('icons/page_white_break.png') no-repeat center center;}

.icon-page-white-c { background:url('icons/page_white_c.png') no-repeat center center;}

.icon-page-white-camera { background:url('icons/page_white_camera.png') no-repeat center center;}

.icon-page-white-cd { background:url('icons/page_white_cd.png') no-repeat center center;}

.icon-page-white-cdr { background:url('icons/page_white_cdr.png') no-repeat center center;}

.icon-page-white-code { background:url('icons/page_white_code.png') no-repeat center center;}

.icon-page-white-code-red { background:url('icons/page_white_code_red.png') no-repeat center center;}

.icon-page-white-coldfusion { background:url('icons/page_white_coldfusion.png') no-repeat center center;}

.icon-page-white-compressed { background:url('icons/page_white_compressed.png') no-repeat center center;}

.icon-page-white-connect { background:url('icons/page_white_connect.png') no-repeat center center;}

.icon-page-white-copy { background:url('icons/page_white_copy.png') no-repeat center center;}

.icon-page-white-cplusplus { background:url('icons/page_white_cplusplus.png') no-repeat center center;}

.icon-page-white-csharp { background:url('icons/page_white_csharp.png') no-repeat center center;}

.icon-page-white-cup { background:url('icons/page_white_cup.png') no-repeat center center;}

.icon-page-white-database { background:url('icons/page_white_database.png') no-repeat center center;}

.icon-page-white-database-yellow { background:url('icons/page_white_database_yellow.png') no-repeat center center;}

.icon-page-white-delete { background:url('icons/page_white_delete.png') no-repeat center center;}

.icon-page-white-dvd { background:url('icons/page_white_dvd.png') no-repeat center center;}

.icon-page-white-edit { background:url('icons/page_white_edit.png') no-repeat center center;}

.icon-page-white-error { background:url('icons/page_white_error.png') no-repeat center center;}

.icon-page-white-excel { background:url('icons/page_white_excel.png') no-repeat center center;}

.icon-page-white-find { background:url('icons/page_white_find.png') no-repeat center center;}

.icon-page-white-flash { background:url('icons/page_white_flash.png') no-repeat center center;}

.icon-page-white-font { background:url('icons/page_white_font.png') no-repeat center center;}

.icon-page-white-freehand { background:url('icons/page_white_freehand.png') no-repeat center center;}

.icon-page-white-gear { background:url('icons/page_white_gear.png') no-repeat center center;}

.icon-page-white-get { background:url('icons/page_white_get.png') no-repeat center center;}

.icon-page-white-go { background:url('icons/page_white_go.png') no-repeat center center;}

.icon-page-white-h { background:url('icons/page_white_h.png') no-repeat center center;}

.icon-page-white-horizontal { background:url('icons/page_white_horizontal.png') no-repeat center center;}

.icon-page-white-key { background:url('icons/page_white_key.png') no-repeat center center;}

.icon-page-white-lightning { background:url('icons/page_white_lightning.png') no-repeat center center;}

.icon-page-white-link { background:url('icons/page_white_link.png') no-repeat center center;}

.icon-page-white-magnify { background:url('icons/page_white_magnify.png') no-repeat center center;}

.icon-page-white-medal { background:url('icons/page_white_medal.png') no-repeat center center;}

.icon-page-white-office { background:url('icons/page_white_office.png') no-repeat center center;}

.icon-page-white-paint { background:url('icons/page_white_paint.png') no-repeat center center;}

.icon-page-white-paintbrush { background:url('icons/page_white_paintbrush.png') no-repeat center center;}

.icon-page-white-paste { background:url('icons/page_white_paste.png') no-repeat center center;}

.icon-page-white-paste-table { background:url('icons/page_white_paste_table.png') no-repeat center center;}

.icon-page-white-php { background:url('icons/page_white_php.png') no-repeat center center;}

.icon-page-white-picture { background:url('icons/page_white_picture.png') no-repeat center center;}

.icon-page-white-powerpoint { background:url('icons/page_white_powerpoint.png') no-repeat center center;}

.icon-page-white-put { background:url('icons/page_white_put.png') no-repeat center center;}

.icon-page-white-refresh { background:url('icons/page_white_refresh.png') no-repeat center center;}

.icon-page-white-ruby { background:url('icons/page_white_ruby.png') no-repeat center center;}

.icon-page-white-side-by-side { background:url('icons/page_white_side_by_side.png') no-repeat center center;}

.icon-page-white-stack { background:url('icons/page_white_stack.png') no-repeat center center;}

.icon-page-white-star { background:url('icons/page_white_star.png') no-repeat center center;}

.icon-page-white-swoosh { background:url('icons/page_white_swoosh.png') no-repeat center center;}

.icon-page-white-text { background:url('icons/page_white_text.png') no-repeat center center;}

.icon-page-white-text-width { background:url('icons/page_white_text_width.png') no-repeat center center;}

.icon-page-white-tux { background:url('icons/page_white_tux.png') no-repeat center center;}

.icon-page-white-vector { background:url('icons/page_white_vector.png') no-repeat center center;}

.icon-page-white-visualstudio { background:url('icons/page_white_visualstudio.png') no-repeat center center;}

.icon-page-white-width { background:url('icons/page_white_width.png') no-repeat center center;}

.icon-page-white-word { background:url('icons/page_white_word.png') no-repeat center center;}

.icon-page-white-world { background:url('icons/page_white_world.png') no-repeat center center;}

.icon-page-white-wrench { background:url('icons/page_white_wrench.png') no-repeat center center;}

.icon-page-white-zip { background:url('icons/page_white_zip.png') no-repeat center center;}

.icon-page-word { background:url('icons/page_word.png') no-repeat center center;}

.icon-page-world { background:url('icons/page_world.png') no-repeat center center;}

.icon-paint { background:url('icons/paint.png') no-repeat center center;}

.icon-paintbrush { background:url('icons/paintbrush.png') no-repeat center center;}

.icon-paintbrush-color { background:url('icons/paintbrush_color.png') no-repeat center center;}

.icon-paintcan { background:url('icons/paintcan.png') no-repeat center center;}

.icon-paintcan-red { background:url('icons/paintcan_red.png') no-repeat center center;}

.icon-paint-can-brush { background:url('icons/paint_can_brush.png') no-repeat center center;}

.icon-palette { background:url('icons/palette.png') no-repeat center center;}

.icon-paste-plain { background:url('icons/paste_plain.png') no-repeat center center;}

.icon-paste-word { background:url('icons/paste_word.png') no-repeat center center;}

.icon-pause-blue { background:url('icons/pause_blue.png') no-repeat center center;}

.icon-pause-green { background:url('icons/pause_green.png') no-repeat center center;}

.icon-pause-record { background:url('icons/pause_record.png') no-repeat center center;}

.icon-pencil { background:url('icons/pencil.png') no-repeat center center;}

.icon-pencil-add { background:url('icons/pencil_add.png') no-repeat center center;}

.icon-pencil-delete { background:url('icons/pencil_delete.png') no-repeat center center;}

.icon-pencil-go { background:url('icons/pencil_go.png') no-repeat center center;}

.icon-personal { background:url('icons/personal.png') no-repeat center center;}

.icon-phone { background:url('icons/phone.png') no-repeat center center;}

.icon-phone-add { background:url('icons/phone_add.png') no-repeat center center;}

.icon-phone-delete { background:url('icons/phone_delete.png') no-repeat center center;}

.icon-phone-edit { background:url('icons/phone_edit.png') no-repeat center center;}

.icon-phone-error { background:url('icons/phone_error.png') no-repeat center center;}

.icon-phone-go { background:url('icons/phone_go.png') no-repeat center center;}

.icon-phone-key { background:url('icons/phone_key.png') no-repeat center center;}

.icon-phone-link { background:url('icons/phone_link.png') no-repeat center center;}

.icon-phone-sound { background:url('icons/phone_sound.png') no-repeat center center;}

.icon-phone-start { background:url('icons/phone_start.png') no-repeat center center;}

.icon-phone-stop { background:url('icons/phone_stop.png') no-repeat center center;}

.icon-photo { background:url('icons/photo.png') no-repeat center center;}

.icon-photos { background:url('icons/photos.png') no-repeat center center;}

.icon-photo-add { background:url('icons/photo_add.png') no-repeat center center;}

.icon-photo-delete { background:url('icons/photo_delete.png') no-repeat center center;}

.icon-photo-edit { background:url('icons/photo_edit.png') no-repeat center center;}

.icon-photo-link { background:url('icons/photo_link.png') no-repeat center center;}

.icon-photo-paint { background:url('icons/photo_paint.png') no-repeat center center;}

.icon-picture { background:url('icons/picture.png') no-repeat center center;}

.icon-pictures { background:url('icons/pictures.png') no-repeat center center;}

.icon-pictures-thumbs { background:url('icons/pictures_thumbs.png') no-repeat center center;}

.icon-picture-add { background:url('icons/picture_add.png') no-repeat center center;}

.icon-picture-clipboard { background:url('icons/picture_clipboard.png') no-repeat center center;}

.icon-picture-delete { background:url('icons/picture_delete.png') no-repeat center center;}

.icon-picture-edit { background:url('icons/picture_edit.png') no-repeat center center;}

.icon-picture-empty { background:url('icons/picture_empty.png') no-repeat center center;}

.icon-picture-error { background:url('icons/picture_error.png') no-repeat center center;}

.icon-picture-go { background:url('icons/picture_go.png') no-repeat center center;}

.icon-picture-key { background:url('icons/picture_key.png') no-repeat center center;}

.icon-picture-link { background:url('icons/picture_link.png') no-repeat center center;}

.icon-picture-save { background:url('icons/picture_save.png') no-repeat center center;}

.icon-pilcrow { background:url('icons/pilcrow.png') no-repeat center center;}

.icon-pill { background:url('icons/pill.png') no-repeat center center;}

.icon-pill-add { background:url('icons/pill_add.png') no-repeat center center;}

.icon-pill-delete { background:url('icons/pill_delete.png') no-repeat center center;}

.icon-pill-error { background:url('icons/pill_error.png') no-repeat center center;}

.icon-pill-go { background:url('icons/pill_go.png') no-repeat center center;}

.icon-play-blue { background:url('icons/play_blue.png') no-repeat center center;}

.icon-play-green { background:url('icons/play_green.png') no-repeat center center;}

.icon-plugin { background:url('icons/plugin.png') no-repeat center center;}

.icon-plugin-add { background:url('icons/plugin_add.png') no-repeat center center;}

.icon-plugin-delete { background:url('icons/plugin_delete.png') no-repeat center center;}

.icon-plugin-disabled { background:url('icons/plugin_disabled.png') no-repeat center center;}

.icon-plugin-edit { background:url('icons/plugin_edit.png') no-repeat center center;}

.icon-plugin-error { background:url('icons/plugin_error.png') no-repeat center center;}

.icon-plugin-go { background:url('icons/plugin_go.png') no-repeat center center;}

.icon-plugin-key { background:url('icons/plugin_key.png') no-repeat center center;}

.icon-plugin-link { background:url('icons/plugin_link.png') no-repeat center center;}

.icon-previous-green { background:url('icons/previous-green.png') no-repeat center center;}

.icon-previous { background:url('icons/previous.png') no-repeat center center;}

.icon-printer { background:url('icons/printer.png') no-repeat center center;}

.icon-printer-add { background:url('icons/printer_add.png') no-repeat center center;}

.icon-printer-cancel { background:url('icons/printer_cancel.png') no-repeat center center;}

.icon-printer-color { background:url('icons/printer_color.png') no-repeat center center;}

.icon-printer-connect { background:url('icons/printer_connect.png') no-repeat center center;}

.icon-printer-delete { background:url('icons/printer_delete.png') no-repeat center center;}

.icon-printer-empty { background:url('icons/printer_empty.png') no-repeat center center;}

.icon-printer-error { background:url('icons/printer_error.png') no-repeat center center;}

.icon-printer-go { background:url('icons/printer_go.png') no-repeat center center;}

.icon-printer-key { background:url('icons/printer_key.png') no-repeat center center;}

.icon-printer-mono { background:url('icons/printer_mono.png') no-repeat center center;}

.icon-printer-start { background:url('icons/printer_start.png') no-repeat center center;}

.icon-printer-stop { background:url('icons/printer_stop.png') no-repeat center center;}

.icon-rainbow { background:url('icons/rainbow.png') no-repeat center center;}

.icon-rainbow-star { background:url('icons/rainbow_star.png') no-repeat center center;}

.icon-record-blue { background:url('icons/record_blue.png') no-repeat center center;}

.icon-record-green { background:url('icons/record_green.png') no-repeat center center;}

.icon-record-red { background:url('icons/record_red.png') no-repeat center center;}

.icon-refresh { background:url('icons/refresh.png') no-repeat center center;}

.icon-reload { background:url('icons/reload.png') no-repeat center center;}

.icon-report { background:url('icons/report.png') no-repeat center center;}

.icon-report-add { background:url('icons/report_add.png') no-repeat center center;}

.icon-report-delete { background:url('icons/report_delete.png') no-repeat center center;}

.icon-report-disk { background:url('icons/report_disk.png') no-repeat center center;}

.icon-report-edit { background:url('icons/report_edit.png') no-repeat center center;}

.icon-report-go { background:url('icons/report_go.png') no-repeat center center;}

.icon-report-key { background:url('icons/report_key.png') no-repeat center center;}

.icon-report-link { background:url('icons/report_link.png') no-repeat center center;}

.icon-report-magnify { background:url('icons/report_magnify.png') no-repeat center center;}

.icon-report-picture { background:url('icons/report_picture.png') no-repeat center center;}

.icon-report-start { background:url('icons/report_start.png') no-repeat center center;}

.icon-report-stop { background:url('icons/report_stop.png') no-repeat center center;}

.icon-report-user { background:url('icons/report_user.png') no-repeat center center;}

.icon-report-word { background:url('icons/report_word.png') no-repeat center center;}

.icon-resultset-first { background:url('icons/resultset_first.png') no-repeat center center;}

.icon-resultset-last { background:url('icons/resultset_last.png') no-repeat center center;}

.icon-resultset-next { background:url('icons/resultset_next.png') no-repeat center center;}

.icon-resultset-previous { background:url('icons/resultset_previous.png') no-repeat center center;}

.icon-reverse-blue { background:url('icons/reverse_blue.png') no-repeat center center;}

.icon-reverse-green { background:url('icons/reverse_green.png') no-repeat center center;}

.icon-rewind-blue { background:url('icons/rewind_blue.png') no-repeat center center;}

.icon-rewind-green { background:url('icons/rewind_green.png') no-repeat center center;}

.icon-rgb { background:url('icons/rgb.png') no-repeat center center;}

.icon-rosette { background:url('icons/rosette.png') no-repeat center center;}

.icon-rosette-blue { background:url('icons/rosette_blue.png') no-repeat center center;}

.icon-rss { background:url('icons/rss.png') no-repeat center center;}

.icon-rss-add { background:url('icons/rss_add.png') no-repeat center center;}

.icon-rss-delete { background:url('icons/rss_delete.png') no-repeat center center;}

.icon-rss-error { background:url('icons/rss_error.png') no-repeat center center;}

.icon-rss-go { background:url('icons/rss_go.png') no-repeat center center;}

.icon-rss-valid { background:url('icons/rss_valid.png') no-repeat center center;}

.icon-ruby { background:url('icons/ruby.png') no-repeat center center;}

.icon-ruby-add { background:url('icons/ruby_add.png') no-repeat center center;}

.icon-ruby-delete { background:url('icons/ruby_delete.png') no-repeat center center;}

.icon-ruby-gear { background:url('icons/ruby_gear.png') no-repeat center center;}

.icon-ruby-get { background:url('icons/ruby_get.png') no-repeat center center;}

.icon-ruby-go { background:url('icons/ruby_go.png') no-repeat center center;}

.icon-ruby-key { background:url('icons/ruby_key.png') no-repeat center center;}

.icon-ruby-link { background:url('icons/ruby_link.png') no-repeat center center;}

.icon-ruby-put { background:url('icons/ruby_put.png') no-repeat center center;}

.icon-script { background:url('icons/script.png') no-repeat center center;}

.icon-script-add { background:url('icons/script_add.png') no-repeat center center;}

.icon-script-code { background:url('icons/script_code.png') no-repeat center center;}

.icon-script-code-red { background:url('icons/script_code_red.png') no-repeat center center;}

.icon-script-delete { background:url('icons/script_delete.png') no-repeat center center;}

.icon-script-edit { background:url('icons/script_edit.png') no-repeat center center;}

.icon-script-error { background:url('icons/script_error.png') no-repeat center center;}

.icon-script-gear { background:url('icons/script_gear.png') no-repeat center center;}

.icon-script-go { background:url('icons/script_go.png') no-repeat center center;}

.icon-script-key { background:url('icons/script_key.png') no-repeat center center;}

.icon-script-lightning { background:url('icons/script_lightning.png') no-repeat center center;}

.icon-script-link { background:url('icons/script_link.png') no-repeat center center;}

.icon-script-palette { background:url('icons/script_palette.png') no-repeat center center;}

.icon-script-save { background:url('icons/script_save.png') no-repeat center center;}

.icon-script-start { background:url('icons/script_start.png') no-repeat center center;}

.icon-script-stop { background:url('icons/script_stop.png') no-repeat center center;}

.icon-search { background:url('icons/search.png') no-repeat center center;}

.icon-seasons { background:url('icons/seasons.png') no-repeat center center;}

.icon-section-collapsed { background:url('icons/section_collapsed.png') no-repeat center center;}

.icon-section-expanded { background:url('icons/section_expanded.png') no-repeat center center;}

.icon-server { background:url('icons/server.png') no-repeat center center;}

.icon-server-add { background:url('icons/server_add.png') no-repeat center center;}

.icon-server-chart { background:url('icons/server_chart.png') no-repeat center center;}

.icon-server-compressed { background:url('icons/server_compressed.png') no-repeat center center;}

.icon-server-connect { background:url('icons/server_connect.png') no-repeat center center;}

.icon-server-database { background:url('icons/server_database.png') no-repeat center center;}

.icon-server-delete { background:url('icons/server_delete.png') no-repeat center center;}

.icon-server-edit { background:url('icons/server_edit.png') no-repeat center center;}

.icon-server-error { background:url('icons/server_error.png') no-repeat center center;}

.icon-server-go { background:url('icons/server_go.png') no-repeat center center;}

.icon-server-key { background:url('icons/server_key.png') no-repeat center center;}

.icon-server-lightning { background:url('icons/server_lightning.png') no-repeat center center;}

.icon-server-link { background:url('icons/server_link.png') no-repeat center center;}

.icon-server-start { background:url('icons/server_start.png') no-repeat center center;}

.icon-server-stop { background:url('icons/server_stop.png') no-repeat center center;}

.icon-server-uncompressed { background:url('icons/server_uncompressed.png') no-repeat center center;}

.icon-server-wrench { background:url('icons/server_wrench.png') no-repeat center center;}

.icon-settings32 { background:url('icons/settings32.png') no-repeat center center;}

.icon-shading { background:url('icons/shading.png') no-repeat center center;}

.icon-shapes-many { background:url('icons/shapes_many.png') no-repeat center center;}

.icon-shapes-many-select { background:url('icons/shapes_many_select.png') no-repeat center center;}

.icon-shape-3d { background:url('icons/shape_3d.png') no-repeat center center;}

.icon-shape-align-bottom { background:url('icons/shape_align_bottom.png') no-repeat center center;}

.icon-shape-align-center { background:url('icons/shape_align_center.png') no-repeat center center;}

.icon-shape-align-left { background:url('icons/shape_align_left.png') no-repeat center center;}

.icon-shape-align-middle { background:url('icons/shape_align_middle.png') no-repeat center center;}

.icon-shape-align-right { background:url('icons/shape_align_right.png') no-repeat center center;}

.icon-shape-align-top { background:url('icons/shape_align_top.png') no-repeat center center;}

.icon-shape-flip-horizontal { background:url('icons/shape_flip_horizontal.png') no-repeat center center;}

.icon-shape-flip-vertical { background:url('icons/shape_flip_vertical.png') no-repeat center center;}

.icon-shape-group { background:url('icons/shape_group.png') no-repeat center center;}

.icon-shape-handles { background:url('icons/shape_handles.png') no-repeat center center;}

.icon-shape-move-back { background:url('icons/shape_move_back.png') no-repeat center center;}

.icon-shape-move-backwards { background:url('icons/shape_move_backwards.png') no-repeat center center;}

.icon-shape-move-forwards { background:url('icons/shape_move_forwards.png') no-repeat center center;}

.icon-shape-move-front { background:url('icons/shape_move_front.png') no-repeat center center;}

.icon-shape-rotate-anticlockwise { background:url('icons/shape_rotate_anticlockwise.png') no-repeat center center;}

.icon-shape-rotate-clockwise { background:url('icons/shape_rotate_clockwise.png') no-repeat center center;}

.icon-shape-shade-a { background:url('icons/shape_shade_a.png') no-repeat center center;}

.icon-shape-shade-b { background:url('icons/shape_shade_b.png') no-repeat center center;}

.icon-shape-shade-c { background:url('icons/shape_shade_c.png') no-repeat center center;}

.icon-shape-shadow { background:url('icons/shape_shadow.png') no-repeat center center;}

.icon-shape-shadow-toggle { background:url('icons/shape_shadow_toggle.png') no-repeat center center;}

.icon-shape-square { background:url('icons/shape_square.png') no-repeat center center;}

.icon-shape-square-add { background:url('icons/shape_square_add.png') no-repeat center center;}

.icon-shape-square-delete { background:url('icons/shape_square_delete.png') no-repeat center center;}

.icon-shape-square-edit { background:url('icons/shape_square_edit.png') no-repeat center center;}

.icon-shape-square-error { background:url('icons/shape_square_error.png') no-repeat center center;}

.icon-shape-square-go { background:url('icons/shape_square_go.png') no-repeat center center;}

.icon-shape-square-key { background:url('icons/shape_square_key.png') no-repeat center center;}

.icon-shape-square-link { background:url('icons/shape_square_link.png') no-repeat center center;}

.icon-shape-square-select { background:url('icons/shape_square_select.png') no-repeat center center;}

.icon-shape-ungroup { background:url('icons/shape_ungroup.png') no-repeat center center;}

.icon-share { background:url('icons/share.png') no-repeat center center;}

.icon-shield { background:url('icons/shield.png') no-repeat center center;}

.icon-shield-add { background:url('icons/shield_add.png') no-repeat center center;}

.icon-shield-delete { background:url('icons/shield_delete.png') no-repeat center center;}

.icon-shield-error { background:url('icons/shield_error.png') no-repeat center center;}

.icon-shield-go { background:url('icons/shield_go.png') no-repeat center center;}

.icon-shield-rainbow { background:url('icons/shield_rainbow.png') no-repeat center center;}

.icon-shield-silver { background:url('icons/shield_silver.png') no-repeat center center;}

.icon-shield-start { background:url('icons/shield_start.png') no-repeat center center;}

.icon-shield-stop { background:url('icons/shield_stop.png') no-repeat center center;}

.icon-sitemap { background:url('icons/sitemap.png') no-repeat center center;}

.icon-sitemap-color { background:url('icons/sitemap_color.png') no-repeat center center;}

.icon-smartphone { background:url('icons/smartphone.png') no-repeat center center;}

.icon-smartphone-add { background:url('icons/smartphone_add.png') no-repeat center center;}

.icon-smartphone-connect { background:url('icons/smartphone_connect.png') no-repeat center center;}

.icon-smartphone-delete { background:url('icons/smartphone_delete.png') no-repeat center center;}

.icon-smartphone-disk { background:url('icons/smartphone_disk.png') no-repeat center center;}

.icon-smartphone-edit { background:url('icons/smartphone_edit.png') no-repeat center center;}

.icon-smartphone-error { background:url('icons/smartphone_error.png') no-repeat center center;}

.icon-smartphone-go { background:url('icons/smartphone_go.png') no-repeat center center;}

.icon-smartphone-key { background:url('icons/smartphone_key.png') no-repeat center center;}

.icon-smartphone-wrench { background:url('icons/smartphone_wrench.png') no-repeat center center;}

.icon-sort-ascending { background:url('icons/sort_ascending.png') no-repeat center center;}

.icon-sort-descending { background:url('icons/sort_descending.png') no-repeat center center;}

.icon-sound { background:url('icons/sound.png') no-repeat center center;}

.icon-sound-add { background:url('icons/sound_add.png') no-repeat center center;}

.icon-sound-delete { background:url('icons/sound_delete.png') no-repeat center center;}

.icon-sound-high { background:url('icons/sound_high.png') no-repeat center center;}

.icon-sound-in { background:url('icons/sound_in.png') no-repeat center center;}

.icon-sound-low { background:url('icons/sound_low.png') no-repeat center center;}

.icon-sound-mute { background:url('icons/sound_mute.png') no-repeat center center;}

.icon-sound-none { background:url('icons/sound_none.png') no-repeat center center;}

.icon-sound-out { background:url('icons/sound_out.png') no-repeat center center;}

.icon-spellcheck { background:url('icons/spellcheck.png') no-repeat center center;}

.icon-sport-8ball { background:url('icons/sport_8ball.png') no-repeat center center;}

.icon-sport-basketball { background:url('icons/sport_basketball.png') no-repeat center center;}

.icon-sport-football { background:url('icons/sport_football.png') no-repeat center center;}

.icon-sport-golf { background:url('icons/sport_golf.png') no-repeat center center;}

.icon-sport-golf-practice { background:url('icons/sport_golf_practice.png') no-repeat center center;}

.icon-sport-raquet { background:url('icons/sport_raquet.png') no-repeat center center;}

.icon-sport-shuttlecock { background:url('icons/sport_shuttlecock.png') no-repeat center center;}

.icon-sport-soccer { background:url('icons/sport_soccer.png') no-repeat center center;}

.icon-sport-tennis { background:url('icons/sport_tennis.png') no-repeat center center;}

.icon-star { background:url('icons/star.png') no-repeat center center;}

.icon-star-bronze { background:url('icons/star_bronze.png') no-repeat center center;}

.icon-star-bronze-half-grey { background:url('icons/star_bronze_half_grey.png') no-repeat center center;}

.icon-star-gold { background:url('icons/star_gold.png') no-repeat center center;}

.icon-star-gold-half-grey { background:url('icons/star_gold_half_grey.png') no-repeat center center;}

.icon-star-gold-half-silver { background:url('icons/star_gold_half_silver.png') no-repeat center center;}

.icon-star-grey { background:url('icons/star_grey.png') no-repeat center center;}

.icon-star-half-grey { background:url('icons/star_half_grey.png') no-repeat center center;}

.icon-star-silver { background:url('icons/star_silver.png') no-repeat center center;}

.icon-status-away { background:url('icons/status_away.png') no-repeat center center;}

.icon-status-be-right-back { background:url('icons/status_be_right_back.png') no-repeat center center;}

.icon-status-busy { background:url('icons/status_busy.png') no-repeat center center;}

.icon-status-invisible { background:url('icons/status_invisible.png') no-repeat center center;}

.icon-status-offline { background:url('icons/status_offline.png') no-repeat center center;}

.icon-status-online { background:url('icons/status_online.png') no-repeat center center;}

.icon-stop { background:url('icons/stop.png') no-repeat center center;}

.icon-stop-blue { background:url('icons/stop_blue.png') no-repeat center center;}

.icon-stop-green { background:url('icons/stop_green.png') no-repeat center center;}

.icon-stop-red { background:url('icons/stop_red.png') no-repeat center center;}

.icon-style { background:url('icons/style.png') no-repeat center center;}

.icon-style-add { background:url('icons/style_add.png') no-repeat center center;}

.icon-style-delete { background:url('icons/style_delete.png') no-repeat center center;}

.icon-style-edit { background:url('icons/style_edit.png') no-repeat center center;}

.icon-style-go { background:url('icons/style_go.png') no-repeat center center;}

.icon-sum { background:url('icons/sum.png') no-repeat center center;}

.icon-sys { background:url('icons/sys.png') no-repeat center center;}

.icon-tab { background:url('icons/tab.png') no-repeat center center;}

.icon-table { background:url('icons/table.png') no-repeat center center;}

.icon-table-add { background:url('icons/table_add.png') no-repeat center center;}

.icon-table-cell { background:url('icons/table_cell.png') no-repeat center center;}

.icon-table-column { background:url('icons/table_column.png') no-repeat center center;}

.icon-table-column-add { background:url('icons/table_column_add.png') no-repeat center center;}

.icon-table-column-delete { background:url('icons/table_column_delete.png') no-repeat center center;}

.icon-table-connect { background:url('icons/table_connect.png') no-repeat center center;}

.icon-table-delete { background:url('icons/table_delete.png') no-repeat center center;}

.icon-table-edit { background:url('icons/table_edit.png') no-repeat center center;}

.icon-table-error { background:url('icons/table_error.png') no-repeat center center;}

.icon-table-gear { background:url('icons/table_gear.png') no-repeat center center;}

.icon-table-go { background:url('icons/table_go.png') no-repeat center center;}

.icon-table-key { background:url('icons/table_key.png') no-repeat center center;}

.icon-table-lightning { background:url('icons/table_lightning.png') no-repeat center center;}

.icon-table-link { background:url('icons/table_link.png') no-repeat center center;}

.icon-table-multiple { background:url('icons/table_multiple.png') no-repeat center center;}

.icon-table-refresh { background:url('icons/table_refresh.png') no-repeat center center;}

.icon-table-relationship { background:url('icons/table_relationship.png') no-repeat center center;}

.icon-table-row { background:url('icons/table_row.png') no-repeat center center;}

.icon-table-row-delete { background:url('icons/table_row_delete.png') no-repeat center center;}

.icon-table-row-insert { background:url('icons/table_row_insert.png') no-repeat center center;}

.icon-table-save { background:url('icons/table_save.png') no-repeat center center;}

.icon-table-sort { background:url('icons/table_sort.png') no-repeat center center;}

.icon-tab-add { background:url('icons/tab_add.png') no-repeat center center;}

.icon-tab-blue { background:url('icons/tab_blue.png') no-repeat center center;}

.icon-tab-delete { background:url('icons/tab_delete.png') no-repeat center center;}

.icon-tab-edit { background:url('icons/tab_edit.png') no-repeat center center;}

.icon-tab-go { background:url('icons/tab_go.png') no-repeat center center;}


.icon-tab-green { background:url('icons/tab_green.png') no-repeat center center;}

.icon-tab-red { background:url('icons/tab_red.png') no-repeat center center;}

.icon-tag { background:url('icons/tag.png') no-repeat center center;}

.icon-tags-grey { background:url('icons/tags_grey.png') no-repeat center center;}

.icon-tags-red { background:url('icons/tags_red.png') no-repeat center center;}

.icon-tag-blue { background:url('icons/tag_blue.png') no-repeat center center;}

.icon-tag-blue-add { background:url('icons/tag_blue_add.png') no-repeat center center;}

.icon-tag-blue-delete { background:url('icons/tag_blue_delete.png') no-repeat center center;}

.icon-tag-blue-edit { background:url('icons/tag_blue_edit.png') no-repeat center center;}

.icon-tag-green { background:url('icons/tag_green.png') no-repeat center center;}

.icon-tag-orange { background:url('icons/tag_orange.png') no-repeat center center;}

.icon-tag-pink { background:url('icons/tag_pink.png') no-repeat center center;}

.icon-tag-purple { background:url('icons/tag_purple.png') no-repeat center center;}

.icon-tag-red { background:url('icons/tag_red.png') no-repeat center center;}

.icon-tag-yellow { background:url('icons/tag_yellow.png') no-repeat center center;}

.icon-telephone { background:url('icons/telephone.png') no-repeat center center;}

.icon-telephone-add { background:url('icons/telephone_add.png') no-repeat center center;}

.icon-telephone-delete { background:url('icons/telephone_delete.png') no-repeat center center;}

.icon-telephone-edit { background:url('icons/telephone_edit.png') no-repeat center center;}

.icon-telephone-error { background:url('icons/telephone_error.png') no-repeat center center;}

.icon-telephone-go { background:url('icons/telephone_go.png') no-repeat center center;}

.icon-telephone-key { background:url('icons/telephone_key.png') no-repeat center center;}

.icon-telephone-link { background:url('icons/telephone_link.png') no-repeat center center;}

.icon-telephone-red { background:url('icons/telephone_red.png') no-repeat center center;}

.icon-television { background:url('icons/television.png') no-repeat center center;}

.icon-television-add { background:url('icons/television_add.png') no-repeat center center;}

.icon-television-delete { background:url('icons/television_delete.png') no-repeat center center;}

.icon-television-in { background:url('icons/television_in.png') no-repeat center center;}

.icon-television-off { background:url('icons/television_off.png') no-repeat center center;}

.icon-television-out { background:url('icons/television_out.png') no-repeat center center;}

.icon-television-star { background:url('icons/television_star.png') no-repeat center center;}

.icon-textfield { background:url('icons/textfield.png') no-repeat center center;}

.icon-textfield-add { background:url('icons/textfield_add.png') no-repeat center center;}

.icon-textfield-delete { background:url('icons/textfield_delete.png') no-repeat center center;}

.icon-textfield-key { background:url('icons/textfield_key.png') no-repeat center center;}

.icon-textfield-rename { background:url('icons/textfield_rename.png') no-repeat center center;}

.icon-text-ab { background:url('icons/text_ab.png') no-repeat center center;}

.icon-text-align-center { background:url('icons/text_align_center.png') no-repeat center center;}

.icon-text-align-justify { background:url('icons/text_align_justify.png') no-repeat center center;}

.icon-text-align-left { background:url('icons/text_align_left.png') no-repeat center center;}

.icon-text-align-right { background:url('icons/text_align_right.png') no-repeat center center;}

.icon-text-allcaps { background:url('icons/text_allcaps.png') no-repeat center center;}

.icon-text-bold { background:url('icons/text_bold.png') no-repeat center center;}

.icon-text-columns { background:url('icons/text_columns.png') no-repeat center center;}

.icon-text-complete { background:url('icons/text_complete.png') no-repeat center center;}

.icon-text-direction { background:url('icons/text_direction.png') no-repeat center center;}

.icon-text-double-underline { background:url('icons/text_double_underline.png') no-repeat center center;}

.icon-text-dropcaps { background:url('icons/text_dropcaps.png') no-repeat center center;}

.icon-text-fit { background:url('icons/text_fit.png') no-repeat center center;}

.icon-text-flip { background:url('icons/text_flip.png') no-repeat center center;}

.icon-text-font-default { background:url('icons/text_font_default.png') no-repeat center center;}

.icon-text-heading-1 { background:url('icons/text_heading_1.png') no-repeat center center;}

.icon-text-heading-2 { background:url('icons/text_heading_2.png') no-repeat center center;}

.icon-text-heading-3 { background:url('icons/text_heading_3.png') no-repeat center center;}

.icon-text-heading-4 { background:url('icons/text_heading_4.png') no-repeat center center;}

.icon-text-heading-5 { background:url('icons/text_heading_5.png') no-repeat center center;}

.icon-text-heading-6 { background:url('icons/text_heading_6.png') no-repeat center center;}

.icon-text-horizontalrule { background:url('icons/text_horizontalrule.png') no-repeat center center;}

.icon-text-indent { background:url('icons/text_indent.png') no-repeat center center;}

.icon-text-indent-remove { background:url('icons/text_indent_remove.png') no-repeat center center;}

.icon-text-inverse { background:url('icons/text_inverse.png') no-repeat center center;}

.icon-text-italic { background:url('icons/text_italic.png') no-repeat center center;}

.icon-text-kerning { background:url('icons/text_kerning.png') no-repeat center center;}

.icon-text-left-to-right { background:url('icons/text_left_to_right.png') no-repeat center center;}

.icon-text-letterspacing { background:url('icons/text_letterspacing.png') no-repeat center center;}

.icon-text-letter-omega { background:url('icons/text_letter_omega.png') no-repeat center center;}

.icon-text-linespacing { background:url('icons/text_linespacing.png') no-repeat center center;}

.icon-text-list-bullets { background:url('icons/text_list_bullets.png') no-repeat center center;}

.icon-text-list-numbers { background:url('icons/text_list_numbers.png') no-repeat center center;}

.icon-text-lowercase { background:url('icons/text_lowercase.png') no-repeat center center;}

.icon-text-lowercase-a { background:url('icons/text_lowercase_a.png') no-repeat center center;}

.icon-text-mirror { background:url('icons/text_mirror.png') no-repeat center center;}

.icon-text-padding-bottom { background:url('icons/text_padding_bottom.png') no-repeat center center;}

.icon-text-padding-left { background:url('icons/text_padding_left.png') no-repeat center center;}

.icon-text-padding-right { background:url('icons/text_padding_right.png') no-repeat center center;}

.icon-text-padding-top { background:url('icons/text_padding_top.png') no-repeat center center;}

.icon-text-replace { background:url('icons/text_replace.png') no-repeat center center;}

.icon-text-right-to-left { background:url('icons/text_right_to_left.png') no-repeat center center;}

.icon-text-rotate-0 { background:url('icons/text_rotate_0.png') no-repeat center center;}

.icon-text-rotate-180 { background:url('icons/text_rotate_180.png') no-repeat center center;}

.icon-text-rotate-270 { background:url('icons/text_rotate_270.png') no-repeat center center;}

.icon-text-rotate-90 { background:url('icons/text_rotate_90.png') no-repeat center center;}

.icon-text-ruler { background:url('icons/text_ruler.png') no-repeat center center;}

.icon-text-shading { background:url('icons/text_shading.png') no-repeat center center;}

.icon-text-signature { background:url('icons/text_signature.png') no-repeat center center;}

.icon-text-smallcaps { background:url('icons/text_smallcaps.png') no-repeat center center;}

.icon-text-spelling { background:url('icons/text_spelling.png') no-repeat center center;}

.icon-text-strikethrough { background:url('icons/text_strikethrough.png') no-repeat center center;}

.icon-text-subscript { background:url('icons/text_subscript.png') no-repeat center center;}

.icon-text-superscript { background:url('icons/text_superscript.png') no-repeat center center;}

.icon-text-tab { background:url('icons/text_tab.png') no-repeat center center;}

.icon-text-underline { background:url('icons/text_underline.png') no-repeat center center;}

.icon-text-uppercase { background:url('icons/text_uppercase.png') no-repeat center center;}

.icon-theme { background:url('icons/theme.png') no-repeat center center;}

.icon-thumb-down { background:url('icons/thumb_down.png') no-repeat center center;}

.icon-thumb-up { background:url('icons/thumb_up.png') no-repeat center center;}

.icon-tick { background:url('icons/tick.png') no-repeat center center;}

.icon-tick-shield { background:url('icons/tick_shield.png') no-repeat center center;}

.icon-time { background:url('icons/time.png') no-repeat center center;}

.icon-timeline-marker { background:url('icons/timeline_marker.png') no-repeat center center;}

.icon-time-add { background:url('icons/time_add.png') no-repeat center center;}

.icon-time-delete { background:url('icons/time_delete.png') no-repeat center center;}

.icon-time-go { background:url('icons/time_go.png') no-repeat center center;}

.icon-time-green { background:url('icons/time_green.png') no-repeat center center;}

.icon-time-red { background:url('icons/time_red.png') no-repeat center center;}

.icon-transmit { background:url('icons/transmit.png') no-repeat center center;}

.icon-transmit-add { background:url('icons/transmit_add.png') no-repeat center center;}

.icon-transmit-blue { background:url('icons/transmit_blue.png') no-repeat center center;}

.icon-transmit-delete { background:url('icons/transmit_delete.png') no-repeat center center;}

.icon-transmit-edit { background:url('icons/transmit_edit.png') no-repeat center center;}

.icon-transmit-error { background:url('icons/transmit_error.png') no-repeat center center;}

.icon-transmit-go { background:url('icons/transmit_go.png') no-repeat center center;}

.icon-transmit-red { background:url('icons/transmit_red.png') no-repeat center center;}

.icon-tree16 { background:url('icons/tree16.png') no-repeat center center;}

.icon-tree32 { background:url('icons/tree32.png') no-repeat center center;}

.icon-tux { background:url('icons/tux.png') no-repeat center center;}

.icon-undo { background:url('icons/undo.png') no-repeat center center;}

.icon-upload { background:url('icons/upload.png') no-repeat center center;}

.icon-user { background:url('icons/user.png') no-repeat center center;}

.icon-users { background:url('icons/users.png') no-repeat center center;}

.icon-user-accept16 { background:url('icons/user_accept16.png') no-repeat center center;}

.icon-user-accept32 { background:url('icons/user_accept32.png') no-repeat center center;}

.icon-user-add { background:url('icons/user_add.png') no-repeat center center;}

.icon-user-alert { background:url('icons/user_alert.png') no-repeat center center;}

.icon-user-b { background:url('icons/user_b.png') no-repeat center center;}

.icon-user-brown { background:url('icons/user_brown.png') no-repeat center center;}

.icon-user-business-boss { background:url('icons/user_business_boss.png') no-repeat center center;}

.icon-user-comment { background:url('icons/user_comment.png') no-repeat center center;}

.icon-user-cross { background:url('icons/user_cross.png') no-repeat center center;}

.icon-user-delete { background:url('icons/user_delete.png') no-repeat center center;}

.icon-user-earth { background:url('icons/user_earth.png') no-repeat center center;}

.icon-user-edit { background:url('icons/user_edit.png') no-repeat center center;}

.icon-user-edit32 { background:url('icons/user_edit32.png') no-repeat center center;}

.icon-user-female { background:url('icons/user_female.png') no-repeat center center;}

.icon-user-go { background:url('icons/user_go.png') no-repeat center center;}

.icon-user-gray { background:url('icons/user_gray.png') no-repeat center center;}

.icon-user-gray-cool { background:url('icons/user_gray_cool.png') no-repeat center center;}

.icon-user-green { background:url('icons/user_green.png') no-repeat center center;}

.icon-user-group { background:url('icons/user_group.png') no-repeat center center;}

.icon-user-home { background:url('icons/user_home.png') no-repeat center center;}

.icon-user-key { background:url('icons/user_key.png') no-repeat center center;}

.icon-user-magnify { background:url('icons/user_magnify.png') no-repeat center center;}

.icon-user-mature { background:url('icons/user_mature.png') no-repeat center center;}

.icon-user-orange { background:url('icons/user_orange.png') no-repeat center center;}

.icon-user-red { background:url('icons/user_red.png') no-repeat center center;}

.icon-user-reject16 { background:url('icons/user_reject16.png') no-repeat center center;}

.icon-user-reject32 { background:url('icons/user_reject32.png') no-repeat center center;}

.icon-user-star { background:url('icons/user_star.png') no-repeat center center;}

.icon-user-suit { background:url('icons/user_suit.png') no-repeat center center;}

.icon-user-suit-black { background:url('icons/user_suit_black.png') no-repeat center center;}

.icon-user-tick { background:url('icons/user_tick.png') no-repeat center center;}

.icon-vcard { background:url('icons/vcard.png') no-repeat center center;}

.icon-vcard-add { background:url('icons/vcard_add.png') no-repeat center center;}

.icon-vcard-delete { background:url('icons/vcard_delete.png') no-repeat center center;}

.icon-vcard-edit { background:url('icons/vcard_edit.png') no-repeat center center;}

.icon-vcard-key { background:url('icons/vcard_key.png') no-repeat center center;}

.icon-vector { background:url('icons/vector.png') no-repeat center center;}

.icon-vector-add { background:url('icons/vector_add.png') no-repeat center center;}

.icon-vector-delete { background:url('icons/vector_delete.png') no-repeat center center;}

.icon-vector-key { background:url('icons/vector_key.png') no-repeat center center;}

.icon-wand { background:url('icons/wand.png') no-repeat center center;}

.icon-weather-cloud { background:url('icons/weather_cloud.png') no-repeat center center;}

.icon-weather-clouds { background:url('icons/weather_clouds.png') no-repeat center center;}

.icon-weather-cloudy { background:url('icons/weather_cloudy.png') no-repeat center center;}

.icon-weather-cloudy-rain { background:url('icons/weather_cloudy_rain.png') no-repeat center center;}

.icon-weather-lightning { background:url('icons/weather_lightning.png') no-repeat center center;}

.icon-weather-rain { background:url('icons/weather_rain.png') no-repeat center center;}

.icon-weather-snow { background:url('icons/weather_snow.png') no-repeat center center;}

.icon-weather-sun { background:url('icons/weather_sun.png') no-repeat center center;}

.icon-webcam { background:url('icons/webcam.png') no-repeat center center;}

.icon-webcam-add { background:url('icons/webcam_add.png') no-repeat center center;}

.icon-webcam-connect { background:url('icons/webcam_connect.png') no-repeat center center;}

.icon-webcam-delete { background:url('icons/webcam_delete.png') no-repeat center center;}

.icon-webcam-error { background:url('icons/webcam_error.png') no-repeat center center;}

.icon-webcam-start { background:url('icons/webcam_start.png') no-repeat center center;}

.icon-webcam-stop { background:url('icons/webcam_stop.png') no-repeat center center;}

.icon-world { background:url('icons/world.png') no-repeat center center;}

.icon-world-add { background:url('icons/world_add.png') no-repeat center center;}

.icon-world-connect { background:url('icons/world_connect.png') no-repeat center center;}

.icon-world-dawn { background:url('icons/world_dawn.png') no-repeat center center;}

.icon-world-delete { background:url('icons/world_delete.png') no-repeat center center;}

.icon-world-edit { background:url('icons/world_edit.png') no-repeat center center;}

.icon-world-go { background:url('icons/world_go.png') no-repeat center center;}

.icon-world-key { background:url('icons/world_key.png') no-repeat center center;}

.icon-world-link { background:url('icons/world_link.png') no-repeat center center;}

.icon-world-night { background:url('icons/world_night.png') no-repeat center center;}

.icon-world-orbit { background:url('icons/world_orbit.png') no-repeat center center;}

.icon-wrench { background:url('icons/wrench.png') no-repeat center center;}

.icon-wrench-orange { background:url('icons/wrench_orange.png') no-repeat center center;}

.icon-writing32 { background:url('icons/writing32.png') no-repeat center center;}

.icon-xhtml { background:url('icons/xhtml.png') no-repeat center center;}

.icon-xhtml-add { background:url('icons/xhtml_add.png') no-repeat center center;}

.icon-xhtml-delete { background:url('icons/xhtml_delete.png') no-repeat center center;}

.icon-xhtml-error { background:url('icons/xhtml_error.png') no-repeat center center;}

.icon-xhtml-go { background:url('icons/xhtml_go.png') no-repeat center center;}

.icon-xhtml-valid { background:url('icons/xhtml_valid.png') no-repeat center center;}

.icon-zoom { background:url('icons/zoom.png') no-repeat center center;}

.icon-zoom-in { background:url('icons/zoom_in.png') no-repeat center center;}

.icon-zoom-out { background:url('icons/zoom_out.png') no-repeat center center;}