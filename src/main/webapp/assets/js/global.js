$.extend($.fn.datagrid.defaults.editors, {
	searchbox: {
        init: function (t, a) {
        		// input readonly="readonly"
            var i = $('<span class="textbox searchbox"><span class="textbox-addon textbox-addon-right" style="right: 0px; top: 0px;"><a href="javascript:;" class="textbox-icon searchbox-button" icon-index="0" tabindex="-1" style="width: 18px; height: 22px;"></a></span><input id="_easyui_textbox_input6" class="textbox-text textbox-prompt" autocomplete="off" tabindex="" placeholder="" style="margin: 0px 18px 0px 0px; padding-top: 0px; padding-bottom: 0px; height: 22px; line-height: 22px;" type="text"></span>').appendTo(t);
            if (a != undefined && a != null && a._medg) {
                i.keydown(function (t) {
                    e(t, a._medg)
                })
            }
            return i;
        },
        destroy: function (e) {
            // 销毁
             $(e).remove();
        },
        getValue: function (e) {
            // datagrid 结束编辑模式，通过该方法返回编辑最终值
            return $(e).children("input").val();
        },
        setValue: function (e, t) {
            // datagrid 进入编辑器模式，通过该方法为编辑赋值
        		$(e).val(t);
            setTimeout(function () {
                e.focus()
            },
            100)
        },
        resize: function (e, t) {
            // 列宽改变后调整编辑器宽度
            $(e[0]).width("100%");
        }
    }

});

$.fn.serializeObject = function() {
	var o = {};
	var a = this.serializeArray();
	$.each(a, function() {
		if (o[this.name] !== undefined) {
			if (!o[this.name].push) {
				o[this.name] = [ o[this.name] ];
			}
			o[this.name].push(this.value || '');
		} else {
			o[this.name] = this.value || '';
		}
	});
	return o;
}

var EasyUIDataGrid = {
	// 设置列的值(适用于所有列)
	setFieldValue : function(fieldName, value, index, grid) {
		var row = grid.datagrid('getRows')[index];
		if (row != null) {
			var editor = grid.datagrid('getEditor', {
				index : index,
				field : fieldName
			});
			if (editor != null) {
				if (editor.type == 'searchbox') {
					$(editor.target).children("input").val(value);
				} else {
					this.setValueToEditor(editor, value);
				}
			} else {
				var view = $('.datagrid-view');
				for (var i = 0; i < view.length; i++) {
					if ($(view[i]).children(grid.selector).length > 0) {
						var view = $(view[i]).children('.datagrid-view2');
						var td = $(view).find(
								'.datagrid-body td[field="' + fieldName + '"]')[index]
						var div = $(td).find('div')[0];
						$(div).text(value);
					}
				}

				row[fieldName] = value;
			}
			grid.datagrid('clearSelections');
		}
	},

	// 设置datagrid的编辑器的值
	setValueToEditor : function(editor, value) {
		switch (editor.type) {
		case "combobox":
			editor.target.combobox("setValue", value);
			break;
		case "combotree":
			editor.target.combotree("setValue", value);
			break;
		case "textbox":
			editor.target.textbox("setValue", value);
			break;
		case "numberbox":
			editor.target.numberbox("setValue", value);
			break;
		case "datebox":
			editor.target.datebox("setValue", value);
			break;
		case "datetimebox":
			editor.target.datebox("setValue", value);
			break;
		default:
			editor.html = value;
			break;
		}
	}
} 

function mergeJsonObject(jsonObjects) {
	var resultJsonObject = {};
	
	for (var i=0;i<jsonObjects.length;i++) {
		for (var attr in jsonObjects[i]) {
			resultJsonObject[attr] = jsonObjects[i][attr];
		}
	}
	
	return resultJsonObject;
}

function serializeObjectByForms(forms) {
	var jsonObjects = [];
	for (var i=0;i<forms.length;i++) {
		jsonObjects.push($(forms[i]).serializeObject());
	}
	
	return mergeJsonObject(jsonObjects);
}

function reloadFooter(datagrid, field) {
	var rows = $(datagrid).datagrid('getFooterRows');
	rows[0][field] = compute(datagrid, field);
	$('.easyui-datagrid').datagrid('reloadFooter');
}
        		
function compute(datagrid, field) {
	var rows = $(datagrid).datagrid('getRows') //获取当前的数据行
	var total = 0;
	for (var i = 0; i < rows.length; i++) {
    		total += parseFloat(rows[i][field]);
    }
	return total.toFixed(2);
}

function isEmptyObj(obj) {
	for (var i in obj) {
		if (hasText(obj[i])) {
			return true;
		}
    }
	
	return false;
}

function hasText(text) {
	if (text != null && text != undefined && text != "") {
		return true;
	} else {
		return false;
	}
}

function getAjax(url, params, dataType, callfunc) {
    $.ajax({
        url: url,
        type: "get",
        data: params,
        async: false,
        cache: false,
        dataType: dataType,
        success: function (data) {
        		if (!data.success) {
        			iAlert(data.msg, "error");
        		} else {
        			callfunc(data);
        		}
        },
        error: function (data) {
        		iAlert("服务器内容错误");
        }
    });
}

function postAjax(url, params, dataType, callfunc) {
    $.ajax({
        url: url,
        type: "post",
        data: params,
        async: false,
        cache: false,
        contentType: "application/json; charset=utf-8",
        dataType: dataType,
        success: function (data) {
        		if (!data.success) {
        			iAlert(data.msg, "error");
        		} else {
        			callfunc(data);
        		}
        },
        error: function (data) {
        		iAlert("服务器内容错误", "error");
        }
    });
}

// 提示框插件，如需了解更多，参照http://ifightcrime.github.io/bootstrap-growl/
function iAlert(info, type, delay, width, hv) {
    $(".bootstrap-growl.alert").remove();
    if ($(".bootstrap-growl.alert").length == 0) {
        var iType = type;
        var iDelay = delay;

        if (!type) {
            iType = "info";
        }

        if (!delay) {
            iDelay = "1500"
        }
        if (!width) {
            width = "auto";
        }
        if (!hv) {
            hv = $(window).height() / 2;
        }
        $.bootstrapGrowl(info, {
            type: iType,
            align: 'center',
            width: width,
            delay: iDelay,
            offset: {from: 'top', amount: hv}
        });

    }
}

function myformatter(date){
	var y = date.getFullYear();
	var m = date.getMonth()+1;
	var d = date.getDate();
	return y+'-'+(m<10?('0'+m):m)+'-'+(d<10?('0'+d):d);
}

function myparser(s) {
	if (!s)
		return new Date();
	var ss = (s.split('-'));
	var y = parseInt(ss[0], 10);
	var m = parseInt(ss[1], 10);
	var d = parseInt(ss[2], 10);
	if (!isNaN(y) && !isNaN(m) && !isNaN(d)) {
		return new Date(y, m - 1, d);
	} else {
		return new Date();
	}
}

function refreshTab(currentTab) {
	var url = $(currentTab.panel('options')).attr('href');
	$('#tabs').tabs('update', {
		tab : currentTab,
		options : {
			href : url
		}
	});
	currentTab.panel('refresh');
}

/** 数字金额大写转换(可以处理整数,小数,负数) */
function smalltoBIG(n) {
	var fraction = [ '角', '分' ];
	var digit = [ '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖' ];
	var unit = [ [ '元', '万', '亿' ], [ '', '拾', '佰', '仟' ] ];
	var head = n < 0 ? '欠' : '';
	n = Math.abs(n);

	var s = '';

	for (var i = 0; i < fraction.length; i++) {
		s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i])
				.replace(/零./, '');
	}
	s = s || '整';
	n = Math.floor(n);

	for (var i = 0; i < unit[0].length && n > 0; i++) {
		var p = '';
		for (var j = 0; j < unit[1].length && n > 0; j++) {
			p = digit[n % 10] + unit[1][j] + p;
			n = Math.floor(n / 10);
		}
		s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
	}
	return head
			+ s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/,
					'零元整');
}

// 资源树选择回填
function onClick(event, treeId, treeNode, clickFlag) {
	$("#rtCurrTreeNodeName").val(treeNode.name);
}	