var qryPayWay = {
	query : function() {
		
		var params = {
			"condition" : $("#payWay-searchText").val()
		}
		
		getAjax("base/resourceTree/queryPayWay", params, "json", function(res) {
			$("#payWayTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#payWayTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'payWay'
			});
			$(indexEd.target).children("input").val(row.payWayName);
			$("#payWayDialog").dialog("close");
		}
	}
}

$(function() {
	$("#payWayTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		rownumbers : true,
		url : "base/resourceTree/queryPayWay",
		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});
});