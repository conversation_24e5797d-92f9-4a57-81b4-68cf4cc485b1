var qryIndex = {
	query : function(current, size) {
		if (!hasText(current)) {
			current = $("#indexTab").datagrid("getPager").data("pagination").options.pageNumber;
		}

		if (!hasText(size)) {
			size = $("#indexTab").datagrid("getPager").data("pagination").options.pageSize;
		}

		var params = {
			"condition" : $("#paraVal").val(),
			"current" : current,
			"size" : size
		}

		getAjax("indexController/qryIndex", params, "json", function(res) {
			$("#indexTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#indexTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			EasyUIDataGrid.setFieldValue("index", row.indexID, rowIndex,
					$('#paydetail-dg'));
			$("#indexDialog").dialog("close");
		}
	}
}

$(function() {
	$("#indexTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		pagination : true,
		rownumbers : true,
		url : "indexController/qryIndex",
		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});

	var pager = $("#indexTab").datagrid("getPager");
	if (pager) {
		$(pager).pagination({
			onSelectPage : function(pageNumber, pageSize) {
				qryIndex.query(pageNumber, pageSize);
			}
		});
	}
});