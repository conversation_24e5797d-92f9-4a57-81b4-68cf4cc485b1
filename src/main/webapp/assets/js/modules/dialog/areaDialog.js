var qryArea = {
	query : function() {
		var rowIndex = $("#rowIndex").val();
		var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
			index : rowIndex,
			field : 'province'
		});
		// 获取当前行，省份名称
		var provinceName = $(indexEd.target).children("input").val();
		
		var params = {
			"condition" : $("#area-searchText").val(),
			"provinceName" : provinceName
		}
		
		getAjax("base/resourceTree/queryArea", params, "json", function(res) {
			$("#areaTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#areaTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'area'
			});
			$(indexEd.target).children("input").val(row.areaName);
			$("#areaDialog").dialog("close");
		}
	}
}

$(function() {
	$("#areaTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		rownumbers : true,
//		url : "base/resourceTree/queryArea",
//		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});
});