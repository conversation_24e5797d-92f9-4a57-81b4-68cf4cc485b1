var qryBankInfo = {
	query : function() {
		var rowIndex = $("#rowIndex").val();
		var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
			index : rowIndex,
			field : 'bankInstitution'
		});
		// 获取当前行，银行机构Code
		var bankInstitutionCode = $(indexEd.target).children("input").val();
		
		var params = {
			"condition" : $("#bankInfo-searchText").val(),
			"bankInstitutionCode" : bankInstitutionCode
		}
		
		getAjax("base/resourceTree/queryBankInfo", params, "json", function(res) {
			$("#bankInfoTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#bankInfoTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'bankInfo'
			});
			$(indexEd.target).children("input").val(row.bankName);
			$("#bankInfoDialog").dialog("close");
		}
	}
}

$(function() {
	$("#bankInfoTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		rownumbers : true,
//		url : "base/resourceTree/queryBankInfo",
//		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});
});