var qryPayee = {
	query : function() {
		var params = {
			"condition" : $("#payee-searchText").val()
		}
		
		getAjax("base/resourceTree/queryPayee", params, "json", function(res) {
			$("#payeeTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#payeeTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			// 收款人
			var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'payee'
			});
			$(indexEd.target).children("input").val(row.payeeName);
			
			// 开户银行
			indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'depositBank'
			});
			$(indexEd.target).textbox('setValue', row.depositBank);
			
			// 银行帐号
			indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'bankAccount'
			});
			$(indexEd.target).textbox('setValue', row.bankAccount);
			$("#payeeDialog").dialog("close");
		}
	}
}

$(function() {
	$("#payeeTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		rownumbers : true,
		url : "base/resourceTree/queryPayee",
		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});
});