var qryBankInstitution = {
	query : function() {
		var params = {
			"condition" : $("#bankInstitution-searchText").val()
		}
		
		getAjax("base/resourceTree/queryBankInstitution", params, "json", function(res) {
			$("#bankInstitutionTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#bankInstitutionTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'bankInstitution'
			});
			$(indexEd.target).children("input").val(row.bankInstitutionCode);
			$("#bankInstitutionDialog").dialog("close");
		}
	}
}

$(function() {
	$("#bankInstitutionTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		rownumbers : true,
		url : "base/resourceTree/queryBankInstitution",
		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});
});