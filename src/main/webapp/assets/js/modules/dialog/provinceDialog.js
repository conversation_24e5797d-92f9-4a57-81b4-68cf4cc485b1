var qryProvince = {
	query : function() {
		var params = {
			"condition" : $("#province-searchText").val()
		}
		
		getAjax("base/resourceTree/queryProvince", params, "json", function(res) {
			$("#provinceTab").datagrid("loadData", res);
		});
	},
	ok : function() {
		var row = $('#provinceTab').datagrid('getSelected');
		if (row != undefined) {
			var rowIndex = $("#rowIndex").val();
			var indexEd = $('#drawMoney-payway-dg').datagrid('getEditor', {
				index : rowIndex,
				field : 'province'
			});
			$(indexEd.target).children("input").val(row.provinceName);
			$("#provinceDialog").dialog("close");
			
			// 城市联动数据（不好使）
			qryArea.query();
//			$("#qryArea").click();
		}
	}
}

$(function() {
	$("#provinceTab").datagrid({
		fitColumns : true,
		singleSelect : true,
		rownumbers : true,
		url : "base/resourceTree/queryProvince",
		method : "get",
		loadFilter : function(res) {
			if (res.success) {
				return res.data;
			}
		}
	});
});