var editIndex = undefined;
var tabIds = [ "#cashLoan-payway-dg" ];
var checkFields = [ "payWay" ];
var currTabIdx = 0;

var cashLoan = {
		init: function() {
			cashLoan.toolbarButtons();
			cashLoan.handleDatagrid();
			cashLoan.tabs();
			cashLoan.bindSearchbox();
		},
		toolbarButtons: function() {
			var hasId = $("#cashLoanId").val();
			if (hasId) {
				alert("update");
			} else {
				// 只允许点击新增按钮
				$("#cashLoan-dialog-toolbar a").linkbutton({disabled:true});
				$('#cashLoan-add').linkbutton({disabled:false});
				$("#cashLoan-tab-tools a").linkbutton({disabled:true});
				
				$("#cashLoan-bill .easyui-textbox").textbox({disabled:true});
				$("#cashLoan-bill .easyui-searchbox").searchbox({disabled:true});
				$("#cashLoan-bill .easyui-datebox").datebox({disabled:true});
			}
		},
		handleDatagrid: function() {
			$("#cashLoan-payway-dg").datagrid({
				onClickRow : cashLoan.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#cashLoan-payway-dg", "amount");
				}
			});
			
			$("#cashLoan-payway-dg").datagrid('reloadFooter', [ {
				payWay : '合计',
				amount : '0.00'
			} ]);
		},
		tabs: function() {
			$("#cashLoan-easyui-tabs").tabs({
				onSelect : function(title, index) {
					currTabIdx = index;
					editIndex = undefined;
				}
			});
		},
		bindSearchbox: function() {
			// 借款人查询
			$("#borrower").searchbox({
				searcher : function(value, name) {
					getAjax("base/resourceTree/queryUsers.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#resourceTree"), {},
										res.data);
								$("#resourceTreeDialog").dialog("open");
							});
				}
			});
			
			// 部门查询
			$("#dept").searchbox({
				searcher : function(value, name) {
					getAjax("base/resourceTree/queryDepts.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#resourceTree"), {},
										res.data);
								$("#resourceTreeDialog").dialog("open");
							});
				}
			});
			
			// 借方科目
			
		},
		add: function() {
			var hasId = $("#cashLoanId").val();
			if (hasId) {
				var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
				refreshTab(currentTab);
			} else {
				$("#cashLoan-dialog-toolbar a").linkbutton({disabled:true});
				$("#cashLoan-save").linkbutton({disabled:false});
				$("#cashLoan-cancel").linkbutton({disabled:false});
				$("#cashLoan-reverse").linkbutton({disabled:false});
				$("#cashLoan-attach").linkbutton({disabled:false});
				$("#cashLoan-tab-tools a").linkbutton({disabled:false});
			}
			
			$("#cashLoan-bill .easyui-textbox").textbox({disabled:false});
			$("#cashLoan-bill .easyui-searchbox").searchbox({disabled:false});
			$("#cashLoan-bill .easyui-datebox").datebox({disabled:false});
		},
		save: function() {
			var empty = cashLoan.emptyCurrRow();
			if (empty) {
				cashLoan.remove();
			}
			$("#cashLoan-payway-dg").datagrid('acceptChanges');
			
			var jsonObject = serializeObjectByForms(["#cashLoanForm_part1","#cashLoanForm_part2"]);
			jsonObject["paywayDetails"] = $("#cashLoan-payway-dg").datagrid('getRows');
			alert(JSON.stringify(jsonObject));
//			postAjax("", JSON.stringify(jsonObject), "json")
			
			$("#cashLoan-dialog-toolbar a").linkbutton({disabled:true});
			$('#cashLoan-print').linkbutton({disabled:false});
			$('#cashLoan-pdfprint').linkbutton({disabled:false});
			$('#cashLoan-update').linkbutton({disabled:false});
			$('#cashLoan-submit').linkbutton({disabled:false});
			$("#cashLoan-tab-tools a").linkbutton({disabled:true});
			
			$("#cashLoan-bill .easyui-textbox").textbox({disabled:true});
			$("#cashLoan-bill .easyui-searchbox").searchbox({disabled:true});
			$("#cashLoan-bill .easyui-datebox").datebox({disabled:true});
		},
		update: function() { // 修改
			$("#cashLoan-dialog-toolbar a").linkbutton({disabled:true});
			$('#cashLoan-save').linkbutton({disabled:false});
			$('#cashLoan-del').linkbutton({disabled:false});
			$('#cashLoan-reverse').linkbutton({disabled:false});
			$('#cashLoan-attach').linkbutton({disabled:false});
			$("#cashLoan-tab-tools a").linkbutton({disabled:false});
			
			$("#cashLoan-bill .easyui-textbox").textbox({disabled:false});
			$("#cashLoan-bill .easyui-searchbox").searchbox({disabled:false});
			$("#cashLoan-bill .easyui-datebox").datebox({disabled:false});
		},
		del: function() { // 删除
			iAlert("删除成功", "success");
			
			// 停顿2秒
			setTimeout(function() {
				var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
				refreshTab(currentTab);
			}, 1500);
		},
		cancel: function() { // 取消
			var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
			refreshTab(currentTab);
		},
		submit: function() { // 提交
			iAlert("提交成功", "success");
			
			$("#cashLoan-dialog-toolbar a").linkbutton({disabled:true});
			$('#cashLoan-print').linkbutton({disabled:false});
			$('#cashLoan-pdfprint').linkbutton({disabled:false});
			$('#cashLoan-add').linkbutton({disabled:false});
			$('#cashLoan-recycle').linkbutton({disabled:false});
			$('#cashLoan-audit').linkbutton({disabled:false});
			$("#cashLoan-tab-tools a").linkbutton({disabled:false});
		},
		endEditing: function() {
			if (editIndex == undefined) {
				return true
			}
			if ($(tabIds[currTabIdx]).datagrid('validateRow', editIndex)) {
				var empty = cashLoan.emptyCurrRow();
				if (empty) {
					return false;
				}
				
				$(tabIds[currTabIdx]).datagrid('endEdit', editIndex);
				editIndex = undefined;
				return true;
			} else {
				return false;
			}
		},
		emptyCurrRow: function() {
			if (editIndex == undefined) {
				return false;
			}
			
			var ed = $(tabIds[currTabIdx]).datagrid('getEditor', {
				index : editIndex,
				field : checkFields[currTabIdx]
			});
			var indexVal = $(ed.target).children("input").val();
			if (hasText(indexVal)) {
				return false;
			} else {
				return true;
			}
		},
		onClickRow: function(index) {
			if (editIndex != index) {
				if (cashLoan.endEditing()) {
					$(tabIds[currTabIdx]).datagrid('selectRow', index).datagrid(
							'beginEdit', index);
					editIndex = index;
				} else {
					$(tabIds[currTabIdx]).datagrid('selectRow', editIndex);
				}
			}
		},
		append: function() {
			if (cashLoan.endEditing()) {
				$(tabIds[currTabIdx]).datagrid('appendRow', {});
				editIndex = $(tabIds[currTabIdx]).datagrid('getRows').length - 1;
				$(tabIds[currTabIdx]).datagrid('selectRow', editIndex).datagrid(
						'beginEdit', editIndex);
				
				// 给搜索按钮绑定点击事件
				cashLoan.bindClickForSearchBox();
			}
		},
		bindClickForSearchBox: function() {
			if (currTabIdx == 0) {
				// 结算方式
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'payWay'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#payWayDialog").dialog("open");
					});
				}
				
				// 科目
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'subject'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						getAjax("base/resourceTree/querySubjects.json", {}, "json",
							      function(res) {
							        $.fn.zTree.init($("#resourceTree"), {},
							          res.data);
							        $("#resourceTreeDialog").dialog("open");
							      });
					});
				}
			}
		},
		remove: function() {
			if (editIndex == undefined) {
				return;
			}
			$(tabIds[currTabIdx]).datagrid('cancelEdit', editIndex).datagrid(
					'deleteRow', editIndex);
			editIndex = undefined;
		}
}

$(function() {
	cashLoan.init();
});

