var editIndex = undefined;
var tabIds = [ "#carfare-dg", "#foodSubsidy-dg", "#publicFees-dg", "#hotelExpense-dg", "#otherFees-dg", "#travelApproval-payway-dg" ];
var checkFields = [ "departureDate", "foodSubsidyType", "publicExpenseType", "stayPeriod", "expenseRemark", "payWay" ];
var currTabIdx = 0;

var travelApproval = {
		init: function() {
			travelApproval.toolbarButtons();
			travelApproval.handleDatagrid();
			travelApproval.tabs();
			travelApproval.bindSearchbox();
		},
		toolbarButtons: function() {
			var hasId = $("#travelApprovalId").val();
			if (hasId) {
				alert("update");
			} else {
				// 只允许点击新增按钮
				$("#travelApproval-dialog-toolbar a").linkbutton({disabled:true});
				$('#travelApproval-add').linkbutton({disabled:false});
				$("#travelApproval-tab-tools a").linkbutton({disabled:true});
				
				$("#travelApproval-bill .easyui-textbox").textbox({disabled:true});
				$("#travelApproval-bill .easyui-searchbox").searchbox({disabled:true});
				$("#travelApproval-bill .easyui-datebox").datebox({disabled:true});
			}
		},
		handleDatagrid: function() {
			$("#carfare-dg").datagrid({
				onClickRow : travelApproval.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#carfare-dg", "amount");
				}
			});

			$("#foodSubsidy-dg").datagrid({
				onClickRow : travelApproval.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#foodSubsidy-dg", "amount");
				}
			});
			
			$("#publicFees-dg").datagrid({
				onClickRow : travelApproval.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#publicFees-dg", "amount");
				}
			});
			
			$("#hotelExpense-dg").datagrid({
				onClickRow : travelApproval.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#hotelExpense-dg", "amount");
				}
			});
			
			$("#otherFees-dg").datagrid({
				onClickRow : travelApproval.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#otherFees-dg", "amount");
				}
			});
			
			$("#travelApproval-payway-dg").datagrid({
				onClickRow : travelApproval.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#travelApproval-payway-dg", "amount");
				}
			});

			$("#carfare-dg").datagrid('reloadFooter', [ {
				0 : '合计',
				amount : '0.00'
			} ]);

			$("#foodSubsidy-dg").datagrid('reloadFooter', [ {
				0 : '合计',
				amount : '0.00'
			} ]);
			
			$("#publicFees-dg").datagrid('reloadFooter', [ {
				0 : '合计',
				amount : '0.00'
			} ]);
			
			$("#hotelExpense-dg").datagrid('reloadFooter', [ {
				0 : '合计',
				amount : '0.00'
			} ]);
			
			$("#otherFees-dg").datagrid('reloadFooter', [ {
				0 : '合计',
				amount : '0.00'
			} ]);
			
			$("#travelApproval-payway-dg").datagrid('reloadFooter', [ {
				payWay : '合计',
				amount : '0.00'
			} ]);
		},
		tabs: function() {
			$("#travelApproval-easyui-tabs").tabs({
				onSelect : function(title, index) {
					currTabIdx = index;
					editIndex = undefined;
				}
			});
		},
		bindSearchbox: function() {
			// 申请人查询
			$("#travelApproval-applyPerson").searchbox({
				searcher : function(value, name) {
					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/resourceTree/queryUsers.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#applyPerson_resourceTree"), setting,
										res.data);
								$("#applyPersonDialog").dialog("open");
							});
					
					$("#applyPerson_rtSearch").click(function() {
						var params = {
								"condition" : $("#applyPerson_rtCondition").val()
						};
						
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryUsers.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#applyPerson_resourceTree"), setting,
											res.data);
									$("#applyPersonDialog").dialog("open");
								});
					});
					
					$("#applyPerson_rtOk").click(function() {
						$("#travelApproval-applyPerson").searchbox("setValue", $("#rtCurrTreeNodeName").val());
						$("#applyPersonDialog").dialog("close");
					});
				}
			});
			
			// 部门查询
			$("#travelApproval-dept").searchbox({
				searcher : function(value, name) {
					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/resourceTree/queryDepts.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#dept_resourceTree"), setting,
										res.data);
								$("#deptDialog").dialog("open");
							});
					
					$("#dept_rtSearch").click(function() {
						var params = {
								"condition" : $("#dept_rtCondition").val()
						};
						
						getAjax("base/resourceTree/queryDepts.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#dept_resourceTree"), setting,
											res.data);
									$("#deptDialog").dialog("open");
								});
					});
					
					$("#dept_rtOk").click(function() {
						$("#travelApproval-dept").searchbox("setValue", $("#rtCurrTreeNodeName").val());
						$("#deptDialog").dialog("close");
					});
				}
			});
			
			// 经济科目
			$("#economicSubject").searchbox({
				searcher : function(value, name) {

					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/resourceTree/queryEconomicSubjects.json", {}, "json",
					      function(res) {
					        $.fn.zTree.init($("#economicSubject_resourceTree"), setting,
					          res.data);
					        $("#economicSubjectDialog").dialog("open");
					      });
					
					$("#economicSubject_rtSearch").click(function() {
						var params = {
								"condition" : $("#economicSubject_rtCondition").val()
						};
						
						getAjax("base/resourceTree/queryEconomicSubjects.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#economicSubject_resourceTree"), setting,
											res.data);
									$("#economicSubjectDialog").dialog("open");
								});
					});
					
					$("#economicSubject_rtOk").click(function() {
						var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
							index : editIndex,
							field : 'economicSubject'
						});
						$("#economicSubject").searchbox("setValue", $("#rtCurrTreeNodeName").val());
						$("#economicSubjectDialog").dialog("close");
					});
				}
			});
		},
		add: function() { // 新增
			var hasId = $("#travelApprovalId").val();
			if (hasId) {
				var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
				refreshTab(currentTab);
			} else {
				$("#travelApproval-dialog-toolbar a").linkbutton({disabled:true});
				$("#travelApproval-save").linkbutton({disabled:false});
				$("#travelApproval-cancel").linkbutton({disabled:false});
				$("#travelApproval-reverse").linkbutton({disabled:false});
				$("#travelApproval-attach").linkbutton({disabled:false});
				$("#travelApproval-tab-tools a").linkbutton({disabled:false});
			}
			$("#travelApproval-bill .easyui-textbox").textbox({disabled:false});
			$("#travelApproval-bill .easyui-searchbox").searchbox({disabled:false});
			$("#travelApproval-bill .easyui-datebox").datebox({disabled:false});
			$("#travelApproval_billNo").textbox({disabled:true}); //单据号不允许编辑
		},
		save : function() { // 保存
			var empty = travelApproval.emptyCurrRow();
			if (empty) {
				travelApproval.remove();
			}
			$("#carfare-dg").datagrid('acceptChanges');
			$("#foodSubsidy-dg").datagrid('acceptChanges');
			$("#publicFees-dg").datagrid('acceptChanges');
			$("#hotelExpense-dg").datagrid('acceptChanges');
			$("#otherFees-dg").datagrid('acceptChanges');
			$("#travelApproval-payway-dg").datagrid('acceptChanges');
			
			var jsonObject = serializeObjectByForms(["#travelApprovalForm_part1","#travelApprovalForm_part2"]);
			jsonObject["carfareDetails"] = $("#carfare-dg").datagrid('getRows');
			jsonObject["foodSubsidyDetails"] = $("#foodSubsidy-dg").datagrid('getRows');
			jsonObject["publicFeesDetails"] = $("#publicFees-dg").datagrid('getRows');
			jsonObject["hotelExpenseDetails"] = $("#hotelExpense-dg").datagrid('getRows');
			jsonObject["otherFeesDetails"] = $("#otherFees-dg").datagrid('getRows');
			jsonObject["paywayDetails"] = $("#travelApproval-payway-dg").datagrid('getRows');
			alert(JSON.stringify(jsonObject));
//			postAjax("", JSON.stringify(jsonObject), "json")
			
			$("#travelApproval-dialog-toolbar a").linkbutton({disabled:true});
			$('#travelApproval-print').linkbutton({disabled:false});
			$('#travelApproval-pdfprint').linkbutton({disabled:false});
			$('#travelApproval-update').linkbutton({disabled:false});
			$('#travelApproval-submit').linkbutton({disabled:false});
			$("#travelApproval-tab-tools a").linkbutton({disabled:true});
			
			$("#travelApproval-bill .easyui-textbox").textbox({disabled:true});
			$("#travelApproval-bill .easyui-searchbox").searchbox({disabled:true});
			$("#travelApproval-bill .easyui-datebox").datebox({disabled:true});
		},
		update: function() {
			$("#travelApproval-dialog-toolbar a").linkbutton({disabled:true});
			$('#travelApproval-save').linkbutton({disabled:false});
			$('#travelApproval-del').linkbutton({disabled:false});
			$('#travelApproval-reverse').linkbutton({disabled:false});
			$('#travelApproval-attach').linkbutton({disabled:false});
			$("#travelApproval-tab-tools a").linkbutton({disabled:false});
			
			$("#travelApproval-bill .easyui-textbox").textbox({disabled:false});
			$("#travelApproval-bill .easyui-searchbox").searchbox({disabled:false});
			$("#travelApproval-bill .easyui-datebox").datebox({disabled:false});
		},
		del: function() { // 删除
			iAlert("删除成功", "success");
			
			// 停顿2秒
			setTimeout(function() {
				var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
				refreshTab(currentTab);
			}, 1500);
		},
		cancel: function() { // 取消
			var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
			refreshTab(currentTab);
		},
		submit: function() { // 提交
			iAlert("提交成功", "success");
			
			$("#travelApproval-dialog-toolbar a").linkbutton({disabled:true});
			$('#travelApproval-print').linkbutton({disabled:false});
			$('#travelApproval-pdfprint').linkbutton({disabled:false});
			$('#travelApproval-add').linkbutton({disabled:false});
			$('#travelApproval-recycle').linkbutton({disabled:false});
			$('#travelApproval-audit').linkbutton({disabled:false});
			$("#travelApproval-tab-tools a").linkbutton({disabled:false});
		},
		endEditing: function() {
			if (editIndex == undefined) {
				return true
			}
			if ($(tabIds[currTabIdx]).datagrid('validateRow', editIndex)) {
				var empty = travelApproval.emptyCurrRow();
				if (empty) {
					return false;
				}
				
				$(tabIds[currTabIdx]).datagrid('endEdit', editIndex);
				editIndex = undefined;
				return true;
			} else {
				return false;
			}
		},
		emptyCurrRow: function() {
			if (editIndex == undefined) {
				return false;
			}
			
			var ed = $(tabIds[currTabIdx]).datagrid('getEditor', {
				index : editIndex,
				field : checkFields[currTabIdx]
			});
			
			
			var indexVal = $(ed.target).datebox("getValue");
			if (hasText(indexVal)) {
				return false;
			} else {
				return true;
			}
		},
		onClickRow: function(index) {
			if (editIndex != index) {
				if (travelApproval.endEditing()) {
					$(tabIds[currTabIdx]).datagrid('selectRow', index).datagrid(
							'beginEdit', index);
					editIndex = index;
				} else {
					$(tabIds[currTabIdx]).datagrid('selectRow', editIndex);
				}
			}
		},
		append: function() {
			if (travelApproval.endEditing()) {
				$(tabIds[currTabIdx]).datagrid('appendRow', {});
				editIndex = $(tabIds[currTabIdx]).datagrid('getRows').length - 1;
				$(tabIds[currTabIdx]).datagrid('selectRow', editIndex).datagrid(
						'beginEdit', editIndex);

				// 给搜索按钮绑定点击事件
				travelApproval.bindClickForSearchBox();
			}
		},
		bindClickForSearchBox: function() {
			if (currTabIdx == 0) {
				// 交通工具
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'vehicle'
				});
				$(indexEd.target).children("span").bind("click", function() {

					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/baseDataController/qryVehicle.json", {}, "json",
					      function(res) {
					        $.fn.zTree.init($("#vehicle_resourceTree"), setting,
					          res.data);
					        $("#vehicleDialog").dialog("open");
					      });
					
					$("#vehicle_rtSearch").click(function() {
						var params = {
								"condition" : $("#vehicle_rtCondition").val()
						};
						
						getAjax("base/resourceTree/queryProjects.json", params, "json",
							function(res) {
								$.fn.zTree.init($("#vehicle_resourceTree"), setting,
										res.data);
								$("#vehicleDialog").dialog("open");
							});
					});
					
					$("#vehicle_rtOk").click(function() {
						var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
							index : editIndex,
							field : 'vehicle'
						});
						$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
						$("#vehicleDialog").dialog("close");
					});
				});
			} else if (currTabIdx == 1) {
				// 伙食补贴类型
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'foodSubsidyType'
				});
				$(indexEd.target).children("span").bind("click", function() {
					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					getAjax("base/baseDataController/qryFoodAllowance.json", {}, "json",
					      function(res) {
					        $.fn.zTree.init($("#foodAllowance_resourceTree"), setting,
					          res.data);
					        $("#foodAllowanceDialog").dialog("open");
					      });
					
					$("#foodAllowance_rtSearch").click(function() {
						var params = {
								"condition" : $("#foodAllowance_rtCondition").val()
						};
						
						getAjax("base/resourceTree/qryFoodAllowance.json", params, "json",
							function(res) {
								$.fn.zTree.init($("#foodAllowance_resourceTree"), setting,
										res.data);
								$("#foodAllowanceDialog").dialog("open");
							});
					});
					
					$("#foodAllowance_rtOk").click(function(){
						var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
							index : editIndex,
							field : 'foodSubsidyType'
						});
						$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
						$("#foodAllowanceDialog").dialog("close");
						// 伙食补贴标准
						
					});
				});
				
				
				
				
				
			} else if (currTabIdx == 2) {
				// 公杂费用类别
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'publicExpenseType'
				});
				$(indexEd.target).children("span").bind("click", function() {
					getAjax("base/resourceTree/queryStandardFoodSubsidys.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#resourceTree"), {},
						          res.data);
						        $("#resourceTreeDialog").dialog("open");
						      });
				});
				
				// 公杂费用标准
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'standardPublicExpense'
				});
				$(indexEd.target).children("span").bind("click", function() {
					getAjax("base/resourceTree/queryStandardFoodSubsidys.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#resourceTree"), {},
						          res.data);
						        $("#resourceTreeDialog").dialog("open");
						      });
				});
			} else if (currTabIdx == 5) {
				alert(tabIds[currTabIdx]);
				// 结算方式
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'payWay'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#payWayDialog").dialog("open");
					});
				}
				
				// 科目
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'subject'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						getAjax("base/resourceTree/querySubjects.json", {}, "json",
							      function(res) {
							        $.fn.zTree.init($("#resourceTree"), {},
							          res.data);
							        $("#resourceTreeDialog").dialog("open");
							      });
					});
				}
			}
		},
		remove: function() {
			if (editIndex == undefined) {
				return;
			}
			$(tabIds[currTabIdx]).datagrid('cancelEdit', editIndex).datagrid(
					'deleteRow', editIndex);
			editIndex = undefined;
		}
}

$(function() {
	travelApproval.init();
})