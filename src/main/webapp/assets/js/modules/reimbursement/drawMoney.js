var editIndex = undefined;
var tabIds = [ "#paydetail-dg", "#drawMoney-payway-dg" ];
var checkFields = [ "index", "payWay" ];

var drawMoney = {
		init: function() {
			drawMoney.toolbarButtons();
			drawMoney.handleDatagrid();
			drawMoney.tabs();
			drawMoney.bindSearchbox();
			drawMoney.bindEvents;
		},
		toolbarButtons: function() {
			var hasId = $("#drawMoneyId").val();
			if (hasId) {
				alert("update");
			} else {
				// 只允许点击新增按钮
				$("#drawMoney-dialog-toolbar a").linkbutton({disabled:true});
				$('#drawMoney-add').linkbutton({disabled:false});
				$("#drawMoney-tab-tools a").linkbutton({disabled:true});
				
				
				$("#drawMoney-bill .easyui-textbox").textbox({disabled:true});
				$("#drawMoney-bill .easyui-searchbox").searchbox({disabled:true});
				$("#drawMoney-bill .easyui-datebox").datebox({disabled:true});
			}
		},
		handleDatagrid: function() {
			$("#paydetail-dg").datagrid({
				onClickRow : drawMoney.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#paydetail-dg", "amount");
				}
			});

			$("#drawMoney-payway-dg").datagrid({
				onClickRow : drawMoney.onClickRow,
				onAfterEdit : function(index, row) {
					reloadFooter("#drawMoney-payway-dg", "amount");
				}
			});

			$("#paydetail-dg").datagrid('reloadFooter', [ {
				index : '合计',
				amount : '0.00'
			} ]);

			$("#drawMoney-payway-dg").datagrid('reloadFooter', [ {
				payWay : '合计',
				amount : '0.00'
			} ]);
		},
		tabs: function() {
			$("#drawMoney-easyui-tabs").tabs({
				onSelect : function(title, index) {
					currTabIdx = index;
					editIndex = undefined;
				}
			});
		},
		bindSearchbox: function() {
			// 申请人查询
			$("#drawMoney-applyPerson").searchbox({
				searcher : function(value, name) {
					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/resourceTree/queryUsers.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#applyPerson_resourceTree"), setting,
										res.data);
								$("#applyPersonDialog").dialog("open");
							});
					
					$("#applyPerson_rtSearch").click(function() {
						var params = {
								"condition" : $("#applyPerson_rtCondition").val()
						};
						
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryUsers.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#applyPerson_resourceTree"), setting,
											res.data);
									$("#applyPersonDialog").dialog("open");
								});
					});
					
					$("#applyPerson_rtOk").click(function() {
						$("#drawMoney-applyPerson").searchbox("setValue", $("#rtCurrTreeNodeName").val());
						$("#applyPersonDialog").dialog("close");
					});
				}
			});
			
			
			// 部门查询
			$("#drawMoney-dept").searchbox({
				searcher : function(value, name) {
					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/resourceTree/queryDepts.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#dept_resourceTree"), setting,
										res.data);
								$("#deptDialog").dialog("open");
							});
					
					$("#dept_rtSearch").click(function() {
						var params = {
								"condition" : $("#dept_rtCondition").val()
						};
						
						getAjax("base/resourceTree/queryDepts.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#dept_resourceTree"), setting,
											res.data);
									$("#deptDialog").dialog("open");
								});
					});
					
					$("#dept_rtOk").click(function() {
						$("#drawMoney-dept").searchbox("setValue", $("#rtCurrTreeNodeName").val());
						$("#deptDialog").dialog("close");
					});
				}
			});
		},
		bindEvents: function() {
			$("#drawMoney-amount").textbox({
			    blur: function(value) {
//			        alert($("#drawMoney-amount").textbox("getValue"));
			    }
			});
		},
		add : function() { // 新增
			var hasId = $("#drawMoneyId").val();
			if (hasId) {
				var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
				refreshTab(currentTab);
			} else {
				$("#drawMoney-dialog-toolbar a").linkbutton({disabled:true});
				$("#drawMoney-save").linkbutton({disabled:false});
				$("#drawMoney-cancel").linkbutton({disabled:false});
				$("#drawMoney-reverse").linkbutton({disabled:false});
				$("#drawMoney-attach").linkbutton({disabled:false});
				$("#drawMoney-tab-tools a").linkbutton({disabled:false});
			}
			
			$("#drawMoney-bill .easyui-textbox").textbox({disabled:false});
			$("#drawMoney-bill .easyui-searchbox").searchbox({disabled:false});
			$("#drawMoney-bill .easyui-datebox").datebox({disabled:false});
		},
		save: function() { // 保存
			var empty = drawMoney.emptyCurrRow();
			if (empty) {
				drawMoney.remove();
			}
			
			$("#paydetail-dg").datagrid('acceptChanges');
			$("#drawMoney-payway-dg").datagrid('acceptChanges');
			
			var jsonObject = serializeObjectByForms(["#drawMoneyForm_part1","#drawMoneyForm_part2"]);
			// 特殊处理
			if (hasText(jsonObject.applyPerson)) {
				jsonObject.applyPerson = jsonObject.applyPerson.split(" ")[0];
			}
			if (hasText(jsonObject.dept)) {
				jsonObject.dept = jsonObject.dept.split(" ")[0];
			}
			
			jsonObject["paydetailDetails"] = $("#paydetail-dg").datagrid('getRows');
			jsonObject["paywayDetails"] = $("#drawMoney-payway-dg").datagrid('getRows');
			//alert(JSON.stringify(jsonObject));
			postAjax("reimbursement/drawMoney/saveBill.json", JSON.stringify(jsonObject), "json", function(res) {
				alert(res);
			});
			
			$("#drawMoney-dialog-toolbar a").linkbutton({disabled:true});
			$('#drawMoney-print').linkbutton({disabled:false});
			$('#drawMoney-pdfprint').linkbutton({disabled:false});
			$('#drawMoney-update').linkbutton({disabled:false});
			$('#drawMoney-submit').linkbutton({disabled:false});
			$("#drawMoney-tab-tools a").linkbutton({disabled:true});
			
			$("#drawMoney-bill .easyui-textbox").textbox({disabled:true});
			$("#drawMoney-bill .easyui-searchbox").searchbox({disabled:true});
			$("#drawMoney-bill .easyui-datebox").datebox({disabled:true});
		},
		update: function() { // 修改
			$("#drawMoney-dialog-toolbar a").linkbutton({disabled:true});
			$('#drawMoney-save').linkbutton({disabled:false});
			$('#drawMoney-del').linkbutton({disabled:false});
			$('#drawMoney-reverse').linkbutton({disabled:false});
			$('#drawMoney-attach').linkbutton({disabled:false});
			$("#drawMoney-tab-tools a").linkbutton({disabled:false});
			
			$("#drawMoney-bill .easyui-textbox").textbox({disabled:false});
			$("#drawMoney-bill .easyui-searchbox").searchbox({disabled:false});
			$("#drawMoney-bill .easyui-datebox").datebox({disabled:false});
		},
		del: function() { // 删除
			iAlert("删除成功", "success");
			
			// 停顿2秒
			setTimeout(function() {
				var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
				refreshTab(currentTab);
			}, 1500);
		},
		cancel: function() { // 取消
			var currentTab = $("#index-tabs").tabs("getSelected");  // get selected panel
			refreshTab(currentTab);
		},
		submit: function() { // 提交
			iAlert("提交成功", "success");
			
			$("#drawMoney-dialog-toolbar a").linkbutton({disabled:true});
			$('#drawMoney-print').linkbutton({disabled:false});
			$('#drawMoney-pdfprint').linkbutton({disabled:false});
			$('#drawMoney-add').linkbutton({disabled:false});
			$('#drawMoney-recycle').linkbutton({disabled:false});
			$('#drawMoney-audit').linkbutton({disabled:false});
			$("#drawMoney-tab-tools a").linkbutton({disabled:false});
		},
		endEditing: function() {
			if (editIndex == undefined) {
				return true
			}
			if ($(tabIds[currTabIdx]).datagrid('validateRow', editIndex)) {
				var empty = drawMoney.emptyCurrRow();
				if (empty) {
					return false;
				}
				
				$(tabIds[currTabIdx]).datagrid('endEdit', editIndex);
				editIndex = undefined;
				return true;
			} else {
				return false;
			}
		},
		emptyCurrRow: function() {
			if (editIndex == undefined) {
				return false;
			}
			
			var ed = $(tabIds[currTabIdx]).datagrid('getEditor', {
				index : editIndex,
				field : checkFields[currTabIdx]
			});
			var indexVal = $(ed.target).children("input").val();
			if (hasText(indexVal)) {
				return false;
			} else {
				return true;
			}
		},
		onClickRow: function(index) {
			if (editIndex != index) {
				if (drawMoney.endEditing()) {
					$(tabIds[currTabIdx]).datagrid('selectRow', index).datagrid(
							'beginEdit', index);
					editIndex = index;
				} else {
					$(tabIds[currTabIdx]).datagrid('selectRow', editIndex);
				}
			}
		},
		append: function() {
			if (drawMoney.endEditing()) {
				$(tabIds[currTabIdx]).datagrid('appendRow', {});
				editIndex = $(tabIds[currTabIdx]).datagrid('getRows').length - 1;
				$(tabIds[currTabIdx]).datagrid('selectRow', editIndex).datagrid(
						'beginEdit', editIndex);

				// 给搜索按钮绑定点击事件
				drawMoney.bindClickForSearchBox();
			}
		},
		bindClickForSearchBox: function() {
			if (currTabIdx == 0) {
				// 指标
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'index'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$('#indexDialog').dialog('open');
						
						$("#rowIndex").val(editIndex);
					});
				}
				
				// 项目
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'project'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryProjects.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#project_resourceTree"), setting,
						          res.data);
						        $("#projectDialog").dialog("open");
						      });
						
						$("#project_rtSearch").click(function() {
							var params = {
									"condition" : $("#project_rtCondition").val()
							};
							
							getAjax("base/resourceTree/queryProjects.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#project_resourceTree"), setting,
											res.data);
									$("#projectDialog").dialog("open");
								});
						});
						
						$("#project_rtOk").click(function() {
							var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
								index : editIndex,
								field : 'project'
							});
							$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
							$("#projectDialog").dialog("close");
						});
					});
				}
				
				// 会计科目
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'accountTitle'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryAccountTitles.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#accountTitle_resourceTree"), setting,
						          res.data);
						        $("#accountTitleDialog").dialog("open");
						      });
						
						$("#accountTitle_rtSearch").click(function() {
							var params = {
									"condition" : $("#accountTitle_rtCondition").val()
							};
							
							getAjax("base/resourceTree/queryAccountTitles.json", params, "json",
									function(res) {
										$.fn.zTree.init($("#accountTitle_resourceTree"), setting,
												res.data);
										$("#accountTitleDialog").dialog("open");
									});
						});
						
						$("#accountTitle_rtOk").click(function() {
							var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
								index : editIndex,
								field : 'accountTitle'
							});
							$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
							$("#accountTitleDialog").dialog("close");
						});
					});
				}
				
				//经济科目
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'economicSubject'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryEconomicSubjects.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#economicSubject_resourceTree"), setting,
						          res.data);
						        $("#economicSubjectDialog").dialog("open");
						      });
						
						$("#economicSubject_rtSearch").click(function() {
							var params = {
									"condition" : $("#economicSubject_rtCondition").val()
							};
							
							getAjax("base/resourceTree/queryEconomicSubjects.json", params, "json",
									function(res) {
										$.fn.zTree.init($("#economicSubject_resourceTree"), setting,
												res.data);
										$("#economicSubjectDialog").dialog("open");
									});
						});
						
						$("#economicSubject_rtOk").click(function() {
							var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
								index : editIndex,
								field : 'economicSubject'
							});
							$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
							$("#economicSubjectDialog").dialog("close");
						});
					});
				}
				
				// 经费归口部门
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'outlayDept'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryEconomicSubjects.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#outlayDept_resourceTree"), setting,
						          res.data);
						        $("#outlayDeptDialog").dialog("open");
						      });
						
						$("#outlayDept_rtSearch").click(function() {
							var params = {
									"condition" : $("#outlayDept_rtCondition").val()
							};
							
							getAjax("base/resourceTree/queryEconomicSubjects.json", params, "json",
									function(res) {
										$.fn.zTree.init($("#outlayDept_resourceTree"), setting,
												res.data);
										$("#outlayDeptDialog").dialog("open");
									});
						});
						
						$("#outlayDept_rtOk").click(function() {
							var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
								index : editIndex,
								field : 'outlayDept'
							});
							$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
							
							$("#outlayDeptDialog").dialog("close");
						});
					});
				}
				
				// 执行方式
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'executeWay'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryExecuteWays.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#executeWay_resourceTree"), setting,
						          res.data);
						        $("#executeWayDialog").dialog("open");
						      });
						
						$("#executeWay_rtSearch").click(function() {
							var params = {
									"condition" : $("#executeWay_rtCondition").val()
							};
							
							getAjax("base/resourceTree/queryExecuteWays.json", params, "json",
									function(res) {
										$.fn.zTree.init($("#executeWay_resourceTree"), setting,
												res.data);
										$("#executeWayDialog").dialog("open");
									});
						});
						
						$("#executeWay_rtOk").click(function() {
							var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
								index : editIndex,
								field : 'executeWay'
							});
							$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
							
							$("#executeWayDialog").dialog("close");
						});
					});
				}
			} else if (currTabIdx == 1) {
				// 结算方式
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'payWay'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#payWayDialog").dialog("open");
						
						$("#rowIndex").val(editIndex);
					});
				}
				
				// 科目
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'subject'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						var setting = {
								data: {
									simpleData: {
										enable: true
									}
								},
								callback: {
									onClick: onClick
								}
						}
						
						getAjax("base/resourceTree/queryAccountTitles.json", {}, "json",
						      function(res) {
						        $.fn.zTree.init($("#accountTitle_resourceTree"), setting,
						          res.data);
						        $("#accountTitleDialog").dialog("open");
						      });
						
						$("#accountTitle_rtSearch").click(function() {
							var params = {
									"condition" : $("#accountTitle_rtCondition").val()
							};
							
							getAjax("base/resourceTree/queryAccountTitles.json", params, "json",
									function(res) {
										$.fn.zTree.init($("#accountTitle_resourceTree"), setting,
												res.data);
										$("#accountTitleDialog").dialog("open");
									});
						});
						
						$("#accountTitle_rtOk").click(function() {
							var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
								index : editIndex,
								field : 'subject'
							});
							$(indexEd.target).children("input").val($("#rtCurrTreeNodeName").val());
							$("#accountTitleDialog").dialog("close");
						});
					});
				}
				
				// 收款人
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'payee'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#payeeDialog").dialog("open");
						
						$("#rowIndex").val(editIndex);
					});
				}
				
				// 省份
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'province'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#provinceDialog").dialog("open");
						
						$("#rowIndex").val(editIndex);
					});
				}
				
				// 城市
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'area'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#areaDialog").dialog("open");
						
						$("#rowIndex").val(editIndex);
					});
				}
				
				// 银行机构
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'bankInstitution'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#bankInstitutionDialog").dialog("open");
						
						$("#rowIndex").val(editIndex);
					});
				}
				
				// 银行行号
				var indexEd = $(tabIds[currTabIdx]).datagrid('getEditor', {
					index : editIndex,
					field : 'bankInfo'
				});
				if (indexEd != null) {
					$(indexEd.target).children("span").bind("click", function() {
						$("#bankInfoDialog").dialog("open");
						
						$("#rowIndex").val(editIndex);
						qryBankInfo.query();
					});
				}
			}
		},
		remove: function() {
			if (editIndex == undefined) {
				return;
			}
			$(tabIds[currTabIdx]).datagrid('cancelEdit', editIndex).datagrid(
					'deleteRow', editIndex);
			editIndex = undefined;
		}
}

$(function(){
	drawMoney.init();
});