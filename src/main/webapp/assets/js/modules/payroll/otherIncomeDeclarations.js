var editIndex = undefined;
var tabIds = [ "#paydetail-dg", "#drawMoney-payway-dg" ];
var checkFields = [ "index", "payWay" ];
var currTabIdx = 0;
var billState = 0;  //0 为保存态   1 为编辑态
var loginInfo;   //登录信息




//单据状态切换
var billStateSwitchOver = {
		add:function(){ //新增数据
			//按钮编辑控制
			$("#otherIncomeDeclarations-dialog-toolbar a").linkbutton({disabled:true});
			$('#otherIncomeDeclarations-add').linkbutton({disabled:false});
			//按钮编辑控制
			$("#otherIncomeDeclarations-dialog-toolbar a").linkbutton({disabled:true});
			$('#otherIncomeDeclarations-add').linkbutton({disabled:false});
			//表体
			$("#otherIncomeDeclarations-tb a").linkbutton({disabled:true});
			//表头控制
			$("#otherIncomeDeclarations-bill .easyui-textbox").textbox({disabled:true});
			$("#otherIncomeDeclarations-bill .easyui-searchbox").searchbox({disabled:true});
			$("#otherIncomeDeclarations-bill .easyui-combobox").combobox({disabled:true});
			//特殊项处理
			$("#otherIncomeDeclarations-dept").searchbox({disabled:true});
			$("#otherIncomeDeclarations-billno").textbox({disabled:true});
		},
		save:function(){ //保存数据
			//按钮编辑控制
			$("#otherIncomeDeclarations-dialog-toolbar a").linkbutton({disabled:true});
			$('#otherIncomeDeclarations-add').linkbutton({disabled:false});
			//表体
			$("#otherIncomeDeclarations-tb a").linkbutton({disabled:true});
			//表头控制
			$("#otherIncomeDeclarations-bill .easyui-textbox").textbox({disabled:true});
			$("#otherIncomeDeclarations-bill .easyui-searchbox").searchbox({disabled:true});
			$("#otherIncomeDeclarations-bill .easyui-combobox").combobox({disabled:true});
			//特殊项处理
			$("#otherIncomeDeclarations-dept").searchbox({disabled:true});
			$("#otherIncomeDeclarations-billno").textbox({disabled:true});
			
			
			//getAjax("base/login", "", "json", function(res){
			//	alert(res.data.deptCode);
			//	$("#otherIncomeDeclarations-dept").searchbox("setValue", res.data.deptCode);
			//	alert("end");
			//});
		},
		edit:function(){  //编辑态
			//按钮编辑控制
			$("#otherIncomeDeclarations-dialog-toolbar a").linkbutton({disabled:true});
			$("#otherIncomeDeclarations-save").linkbutton({disabled:false});///保存
			$("#otherIncomeDeclarations-cancel").linkbutton({disabled:false});//取消
			$("#otherIncomeDeclarations-import").linkbutton({disabled:false});//导入
			$("#otherIncomeDeclarations-reverse").linkbutton({disabled:false});//冲销借款单
			//表体
			$("#otherIncomeDeclarations-tb a").linkbutton({disabled:false});
			//表头控制
			$("#otherIncomeDeclarations-bill .easyui-textbox").textbox({disabled:false});
			$("#otherIncomeDeclarations-bill .easyui-searchbox").searchbox({disabled:false});
			$("#otherIncomeDeclarations-bill .easyui-combobox").combobox({disabled:false});
			//特殊项处理
			$("#otherIncomeDeclarations-dept").searchbox({disabled:true});
			$("#otherIncomeDeclarations-billno").textbox({disabled:true});
		}
}

//界面初始化
var otherIncomeDeclarations = {
		init: function() {
			billStateSwitchOver.add();
			//获取部门当前登录部门
			//getAjax("base/login", "", "json", function(res){
			//	alert(res.data.deptCode);
			//	$("#otherIncomeDeclarations-dept").searchbox("setValue", res.data.deptCode);
			//});
		},
		toolbarButtons: function() {
			var hasId = $("#otherIncomeDeclarationsId").val();
			if (hasId) {
				alert("update");
			} else {
				 //只允许点击新增按钮
				$("#otherIncomeDeclarations-dialog-toolbar a").linkbutton({disabled:true});
				$('#otherIncomeDeclarations-add').linkbutton({disabled:false});
				$("#otherIncomeDeclarations-tb a").linkbutton({disabled:true});
				
				$("#otherIncomeDeclarations-bill .easyui-textbox").textbox({disabled:true});
				$("#otherIncomeDeclarations-bill .easyui-searchbox").searchbox({disabled:true});
				$("#otherIncomeDeclarations-bill .easyui-combobox").combobox({disabled:true});
			}
		},
		bindSearchbox: function() {
			 //部门查询
			$("#otherIncomeDeclarations-dept").searchbox({
				searcher : function(value, name) {
					var setting = {
							data: {
								simpleData: {
									enable: true
								}
							},
							callback: {
								onClick: onClick
							}
					}
					
					getAjax("base/resourceTree/queryDepts.json", {}, "json",
							function(res) {
								$.fn.zTree.init($("#resourceTree"), setting,
										res.data);
								$("#resourceTreeDialog").dialog("open");
							});
					
					$("#rtSearch").click(function() {
						var params = {
								"condition" : $("#rtCondition").val()
						};
						
						getAjax("base/resourceTree/queryDepts.json", params, "json",
								function(res) {
									$.fn.zTree.init($("#resourceTree"), setting,
											res.data);
									$("#resourceTreeDialog").dialog("open");
								});
					});
					
					$('#rtOk').unbind("click");
					$("#rtOk").click(function() {
						$("#otherIncomeDeclarations-dept").searchbox("setValue", $("#rtCurrTreeNodeName").val());
						$("#resourceTreeDialog").dialog("close");
					});
				}
			});
		}
}
//界面初始化
$(function() {
	billStateSwitchOver.add();   //按钮控制
	otherIncomeDeclarations.init(); //页面加载
});
