
var date = new Date();

var salaryQry = {
		search : function(){
			var params = {
					"year" : $("#financeSalaryQry_year").val(),
					"startMonth" :$("#financeSalaryQry_smonth").val(),
					"endMonth" : $("#financeSalaryQry_emonth").val()
			}
			
			getAjax("salaryQry/qrySalary", params, "json", function(res){
				$("#financeSalaryQry_tableSalary").datagrid("loadData",res.data);
				
				$("#financeSalaryQry_cardDiv").empty();
				var rows = res.data["rows"];
				for(var i=0;i<rows.length;i++){
					setCardDiv(rows[i]);
				}
			});
			
		}
}

function setCardDiv(data){
	var divInnerHTML = "<div style='float:left;margin-left:20px;margin-top:10px;'>";
	divInnerHTML+="<table style='border-collapse:collapse;'>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td colspan='4' style='text-align:left;padding-left:10px;background-color: #E0ECFF;border:1px solid #33517D;border-bottom:1px solid #FF82AB;font-weight:bold;'>第"+data["salaryMonth"]+"月</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='color:#8391AD;border:1px solid #33517D;background-color: #F7F7F7;font-weight:bold;'>薪酬项目1</td><td style='color:#8391AD;border:1px solid #33517D;background-color: #F7F7F7;font-weight:bold;'>项目金额1</td>";
	divInnerHTML+="		<td style='color:#8391AD;border:1px solid #33517D;background-color: #F7F7F7;font-weight:bold;'>薪酬项目2</td><td style='color:#8391AD;border:1px solid #33517D;background-color: #F7F7F7;font-weight:bold;'>项目金额2</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>部门代码</td><td style='color:#989898;border:1px solid #33517D;'>"+data["deptCode"]+"</td><td style='border:1px solid #33517D;color:#989898;'>部门名称</td><td style='color:#989898;border:1px solid #33517D;'>"+data["deptName"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>职员代码</td><td style='color:#989898;border:1px solid #33517D;'>"+data["employeeCode"]+"</td><td style='border:1px solid #33517D;color:#989898;'>职员姓名</td><td style='color:#989898;border:1px solid #33517D;'>"+data["employeeName"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>发放月份</td><td style='color:#989898;border:1px solid #33517D;'>"+data["salaryMonth"]+"</td><td style='border:1px solid #33517D;color:#989898;'>薪级工资</td><td style='color:#989898;border:1px solid #33517D;'>"+data["rankSalary"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="<td style='border:1px solid #33517D;color:#989898;'>岗位工资</td><td style='border:1px solid #33517D;color:#989898;'>"+data["jobSalary"]+"</td><td style='border:1px solid #33517D;color:#989898;'>生活性补贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["lifeSubsidy"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="<tr>";
	divInnerHTML+="<td style='border:1px solid #33517D;color:#989898;'>岗位津贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["jobSubsidy"]+"</td><td style='border:1px solid #33517D;color:#989898;'>月度考核奖</td><td style='border:1px solid #33517D;color:#989898;'>"+data["monthAssess"]+"</td>";
	divInnerHTML+="</tr>";
	divInnerHTML+="<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>工龄补贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["workYearSubsidy"]+"</td><td style='border:1px solid #33517D;color:#989898;'>医疗补贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["medicalSubsidy"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>住房提租补贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["rentSubsidy"]+"</td><td style='border:1px solid #33517D;color:#989898;'>其他补贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["otSubsidy"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>临时性补贴</td><td style='border:1px solid #33517D;color:#989898;'>"+data["tempSubsidy"]+"</td><td style='color:red;border:1px solid #33517D;color:#989898;'>财政应发工资</td><td style='border:1px solid #33517D;color:#989898;'>"+data["payableSalary"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='font-style:italic;border:1px solid #33517D;color:#989898;'>住房公积金</td><td style='border:1px solid #33517D;color:#989898;'>"+data["housFund"]+"</td><td style='font-style:italic;border:1px solid #33517D;color:#989898;'>医疗保险</td><td style='border:1px solid #33517D;color:#989898;'>"+data["medicare"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='font-style:italic;border:1px solid #33517D;color:#989898;'>养老保险</td><td style='border:1px solid #33517D;color:#989898;'>"+data["endowmentInsurance"]+"</td><td style='color:#989898;font-style:italic;border:1px solid #33517D;'>失业保险</td><td style='border:1px solid #33517D;color:#989898;'>"+data["unemployInsurance"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='font-style:italic;border:1px solid #33517D;color:#989898;'>职业年金</td><td style='border:1px solid #33517D;color:#989898;'>"+data["occupationalPension"]+"</td><td style='color:#989898;font-style:italic;border:1px solid #33517D;'>其他扣款</td><td style='border:1px solid #33517D;color:#989898;'>"+data["otDeduc"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='font-style:italic;border:1px solid #33517D;color:#989898;'>减发工资</td><td style='border:1px solid #33517D;color:#989898;'>"+data["subSalary"]+"</td><td style='color:#989898;font-style:italic;color:red;border:1px solid #33517D;'>扣款合计</td><td style='border:1px solid #33517D;color:#989898;'>"+data["deducTotal"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td style='border:1px solid #33517D;color:#989898;'>补发工资</td><td style='border:1px solid #33517D;color:#989898;'>"+data["backPay"]+"</td><td style='border:1px solid #33517D;color:#989898;'></td><td style='border:1px solid #33517D;'></td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	<tr>";
	divInnerHTML+="		<td colspan='2' style='border:1px solid #33517D;color:#989898;'>财政实发工资</td><td colspan='2' style='border:1px solid #33517D;color:#989898;'>"+data["netPayroll"]+"</td>";
	divInnerHTML+="	</tr>";
	divInnerHTML+="	</table>";
	divInnerHTML+="</div>";
	divInnerHTML += "</div>"; 
	
	$("#financeSalaryQry_cardDiv").append(divInnerHTML);
}


var yearSelect = {
		init : function(){
			var year = date.getFullYear();
			for(var y = 2014;y<year +1;y++){
				if(y == year){
					$("#financeSalaryQry_year").append("<option value='"+y+"' selected>"+y+"</option>");
				}else{
					$("#financeSalaryQry_year").append("<option value='"+y+"'>"+y+"</option>");
				}
			}
		}
}

var monthSelect = {
		init : function(){
			for(var i = 1;i<13;i++){
				var mon = date.getMonth()+1;
				if(i < 10){
					if(i == mon){
						$("#financeSalaryQry_smonth").append("<option value='0"+i+"' selected>0"+i+"</option>");
						$("#financeSalaryQry_emonth").append("<option value='0"+i+"' selected>0"+i+"</option>");
					}else{
						$("#financeSalaryQry_smonth").append("<option value='0"+i+"'>0"+i+"</option>");
						$("#financeSalaryQry_emonth").append("<option value='0"+i+"'>0"+i+"</option>");
					}
				}else{
					if(i == mon){
						$("#financeSalaryQry_smonth").append("<option value='"+i+"' selected>"+i+"</option>");
						$("#financeSalaryQry_emonth").append("<option value='"+i+"' selected>"+i+"</option>");
					}else{
						$("#financeSalaryQry_smonth").append("<option value='"+i+"'>"+i+"</option>");
						$("#financeSalaryQry_emonth").append("<option value='"+i+"'>"+i+"</option>");
					}
				}
			}
		}

}

$(function(){
	yearSelect.init();
	monthSelect.init();
});