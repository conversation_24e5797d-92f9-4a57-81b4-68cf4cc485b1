body { font:12px/1.2em Microsoft YaHei;}

p,h1,div {
    margin:0px;
    padding:0px;
}

.w6p {
    width:6%;
}
.w8p {
    width:8%;
}
.w12p {
    width:12%;
}
.w14p {
    width:14%;
}
.w17p {
    width:17%;    
}
.w28p {
    width:28%;
}
.w32p {
    width:32%;
}
.w30p {
    width:40%;
}
.w42p {
    width:42%;
}
.w52p {
    width:52%;    
}
.w60p {
    width:60%;    
}
.w100p {
    width:100%;
}
.color-red {
    color:red;    
}
.text-right {
    text-align: right;    
}
.text-center {
    text-align:center;
}

.row {
    display:table;
    width:100%;
    margin-top:10px;
}
.row2 {
    width:100%;
    margin-top:10px;
}
.row div {
    height:24px;
    line-height:24px;
    float: left;
}
.row div label {
    margin-right:6px;
}

/* 消息框插件 begin */
.bootstrap-growl .sr-only {
    display:none;
}
.alert-info {
    color: #3a87ad;
    background-color: #d9edf7;
    border-color: #bce8f1;
}
 .alert-error {
    color: #b94a48;
    background-color: #f2dede;
    border-color: #eed3d7;
}
.alert-success {
    color: #468847;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}
.alert {
    padding: 8px 35px 8px 14px;
    border-radius: 4px;
}
.alert .close {
    position: relative;
    top: -2px;
    right: -21px;
    line-height: 20px;
}
button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none;
}
.close {
    float: right;
    font-size: 20px;
    font-weight: bold;
    line-height: 20px;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .2;
    filter: alpha(opacity=20);
}
/* 消息框插件 end */

a,
a:link { color:#000; text-decoration:none; outline:none;}
a:hover,
a:focus { text-decoration:none; color:#39f;}

.pd3 { padding:3px;}
.pd5 { padding:5px;}
.pd10 { padding:10px;}

label,
button,
input,
select,
textarea { font-size:12px; vertical-align: middle;}

input,
button,
select,
textarea { font:12px/1.2em Microsoft YaHei;}

select,
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
input[type="radio"],
input[type="checkbox"] { cursor: pointer;}

.layout-header {
/*     height: 55px; */
/*     position: relative; */
/*     z-index: 0; */
/*     overflow: hidden; */
/*     border-bottom: 1px #95b8e7 solid; */
/*     background: url(../imgs/bg_header.jpg) bottom repeat-x; */
    
    height: 25px;
    line-height: 25px;
    background: rgb(127, 153, 190) url("../imgs/layout-browser-hd-bg.gif") repeat-x scroll center 50%;
    color: rgb(255, 255, 255);
}
.layout-header-right {
    float: right;
    padding-right: 20px;
}
.layout-header-right a {color:#fff}

.layout-sidebar { width:200px;}
.layout-side-tree .tree-node { padding:3px 0px; }
.layout-side-tree .tree-node-selected { padding:2px 0; border:1px #fade23 solid;}

.bill {
    margin-top:14px;
}