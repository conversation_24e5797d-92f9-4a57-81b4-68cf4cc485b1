package com.gg.grp.hkycg.utils;

import com.gg.grp.hkycg.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Configurable
public class CacheStatisticsUtils {

    private static CacheManager cacheManager;

    @Autowired
    private CacheManager instanceCacheManager;

    @PostConstruct
    public void init() {
        cacheManager = this.instanceCacheManager;
    }

    public static void clearStatisticsCache(){
        try {
            log.info("开始清除统计数据缓存");
            Cache cache = cacheManager.getCache("billStatistics");
            if (cache != null) {
                cache.clear();
                log.info("清除统计数据缓存成功");
            } else {
                log.warn("未找到统计数据缓存");
            }
        } catch (Exception e) {
            log.error("清除统计数据缓存异常", e);
        }
    }
}
