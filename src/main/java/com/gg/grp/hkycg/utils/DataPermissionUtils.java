package com.gg.grp.hkycg.utils;

import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.enums.DataPermission;
import com.gg.grp.hkycg.model.pojo.GpmData;
import com.gg.grp.hkycg.service.EmployeeService;
import com.gg.grp.hkycg.service.GpmUserdataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.model.pojo.Employee;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DataPermissionUtils {
    @Autowired
    private GpmUserdataService userdataService;

    @Autowired
    private EmployeeService employeeService;

    /**
     * 获取用户数据权限代码
     * @param zydm 职员代码
     * @return 数据权限代码
     */
    public String getUserDataPermission(String zydm) {
        try {
            GpmData gpmData = userdataService.getUserDataPermission(zydm);
            if (gpmData != null && StringUtils.isNotBlank(gpmData.getDatacode())) {
                return gpmData.getDatacode();
            }
        } catch (Exception e) {
            // 如果查询失败，返回默认权限
        }
        return DataPermission.ZY.getDatacode(); // 默认返回职员权限
    }

    /**
     * 获取当前登录用户的数据权限代码
     * @return 数据权限代码
     */
    public String getCurrentUserDataPermission() {
        String currentEmployeeCode = LoginInfo.getCurrEmployeeCode();
        if (StringUtils.isBlank(currentEmployeeCode)) {
            return DataPermission.ZY.getDatacode(); // 默认返回职员权限
        }
        return getUserDataPermission(currentEmployeeCode);
    }

    /**
     * 获取当前登录用户的部门代码
     * @return 部门代码
     */
    public String getCurrentUserDeptCode() {
        String currentEmployeeCode = LoginInfo.getCurrEmployeeCode();
        String gsdm = LoginInfo.getCurrCorpCode();
        String kjnd = LoginInfo.getCurrAccountantYear();

        if (StringUtils.isBlank(currentEmployeeCode) || StringUtils.isBlank(gsdm) || StringUtils.isBlank(kjnd)) {
            return null;
        }

        try {
            LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Employee::getEmployeeCode, currentEmployeeCode);
            queryWrapper.eq(Employee::getGsdm, gsdm);
            queryWrapper.eq(Employee::getKjnd, kjnd);

            Employee employee = employeeService.getOne(queryWrapper);
            if (employee != null) {
                return employee.getBmdm();
            }
        } catch (Exception e) {
            // 查询失败，返回null
        }

        return null;
    }

    /**
     * 检查用户是否有指定的数据权限
     * @param zydm 职员代码
     * @param requiredPermission 需要的权限
     * @return 是否有权限
     */
    public boolean hasDataPermission(String zydm, DataPermission requiredPermission) {
        String userPermission = getUserDataPermission(zydm);
        return requiredPermission.getDatacode().equals(userPermission);
    }

    /**
     * 检查当前用户是否有指定的数据权限
     * @param requiredPermission 需要的权限
     * @return 是否有权限
     */
    public boolean currentUserHasDataPermission(DataPermission requiredPermission) {
        String currentEmployeeCode = LoginInfo.getCurrEmployeeCode();
        if (StringUtils.isBlank(currentEmployeeCode)) {
            return false;
        }
        return hasDataPermission(currentEmployeeCode, requiredPermission);
    }
}
