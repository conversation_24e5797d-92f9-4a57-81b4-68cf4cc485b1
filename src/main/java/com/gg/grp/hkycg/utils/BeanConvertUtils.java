package com.gg.grp.hkycg.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gg.grp.hkycg.utils.ClassTool.getObjectByClass;

/**
 * 转换对象工具
 *
 * <AUTHOR>
 */
public class BeanConvertUtils extends BeanUtils {
    /**
     * 转换模式
     */
    public static ConvertMode convertMode = ConvertMode.both;
    public enum ConvertMode{
        /**
         * 使用setter和getter复制对象
         */
        setter(),
        /**
         * 使用属性复制对象
         */
        field(),
        /**
         * 两种方法都使用
         */
        both();
    }
    /**
     * 本地类必有的开始路径
     */
    private static final String BASE_CLASS_NAME = "com.gg.grp";

    /**
     * 将对象的各个属性赋值给目标对象
     *
     * @param source 源对象
     * @param targetSupplier 目标对象newer
     * @param <S> 源对象类
     * @param <T> 目标对象类
     * @return 与源对象的相同属性有相同值的目标对象
     */
    public static <S, T> T convertTo(S source, Supplier<T> targetSupplier) {
        return convertTo(source, targetSupplier, null);
    }

    /**
     * 将对象的各个属性赋值给目标对象
     * 附加回调方法的实现：{@link ConvertCallBack#callBack(Object, Object) callBack}
     * @param source         源对象
     * @param targetSupplier 目标对象供应方
     * @param callBack       回调方法
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象
     */
    public static <S, T> T convertTo(S source, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack) {
        if (null == source || null == targetSupplier) {
            return null;
        }

        T target = targetSupplier.get();
        convert(source, target);
        if (callBack != null) {
            callBack.callBack(source, target);
        }
        convertMode = ConvertMode.both;
        return target;
    }

    /**
     * 复制根据属性名称和类型复制对象属性
     * @param source 源对象
     * @param target 目标对象
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     */
    private static <S, T> void convertToByField(S source, T target) {
        Map<String, Field> sourceFields = Arrays.stream(source.getClass().getDeclaredFields())
                .collect(Collectors.toMap(Field::getName, field -> field));
        //获取目标对象属性
        Arrays.stream(target.getClass().getDeclaredFields())
                //与源对象同名的属性
                .filter(field -> sourceFields.containsKey(field.getName()))
                //与源对象同类型且类型内泛型相同的属性
                .filter(field -> {
                    String sourceField = sourceFields.get(field.getName()).getGenericType().getTypeName();
                    String typeName = field.getGenericType().getTypeName();
                    return sourceField.equals(typeName);
                })
                .forEach(targetField -> {
                    //获取源对象的本地属性对象
                    Field sourceField = sourceFields.get(targetField.getName());
                    try {
                        sourceField.setAccessible(true);
                        Object sourceObject = sourceField.get(source);
                        //关闭安全检查
                        targetField.setAccessible(true);
                        //将复制好的子对象放回目标对象
                        targetField.set(target,sourceObject);
                    }catch (ClassCastException | IllegalAccessException e) {
                        e.printStackTrace();
                    }
                });
    }

    /**
     * 复制对象的同类同名属性，递归复制其中的同名不同类但都在本项目中的属性
     * @param source 源对象
     * @param target 目标对象
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     */
    private static <S, T> void convertTo(S source, T target) {
        //源对象中的自定义类型属性
        Function<Field, String> getFieldKey = field -> field.getGenericType().getTypeName();
        Map<String, Field> sourceFields = Arrays.stream(source.getClass().getDeclaredFields())
                .filter(field -> getFieldKey.apply(field).contains(BASE_CLASS_NAME))
                .collect(Collectors.toMap(Field::getName, field -> field));
        //获取目标对象属性
        Stream<Field> targetFieldStream = Arrays.stream(target.getClass().getDeclaredFields())
                //与源对象同名的属性
                .filter(field -> sourceFields.containsKey(field.getName()))
                //与源对象的属性的类型或类型的泛型不同的属性
                .filter(field -> !getFieldKey.apply(sourceFields.get(field.getName())).equals(getFieldKey.apply(field)))
                //属性类型或属性类型中的泛型中是本地自定义的
                .filter(field -> field.getGenericType().getTypeName().contains(BASE_CLASS_NAME));
        targetFieldStream.forEach(targetField -> {
            // 获取源对象的本地属性对象
            Field sourceField = sourceFields.get(targetField.getName());
            // 如果
            if (targetField.getType().equals(sourceField.getType())
                    && sourceField.getType().isAssignableFrom(List.class)){
                try {
                    //关闭安全检查，获取源对象属性
                    sourceField.setAccessible(true);
                    List<?> sourceListFieldObjectList = (List<?>) sourceField.get(source);
                    //获取目标对象属性的第一个泛型类型
                    Type[] actualTypeArguments = ((ParameterizedTypeImpl)
                            targetField.getGenericType()).getActualTypeArguments();
                    Class<?> targetListClass = (Class<?>) actualTypeArguments[0];
                    List<?> targetFieldObject = convertListTo(sourceListFieldObjectList, targetListClass);
                    //关闭安全检查，设定目标对象属性
                    targetField.setAccessible(true);
                    targetField.set(target, targetFieldObject);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }else {
                //为目标对象的本地属性创造空对象
                Object targetFieldObject = getObjectByClass(targetField.getType());
                if (null == targetFieldObject){
                    return;
                }
                try {
                    //关闭安全检查
                    sourceField.setAccessible(true);
                    //获取源对象本地属性的对象
                    Object sourceFieldObject = sourceField.get(source);
                    //复制对象的同类同名属性
                    if (null == sourceFieldObject) {
                        return;
                    }
                    convert(sourceFieldObject, targetFieldObject);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                try {
                    //关闭安全检查
                    targetField.setAccessible(true);
                    //将复制好的子对象放回目标对象
                    targetField.set(target,targetFieldObject);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 转换列表中的源对象到目标类型的新列表
     * @param sourceListField 源对象的列表类型属性
     * @param targetClass 目标对象类型
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     * @return 目标对象类型
     */
    public static <S,T> List<T> convertListTo(List<S> sourceListField, Class<T> targetClass) {
        List<T> targetList = new ArrayList<>();
        if (null == sourceListField){
            return new ArrayList<>();
        }
        sourceListField.forEach(source->{
            T target = getObjectByClass(targetClass);
            if (null == target){
                return;
            }
            convert(source, target);

            targetList.add(target);
        });
        convertMode = ConvertMode.both;
        return targetList;
    }

    private static <S, T> void convert(S source, T target) {
        if (convertMode.equals(ConvertMode.setter)
                ||convertMode.equals(ConvertMode.both)) {
            //根据源的getter和目标的setter复制对象的同类同名属性
            copyProperties(source, target);
        }
        if (convertMode.equals(ConvertMode.setter)
                ||convertMode.equals(ConvertMode.both)) {
            //根据属性名复制对象
            convertToByField(source, target);
        }
        //复制属性的名称相同，但类型或者类型的泛型不同的属性
        convertTo(source, target);
    }


    /**
     * 将List中各个对象的各个属性赋值给目标对象，并将目标对象装入目标列表
     *
     * @param sources        源对象list
     * @param targetSupplier 目标对象供应方
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象list
     */
    public static <S, T> List<T> convertListTo(List<S> sources, Supplier<T> targetSupplier) {
        return convertListTo(sources, targetSupplier, null);
    }
    /**
     * 将IPave中recordsList中各个对象的各个属性赋值给目标对象，并将目标对象装入目标列表
     *
     * @param sources        源对象IPage
     * @param targetSupplier 目标对象供应方
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象list
     */
    public static <S, T> Page<T> convertPageTo(Page<S> sources, Supplier<T> targetSupplier) {
        return convertPageTo(sources, targetSupplier, null);
    }

    /**
     * 将List中各个对象的各个属性赋值给目标对象，并将目标对象装入目标列表
     * 附加回调方法的实现：{@link ConvertCallBack#callBack(Object, Object) callBack}
     * @param sources        源对象list
     * @param targetSupplier 目标对象供应方
     * @param callBack       回调方法
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象list
     */
    public static <S, T> List<T> convertListTo(List<S> sources, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack) {
        if (null == sources || null == targetSupplier) {
            return null;
        }

        List<T> list = new ArrayList<>(sources.size());
        for (S source : sources) {
            T target = targetSupplier.get();
            convert(source,target);
            if (callBack != null) {
                callBack.callBack(source, target);
            }
            list.add(target);
        }
        convertMode = ConvertMode.both;
        return list;
    }


    /**
     * 将List中各个对象的各个属性赋值给目标对象，并将目标对象装入目标列表
     * 附加回调方法的实现：{@link ConvertCallBack#callBack(Object, Object) callBack}
     * @param sources        源对象list
     * @param targetSupplier 目标对象供应方
     * @param callBack       回调方法
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象list
     */
    public static <S, T> Page<T> convertPageTo(Page<S> sources, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack){
        if (null == sources || null == targetSupplier){
            return null;
        }
        Page<T> page = new Page<>();
        page.setRecords(convertListTo(sources.getRecords(), targetSupplier, callBack));
        page.setTotal(sources.getTotal());
        return page;
    }

    /**
     * 回调接口
     *
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     */
    @FunctionalInterface
    public interface ConvertCallBack<S, T> {
        /**
         * 在赋值给目标对象后，再对目标对象或者源对象做的操作
         * @param s 源对象
         * @param t 目标对象
         */
        void callBack(S s, T t);
    }
}
