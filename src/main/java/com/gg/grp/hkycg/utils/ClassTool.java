package com.gg.grp.hkycg.utils;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public class ClassTool {

    /**
     * 根据类的无参构造方法获取其对象
     * @param type 类型
     * @param <C> 类型的类型
     * @return 类型的对象
     */
    public static <C> C getObjectByClass(Class<C> type) {
        try {
            Constructor<C> constructor = type.getConstructor();
            return constructor.newInstance();
        } catch (NoSuchMethodException | InvocationTargetException
                 | InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }
    public static Class<?> getEClass(Class<?> thisClass) {


        //get the Type Object of supper class
        Type superClassType = thisClass.getGenericSuperclass();
        ParameterizedType pt = (ParameterizedType)superClassType;

        //get the Generic Type array
        Type[] genTypeArr = pt.getActualTypeArguments();
        Type genType = genTypeArr[0];
        if (!(genType instanceof Class)){
            return Object.class;
        }else {
            return (Class<?>) genType;
        }
    }
}
