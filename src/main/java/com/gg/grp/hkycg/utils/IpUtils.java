package com.gg.grp.hkycg.utils;

import javax.servlet.http.HttpServletRequest;

/**
 * IP工具类
 * 用于获取客户端真实IP地址
 */
public class IpUtils {

    private static final String UNKNOWN = "unknown";
    private static final String LOCAL_IPV4 = "127.0.0.1";
    private static final String LOCAL_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HttpServletRequest
     * @return 客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            return LOCAL_IPV4;
        }

        String ip = null;

        // 1. X-Forwarded-For：代理服务器转发请求时的原始IP
        ip = request.getHeader("X-Forwarded-For");
        if (isValidIp(ip)) {
            // 多级代理的情况，取第一个IP
            int index = ip.indexOf(',');
            if (index != -1) {
                ip = ip.substring(0, index);
            }
            return ip.trim();
        }

        // 2. Proxy-Client-IP：Apache服务器代理
        ip = request.getHeader("Proxy-Client-IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 3. WL-Proxy-Client-IP：WebLogic服务器代理
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 4. HTTP_CLIENT_IP：代理服务器
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 5. HTTP_X_FORWARDED_FOR：代理服务器
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            return ip;
        }

        // 6. X-Real-IP：Nginx代理
        ip = request.getHeader("X-Real-IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 7. 最后使用request.getRemoteAddr()
        ip = request.getRemoteAddr();
        
        // IPv6本地地址转换为IPv4
        if (LOCAL_IPV6.equals(ip)) {
            ip = LOCAL_IPV4;
        }

        return ip != null ? ip : LOCAL_IPV4;
    }

    /**
     * 判断IP是否有效
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return ip != null && !ip.isEmpty() && !UNKNOWN.equalsIgnoreCase(ip);
    }

    /**
     * 判断是否为内网IP
     * 
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        try {
            String[] parts = ip.split("\\.");
            if (parts.length != 4) {
                return false;
            }

            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }

            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }

            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }

            // ********* - *************** (本地回环)
            if (first == 127) {
                return true;
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }
} 