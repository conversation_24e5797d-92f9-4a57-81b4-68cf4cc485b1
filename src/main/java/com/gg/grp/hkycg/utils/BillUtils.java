package com.gg.grp.hkycg.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 单据工具类
 */
public class BillUtils {
    
    /**
     * 根据单据号判断单据类型
     * @param billNo 单据号
     * @return 单据类型代码：CGJH, CGSQ, CGZB, CGJG, CGYS
     */
    public static String getBillTypeByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }
        
        // 根据单据号前缀判断类型
        if (billNo.startsWith("CGJH")) {
            return "CGJH"; // 采购计划
        } else if (billNo.startsWith("CGSQ")) {
            return "CGSQ"; // 采购申请
        } else if (billNo.startsWith("CGZB")) {
            return "CGZB"; // 采购招标
        } else if (billNo.startsWith("CGJG")) {
            return "CGJG"; // 采购结果
        } else if (billNo.startsWith("CGYS")) {
            return "CGYS"; // 采购验收
        }
        return null; // 无法判断
    }
} 