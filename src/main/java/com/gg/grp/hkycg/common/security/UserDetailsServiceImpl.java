package com.gg.grp.hkycg.common.security;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.model.pojo.Employee;
import com.gg.grp.hkycg.model.pojo.LoginEmployee;
import com.gg.grp.hkycg.service.EmployeeService;
import com.gg.grp.hkycg.common.consts.StaticValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.ArrayList;

/**
 * 用户详情服务实现类
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private EmployeeService employeeService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        String userCode;
        String businessDate = null;
        
        try {
            // 尝试解析JSON字符串
            JSONObject loginInfo = JSON.parseObject(username);
            userCode = loginInfo.getString("userCode");
            businessDate = loginInfo.getString("businessDate");
        } catch (Exception e) {
            // 如果不是JSON字符串，则直接使用原值作为userCode
            userCode = username;
        }
        
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Employee::getEmployeeCode, userCode);
        queryWrapper.eq(Employee::getGsdm, StaticValue.getGsdm());
        if (businessDate != null && !businessDate.trim().isEmpty()) {
            queryWrapper.eq(Employee::getKjnd, businessDate.substring(0, 4));
        }
        
        List<Employee> employees = employeeService.list(queryWrapper);
        
        if (employees == null || employees.isEmpty()) {
            throw new UsernameNotFoundException("用户不存在: " + userCode);
        }
        
        Employee employee = employees.get(0);

        // 创建LoginEmployee对象，提供空的permissions列表避免null异常
        return new LoginEmployee(employee, new ArrayList<>());
    }
} 