package com.gg.grp.hkycg.common.aop;

import com.gg.grp.hkycg.annotation.RateLimit;
import com.gg.grp.hkycg.exception.RateLimitException;
import com.gg.grp.hkycg.service.RateLimitService;
import com.gg.grp.hkycg.utils.IpUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 限流AOP切面
 * 实现基于注解的限流和IP黑白名单检查
 */
@Aspect
@Component
public class RateLimitAspect {

    @Autowired
    private RateLimitService rateLimitService;

    @Around("@annotation(com.gg.grp.hkycg.annotation.RateLimit)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            // 非HTTP请求，直接执行
            return joinPoint.proceed();
        }
        
        HttpServletRequest request = attributes.getRequest();
        String clientIp = IpUtils.getClientIp(request);
        
        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RateLimit rateLimit = method.getAnnotation(RateLimit.class);
        
        // 生成限流key
        String key = generateKey(rateLimit, method, clientIp);
        
        System.out.println("限流检查开始: IP=" + clientIp + ", Key=" + key);
        
        try {
            // 1. 检查IP黑白名单
            if (rateLimit.enableBlacklist()) {
                checkBlacklist(clientIp, key, rateLimit);
            }
            
            // 2. 执行限流检查
            RateLimitService.RateLimitResult result = rateLimitService.checkRateLimit(
                key, rateLimit.window(), rateLimit.maxRequests());
            
            if (!result.isAllowed()) {
                System.out.println("限流拦截: IP=" + clientIp + ", 剩余=" + result.getRemaining());
                throw new RateLimitException(rateLimit.message(), clientIp, key);
            }
            
            System.out.println("限流检查通过: IP=" + clientIp + ", 剩余=" + result.getRemaining());
            
            // 3. 执行目标方法
            Object result_method = joinPoint.proceed();
            
            // 4. 成功执行后，清除失败记录
            if (rateLimit.maxFailures() > 0) {
                rateLimitService.clearFailureRecord(clientIp);
            }
            
            return result_method;
            
        } catch (RateLimitException e) {
            // 限流异常直接抛出
            throw e;
        } catch (Exception e) {
            // 业务异常，记录失败次数
            if (rateLimit.maxFailures() > 0) {
                handleFailure(clientIp, rateLimit);
            }
            throw e;
        }
    }

    /**
     * 生成限流key
     */
    private String generateKey(RateLimit rateLimit, Method method, String clientIp) {
        String keyPrefix = rateLimit.key();
        if (keyPrefix.isEmpty()) {
            keyPrefix = method.getDeclaringClass().getSimpleName() + "." + method.getName();
        }
        return keyPrefix + ":" + clientIp;
    }

    /**
     * 检查IP黑白名单
     */
    private void checkBlacklist(String clientIp, String key, RateLimit rateLimit) {
        // 检查白名单
        if (rateLimitService.isInWhitelist(clientIp)) {
            System.out.println("IP在白名单中，跳过限流: " + clientIp);
            return;
        }
        
        // 检查黑名单
        if (rateLimitService.isInBlacklist(clientIp)) {
            System.out.println("IP在黑名单中，拒绝访问: " + clientIp);
            throw new RateLimitException("您的IP已被封禁，请联系管理员", clientIp, key);
        }
    }

    /**
     * 处理失败情况，记录失败次数并判断是否需要加入黑名单
     */
    private void handleFailure(String clientIp, RateLimit rateLimit) {
        try {
            int failureCount = rateLimitService.recordFailure(
                clientIp, rateLimit.maxFailures(), rateLimit.blacklistDuration());
            
            System.out.println("记录失败: IP=" + clientIp + ", 失败次数=" + failureCount);
            
            // 达到最大失败次数，加入黑名单
            if (failureCount >= rateLimit.maxFailures()) {
                String reason = "连续失败" + failureCount + "次，自动加入黑名单";
                rateLimitService.addToBlacklist(clientIp, rateLimit.blacklistDuration(), reason);
                System.out.println("IP已被加入黑名单: " + clientIp + ", 原因: " + reason);
            }
        } catch (Exception e) {
            System.err.println("处理失败记录异常: " + e.getMessage());
        }
    }
} 