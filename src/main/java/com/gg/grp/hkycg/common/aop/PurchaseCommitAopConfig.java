package com.gg.grp.hkycg.common.aop;

import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.mapper.PubAuditLogMapper;
import com.gg.grp.hkycg.mapper.PubWorkflowMapper;
import com.gg.grp.hkycg.mapper.GpmCgjhmlMapper;
import com.gg.grp.hkycg.mapper.GpmCgsqmlMapper;
import com.gg.grp.hkycg.mapper.GpmCgzbsqmlMapper;
import com.gg.grp.hkycg.mapper.GpmCgdjmlMapper;
import com.gg.grp.hkycg.mapper.GpmCgysmlMapper;
import com.gg.grp.hkycg.model.pojo.PubAuditLog;
import com.gg.grp.hkycg.model.pojo.PubObjFlow;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;
import com.gg.grp.hkycg.model.pojo.PubWorkflow;
import com.gg.grp.hkycg.model.pojo.GpmCgjhml;
import com.gg.grp.hkycg.model.pojo.GpmCgsqml;
import com.gg.grp.hkycg.model.pojo.GpmCgzbsqml;
import com.gg.grp.hkycg.model.pojo.GpmCgdjml;
import com.gg.grp.hkycg.model.pojo.GpmCgysml;
import com.gg.grp.hkycg.service.PubObjFlowService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购业务通用AOP配置
 * 支持采购计划、采购申请、招标申请和采购结果的工作流程创建逻辑
 */
@Slf4j
@Aspect
@Component
public class PurchaseCommitAopConfig {

    @Autowired
    private PubObjFlowService pubObjFlowService;

    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    @Autowired
    private PubWorkflowMapper pubWorkflowMapper;

    @Autowired
    private PubAuditLogMapper pubAuditLogMapper;

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;

    @Autowired
    private GpmCgsqmlMapper cgsqmlMapper;

    @Autowired
    private GpmCgzbsqmlMapper cgzbsqmlMapper;

    @Autowired
    private GpmCgdjmlMapper cgjgmlMapper;

    @Autowired
    private GpmCgysmlMapper cgysmlMapper;

    /**
     * 拦截采购计划、采购申请、招标申请、采购结果和采购验收的提交方法
     */
    @Pointcut(value = "execution(* com.gg.grp.hkycg.service.impl.CgjhServiceImpl.commitProxy(*, String, Double, ..)) || " +
                     "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.commitProxyBySqbh(*, String, Double, ..)) || " +
                     "execution(* com.gg.grp.hkycg.service.impl.CgzbsqServiceImpl.commitProxyByZbsqbh(*, String, Double, ..)) || " +
                     "execution(* com.gg.grp.hkycg.service.impl.CgdjServiceImpl.commitProxyByJgdjbh(*, String, Double, ..)) || " +
                     "execution(* com.gg.grp.hkycg.service.impl.CgysServiceImpl.commitProxyByYsbh(*, String, Double, ..))")
    private void purchaseCommitCut() {}

    /**
     * 采购业务提交的AOP拦截器
     * 实现完整的工作流程创建逻辑
     */
    @Around("purchaseCommitCut()")
    public Object purchaseCommitAround(ProceedingJoinPoint pjp) throws Throwable {
        Object[] args = pjp.getArgs();
        String gsdm = LoginInfo.getCurrCorpCode();
        String kjnd = LoginInfo.getCurrAccountantYear();
        BillnoType billType = (BillnoType) args[0];
        String djbh = args[1].toString();
        Double money = (Double) args[2];
        Boolean flag = false;

        // 判断业务类型
        String methodName = pjp.getSignature().getName();
        String businessType;
        if ("commitProxyBySqbh".equals(methodName)) {
            businessType = "采购申请";
            LambdaUpdateWrapper<GpmCgsqml> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(GpmCgsqml::getSqbh, djbh);
            GpmCgsqml cgjhml = cgsqmlMapper.selectOne(wrapper);
            if (cgjhml.getZt().equals("5")){
                flag = true;
            }
        } else if ("commitProxyByZbsqbh".equals(methodName)) {
            businessType = "招标申请";
            LambdaUpdateWrapper<GpmCgzbsqml> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(GpmCgzbsqml::getZbsqbh, djbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(wrapper);
            if (cgzbsqml.getZt().equals("5")){
                flag = true;
            }
        } else if ("commitProxyByJgdjbh".equals(methodName)) {
            businessType = "采购结果";
            LambdaUpdateWrapper<GpmCgdjml> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(GpmCgdjml::getJgdjbh, djbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(wrapper);
            if (cgjgml.getZt().equals("5")){
                flag = true;
            }
        } else if ("commitProxyByYsbh".equals(methodName)) {
            businessType = "采购验收";
            LambdaUpdateWrapper<GpmCgysml> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(GpmCgysml::getYsbh, djbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(wrapper);
            if (cgysml.getZt().equals("5")){
                flag = true;
            }
        } else {
            businessType = "采购计划";
            LambdaUpdateWrapper<GpmCgjhml> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(GpmCgjhml::getJhbh, djbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(wrapper);
            if (cgjhml.getZt().compareTo(BigDecimal.ZERO)==0){
                flag = true;
            }
        }

        try {
            log.info("开始处理{}提交流程，标识符：{}", businessType, djbh);

            // 1. 删除已存在的流程数据
            if (!flag){
                pubObjFlowService.deleteByCon(billType.getModCode(), billType.getCode().toString(), djbh);
            }

            // 2. 调用原始方法（更新状态等）
            Map<Integer, List<PubObjFlowTemp>> nodeMap = (Map<Integer, List<PubObjFlowTemp>>) pjp.proceed(args);

            // 3. 处理工作流程模板节点，按节点代码排序
            Map<Integer, List<PubObjFlowTemp>> sortedNodeMap = new TreeMap<>(nodeMap);

            // 4. 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    billType.getModCode(), billType.getCode().toString());
            
            // 根据业务类型设置默认流程配置
            String flowCode, flowName, bizName;
            if ("采购申请".equals(businessType)) {
                flowCode = "CGSQ_FLOW_001";
                flowName = "采购申请审核流程";
                bizName = "采购申请";
            } else if ("招标申请".equals(businessType)) {
                flowCode = "CGZBSQ_FLOW_001";
                flowName = "招标申请审核流程";
                bizName = "招标申请";
            } else if ("采购结果".equals(businessType)) {
                flowCode = "CGJG_FLOW_001";
                flowName = "采购结果审核流程";
                bizName = "采购结果";
            } else if ("采购验收".equals(businessType)) {
                flowCode = "CGYS_FLOW_001";
                flowName = "采购验收审核流程";
                bizName = "采购验收";
            } else {
                flowCode = "CGJH_FLOW_001";
                flowName = "采购计划审核流程";
                bizName = "采购计划";
            }
            
            if (workflow != null) {
                flowCode = workflow.getFlowcode();
                flowName = workflow.getFlowname();
                bizName = workflow.getBizname();
            }

            // 5. 创建工作流程实例
            if (!flag){
                createWorkflowInstance(sortedNodeMap, billType, djbh, flowCode, gsdm, kjnd);
            }

            // 6. 创建提交审核日志
            createSubmitAuditLog(billType, djbh, money, workflow, flowCode, flowName, bizName, gsdm, kjnd);

            log.info("{}提交流程处理完成，标识符：{}", businessType, djbh);
            return sortedNodeMap;

        } catch (Exception e) {
            log.error("处理{}提交流程失败，标识符：{}，错误：{}", businessType, djbh, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 采购业务提交后置处理
     * 根据业务类型分别更新对应的表
     */
    @AfterReturning(value = "purchaseCommitCut()", returning = "res")
    public void purchaseCommitAfterAround(JoinPoint joinPoint, Object res) {
        try {
            Map<Integer, List<PubObjFlowTemp>> listMap = (Map<Integer, List<PubObjFlowTemp>>) res;
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new TreeMap<>(listMap);

            Object[] args = joinPoint.getArgs();
            BillnoType billnoType = (BillnoType) args[0];
            String flowId = args[1].toString();

            // 判断业务类型
            String methodName = joinPoint.getSignature().getName();
            String businessType;
            if ("commitProxyBySqbh".equals(methodName)) {
                businessType = "采购申请";
            } else if ("commitProxyByZbsqbh".equals(methodName)) {
                businessType = "招标申请";
            } else if ("commitProxyByJgdjbh".equals(methodName)) {
                businessType = "采购结果";
            } else if ("commitProxyByYsbh".equals(methodName)) {
                businessType = "采购验收";
            } else {
                businessType = "采购计划";
            }

            // 获取第一个审核节点
            int nextNode = nodeMap.keySet().stream()
                    .min(Integer::compareTo)
                    .orElse(1); // 如果没有节点，默认为1

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    billnoType.getModCode(), billnoType.getCode().toString());

            String flowCode = workflow != null ? workflow.getFlowcode() : getDefaultFlowCode(businessType);

            // 根据业务类型进行后置处理
            switch (businessType) {
                case "采购申请":
                    updateCgsqAfterCommit(flowId, flowCode, nextNode);
                    break;
                case "招标申请":
                    updateCgzbsqAfterCommit(flowId, flowCode, nextNode);
                    break;
                case "采购结果":
                    updateCgjgAfterCommit(flowId, flowCode, nextNode);
                    break;
                case "采购验收":
                    updateCgysAfterCommit(flowId, flowCode, nextNode);
                    break;
                default:
                    updateCgjhAfterCommit(flowId, flowCode, nextNode);
                    break;
            }

            log.info("{}提交后置处理完成，标识符：{}，当前审核节点：-9，下一审核节点：{}，流程代码：{}", 
                    businessType, flowId, nextNode, flowCode);

        } catch (Exception e) {
            log.error("采购业务提交后置处理失败，错误：{}", e.getMessage(), e);
            throw new RuntimeException("采购业务提交后置处理失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新采购申请提交后的状态和工作流程字段
     */
    private void updateCgsqAfterCommit(String sqbh, String flowCode, int nextNode) {
        // 更新采购申请状态为已提交(2)
        LambdaUpdateWrapper<GpmCgsqml> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GpmCgsqml::getSqbh, sqbh)
                .set(GpmCgsqml::getZt, "2"); // 2-已提交状态

        int result = cgsqmlMapper.update(null, updateWrapper);
        
        log.info("采购申请状态已更新为已提交(2)，sqbh：{}，流程代码：{}，下一审核节点：{}", sqbh, flowCode, nextNode);
    }

    /**
     * 更新招标申请提交后的状态和工作流程字段
     */
    private void updateCgzbsqAfterCommit(String zbsqbh, String flowCode, int nextNode) {
        // 更新招标申请状态为已提交(2)
        LambdaUpdateWrapper<GpmCgzbsqml> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh)
                .set(GpmCgzbsqml::getZt, "2") // 2-已提交状态
                .set(GpmCgzbsqml::getXgsj, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())); // 更新修改时间

        int result = cgzbsqmlMapper.update(null, updateWrapper);
        
        log.info("招标申请状态已更新为已提交(2)，zbsqbh：{}，流程代码：{}，下一审核节点：{}", zbsqbh, flowCode, nextNode);
    }

    /**
     * 更新采购结果提交后的状态和工作流程字段
     */
    private void updateCgjgAfterCommit(String jgdjbh, String flowCode, int nextNode) {
        // 更新采购结果状态为已提交(2)
        LambdaUpdateWrapper<GpmCgdjml> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh)
                .set(GpmCgdjml::getZt, "2") // 2-已提交状态
                .set(GpmCgdjml::getUpdateTime, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())); // 更新修改时间

        int result = cgjgmlMapper.update(null, updateWrapper);
        
        log.info("采购结果状态已更新为已提交(2)，jgdjbh：{}，流程代码：{}，下一审核节点：{}", jgdjbh, flowCode, nextNode);
    }

    /**
     * 更新采购计划提交后的状态和工作流程字段
     */
    private void updateCgjhAfterCommit(String djbh, String flowCode, int nextNode) {
        LambdaUpdateWrapper<GpmCgjhml> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GpmCgjhml::getJhbh, djbh)
                .set(GpmCgjhml::getZt, new BigDecimal("2")) // 2-已提交状态
                .set(GpmCgjhml::getFlowcode, flowCode) // 设置流程代码
                .set(GpmCgjhml::getAshjd, -9) // 当前审核节点
                .set(GpmCgjhml::getAxshjd, nextNode) // 下一审核节点
                .set(GpmCgjhml::getAshr, "") // 审核人
                .set(GpmCgjhml::getAshrId, -1) // 审核人ID
                .set(GpmCgjhml::getAshRq, ""); // 审核日期

        cgjhmlMapper.update(null, updateWrapper);
    }

    /**
     * 更新采购验收提交后的状态和工作流程字段
     */
    private void updateCgysAfterCommit(String ysbh, String flowCode, int nextNode) {
        // 更新采购验收状态为已提交(2)
        LambdaUpdateWrapper<GpmCgysml> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GpmCgysml::getYsbh, ysbh)
                .set(GpmCgysml::getZt, "2") // 2-已提交状态
                .set(GpmCgysml::getUpdateTime, new Date()) // 更新修改时间
                .set(GpmCgysml::getUpdateUser, LoginInfo.getCurrEmployeeName()); // 更新修改人

        int result = cgysmlMapper.update(null, updateWrapper);
        
        log.info("采购验收状态已更新为已提交(2)，ysbh：{}，流程代码：{}，下一审核节点：{}", ysbh, flowCode, nextNode);
    }

    /**
     * 根据业务类型获取默认流程代码
     */
    private String getDefaultFlowCode(String businessType) {
        switch (businessType) {
            case "采购申请":
                return "CGSQ_FLOW_001";
            case "招标申请":
                return "CGZBSQ_FLOW_001";
            case "采购结果":
                return "CGJG_FLOW_001";
            case "采购验收":
                return "CGYS_FLOW_001";
            default:
                return "CGJH_FLOW_001";
        }
    }

    /**
     * 创建工作流程实例
     */
    private void createWorkflowInstance(Map<Integer, List<PubObjFlowTemp>> nodeMap, 
                                      BillnoType billType, String flowId, String flowCode,
                                      String gsdm, String kjnd) {
        List<Integer> keys = nodeMap.keySet().stream().sorted().collect(Collectors.toList());
        
        for (int i = 0; i < keys.size(); i++) {
            List<PubObjFlowTemp> models = nodeMap.get(keys.get(i));
            StringBuilder shrid = new StringBuilder(",");
            StringBuilder shrxm = new StringBuilder(",");

            models.forEach(model -> {
                shrid.append(model.getShrdm()).append(",");
                shrxm.append(model.getShrxm()).append(",");
            });

            // 创建工作流程实例
            PubObjFlow pubObjFlow = new PubObjFlow();
            pubObjFlow.setPofId(UUID.randomUUID().toString().replace("-", "").substring(0, 20));
            pubObjFlow.setGsdm(gsdm);
            pubObjFlow.setKjnd(kjnd);
            pubObjFlow.setModcode(billType.getModCode());
            pubObjFlow.setDjlx(billType.getCode().toString());
            pubObjFlow.setDjh(flowId); // 采购计划用DJBH，采购申请用SQBH，招标申请用ZBSQBH，采购结果用JGDJBH
            pubObjFlow.setFlowcode(flowCode);
            pubObjFlow.setShr1(shrid.toString());
            
            String xm = shrxm.length() > 0 ? shrxm.substring(0, shrxm.length() - 1) : "";
            pubObjFlow.setShrmc(xm);

            // 当前节点
            pubObjFlow.setAuditFlag(models.get(0).getXh());

            // 获取下一个节点
            String auditAftFlag = "-1";
            if (i + 1 < keys.size()) {
                auditAftFlag = nodeMap.get(keys.get(i+1)).get(0).getXh();
            }
            
            pubObjFlow.setAuditAftFlag(auditAftFlag);
            pubObjFlow.setIsaudit("0");
            pubObjFlow.setSpecificCheckPerson("");
            pubObjFlow.setNodeName(models.get(0).getJdmc());
            
            pubObjFlowService.insert(pubObjFlow);
        }
    }

    /**
     * 创建提交审核日志
     */
    private void createSubmitAuditLog(BillnoType billType, String flowId, Double money, 
                                    PubWorkflow workflow, String flowCode, String flowName, 
                                    String bizName, String gsdm, String kjnd) {
        PubAuditLog auditLog = new PubAuditLog();
        auditLog.setGsdm(gsdm);
        auditLog.setKjnd(kjnd);

        // 获取新的日志ID
        Integer newLogID = pubAuditLogMapper.selectMaxLogID();
        if (newLogID == null) {
            newLogID = 1;
        }
        auditLog.setLogid(newLogID.longValue());

        // 设置单据信息
        auditLog.setBillid(flowId); // 采购计划用DJBH，采购申请用SQBH，招标申请用ZBSQBH，采购结果用JGDJBH
        auditLog.setBillname(bizName);
        auditLog.setFlowcode(flowCode);
        auditLog.setFlowname(flowName);
        auditLog.setModname(billType.getModCode());
        auditLog.setBizname(bizName);

        // 设置审核节点信息（-9表示送审人）
        auditLog.setNodeseq(-9);
        auditLog.setNodename("送审人");

        // 设置审核人信息
        auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
        auditLog.setAuditor(LoginInfo.getCurrEmployeeName());
        auditLog.setCertigierid(0);
        auditLog.setCertigier("");

        // 设置审核时间
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmss");
        auditLog.setAdatetime(df1.format(new Date()));

        // 设置金额和备注
        auditLog.setAmt(money != null ? new BigDecimal(money) : BigDecimal.ZERO);
        auditLog.setRemark("");
        auditLog.setAtype("送审");

        // 设置服务器时间
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        auditLog.setServDateTime(df2.format(new Date()));

        // 设置计算机信息
        try {
            String computerMsg = java.net.InetAddress.getLocalHost().getHostName()
                    + "/" + java.net.InetAddress.getLocalHost().getHostAddress();
            auditLog.setComputerName(computerMsg);
        } catch (Exception e) {
            auditLog.setComputerName("Unknown");
        }

        // 保存审核日志
        pubAuditLogMapper.insert(auditLog);
    }
} 