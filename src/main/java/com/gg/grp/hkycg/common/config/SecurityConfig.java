package com.gg.grp.hkycg.common.config;

import com.gg.grp.hkycg.common.security.UserDetailsServiceImpl;
import com.gg.grp.hkycg.common.security.JwtAuthenticationEntryPoint;
import com.gg.grp.hkycg.common.security.CustomAuthenticationProvider;
import com.gg.grp.hkycg.common.security.UrlAccessDecisionManager;
import com.gg.grp.hkycg.common.security.UrlFilterInvocationSecurityMetadataSource;
import com.gg.grp.hkycg.filter.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

/**
 * Spring Security配置类
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private JwtAuthenticationEntryPoint authenticationEntryPoint;

    @Autowired
    private JwtAuthenticationFilter authenticationFilter;
    
    @Autowired
    @Lazy
    private CustomAuthenticationProvider customAuthenticationProvider;
    
    @Autowired
    @Lazy
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UrlFilterInvocationSecurityMetadataSource urlFilterInvocationSecurityMetadataSource;

    @Autowired
    private UrlAccessDecisionManager urlAccessDecisionManager;

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        // 配置自定义认证Provider
        auth.authenticationProvider(customAuthenticationProvider);
        // 保留默认的UserDetailsService认证（用于JWT验证）
        auth.userDetailsService(userDetailsService).passwordEncoder(passwordEncoder);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.formLogin().disable()
                .logout().disable()
                .httpBasic().disable()
                .sessionManagement().disable()
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .csrf().disable()
                .anonymous().and()
                .exceptionHandling().authenticationEntryPoint(authenticationEntryPoint).and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .authorizeRequests()
                // 后端API路径：完全放行，不进行权限拦截
                .antMatchers("/api/**").permitAll()
                .antMatchers("/upload/**","/api/attachment/download/**", "/api/attachment/preview/**").permitAll()
                .antMatchers("/doc.html","/swagger-ui.html").permitAll()
                .antMatchers("/v3/api-docs/**","/api/v3/api-docs/**").permitAll()
                // 基础功能路径：允许访问
                .antMatchers("/login", "/loginOut", "/test-user", "/test-redis", "/redis-pool-status").permitAll()
                .antMatchers("/test/public").permitAll()
                .antMatchers("/swagger-ui/**", "/swagger-resources/**", "/v2/api-docs", "/webjars/**").permitAll()
                
                // 静态资源：允许访问
                .antMatchers("/static/**", "/assets/**", "/css/**", "/js/**", "/images/**", "/favicon.ico").permitAll()
                .antMatchers("/index.html", "/WEB-INF/**").permitAll()
                
                // 前端页面路径：需要权限验证（非/api开头的路径）
//                .anyRequest().authenticated();
                .anyRequest().permitAll();

        // 创建自定义的权限验证过滤器，只对前端页面进行权限控制
        FilterSecurityInterceptor frontendSecurityInterceptor = new FilterSecurityInterceptor();
        frontendSecurityInterceptor.setSecurityMetadataSource(urlFilterInvocationSecurityMetadataSource);
        frontendSecurityInterceptor.setAccessDecisionManager(urlAccessDecisionManager);
        
        http.addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        config.setAllowCredentials(true);
        config.addExposedHeader("Authorization");
        source.registerCorsConfiguration("/**", config);
        return source;
    }
} 