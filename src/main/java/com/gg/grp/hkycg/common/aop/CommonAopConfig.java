package com.gg.grp.hkycg.common.aop;

import com.alibaba.fastjson.JSONArray;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.tool.StringTool;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Aspect
//@Component  // 禁用旧版本AOP，使用CommonTransactionAopConfig
public class CommonAopConfig {

    @Autowired
    private DataSourceTransactionManager transactionManager;
//
//    @Autowired
//    private IPubObjFlowService pubObjFlowService;
//
//    @Autowired
//    private IPubworkflowService pubworkflowService;
//
//    @Autowired
//    private PAOSHFlowMapper paoSHFlowMapper;
//
//    @Autowired
//    private IOERBillHVOService hvoService;
//
//    @Autowired
//    private PubauditlogMapper pubauditlogMapper;
//
//    @Autowired
//    private IIndexUseService indexUseService;

    @Pointcut(value = "execution(* com.gg.grp.*.controller.*.*(..))")
    public void transactionalCut(){};
//
//    @Pointcut(value = "execution(* com.gg.grp.*.controller.*.*checkParam*(..))")
//    public void validCut(){};
//
//    @Pointcut(value = "execution(* com.gg.grp.*.service.impl.*.commitProxy(*, String, Double, ..))")
//    private void commitCut(){};
//
//    /**
//     * 使用oer_djml的提交后置
//     */
//    @Pointcut(value = "execution(* com.gg.grp.income.service.impl.*.commitProxy(*, String, Double, ..))" +
//            "|| execution(* com.gg.grp.reimbursement.service.impl.DrawMoneyServiceImpl.commitProxy(*, String, Double, ..))" +
//            "|| execution(* com.gg.grp.reimbursement.service.impl.ContractPaymentCounterSignServiceImpl.commitProxy(*, String, Double, ..))" +
//            "|| execution(* com.gg.grp.reimbursement.service.impl.TravelApprovalServiceImpl.commitProxy(*, String, Double, ..))" +
//            "|| execution(* com.gg.grp.reimbursement.service.impl.CashLoanServiceImpl.commitProxy(*, String, Double, ..))" +
//            "|| execution(* com.gg.grp.salary.service.impl.OutSchoolServiceImpl.commitProxy(*, String, Double, ..))" +
//            "|| execution(* com.gg.grp.salary.service.impl.ScholarshipServiceImpl.commitProxy(*, String, Double, ..))")
//    private void hvoCommitAfterCut(){};

    @Around("transactionalCut()")
    public Object commitAround(ProceedingJoinPoint joinPoint){
        String requestURI = logInfo(joinPoint);
        DefaultTransactionDefinition transactionDefinition  = new DefaultTransactionDefinition();
        //开启事务(隔离级别为新建事务)
        transactionDefinition.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        Object result;
        try{
            result = joinPoint.proceed(joinPoint.getArgs());
            transactionManager.commit(transactionStatus);
        } catch (Throwable e){
            //异常回滚
            transactionManager.rollback(transactionStatus);
            log.error("请求：request:"+requestURI+" 失败。。failed..",  e);
            throw new GlobalException(500, e.getMessage());
        }
        log.info("请求：request:{} 完成。。completed..", requestURI);
        return result;
    }

//    @Before("validCut()")
//    public void beforeFunc(JoinPoint point){
//        Object[] objects = point.getArgs();
//        for(Object object: objects){
//            BindingResult bind = object instanceof BindingResult ? (BindingResult) object : null;
//            if(bind != null && bind.hasErrors()){
//                List<FieldError> errors = bind.getFieldErrors();
//                StringBuilder stringBuffer = new StringBuilder();
//                for(FieldError error : errors){
//                    stringBuffer.append(error.getDefaultMessage()).append(",");
//                }
//                throw new GlobalException(500, stringBuffer.substring(0, stringBuffer.length() - 1));
//            }
//        }
//    }

//    @Around("commitCut()")
//    public Object billCommitAround(ProceedingJoinPoint pjp) throws Throwable {
//        Object[] args = pjp.getArgs();
//        String gsdm = LoginInfo.getCurrCorpCode();
//        String kjnd = LoginInfo.getCurrAccountantYear();
//        BillnoType billType = (BillnoType) args[0];
//        String mlid = args[1].toString();
//        pubObjFlowService.deleteByCon(billType.getModCode(), billType.getCode(), mlid);
//
//        Map<Integer, List<PubObjFlowTempModel>> listMap = (Map<Integer, List<PubObjFlowTempModel>>) pjp.proceed(args);
//        Map<Integer, List<PubObjFlowTempModel>> nodeMap = new TreeMap<>(listMap);
//
//        PubworkflowModel pubworkflowModel = pubworkflowService.findByCon(billType.getModCode(), billType.getCode());
//        String flowCode = pubworkflowModel.getFlowcode();
//        List<Integer> keys = new ArrayList<>(nodeMap.keySet());
//        for (int i = 0; i < keys.size(); i++) {
//            List<PubObjFlowTempModel> models = nodeMap.get(keys.get(i));
//            StringBuilder shrid = new StringBuilder(",");
//            StringBuilder shrxm = new StringBuilder();
//            models.forEach(model -> {
//                shrid.append(model.getShrdm()).append(",");
//                shrxm.append(model.getShrxm()).append(",");
//            });
//
//            //4.储存 PUB_OBJ_FLOW
//            PubObjFlowModel pubObjFlowModel = new PubObjFlowModel();
//            pubObjFlowModel.setPofId(UUID.randomUUID().toString().toUpperCase());
//            pubObjFlowModel.setGsdm(gsdm);
//            pubObjFlowModel.setKjnd(kjnd);
//            pubObjFlowModel.setModcode(billType.getModCode());
//            pubObjFlowModel.setDjlx(String.valueOf(billType.getCode()));
//            pubObjFlowModel.setDjh(mlid);
//            pubObjFlowModel.setFlowcode(flowCode);
//
//            pubObjFlowModel.setShr1(shrid.toString());
//            String xm = shrxm.substring(0, shrxm.length() - 1);
//            pubObjFlowModel.setShrmc(xm);
//
//            //当前节点
//            pubObjFlowModel.setAuditFlag(String.valueOf(keys.get(i)));
//
//            //获取下一个节点，如果本节点审核人和下一审核节审核人都是一个且是同一个人，去掉下一个节点
//            String auditAftFlag = "-1";
//            for (int j = i + 1; j < keys.size(); j++) {
////                // 获取下一个审核节点
////                List<PubObjFlowTempModel> nextModels = nodeMap.get(keys.get(j));
////                if(models.size() == 1 && nextModels.size() == 1
////                        && models.get(0).getShrdm().equals(nextModels.get(0).getShrdm()) && !nextModels.get(0).getCountersignFlag()){
////                    keys.remove(j);
////                    j--;
////                    continue;
////                }
//                auditAftFlag = String.valueOf(keys.get(j));
//                break;
//            }
//
//            pubObjFlowModel.setAuditAftFlag(auditAftFlag);
//            pubObjFlowModel.setIsaudit("0");
//            pubObjFlowModel.setSpecificCheckPerson("");
//            pubObjFlowModel.setNodeName(models.get(0).getJdmc());
//            pubObjFlowModel.setCountersignFlag(models.get(0).getCountersignFlag());
//            pubObjFlowService.insert(pubObjFlowModel);
//
//            //只插入向 PAO_SHFlow 插入最后一条审核信息
//            if (i == (keys.size() - 1)) {
//                paoSHFlowMapper.deletePAOSHFlowByMXDH(mlid);
//                PAOSHFlowModel paoShowModel = new PAOSHFlowModel();
//                paoShowModel.setGsdm(gsdm);
//                paoShowModel.setMxdh(mlid);
//                paoShowModel.setCzyid("1");
//                paoShowModel.setCzyidcs("5");
//                paoShowModel.setCzyname("审批节点");
//                paoShowModel.setBizcode("LX" + org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(billType.getCode()), 5, "0"));
//                paoShowModel.setFlowcode(flowCode);
//                paoShowModel.setLnodeid(1);
//                paoShowModel.setLnodename("部门领导审批");
//                paoShowModel.setLshrid(shrid.toString());
//                paoShowModel.setLshrname(shrxm.toString());
//                paoShowModel.setLzt("1");
//                paoShowModel.setRnodeid(1000);
//                paoShowModel.setRnodename("已终审");
//                paoShowModel.setRzt("2");
//                paoShowModel.setOpertype("审核");
//                paoShowModel.setFzrsign(0);
//                paoSHFlowMapper.insert(paoShowModel);
//            }
//        }
//
//        double money = (Double) args[2];
//        //储存日志表PUBAUDITLOG
//        PubauditlogModel pubauditlogModel = new PubauditlogModel();
//        pubauditlogModel.setGsdm(gsdm);
//        pubauditlogModel.setKjnd(kjnd);
//
//        Integer newLogID = pubauditlogMapper.selectMaxLogID();
//        if (newLogID == null) {
//            newLogID = 1;
//        }
//        pubauditlogModel.setLogid(newLogID);
//        //储存网页端的单据编号
//        pubauditlogModel.setBillid(mlid);
//        pubauditlogModel.setBillname(pubworkflowModel.getBizname());
//        pubauditlogModel.setFlowcode(flowCode);
//        pubauditlogModel.setFlowname(pubworkflowModel.getFlowname());
//        pubauditlogModel.setModname(billType.getModCode());
//        pubauditlogModel.setBizname(pubworkflowModel.getBizname());
//        //当前审核节点号
//        pubauditlogModel.setNodeseq(-9);
//        //获取当前审核节点名称
////        String nodeName = pubwfnodeService.getNodeNameByNodeCode(flowCode, -9);
//
//        pubauditlogModel.setNodename("送审人");
//
//        pubauditlogModel.setAuditorid(LoginInfo.getCurrEmployeeCode());
//        pubauditlogModel.setAuditor(LoginInfo.getCurrEmployeeName());
//        pubauditlogModel.setCertigierid(0);
//        pubauditlogModel.setCertigier("");
//        //设置日期格式
//        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmss");
//        pubauditlogModel.setAdatetime(df1.format(new Date()));
//
//
//        pubauditlogModel.setAmt(money);
//        pubauditlogModel.setRemark("");
//        pubauditlogModel.setAtype("送审");
//
//        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        pubauditlogModel.setServDateTime(df2.format(new Date()));
//
//        //获取本机信息
//        try {
//            // 接收到请求，记录请求内容
//            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//            //获取请求的request
//            HttpServletRequest request = attributes.getRequest();
//            String ip =Doc2Html.getIP(request);
//            System.out.println(ip);
//            String computerMsg = InetAddress.getLocalHost().getHostName()
//                    .concat("/")
//                    .concat(InetAddress.getLocalHost().getHostAddress());
//            pubauditlogModel.setComputerName(computerMsg);
//        } catch (Exception e) {
//            throw new RuntimeException("获取本机信息异常");
//        }
//
//        pubauditlogMapper.insert(pubauditlogModel);
//
//        return nodeMap;
//    }
//
//    @AfterReturning(value = "hvoCommitAfterCut()", returning = "res")
//    public void billCommitAfterAround(JoinPoint joinPoint, Object res) {
//        Map<Integer, List<PubObjFlowTempModel>> listMap = (Map<Integer, List<PubObjFlowTempModel>>) res;
//        Map<Integer, List<PubObjFlowTempModel>> nodeMap = new TreeMap<>(listMap);
//
//        Object[] args = joinPoint.getArgs();
//        BillnoType billnoType = (BillnoType) args[0];
//        int nextNode = nodeMap.keySet().stream()
//                .min(Integer::compareTo)
//                .orElseThrow(() -> new GlobalException(400, "未找到单据类型:" + billnoType.getComment() + "的审核节点"));
//
//        String mlid = args[1].toString();
//        OERBillHVOModel model = hvoService.queryHVOModel(mlid, null);
//        OERBillHVOModel hvoModel = new OERBillHVOModel();
//
//        PubworkflowModel pubworkflowModel = pubworkflowService.findByCon(billnoType.getModCode(), billnoType.getCode());
//
//        //更新OER_DJML
//        hvoModel.setCurSHJD(-9);
//        //第一个审核节点
//        hvoModel.setNextSHJD(nextNode);
//        hvoModel.setSHR("");
//        hvoModel.setSHRID("-1");
//        hvoModel.setSHRQ("");
//        hvoModel.setBillState("2");
//        hvoModel.setFlowCode(pubworkflowModel.getFlowcode());
//
//        hvoModel.setMoney(model.getMoney());
////        hvoModel.setWHJE(model.getWHJE());
//        hvoService.updateOne(hvoModel, mlid);
//
//        indexUseService.updateDjztByMlid(mlid, "1");
//    }
    /**
     * 日志信息整理
     * @param joinPoint 请求参数
     * @return 请求的uri
     */
    private String logInfo(ProceedingJoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String remoteAddr = request.getRemoteAddr();
            String remoteHost = request.getRemoteHost();
            String requestURI = request.getRequestURI();
            UUID uuid = UUID.randomUUID();
            String uuidString = uuid.toString().replace("-", "");
            Thread.currentThread().setName(uuidString);
            Object[] args = joinPoint.getArgs();
            if (null != LoginInfo.getLoginInfo()) {
                String gsdm = LoginInfo.getCurrCorpCode();
                String kjnd = LoginInfo.getCurrBusiDate();
                String zy = StringTool.getMergeString(LoginInfo.getCurrEmployeeCode(),
                        LoginInfo.getCurrEmployeeName());
                String loginInfo = gsdm + " " + kjnd + zy;
                JSONArray argsJson = new JSONArray(Collections
                        .singletonList(Arrays.stream(args)
                                .filter(arg -> (!(arg instanceof HttpServletResponse
                                        || arg instanceof HttpServletRequest)))
                                .collect(Collectors.toList())));
                log.info("{}的{}用户：user:[{}] 使用事务：use transaction:[{}] 请求：request:{} 接到参数：{}",
                        remoteAddr, remoteHost, loginInfo, uuidString, requestURI, argsJson);
            }
            return requestURI;
        }catch (Exception e){
            log.warn("请求信息获取异常", e);
            return "";
        }
    }
}
