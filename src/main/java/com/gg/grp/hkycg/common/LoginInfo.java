package com.gg.grp.hkycg.common;

import lombok.Data;

/**
 * 全局登录信息类
 *
 * <AUTHOR>
 * @version $Id: LoginInfo.java, v 0.1 2018年4月15日 下午3:00:59 GuYuanyuan Exp $
 */
@Data
public class LoginInfo {

    private static final ThreadLocal<LoginInfo> holder = new ThreadLocal<LoginInfo>();

    //设置线程数据
    public static void setLoginInfo(LoginInfo loginInfo) {
        //holder.
        holder.set(loginInfo);
    }

    //获取线程数据
    public static LoginInfo getLoginInfo() {
        LoginInfo loginInfo = holder.get();
        return loginInfo;
    }

    //销毁线程数据
    public static void loginOut() {
        holder.remove();
    }

    public static String getCurrAccountantYear() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getAccountantYear();
        }
    }

    public static void setCurrAccountantYear(String kjnd) {
        LoginInfo loginInfo = getLoginInfo();
        if (null != loginInfo) {
            loginInfo.setAccountantYear(kjnd);
        }
    }

    public static String getCurrEmployeeCode() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getEmployeeCode();
        }
    }

    public static String getCurrEmployeeName() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getEmployeeName();
        }
    }

    public static String getCurrCorpCode() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getCorpCode();
        }
    }

    public static String getCurrDeptCode() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getDeptCode();
        }
    }

    public static String getCurrDeptName() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getDeptName();
        }
    }

    public static String getCurrBusiDate() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getBusiDate();
        }
    }

    public static String getCurrCzyId() {
        LoginInfo loginInfo = getLoginInfo();
        if (null == loginInfo) {
            return null;
        } else {
            return loginInfo.getCzyId();
        }
    }

    public static void clear() {
        holder.remove();
    }

    /**
     * 公司代码
     */
    private String corpCode;
    /**
     * 会计年度
     */
    private String accountantYear;
    /**
     * 职员编码
     */
    private String employeeCode;
    /**
     * 部门代码
     */
    private String deptCode;
    /**
     * 职员姓名
     */
    private String employeeName;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 业务时间
     */
    private String busiDate;
    /**
     * 业务时间
     */
    private String czyId;
}
