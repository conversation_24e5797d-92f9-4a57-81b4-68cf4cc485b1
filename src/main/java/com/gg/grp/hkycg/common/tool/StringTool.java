package com.gg.grp.hkycg.common.tool;

import com.alibaba.fastjson.JSONObject;
//import com.gg.grp.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.BillnoType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.web.util.UriUtils;

import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Pattern;

//import static com.gg.grp.common.enums.BillnoType.OSB;

public class StringTool {
    public static String getString(Object obj){
        if(null == obj){
            return "";
        }
        return String.valueOf(obj).trim();
    }
    /**
     * 获取前半部String     AA BB
     * @return
     */
    public static String getFHString4Empty(Object obj){
        String str = getString(obj);
        if (StringUtils.isBlank(str) || !str.contains(" ")) {

            return "";
        }
        return str.substring(0,str.indexOf(" "));
    }
    /**
     * 获取后半部String  AA BB
     * @param obj
     * @return
     */
    public static String getEHString4Empty(Object obj){
        String str = getString(obj);
        if (StringUtils.isBlank(str) || !str.contains(" ")) {

            return "";
        }
        return str.substring(str.indexOf(" ")+1,str.length());
    }

    /**
     * aaa123445 获取字符串后面数字
     * @return
     */
    public static String getCNString(Object obj){
        String str = getString(obj);
        return Pattern.compile("[^0-9]").matcher(str).replaceAll("").trim();
    }

    public static String getSqlChar(String param) {
        return "'" + param + "'";
    }

    /**
     * [AA]BB
     * @param obj    [AA]BB
     * @return
     */
    public static String getFHString(Object obj){
        String str = getString(obj);
        if(StringUtils.isBlank(str)){
            return "";
        }
        if(str.contains("[")&&str.contains("]")) {
            return str.substring(str.indexOf("[") + 1, str.indexOf("]"));
        }else{
            return str;
        }
    }
    /**
     * [AA]BB
     * @param obj
     * @return
     */
    public static String getEHString(Object obj){
        String str = getString(obj);
        if(StringUtils.isBlank(str)){
            return "";
        }
        return str.substring(str.indexOf("]")+1,str.length());
    }

    /**
     * [AA]BB  AA[BB]转为[AA]BB 格式化
     * @param obj
     * @return
     */
    public static String getFormatString(Object obj){
        String str = getString(obj);
        if("".equals(str)){
            return "";
        }
        return "["+str.substring(0,str.indexOf("["))+"]"+str.substring(str.indexOf("[")+1,str.indexOf("]"));
    }

    /**
     * [AA]BB 加括号 合并
     * @param
     * @return
     */
    public static String getMergeString(Object code,Object name){
        String codeStr = getString(code);
        String nameStr = getString(name);
        if("".equals(codeStr) || "".equals(nameStr)){
            return "";
        }
        return "["+codeStr+"]"+nameStr;
    }

    /**
     * AA BB 加空格 合并
     *
     * @param
     * @return
     */
    public static String getSpaceString(Object code, Object name) {
        String codeStr = getString(code);
        String nameStr = getString(name);
        if ("".equals(codeStr) || "".equals(nameStr)) {
            return "";
        }
        return  codeStr + " " + nameStr;
    }

    /**
     * 比较当前时间和业务时间
     * @param billDate
     * @param currBusiDate
     * @return boolean
     */
    public static boolean compareToData(String billDate, String currBusiDate) {
        String beginDate = billDate.replace("-", "").substring(0, 6);
        String newDate = currBusiDate.replace("-", "").substring(0, 6);
        if (beginDate.compareTo(newDate) > 0) {
            return true;
        }
        return false;
    }

    public static String replaceDateString(Object obj){
        if(null == obj){
            return "";
        }
        String str = getString(obj);
        return str.replace("-", "");
    }

    public static String getDateString(Object obj){
        if(null == obj){
            return "";
        }
        String str = getString(obj);
        if(str.length() < 8){
            return "";
        }
        return str.substring(0, 4)+"-"+str.substring(4,6)+"-"+str.substring(6,8);
    }

    /**
     * 获取当前日期 返回YYYYMMDD
     *
     * @return
     */
    public static String getCurrentDate(){
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        return format.format(date);
    }



    public static List<String> getListComma(Object obj){
        String str = getString(obj);
        if (null == obj) {
            return Collections.singletonList("");
        }
        String str3=str.replaceAll(",,"," ").replace(" ",",");
        String substring = null;
        if (str3.length() == 1){
            substring = str3.substring(0, str3.length());
        }else {
            substring = str3.substring(1, str3.length() - 1);
        }
        //System.out.println(str3);

        //以逗号分割，得出的数据存到 result 里面
        String[] result = substring.split(",");
        List<String> s = new ArrayList<>();
        for (String r : result) {
            s.add(r);
        }
        return s;
    }

    /**
     * 计算年龄
     *
     * @param date 要修改的字符串
     */
    public static String getAge(String date) throws Exception {
        if (org.apache.commons.lang3.StringUtils.isBlank(date)) {
            return "";
        } else {

            Date birthDay = getDateByTime(date);
            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) {
                //出生日期晚于当前时间，无法计算
                throw new IllegalArgumentException(
                        "生日在现在之前，真是难以置信");
            }
            int yearNow = cal.get(Calendar.YEAR);  //当前年份
            int monthNow = cal.get(Calendar.MONTH);  //当前月份
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH); //当前日期
            cal.setTime(birthDay);
            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
            int age = yearNow - yearBirth;   //计算整岁数
            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth) {
                        age--;//当前日期在生日之前，年龄减一
                    }
                } else {
                    //当前月份在生日之前，年龄减一
                    age--;
                }
            }
            return String.valueOf(age);
        }
    }
    /**
     * 字符串格式转换成日期格式
     *
     */
    public static Date getDateByTime(String date) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = formatter.parse(date);
        return parse;
    }

    /**
     * 获取某年某月第一天
     *
     */
    public static String getDateFirst(String dateStr) {

        LocalDate date = LocalDate.parse(dateStr + "01", DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate dateFirst = date.with(TemporalAdjusters.firstDayOfMonth()); // 指定年月的第一天

        return dateFirst.toString().replace("-","");
    }

    /**
     * 获取某年某月最后一天
     *
     */
    public static String getDateEnd(String dateStr) {

        LocalDate date = LocalDate.parse(dateStr + "01", DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate dateEnd = date.with(TemporalAdjusters.lastDayOfMonth()); // 指定年月的最后一天

        return dateEnd.toString().replace("-","");
    }

    public static String messageFormat(String message, Object... args) {
        FormattingTuple formattingTuple = MessageFormatter.arrayFormat(message, args);
        return formattingTuple.getMessage();
    }

    public static boolean isEmpty(String string) {
        if (string == null) {
            return true;
        } else {
            return string.trim().length() == 0;
        }
    }
    public static boolean isNotEmpty(String string) {
        return !isEmpty(string);
    }

    /**
     *判断字符串是否全是数字
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        return str.matches("\\d+");
    }
    /**
     * 转换字符串
     * @param var 要转成加上单引号的字符串
     * @return 'var'
     */
    public static String toChar(String var) {
        return "'".concat(var.concat("'"));
    }

    public static String creatParticular(String particular) {
        String billType = StringUtils.substringBefore(particular, "billType");
        String type = StringUtils.substringBetween(particular, "billType", "type");
        String billNo = StringUtils.substringBetween(particular, "type", "billno");

        String subResult="";
//        if(BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.DMB)
//                ||BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.CLB)
//                ||BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.TAB)){
//            //?type=0&billno=BXD202406110024&billType=15
//            //15billType0typeBXD202406110024billno
//
//            subResult="/reimbursement/founds/auditDetail?type="+type+"&billno="+billNo+"&billType="+billType;
//        } else if(BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.OSB)
//                ||BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.SSB)){
//            //type=1&billno=BXD202311100100&billTypeId=salary-01&billType=校外人员劳务
//            //901billType0typeBXD202311100100billno
//            subResult="/provide/auditDetail?type="+type+"&billno="+billNo+"&billTypeId="+BillnoType.findByCode(Integer.valueOf(billType)).getMenu()
//                    +"&billType="+BillnoType.findByCode(Integer.valueOf(billType)).getComment();
//        }else if(BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.PAB)
//                ||BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.ORB)
//                ||BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.TRB)){
//            //?type=0&billno=PAB20240417001&billType=PAB&
//            //102billType0typeBXD202311100100billno
//            subResult="/priorapproval/auditDetail?type="+type+"&billno="+billNo +"&billType="+BillnoType.findByCode(Integer.valueOf(billType)).getModCode();
//        }else if(BillnoType.findByCode(Integer.valueOf(billType)).equals(BillnoType.PB)){
//            //?type=2&xmbh=PXM2023000367&
//            //2billType0typePXM2023000367billno
//            subResult="/projectAuditDetail?type="+type+"&xmbh="+billNo;
//        }

        return subResult;
    }

    public static String creatStUrl(String billType, String billstatus, String billNo) {
        return billType+"billType"+billstatus+"type"+billNo+"billno";
    }


    public static String searchBank(String bankname) {
        String bn = "";
        if (bankname.indexOf("中国银行") != -1 || bankname.indexOf("中行") != -1) {
            bn = "中国银行浙江省分行";
        } else if (bankname.indexOf("工商银行") != -1 || bankname.indexOf("工行") != -1) {
            bn = "中国工商银行股份有限公司浙江省分行营业部核算中心";
        } else if (bankname.indexOf("建设银行") != -1 || bankname.indexOf("建行") != -1) {
            bn = "中国建设银行浙江省分行";
        } else if (bankname.indexOf("农业银行") != -1 || bankname.indexOf("农行") != -1) {
            bn = "中国农业银行浙江省分行营业部营业中心";
        } else if (bankname.indexOf("杭州银行") != -1) {
            bn = "杭州银行股份有限公司";
        } else if (bankname.indexOf("邮政储蓄") != -1 || bankname.indexOf("邮储") != -1) {
            bn = "中国邮政储蓄银行股份有限公司浙江省分行";
        } else if (bankname.indexOf("农村商业") != -1
                || bankname.indexOf("农商") != -1
                || bankname.indexOf("农信") != -1
                || bankname.indexOf("联合") != -1
                || bankname.indexOf("农村合作") != -1
                || bankname.indexOf("信用合作") != -1
                || bankname.indexOf("信用社") != -1) {
            bn = "浙江省农村信用社联合社";
        } else if (bankname.indexOf("交通银行") != -1 || bankname.indexOf("交行") != -1) {
            bn = "交通银行浙江省分行";
        } else if (bankname.indexOf("民生") != -1) {
            bn = "中国民生银行股份有限公司杭州分行";
        } else if (bankname.indexOf("招商") != -1) {
            bn = "招商银行股份有限公司杭州分行";
        } else if (bankname.indexOf("平安") != -1) {
            bn = "平安银行杭州分行";
        } else if (bankname.indexOf("浦东发展") != -1 || bankname.indexOf("浦发") != -1) {
            bn = "上海浦东发展银行股份有限公司杭州分行营业部";
        } else if (bankname.indexOf("浙商") != -1) {
            bn = "浙商银行营业部";
        } else if (bankname.indexOf("广东发展") != -1 || bankname.indexOf("广发") != -1) {
            bn = "广发银行股份有限公司杭州分行运营部";
        } else if (bankname.indexOf("中信") != -1) {
            bn = "中信银行杭州分行";
        } else if (bankname.indexOf("兴业") != -1) {
            bn = "兴业银行杭州分行营业部";
        } else if (bankname.indexOf("光大") != -1) {
            bn = "中国光大银行杭州分行营业部";
        } else if (bankname.indexOf("华夏") != -1) {
            bn = "华夏银行杭州分行";
        }
        return bn;
    }
    /**
     * 指定日期加上指定天数得到新日期
     * @param date
     * @param day
     * @return
     * @throws ParseException
     */
    public static Date addDate(Date date, long day) throws ParseException {
        long time = date.getTime(); // 得到指定日期的毫秒数
        day = day * 24 * 60 * 60 * 1000; // 要加上的天数转换成毫秒数
        time += day; // 相加得到新的毫秒数
        return new Date(time); // 将毫秒数转换成日期
    }

    /**
     * 计算时间间隔，返回格式 --天--小时--分钟
     * @param startTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static String interval(LocalDateTime startTime , LocalDateTime endTime ) {

        Period between = Period.between(startTime.toLocalDate(), endTime.toLocalDate());
        int  month= between.getMonths();
        int  days = between.getDays();

        Duration duration = Duration.between(startTime, endTime);
        long day = duration.toDays();
        long hours = duration.toHours();
        long hourss = hours-day*24;

        long min = duration.toMinutes();
        long mins = min-day*24*60-hourss*60;

        return days+"天"+hourss+"小时"+mins+"分钟";
    }

    @NotNull
    public static String getPure0xUUID() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replace("-", "");
    }

}