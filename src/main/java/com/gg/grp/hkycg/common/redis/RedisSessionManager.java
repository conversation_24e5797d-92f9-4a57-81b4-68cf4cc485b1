package com.gg.grp.hkycg.common.redis;

import com.alibaba.fastjson.JSON;
import com.gg.grp.hkycg.model.pojo.Employee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis会话管理工具类
 * 使用连接池进行Redis操作
 */
@Component
public class RedisSessionManager {
    @Autowired
    private JedisPool jedisPool;
    
    // Redis key前缀
    private static final String USER_KEY_PREFIX = "user:";
    private static final String TOKEN_KEY_PREFIX = "token:";
    
    // 默认过期时间（24小时）
    private static final int DEFAULT_EXPIRE_TIME = 24 * 60 * 60;
    
    /**
     * 存储用户会话信息（使用连接池）
     */
    public void saveUserSession(Employee employee, String businessDate, String jwt) {
        Jedis jedis = null;
        try {
            // 从连接池获取连接
            jedis = jedisPool.getResource();
            
            // 构建用户会话信息
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("employeeCode", employee.getEmployeeCode());
            sessionInfo.put("employeeName", employee.getEmployeeName());
            sessionInfo.put("gsdm", employee.getGsdm());
            sessionInfo.put("bmdm", employee.getBmdm());
            sessionInfo.put("kjnd", employee.getKjnd());
            sessionInfo.put("businessDate", businessDate);
            sessionInfo.put("loginTime", System.currentTimeMillis());
            sessionInfo.put("jwt", jwt);
            
            // 用户信息存储
            String userKey = USER_KEY_PREFIX + employee.getEmployeeCode();
            String sessionJson = JSON.toJSONString(sessionInfo);
            jedis.setex(userKey, DEFAULT_EXPIRE_TIME, sessionJson);
            
            // JWT Token存储
            String tokenKey = TOKEN_KEY_PREFIX + jwt;
            Map<String, String> tokenInfo = new HashMap<>();
            tokenInfo.put("employeeCode", employee.getEmployeeCode());
            tokenInfo.put("createTime", String.valueOf(System.currentTimeMillis()));
            String tokenJson = JSON.toJSONString(tokenInfo);
            jedis.setex(tokenKey, DEFAULT_EXPIRE_TIME, tokenJson);
            
            System.out.println("用户会话已存入Redis - userKey: " + userKey + ", tokenKey: " + tokenKey);
            System.out.println("连接池状态 - Active: " + jedisPool.getNumActive() + 
                             ", Idle: " + jedisPool.getNumIdle() + 
                             ", Waiters: " + jedisPool.getNumWaiters());
        } catch (Exception e) {
            System.err.println("Redis存储用户会话失败: " + e.getMessage());
            throw new RuntimeException("会话存储失败", e);
        } finally {
            // 确保连接返回到连接池
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 获取用户会话信息（使用连接池）
     */
    public Map<String, Object> getUserSession(String employeeCode) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String userKey = USER_KEY_PREFIX + employeeCode;
            String sessionJson = jedis.get(userKey);
            
            if (sessionJson != null) {
                return JSON.parseObject(sessionJson, Map.class);
            }
            return null;
        } catch (Exception e) {
            System.err.println("Redis获取用户会话失败: " + e.getMessage());
            return null;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 通过JWT Token获取用户信息（使用连接池）
     */
    public Map<String, String> getTokenInfo(String jwt) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String tokenKey = TOKEN_KEY_PREFIX + jwt;
            String tokenJson = jedis.get(tokenKey);
            
            if (tokenJson != null) {
                return JSON.parseObject(tokenJson, Map.class);
            }
            return null;
        } catch (Exception e) {
            System.err.println("Redis获取Token信息失败: " + e.getMessage());
            return null;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 清除用户会话信息（使用连接池）
     */
    public void clearUserSession(String employeeCode) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String userKey = USER_KEY_PREFIX + employeeCode;
            
            // 获取用户信息以便清理JWT Token
            String userSessionInfo = jedis.get(userKey);
            if (userSessionInfo != null) {
                Map<String, Object> userSession = JSON.parseObject(userSessionInfo, Map.class);
                String jwt = (String) userSession.get("jwt");
                if (jwt != null) {
                    String tokenKey = TOKEN_KEY_PREFIX + jwt;
                    jedis.del(tokenKey);
                    System.out.println("已删除Redis中的Token: " + tokenKey);
                }
            }
            
            // 删除用户信息
            jedis.del(userKey);
            System.out.println("已删除Redis中的用户信息: " + userKey);
        } catch (Exception e) {
            System.err.println("Redis清理用户会话失败: " + e.getMessage());
            // 清理失败不抛出异常，不影响主流程
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 刷新用户会话过期时间（使用连接池）
     */
    public void refreshUserSession(String employeeCode) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String userKey = USER_KEY_PREFIX + employeeCode;
            jedis.expire(userKey, DEFAULT_EXPIRE_TIME);
            
            // 也刷新对应的JWT Token过期时间
            String userSessionInfo = jedis.get(userKey);
            if (userSessionInfo != null) {
                Map<String, Object> userSession = JSON.parseObject(userSessionInfo, Map.class);
                String jwt = (String) userSession.get("jwt");
                if (jwt != null) {
                    String tokenKey = TOKEN_KEY_PREFIX + jwt;
                    jedis.expire(tokenKey, DEFAULT_EXPIRE_TIME);
                }
            }
        } catch (Exception e) {
            System.err.println("Redis刷新用户会话失败: " + e.getMessage());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 检查JWT Token是否有效（使用连接池）
     */
    public boolean isTokenValid(String jwt) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String tokenKey = TOKEN_KEY_PREFIX + jwt;
            return jedis.exists(tokenKey);
        } catch (Exception e) {
            System.err.println("Redis检查Token有效性失败: " + e.getMessage());
            return false;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 批量操作：同时存储用户信息和Token
     */
    public void saveUserSessionWithPipeline(Employee employee, String businessDate, String isCheck, String jwt) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            
            // 开启管道
            redis.clients.jedis.Pipeline pipeline = jedis.pipelined();
            
            // 构建用户会话信息
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("employeeCode", employee.getEmployeeCode());
            sessionInfo.put("employeeName", employee.getEmployeeName());
            sessionInfo.put("gsdm", employee.getGsdm());
            sessionInfo.put("bmdm", employee.getBmdm());
            sessionInfo.put("kjnd", employee.getKjnd());
            sessionInfo.put("businessDate", businessDate);
            sessionInfo.put("isCheck", isCheck);
            sessionInfo.put("loginTime", System.currentTimeMillis());
            sessionInfo.put("jwt", jwt);
            
            // 用户信息存储
            String userKey = USER_KEY_PREFIX + employee.getEmployeeCode();
            String sessionJson = JSON.toJSONString(sessionInfo);
            pipeline.setex(userKey, DEFAULT_EXPIRE_TIME, sessionJson);
            
            // JWT Token存储
            String tokenKey = TOKEN_KEY_PREFIX + jwt;
            Map<String, String> tokenInfo = new HashMap<>();
            tokenInfo.put("employeeCode", employee.getEmployeeCode());
            tokenInfo.put("createTime", String.valueOf(System.currentTimeMillis()));
            String tokenJson = JSON.toJSONString(tokenInfo);
            pipeline.setex(tokenKey, DEFAULT_EXPIRE_TIME, tokenJson);
            
            // 执行管道中的所有命令
            pipeline.sync();
            
            System.out.println("用户会话已通过管道存入Redis - userKey: " + userKey + ", tokenKey: " + tokenKey);
        } catch (Exception e) {
            System.err.println("Redis管道存储用户会话失败: " + e.getMessage());
            throw new RuntimeException("管道会话存储失败", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 获取连接池状态信息
     */
    public Map<String, Object> getPoolStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("numActive", jedisPool.getNumActive());
        status.put("numIdle", jedisPool.getNumIdle());
        status.put("numWaiters", jedisPool.getNumWaiters());
        status.put("maxTotal", jedisPool.getMaxTotal());
        status.put("maxIdle", jedisPool.getMaxIdle());
        return status;
    }
    
    /**
     * 测试连接池连接
     */
    public boolean testConnection() {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String pong = jedis.ping();
            return "PONG".equals(pong);
        } catch (Exception e) {
            System.err.println("Redis连接测试失败: " + e.getMessage());
            return false;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
    
    /**
     * 保存用户会话数据
     * @param employeeCode 员工代码
     * @param sessionData 会话数据
     */
    public void saveUserSessionData(String employeeCode, Map<String, Object> sessionData) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            String userKey = USER_KEY_PREFIX + employeeCode;
            String sessionJson = JSON.toJSONString(sessionData);
            jedis.setex(userKey, DEFAULT_EXPIRE_TIME, sessionJson);
            System.out.println("用户会话数据已更新到Redis: " + userKey);
        } catch (Exception e) {
            System.err.println("Redis保存用户会话数据失败: " + e.getMessage());
            throw new RuntimeException("会话数据保存失败", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
} 