package com.gg.grp.hkycg.common.aop;

import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.mapper.PubObjFlowMapper;
import com.gg.grp.hkycg.model.pojo.PubObjFlow;
import com.gg.grp.hkycg.service.ApprovalReminderService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 审核催办状态更新AOP切面
 * 当审核、销审、退审成功后，自动更新催办状态为3（已处理）
 */
@Aspect
@Component
@Slf4j
public class ReminderUpdateAspect {

    @Autowired
    private ApprovalReminderService approvalReminderService;

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    /**
     * 拦截审核方法
     * 切点：各个Service实现类的审核方法
     */
    @AfterReturning("execution(* com.gg.grp.hkycg.service.impl.CgjhServiceImpl.check(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.check(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.checkBySqbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgzbsqServiceImpl.checkByZbsqbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgdjServiceImpl.checkByJgdjbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgysServiceImpl.checkByYsbh(..))")
    public void afterAuditSuccess(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            String methodName = joinPoint.getSignature().getName();
            
            // 提取参数
            BillnoType billType = null;
            String billNo = null;
            
            if (args.length >= 2) {
                billType = (BillnoType) args[0];
                billNo = (String) args[1];
            }
            
            if (billType != null && billNo != null) {
                // 获取当前审核节点
                String nodeName = getCurrentNodeName(billType, billNo);
                if (nodeName != null) {
                    // 更新催办状态
                    approvalReminderService.updateReminderStatusAfterAudit(billNo, billType.getBillType(), nodeName);
                    log.info("审核后自动更新催办状态，方法：{}，单据号：{}，类型：{}，节点：{}", methodName, billNo, billType.getBillType(), nodeName);
                }
            }
        } catch (Exception e) {
            log.error("审核后更新催办状态失败", e);
        }
    }

    /**
     * 拦截销审方法
     * 切点：各个Service实现类的销审方法
     */
    @AfterReturning("execution(* com.gg.grp.hkycg.service.impl.CgjhServiceImpl.noAudit(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.noAudit(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.noAuditBySqbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgzbsqServiceImpl.noAuditByZbsqbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgdjServiceImpl.noAuditByJgdjbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgysServiceImpl.noAuditByYsbh(..))")
    public void afterNoAuditSuccess(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            String methodName = joinPoint.getSignature().getName();
            
            // 根据方法名判断单据类型和单据号
            BillnoType billType = null;
            String billNo = null;
            
            if (args.length >= 1) {
                billNo = (String) args[0];
                
                // 根据类名确定单据类型
                String className = joinPoint.getTarget().getClass().getSimpleName();
                billType = getBillTypeByClassName(className);
            }
            
            if (billType != null && billNo != null) {
                // 获取当前审核节点（销审时可能没有当前节点，尝试获取最后一个节点）
                String nodeName = getLastAuditNodeName(billType, billNo);
                if (nodeName != null) {
                    // 更新催办状态
                    approvalReminderService.updateReminderStatusAfterAudit(billNo, billType.getBillType(), nodeName);
                    log.info("销审后自动更新催办状态，方法：{}，单据号：{}，类型：{}，节点：{}", methodName, billNo, billType.getBillType(), nodeName);
                }
            }
        } catch (Exception e) {
            log.error("销审后更新催办状态失败", e);
        }
    }

    /**
     * 拦截退审方法
     * 切点：各个Service实现类的退审方法
     */
    @AfterReturning("execution(* com.gg.grp.hkycg.service.impl.CgjhServiceImpl.checkCallBack(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.checkCallBack(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgsqServiceImpl.checkCallBackBySqbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgzbsqServiceImpl.checkCallBackByZbsqbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgdjServiceImpl.checkCallBackByJgdjbh(..)) || " +
                   "execution(* com.gg.grp.hkycg.service.impl.CgysServiceImpl.checkCallBackByYsbh(..))")
    public void afterCheckCallBackSuccess(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            String methodName = joinPoint.getSignature().getName();
            
            // 根据方法名判断单据类型和单据号
            BillnoType billType = null;
            String billNo = null;
            
            if (args.length >= 1) {
                billNo = (String) args[0];
                
                // 根据类名确定单据类型
                String className = joinPoint.getTarget().getClass().getSimpleName();
                billType = getBillTypeByClassName(className);
            }
            
            if (billType != null && billNo != null) {
                // 获取当前审核节点（退审时可能没有当前节点，尝试获取最后一个节点）
                String nodeName = getLastAuditNodeName(billType, billNo);
                if (nodeName != null) {
                    // 更新催办状态
                    approvalReminderService.updateReminderStatusAfterAudit(billNo, billType.getBillType(), nodeName);
                    log.info("退审后自动更新催办状态，方法：{}，单据号：{}，类型：{}，节点：{}", methodName, billNo, billType.getBillType(), nodeName);
                }
            }
        } catch (Exception e) {
            log.error("退审后更新催办状态失败", e);
        }
    }

    /**
     * 根据类名获取单据类型
     */
    private BillnoType getBillTypeByClassName(String className) {
        switch (className) {
            case "CgjhServiceImpl":
                return BillnoType.CGJH;
            case "CgsqServiceImpl":
                return BillnoType.CGSQ;
            case "CgzbsqServiceImpl":
                return BillnoType.CGZB;
            case "CgjgServiceImpl":
                return BillnoType.CGJG;
            case "CgysServiceImpl":
                return BillnoType.CGYS;
            default:
                return null;
        }
    }

    /**
     * 获取当前审核节点名称
     */
    private String getCurrentNodeName(BillnoType billType, String billNo) {
        try {
            PubObjFlow currentNode = pubObjFlowMapper.selectNowNodeByDjh(billType.getModCode(), 
                    String.valueOf(billType.getCode()), billNo);
            
            if (currentNode != null) {
                // 优先使用nodeName字段，如果为空则使用shrmc字段
                String nodeName = currentNode.getNodeName();
                if (nodeName == null || nodeName.trim().isEmpty()) {
                    nodeName = currentNode.getShrmc();
                }
                return nodeName;
            }
            return null;
        } catch (Exception e) {
            log.error("获取当前审核节点失败，单据号：{}，类型：{}", billNo, billType.getBillType(), e);
            return null;
        }
    }

    /**
     * 获取最后一个审核节点名称（用于销审和退审）
     */
    private String getLastAuditNodeName(BillnoType billType, String billNo) {
        try {
            PubObjFlow lastNode = pubObjFlowMapper.selectLastAuditNodeByDjh(billType.getModCode(), 
                    String.valueOf(billType.getCode()), billNo);
            
            if (lastNode != null) {
                // 优先使用nodeName字段，如果为空则使用shrmc字段
                String nodeName = lastNode.getNodeName();
                if (nodeName == null || nodeName.trim().isEmpty()) {
                    nodeName = lastNode.getShrmc();
                }
                return nodeName;
            }
            return null;
        } catch (Exception e) {
            log.error("获取最后审核节点失败，单据号：{}，类型：{}", billNo, billType.getBillType(), e);
            return null;
        }
    }
} 