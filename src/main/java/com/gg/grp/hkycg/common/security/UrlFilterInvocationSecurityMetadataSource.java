package com.gg.grp.hkycg.common.security;

import com.gg.grp.hkycg.model.pojo.GpmGnfl;
import com.gg.grp.hkycg.service.GnflService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;

/**
 * URL权限元数据源
 */
@Component
public class UrlFilterInvocationSecurityMetadataSource implements FilterInvocationSecurityMetadataSource {

    @Autowired
    private GnflService gnflService;

    private AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public Collection<ConfigAttribute> getAttributes(Object object) throws IllegalArgumentException {
        if (object instanceof FilterInvocation) {
            FilterInvocation filterInvocation = (FilterInvocation) object;
            HttpServletRequest request = filterInvocation.getHttpRequest();
            String url = request.getRequestURI();
            
            // 移除应用上下文路径
            String contextPath = request.getContextPath();
            if (url.startsWith(contextPath)) {
                url = url.substring(contextPath.length());
            }
            
            // 如果是API路径，直接返回null，不进行权限控制
            if (url.startsWith("/api/")) {
                System.out.println("API路径，跳过权限验证: " + url);
                return null;
            }
            
            // 如果是静态资源或基础功能，直接返回null
            if (isPublicResource(url)) {
                System.out.println("公共资源，跳过权限验证: " + url);
                return null;
            }
            
            // 获取所有功能URL配置，只检查前端页面权限
            List<GpmGnfl> allFunctions = gnflService.getAllFunctions();
            
            for (GpmGnfl function : allFunctions) {
                if (function.getUrl() != null && antPathMatcher.match(function.getUrl(), url)) {
                    System.out.println("匹配到前端页面权限配置: " + url + " -> " + function.getCode());
                    // 返回需要的权限，这里使用功能代码作为权限标识
                    return SecurityConfig.createList("FUNCTION_" + function.getCode());
                }
            }
            
            // 如果是前端页面但没有在权限配置中，返回默认权限
            System.out.println("前端页面但无权限配置，使用默认权限: " + url);
        }
        
        // 返回默认权限
        return SecurityConfig.createList("ROLE_USER");
    }
    
    /**
     * 判断是否为公共资源
     */
    private boolean isPublicResource(String url) {
        String[] publicPatterns = {
            "/login", "/","/#/**","/loginOut", "/test-user", "/test-redis", "/redis-pool-status",
            "/test/public", "/swagger-ui/**", "/swagger-resources/**", "/v2/api-docs", "/webjars/**",
            "/static/**", "/assets/**", "/css/**", "/js/**", "/images/**", "/favicon.ico",
            "/index.html", "/WEB-INF/**", "/upload/**", "/doc.html", "/v3/api-docs/**", "/swagger-ui.html",
                "/api/v3/api-docs/**", "/api/attachment/download/**", "/api/attachment/preview/**"
        };
        
        for (String pattern : publicPatterns) {
            if (antPathMatcher.match(pattern, url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Collection<ConfigAttribute> getAllConfigAttributes() {
        return null;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return FilterInvocation.class.isAssignableFrom(clazz);
    }
} 