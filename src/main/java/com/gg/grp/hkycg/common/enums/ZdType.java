package com.gg.grp.hkycg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ZdType {
    CGFS("001", "采购方式","CG<PERSON>"),
    BMXX("002", "部门信息","BMXX"),
    CGLX("003", "采购类型","CGLX"),
    CGPM("004", "采购品目","CGPM"),
    CGZZFS("005", "采购组织方式","CGZZFS"),
    YSSJFS("006", "预算审计方式","YSSJFS"),
    DLZJJG("007", "代理中介机构","DLZJJG"),
    ZJLYFS("008", "专家来源方式","ZJLYFS");
    private String zddm;
    private String zdmc;
    private String zdlx;
}
