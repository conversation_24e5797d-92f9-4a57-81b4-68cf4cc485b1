package com.gg.grp.hkycg.common.aop;

import com.alibaba.fastjson.JSONArray;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.exception.GlobalException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 通用AOP配置
 * 包含事务管理和参数校验
 */
@Slf4j
@Aspect
@Component
public class CommonTransactionAopConfig {

    @Autowired
    private DataSourceTransactionManager transactionManager;

    /**
     * 拦截所有Controller方法进行事务管理
     */
    @Pointcut(value = "execution(* com.gg.grp.hkycg.controller.*.*(..))")
    public void transactionalCut() {}

    /**
     * 拦截参数校验方法
     */
    @Pointcut(value = "execution(* com.gg.grp.hkycg.controller.*.*checkParam*(..))")
    public void validCut() {}

    /**
     * 环绕通知：事务管理
     */
    @Around("transactionalCut()")
    public Object commitAround(ProceedingJoinPoint joinPoint) {
        String requestURI = logInfo(joinPoint);
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        // 开启事务(隔离级别为新建事务)
        transactionDefinition.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        Object result;
        try {
            result = joinPoint.proceed(joinPoint.getArgs());
            transactionManager.commit(transactionStatus);
        } catch (Throwable e) {
            // 异常回滚
            transactionManager.rollback(transactionStatus);
            e.printStackTrace();
            
            // 如果是GlobalException，直接重新抛出，保持原始异常信息
            if (e instanceof GlobalException) {
                throw (GlobalException) e;
            }
            
            // 其他异常包装成RuntimeException
            throw new RuntimeException(e.getMessage(), e);
        }
        log.info("请求：request:{} 完成。。completed..", requestURI);
        return result;
    }

    /**
     * 前置通知：参数校验
     */
    @Before("validCut()")
    public void beforeFunc(JoinPoint point) {
        Object[] objects = point.getArgs();
        for (Object object : objects) {
            BindingResult bind = object instanceof BindingResult ? (BindingResult) object : null;
            if (bind != null && bind.hasErrors()) {
                List<FieldError> errors = bind.getFieldErrors();
                StringBuilder stringBuffer = new StringBuilder();
                for (FieldError error : errors) {
                    stringBuffer.append(error.getDefaultMessage()).append(",");
                }
                throw new RuntimeException(stringBuffer.substring(0, stringBuffer.length() - 1));
            }
        }
    }

    /**
     * 日志信息整理
     * @param joinPoint 请求参数
     * @return 请求的uri
     */
    private String logInfo(ProceedingJoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return "unknown";
        }
        
        HttpServletRequest request = attributes.getRequest();
        String requestURI = request.getRequestURI();
        try {
            UUID uuid = UUID.randomUUID();
            Object[] args = joinPoint.getArgs();
            if (LoginInfo.getLoginInfo() != null) {
                String gsdm = LoginInfo.getCurrCorpCode();
                String kjnd = LoginInfo.getCurrAccountantYear();
                String zy = LoginInfo.getCurrEmployeeCode() + " " + LoginInfo.getCurrEmployeeName();
                String loginInfo = gsdm + " " + kjnd + " " + zy;
                String uuidString = uuid.toString().replace("-", "");
                JSONArray argsJson = new JSONArray(Arrays.asList(args));
                log.info(
                        "用户：user:[{}] 使用事务：use transaction:[{}] 请求：request:{} 接到参数：{}",
                        loginInfo, uuidString, requestURI, argsJson);
                Thread.currentThread().setName(uuidString);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return requestURI;
    }
} 