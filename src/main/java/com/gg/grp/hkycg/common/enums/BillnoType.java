package com.gg.grp.hkycg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 单据类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BillnoType {

    /**
     * 采购计划
     */
    CGJH(201, "GPM", "CGJH", "采购计划", "purchase-01"),

    /**
     * 采购申请
     */
    CGSQ(202, "GPM", "CGSQ", "采购申请", "purchase-02"),

    /**
     * 采购招标申请
     */
    CGZB(203, "GPM", "CGZB", "采购招标申请", "purchase-03"),

    /**
     * 采购结果
     */
    CGJG(204, "GPM", "CGDJ", "采购登记", "purchase-04"),

    /**
     * 采购验收
     */
    CGYS(205, "GPM", "CGYS", "采购验收", "purchase-05");

    /**
     * 代码
     */
    private Integer code;

    /**
     * 模型
     */
    private String modCode;

    /**
     * 单据类型
     */
    private String billType;

    /**
     * 描述
     */
    private String comment;

    /**
     * 菜单
     */
    private String menu;

    /**
     * 查询
     * @param code 代码
     * @return BillnoType模型
     */
    public static BillnoType findByCode(Integer code){
        return Stream.of(BillnoType.values())
                .filter(item -> code.equals(item.getCode())).findFirst()
                .orElseThrow(() -> new RuntimeException("未找到代码:" + code + "请联系管理员"));
    }
    /**
     * 查询
     * @param code 代码
     * @return BillnoType模型
     */
    public static BillnoType findByCode(String code){
        return Stream.of(BillnoType.values())
                .filter(item -> code.equals(String.valueOf(item.getCode()))).findFirst()
                .orElseThrow(() -> new RuntimeException("未找到代码:" + code + "请联系管理员"));
    }

    public static BillnoType findByBillType(String billType){
        return Stream.of(BillnoType.values())
                .filter(item -> billType.equals(item.getBillType())).findFirst()
                .orElseThrow(() -> new RuntimeException("未找到单据:" + billType + "请联系管理员"));
    }

    /**
     * 查询
     * @param comment 描述
     * @return BillnoType模型
     */
    public static BillnoType findByComment(String comment){
        return Stream.of(BillnoType.values())
                .filter(item -> comment.equals(item.getComment())).findFirst()
                .orElseThrow(() -> new RuntimeException("未找到单据:" + comment + "请联系管理员"));
    }

    public static Integer getCodeByName(String enumName) {
        return BillnoType.valueOf(enumName).getCode();
    }

} 