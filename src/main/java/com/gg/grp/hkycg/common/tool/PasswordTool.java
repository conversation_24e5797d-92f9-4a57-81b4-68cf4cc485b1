package com.gg.grp.hkycg.common.tool;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码工具类
 */
public class PasswordTool {
    
    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 加密密码
     */
    public static String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }
    
    /**
     * 验证密码
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    /**
     * 测试方法 - 生成加密密码
     */
    public static void main(String[] args) {
        String password = "123456"; // 明文密码
        String encoded = encodePassword(password);
        System.out.println("明文密码: " + password);
        System.out.println("加密密码: " + encoded);
        System.out.println("验证结果: " + matches(password, encoded));
        
        // 生成多个测试密码
        System.out.println("\n=== 测试用户密码 ===");
        String[] testPasswords = {"123456", "admin", "test", "password"};
        for (String pwd : testPasswords) {
            System.out.println("密码: " + pwd + " -> 加密: " + encodePassword(pwd));
        }
    }
} 