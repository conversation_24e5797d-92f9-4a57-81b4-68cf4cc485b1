package com.gg.grp.hkycg.common.security;

import com.gg.grp.hkycg.service.UrlAuthorizationService;
import com.gg.grp.hkycg.common.LoginInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.FilterInvocation;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;

/**
 * URL访问决策管理器
 * 只对前端页面进行权限验证，后端API路径不进行权限控制
 */
@Component
public class UrlAccessDecisionManager implements AccessDecisionManager {

    @Autowired
    private UrlAuthorizationService urlAuthorizationService;

    @Override
    public void decide(Authentication authentication, Object object, Collection<ConfigAttribute> configAttributes) 
            throws AccessDeniedException, InsufficientAuthenticationException {
        
        // 如果没有权限配置，直接放行（比如API路径、静态资源等）
        if (configAttributes == null || configAttributes.isEmpty()) {
            return;
        }

        if (object instanceof FilterInvocation) {
            FilterInvocation filterInvocation = (FilterInvocation) object;
            HttpServletRequest request = filterInvocation.getHttpRequest();
            String url = request.getRequestURI();
            
            // 移除应用上下文路径
            String contextPath = request.getContextPath();
            if (url.startsWith(contextPath)) {
                url = url.substring(contextPath.length());
            }

            // 如果是API路径，直接放行
            if (url.startsWith("/api/")) {
                System.out.println("API路径，直接放行: " + url);
                return;
            }

            // 获取当前登录用户信息
            LoginInfo loginInfo = LoginInfo.getLoginInfo();
            if (loginInfo == null || loginInfo.getEmployeeCode() == null) {
                throw new InsufficientAuthenticationException("用户未登录，无法访问前端页面: " + url);
            }

            String employeeCode = loginInfo.getEmployeeCode();
            
            // 只对前端页面进行权限验证
            boolean hasPermission = urlAuthorizationService.hasUrlPermission(employeeCode, url);
            
            if (!hasPermission) {
                System.out.println("用户 " + employeeCode + " 无权限访问前端页面: " + url);
                throw new AccessDeniedException("您没有权限访问该页面: " + url);
            }
            
            System.out.println("用户 " + employeeCode + " 允许访问前端页面: " + url);
        }
    }

    @Override
    public boolean supports(ConfigAttribute attribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return FilterInvocation.class.isAssignableFrom(clazz);
    }
} 