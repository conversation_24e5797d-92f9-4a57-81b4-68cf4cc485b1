package com.gg.grp.hkycg.common.redis;

/**
 * <AUTHOR>
 * @create 2020-11-03 14:30
 */
public interface JedisClient {

    String get(String key);

    String set(String key, String value);

    /**
     * @param key
     * @param value
     * @param expire 过期时间
     * @return
     */
    String set(String key, String value,int expire);

    String hget(String hkey, String key);// 获取存储结构是hashMap类型的操作

    long hset(String hkey, String key, String value);

    long incr(String key);

    long expire(String key, int second);// 设置缓存时间

    long ttl(String key);

    long del(String key);// 删除数据

    long hdel(String hkey, String key);

}
