package com.gg.grp.hkycg.common.exception;


import com.gg.grp.hkycg.common.tool.StringTool;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GlobalException extends RuntimeException{
    private Integer code;

    private String message;

    public GlobalException(String message){
        this.code = 500;
        this.message = message;
    }
    public GlobalException(Throwable  throwable){
        super(throwable);
        this.code = 500;
        this.message = throwable.getMessage();
    }
    public GlobalException(String message, Object... args){
        this(StringTool.messageFormat(message, args));
    }
    public GlobalException(int code,String message, Object... args){
        this(StringTool.messageFormat(message, args));
        this.code = code;
    }

    public GlobalException(Throwable e,String errorMessage){
        this(e);
        this.setMessage(errorMessage + this.getMessage());
    }
}
