package com.gg.grp.hkycg.common.redis;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Jedis配置 - 优化连接池配置
 */
@PropertySource("classpath:application.yml")
@Configuration
public class JedisConf {

    @Value("${spring.redis.jedis.pool.max-active}")
    private Integer maxTotal;

    @Value("${spring.redis.jedis.pool.max-idle}")
    private Integer maxIdle;

    @Value(("${spring.redis.jedis.pool.max-wait}"))
    private Long maxWaitMillis;

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private int port;

    @Value("${spring.redis.timeout}")
    private int timeout;

    @Value("${spring.redis.database}")
    private int database;

    @Bean
    public JedisPoolConfig redisPoolConfig(){
        JedisPoolConfig config = new JedisPoolConfig();
        
        // 基本配置
        config.setJmxEnabled(false);
        config.setMaxTotal(maxTotal);  // 连接池最大连接数
        config.setMaxIdle(maxIdle);    // 连接池最大空闲连接数
        config.setMinIdle(8);          // 连接池最小空闲连接数
        config.setMaxWaitMillis(maxWaitMillis); // 最大等待时间
        
        // 连接有效性检测
        config.setTestOnBorrow(true);   // 获取连接时检测有效性
        config.setTestOnReturn(false);  // 归还连接时检测有效性
        config.setTestWhileIdle(true);  // 空闲时检测有效性
        
        // 空闲连接检测
        config.setTimeBetweenEvictionRunsMillis(30000); // 空闲连接检测周期(30秒)
        config.setMinEvictableIdleTimeMillis(60000);    // 连接空闲多久后可被清除(60秒)
        config.setNumTestsPerEvictionRun(3);            // 每次检测连接数
        
        // 连接池耗尽时的行为
        config.setBlockWhenExhausted(true); // 连接耗尽时是否阻塞
        
        System.out.println("Jedis连接池配置完成 - maxTotal: " + maxTotal + 
                          ", maxIdle: " + maxIdle + 
                          ", minIdle: 8" + 
                          ", maxWait: " + maxWaitMillis + "ms");

        return config;
    }

    @Bean
    public JedisPool jedisPool(JedisPoolConfig jedisPoolConfig){
        JedisPool pool = new JedisPool(jedisPoolConfig, host, port, timeout, null, database);
        
        System.out.println("Jedis连接池创建完成 - host: " + host + 
                          ", port: " + port + 
                          ", database: " + database + 
                          ", timeout: " + timeout + "ms");
        
        return pool;
    }

    @Bean
    public RedisClient redisClient(JedisPool jedisPool){
        RedisClient client = new RedisClient(jedisPool);
        client.setExpire(1800); // 30分钟默认过期时间
        
        System.out.println("RedisClient创建完成 - 默认过期时间: 30分钟");
        
        return client;
    }
}
