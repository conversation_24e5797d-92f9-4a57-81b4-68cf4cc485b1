package com.gg.grp.hkycg.common.config;

import com.gg.grp.hkycg.interceptor.LoginInfoClearInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.io.File;

/**
 * Web MVC配置类
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    /**
     * 配置路径匹配规则
     * 为所有RestController添加/api前缀
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix("/api", HandlerTypePredicate.forAnnotation(RestController.class));
    }

    /**
     * 添加拦截器
     * @param registry 拦截器注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册ThreadLocal清理拦截器，应用到所有路径
        registry.addInterceptor(new LoginInfoClearInterceptor())
                .addPathPatterns("/**");
    }
    
    @Value("${attachment.upload.path:upload/attachment}")
    private String uploadPath;
    
    private String absoluteUploadPath;
    
    @PostConstruct
    public void init() {
        // 将相对路径转换为绝对路径
        if (!uploadPath.startsWith("/")) {
            String userDir = System.getProperty("user.dir");
            absoluteUploadPath = userDir + "/" + (uploadPath.startsWith("./") ? uploadPath.substring(2) : uploadPath);
        } else {
            absoluteUploadPath = uploadPath;
        }
        
        // 确保路径以/结尾
        if (!absoluteUploadPath.endsWith("/")) {
            absoluteUploadPath += "/";
        }
        
        // 确保目录存在
        File dir = new File(absoluteUploadPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                System.err.println("无法创建附件目录: " + absoluteUploadPath);
            } else {
                System.out.println("已创建附件目录: " + absoluteUploadPath);
            }
        }
        
        System.out.println("附件上传目录配置为: " + absoluteUploadPath);
    }
    
    /**
     * 添加静态资源处理器
     * @param registry 资源处理器注册表
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 注释掉原有配置，因为我们使用Controller处理下载请求
        // registry.addResourceHandler("/attachment/**")
        //        .addResourceLocations("file:" + uploadPath + "/");
        
        // 如果确实需要配置静态资源访问，应确保路径正确
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }
}
