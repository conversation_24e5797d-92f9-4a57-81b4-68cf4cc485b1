package com.gg.grp.hkycg.common.security;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.model.pojo.Employee;
import com.gg.grp.hkycg.service.EmployeeService;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.common.consts.StaticValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义认证Provider
 * 支持验证用户名、密码、businessDate和isCheck字段
 */
@Component
public class CustomAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private EmployeeService employeeService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        UsernamePasswordAuthenticationToken token = (UsernamePasswordAuthenticationToken) authentication;
        String userNameJson = (String) token.getPrincipal();
        String password = (String) token.getCredentials();

        // 解析JSON字符串获取真实的用户信息
        String userCode;
        String realBusinessDate;
        String realIsCheck;

        try {
            JSONObject loginInfo = JSON.parseObject(userNameJson);
            userCode = loginInfo.getString("userCode");
            realBusinessDate = loginInfo.getString("businessDate");
            realIsCheck = loginInfo.getString("isCheck");
            System.out.println("解析JSON成功 - userCode: " + userCode + ", businessDate: " + realBusinessDate + ", isCheck: " + realIsCheck);
        } catch (Exception e) {
            // 如果解析失败，直接使用传入的JSON字符串作为userCode
            userCode = userNameJson;
            realBusinessDate = LocalDate.now().toString();
            realIsCheck = "Y"; // 默认值，需要验证密码
            System.out.println("解析JSON失败，使用默认值 - userCode: " + userCode + ", businessDate: " + realBusinessDate + ", isCheck: " + realIsCheck);
        }

        // 验证用户是否存在
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneEmpty(userCode), Employee::getEmployeeCode, userCode.trim());
        queryWrapper.eq(StringUtils.isNoneEmpty(StaticValue.getGsdm()), Employee::getGsdm, StaticValue.getGsdm());
        queryWrapper.eq(StringUtils.isNoneEmpty(realBusinessDate), Employee::getKjnd, realBusinessDate.substring(0, 4));

        List<Employee> employees = employeeService.list(queryWrapper);
        if (employees == null || employees.isEmpty()) {
            System.out.println("用户不存在，抛出异常");
            throw new BadCredentialsException("用户不存在");
        }
        Employee employee = employees.get(0);

        // 验证密码
        if (!"N".equals(realIsCheck)) {
            if (StringUtils.isEmpty(password)) {
                throw new GlobalException("密码不能为空!");
            }
            if (!employee.getPassword().equals(password.trim())) {
                throw new GlobalException("账号或密码错误!");
            }
        }

        // 创建权限列表
        List<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        // 返回认证成功的Token
        return new UsernamePasswordAuthenticationToken(
                userNameJson,
                password,
                authorities
        );
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
} 