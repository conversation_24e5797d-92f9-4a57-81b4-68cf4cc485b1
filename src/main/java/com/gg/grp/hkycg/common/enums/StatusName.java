package com.gg.grp.hkycg.common.enums;

import com.gg.grp.hkycg.common.exception.GlobalException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum StatusName {
    SAVE(new BigDecimal(1), "已保存"),
    COMMIT(new BigDecimal(2), "已提交"),
    AUDIT(new BigDecimal(3), "审核中"),
    REVIEWED(new BigDecimal(4), "已审核"),
    RETURN(new BigDecimal(5), "已退回"),;
    private BigDecimal status;
    private String name;
    /**
     * 查询
     * @param status
     * @return BillnoType模型
     */
    public static String findByStatus(BigDecimal status){
        return Stream.of(StatusName.values())
                .filter(item -> status.compareTo(item.getStatus()) == 0).findFirst()
                .orElseThrow(() -> new GlobalException("未找到代码:" + status + "请联系管理员")).getName();
    }

    /**
     * 查询
     * @param status
     * @return BillnoType模型
     */
    public static String findByStatus(String status){
        return Stream.of(StatusName.values())
                .filter(item -> status.equals(item.getStatus().toString())).findFirst()
                .orElseThrow(() -> new GlobalException("未找到代码:" + status + "请联系管理员")).getName();
    }

    /**
     * 查询
     * @param comment 描述
     * @return BillnoType模型
     */
    public static BillnoType findByComment(String comment){
        return Stream.of(BillnoType.values())
                .filter(item -> comment.equals(item.getComment())).findFirst()
                .orElseThrow(() -> new RuntimeException("未找到单据:" + comment + "请联系管理员"));
    }
}
