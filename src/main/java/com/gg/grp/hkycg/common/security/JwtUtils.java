package com.gg.grp.hkycg.common.security;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * JWT工具类
 */
@Component
public class JwtUtils {

    @Value("${app.jwtSecret:hkyCgSecretKey}")
    private String jwtSecret;

    @Value("${app.jwtExpirationInMs:********}")
    private int jwtExpirationInMs;

    /**
     * 生成JWT Token，包含完整的用户信息
     * @param userCode 用户代码
     * @param employeeName 用户姓名
     * @param corpCode 公司代码
     * @param deptCode 部门代码
     * @param deptName 部门名称
     * @param accountantYear 会计年度
     * @param businessDate 业务日期
     * @param isCheck 是否检查
     * @return JWT token
     */
    public String generateJwtTokenWithUserInfo(String userCode, String employeeName, String corpCode, 
                                             String deptCode, String deptName, String accountantYear, 
                                             String businessDate, String isCheck) {
        return Jwts.builder()
                .setSubject(userCode)
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationInMs))
                .claim("employeeName", employeeName)
                .claim("corpCode", corpCode)
                .claim("deptCode", deptCode)
                .claim("deptName", deptName)
                .claim("accountantYear", accountantYear)
                .claim("businessDate", businessDate)
                .claim("isCheck", isCheck)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    public String generateJwtToken(Authentication authentication) {
        String username;
        String businessDate = null;
        String isCheck = null;
        
        if (authentication instanceof UsernamePasswordAuthenticationToken) {
            // 获取用户名，可能是JSON字符串
            String userNameOrJson = (String) authentication.getPrincipal();
            
            // 尝试解析JSON字符串
            try {
                JSONObject loginInfo = JSON.parseObject(userNameOrJson);
                username = loginInfo.getString("userCode");
                businessDate = loginInfo.getString("businessDate");
                isCheck = loginInfo.getString("isCheck");
            } catch (Exception e) {
                // 如果不是JSON字符串，直接使用原值
                username = userNameOrJson;
            }
        } else {
            UserDetails userPrincipal = (UserDetails) authentication.getPrincipal();
            username = userPrincipal.getUsername();
        }
        
        JwtBuilder builder = Jwts.builder()
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationInMs))
                .signWith(SignatureAlgorithm.HS512, jwtSecret);
        
        // 添加自定义字段到JWT claims中
        if (businessDate != null) {
            builder.claim("businessDate", businessDate);
        }
        if (isCheck != null) {
            builder.claim("isCheck", isCheck);
        }
        
        return builder.compact();
    }

    public String getUserNameFromJwtToken(String token) {
        return Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody().getSubject();
    }

    public boolean validateJwtToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(authToken);
            return true;
        } catch (SignatureException e) {
            System.err.println("Invalid JWT signature: " + e.getMessage());
        } catch (MalformedJwtException e) {
            System.err.println("Invalid JWT token: " + e.getMessage());
        } catch (ExpiredJwtException e) {
            System.err.println("JWT token is expired: " + e.getMessage());
        } catch (UnsupportedJwtException e) {
            System.err.println("JWT token is unsupported: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            System.err.println("JWT claims string is empty: " + e.getMessage());
        }
        return false;
    }

    public String getBusinessDateFromJwtToken(String token) {
        try {
            Claims claims = Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();
            return claims.get("businessDate", String.class);
        } catch (Exception e) {
            return null;
        }
    }

    public String getIsCheckFromJwtToken(String token) {
        try {
            Claims claims = Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();
            return claims.get("isCheck", String.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取JWT中的所有Claims信息
     * @param token JWT token
     * @return Claims对象，包含所有自定义字段
     */
    public Claims getClaimsFromJwtToken(String token) {
        try {
            return Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token).getBody();
        } catch (Exception e) {
            System.err.println("获取JWT Claims失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从JWT中获取用户姓名
     */
    public String getEmployeeNameFromJwtToken(String token) {
        try {
            Claims claims = getClaimsFromJwtToken(token);
            return claims != null ? claims.get("employeeName", String.class) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JWT中获取公司代码
     */
    public String getCorpCodeFromJwtToken(String token) {
        try {
            Claims claims = getClaimsFromJwtToken(token);
            return claims != null ? claims.get("corpCode", String.class) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JWT中获取部门代码
     */
    public String getDeptCodeFromJwtToken(String token) {
        try {
            Claims claims = getClaimsFromJwtToken(token);
            return claims != null ? claims.get("deptCode", String.class) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JWT中获取部门名称
     */
    public String getDeptNameFromJwtToken(String token) {
        try {
            Claims claims = getClaimsFromJwtToken(token);
            return claims != null ? claims.get("deptName", String.class) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从JWT中获取会计年度
     */
    public String getAccountantYearFromJwtToken(String token) {
        try {
            Claims claims = getClaimsFromJwtToken(token);
            return claims != null ? claims.get("accountantYear", String.class) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取JWT token的完整信息用于调试
     */
    public String getTokenInfo(String token) {
        try {
            Claims claims = getClaimsFromJwtToken(token);
            if (claims != null) {
                StringBuilder info = new StringBuilder();
                info.append("Subject: ").append(claims.getSubject()).append(", ");
                info.append("EmployeeName: ").append(claims.get("employeeName")).append(", ");
                info.append("CorpCode: ").append(claims.get("corpCode")).append(", ");
                info.append("DeptCode: ").append(claims.get("deptCode")).append(", ");
                info.append("DeptName: ").append(claims.get("deptName")).append(", ");
                info.append("AccountantYear: ").append(claims.get("accountantYear")).append(", ");
                info.append("BusinessDate: ").append(claims.get("businessDate")).append(", ");
                info.append("IsCheck: ").append(claims.get("isCheck"));
                return info.toString();
            }
        } catch (Exception e) {
            System.err.println("获取Token信息失败: " + e.getMessage());
        }
        return "Token信息获取失败";
    }
} 