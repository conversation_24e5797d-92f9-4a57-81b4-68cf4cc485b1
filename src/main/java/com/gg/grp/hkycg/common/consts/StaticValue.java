package com.gg.grp.hkycg.common.consts;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 静态信息
 * <AUTHOR>
 */
@Component
@Setter
@ConfigurationProperties(prefix = "project")
public class StaticValue {
    /**
     * 公司代码
     */
    private String gsdm;

    private String zydm;

    private String zymc;

    private static StaticValue instance;

    @PostConstruct
    public void init() {
        instance = this;
    }

    public static String getGsdm() {
        return instance.gsdm;
    }

    public static String getZydm() {
        return instance.zydm;
    }

    public static String getZymc() {
        return instance.zymc;
    }

}
