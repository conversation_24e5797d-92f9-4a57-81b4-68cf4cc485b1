package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.mapper.BudgetIndexMapper;
import com.gg.grp.hkycg.model.dto.BudgetIndexDTO;
import com.gg.grp.hkycg.model.pojo.BudgetIndex;
import com.gg.grp.hkycg.service.BudgetIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预算指标服务实现类
 */
@Slf4j
@Service
public class BudgetIndexServiceImpl extends ServiceImpl<BudgetIndexMapper, BudgetIndex> implements BudgetIndexService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BudgetIndexDTO> saveList(List<BudgetIndexDTO> budgetIndexDTOList, String lid) {
        if (CollectionUtils.isEmpty(budgetIndexDTOList)) {
            return new ArrayList<>();
        }

        // 转换DTO为实体
        List<BudgetIndex> budgetIndexList = budgetIndexDTOList.stream().map(dto -> {
            BudgetIndex budgetIndex = new BudgetIndex();
            BeanUtils.copyProperties(dto, budgetIndex);
            budgetIndex.setLid(lid);
            budgetIndex.setDel(false);
            return budgetIndex;
        }).collect(Collectors.toList());

        // 使用自定义的批量插入方法
        baseMapper.batchInsert(budgetIndexList);

        // 转换实体为DTO并返回
        return budgetIndexList.stream().map(entity -> {
            BudgetIndexDTO dto = new BudgetIndexDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BudgetIndexDTO> updateList(List<BudgetIndexDTO> budgetIndexDTOList, String lid) {
        // 先删除原有数据
        del(lid);
        
        // 再保存新数据
        return saveList(budgetIndexDTOList, lid);
    }

    @Override
    public List<BudgetIndexDTO> findByBid(String lid) {
        List<BudgetIndex> budgetIndexList = baseMapper.selectByLid(lid);
        
        return budgetIndexList.stream().map(entity -> {
            BudgetIndexDTO dto = new BudgetIndexDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(String lid) {
        baseMapper.deleteByLid(lid);
    }
} 