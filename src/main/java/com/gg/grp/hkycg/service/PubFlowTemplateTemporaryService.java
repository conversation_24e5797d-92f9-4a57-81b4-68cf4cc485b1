package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.FlowTemplateDTO;
import com.gg.grp.hkycg.model.vo.FlowTemplateNodeVO;
import com.gg.grp.hkycg.model.pojo.PubFlowTemplateTemporary;

import java.util.List;

public interface PubFlowTemplateTemporaryService extends IService<PubFlowTemplateTemporary> {
    /**
     * 查询登录人临时缓存的节点列表
     * @param billTypeId 单据类型id
     * @return 登录人缓存的节点列表
     */
    List<FlowTemplateDTO> queryCacheNodes(Integer billTypeId);

    /**
     * 保存临时修改的审核流程节点列表
     * @param flowTemplateDtoList 节点列表
     * @param billTypeId 单据类型id
     */
    void save(List<FlowTemplateDTO> flowTemplateDtoList, Integer billTypeId);

    /**
     * 删除临时保存的审核流程
     * @param billTypeId 审核流程对应的单据类型id
     */
    void delete(Integer billTypeId);

    /**
     * 将单据类型的审核流程的节点标记为合法
     * @param billTypeId 单据类型
     */
    void legal(Integer billTypeId);

    /**
     * 检查单据类型对应的审核流程是否经过了校验
     * @param billTypeId 单据类型id
     */
    void checkLegal(Integer billTypeId);

    /**
     * 退回必要性检验
     * @param billTypeId 单据类型id
     * @return 退回必要性提示信息
     */
    String rejectNecessity(Integer billTypeId);

    /**
     * 保存前校验
     * @param nodeList 要保存的节点及其审核人
     */
    void checkBeforeSave(List<FlowTemplateNodeVO> nodeList);

    /**
     * 将临时保存的流程和推送前的正式流程转存至临时表中
     * @param billTypeId 单据类型id
     * @param flowTemplateDTOList 推送前的正式流程
     */
    void move2Log(Integer billTypeId, List<FlowTemplateDTO> flowTemplateDTOList);
}
