package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.CgzzfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzzfsListVO;
import com.gg.grp.hkycg.model.vo.CgzzfsVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzzfs;

import java.util.List;

/**
 * 采购组织方式Service接口
 */
public interface CgzzfsService extends IService<GpmCgzzfs> {

    /**
     * 查询所有采购组织方式
     * @return 采购组织方式列表
     */
    List<CgzzfsVO> getAllCgzzfs();

    /**
     * 分页查询采购组织方式列表
     * @param queryDTO 查询参数
     * @return 采购组织方式列表
     */
    List<CgzzfsListVO> getCgzzfsPageList(CgzzfsPageQueryDTO queryDTO);
} 