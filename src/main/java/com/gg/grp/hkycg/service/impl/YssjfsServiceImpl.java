package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.GpmYssjfsMapper;
import com.gg.grp.hkycg.model.dto.YssjfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.YssjfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmYssjfs;
import com.gg.grp.hkycg.service.YssjfsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预算审计方式Service实现类
 */
@Slf4j
@Service
public class YssjfsServiceImpl extends ServiceImpl<GpmYssjfsMapper, GpmYssjfs> implements YssjfsService {

    @Autowired
    private GpmYssjfsMapper yssjfsMapper;

    @Override
    public List<YssjfsListVO> getYssjfsPageList(YssjfsPageQueryDTO queryDTO) {
        try {
            log.info("分页查询预算审计方式，查询条件：{}", queryDTO);

            // 设置默认公司代码和年度
            if (StringUtils.isBlank(queryDTO.getGsdm())) {
                queryDTO.setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(queryDTO.getKjnd())) {
                queryDTO.setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 调用Mapper查询
            List<YssjfsListVO> resultList = yssjfsMapper.getYssjfsPageList(queryDTO);

            // 状态转换
            resultList.forEach(item -> {
                // 使用状态转换：1->启用，0->停用
                if ("1".equals(item.getSyzt())) {
                    item.setSyztmc("启用");
                } else if ("0".equals(item.getSyzt())) {
                    item.setSyztmc("停用");
                } else {
                    item.setSyztmc(item.getSyzt());
                }
            });

            log.info("分页查询预算审计方式完成，返回记录数：{}", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("分页查询预算审计方式失败，查询条件：{}", queryDTO, e);
            throw new RuntimeException("分页查询预算审计方式失败：" + e.getMessage(), e);
        }
    }
} 