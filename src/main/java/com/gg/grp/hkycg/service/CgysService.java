package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 采购验收Service接口
 */
public interface CgysService {

    /**
     * 保存采购验收
     * @param cgysSaveDTO 采购验收保存DTO
     * @return 保存的采购验收详情
     */
    CgysSaveResponseVO saveCgys(CgysSaveDTO cgysSaveDTO);

    /**
     * 更新采购验收（使用验收申请编号）
     * @param ysbh 验收申请编号
     * @param cgysSaveDTO 采购验收保存DTO
     * @return 更新后的采购验收详情
     */
    CgysSaveResponseVO updateCgysByYsbh(String ysbh, CgysSaveDTO cgysSaveDTO);

    /**
     * 根据验收申请编号删除采购验收
     * @param ysbh 验收申请编号
     * @return 是否删除成功
     */
    boolean deleteByYsbh(String ysbh);

    /**
     * 根据验收申请编号查询采购验收详情
     * @param ysbh 验收申请编号
     * @return 采购验收详情VO
     */
    CgysSaveResponseVO getCgysDetailByYsbh(String ysbh);

    /**
     * 分页查询采购验收列表
     * @param queryDTO 查询参数
     * @return 采购验收列表
     */
    List<CgysListVO> getCgysPageList(CgysPageQueryDTO queryDTO);

    /**
     * 提交采购验收（使用验收申请编号）
     * @param billnoType 单据类型
     * @param ysbh 验收申请编号
     * @param money 金额
     * @param auditor 审核人
     * @return 工作流程节点映射
     */
    Map<Integer, List<PubObjFlowTemp>> commitProxyByYsbh(BillnoType billnoType, String ysbh, Double money, String auditor);

    /**
     * 审核采购验收
     * @param billType 单据类型
     * @param ysbh 验收申请编号
     * @param opinion 审核意见
     * @param auditor 审核人
     * @param money 金额
     */
    void checkByYsbh(BillnoType billType, String ysbh, String opinion, String auditor, BigDecimal money);

    /**
     * 更新采购验收状态
     * @param ysbh 验收申请编号
     * @param status 状态
     */
    void updateStatusByYsbh(String ysbh, Integer status);

    /**
     * 收回采购验收
     * @param ysbh 验收申请编号
     * @return 收回后的采购验收详情
     */
    CgysSaveResponseVO callBackByYsbh(String ysbh);

    /**
     * 退审采购验收
     * @param ysbh 验收申请编号
     * @param opinion 退审意见
     * @param auditor 退审人
     * @return 退审后的采购验收详情
     */
    CgysSaveResponseVO checkCallBackByYsbh(String ysbh, String opinion, String auditor);

    /**
     * 销审采购验收
     * @param ysbh 验收申请编号
     * @return 销审后的采购验收详情
     */
    CgysSaveResponseVO noAuditByYsbh(String ysbh);

    /**
     * 查询采购验收审核记录
     * @param billnoType 单据类型
     * @param ysbh 验收申请编号
     * @return 审核记录
     */
    CgysAuditLogVO findCheckLogByYsbh(BillnoType billnoType, String ysbh);

    /**
     * 检查采购验收是否有权限审核
     * @param ysbh 验收申请编号
     * @return 权限检查结果
     */
    Object isCheckedByAuthorityByYsbh(String ysbh);
}