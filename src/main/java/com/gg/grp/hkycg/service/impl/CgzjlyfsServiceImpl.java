package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.GpmCgzjlyfsMapper;
import com.gg.grp.hkycg.model.dto.CgzjlyfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjlyfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzjlyfs;
import com.gg.grp.hkycg.service.CgzjlyfsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 专家来源方式Service实现类
 */
@Slf4j
@Service
public class CgzjlyfsServiceImpl extends ServiceImpl<GpmCgzjlyfsMapper, GpmCgzjlyfs> implements CgzjlyfsService {

    @Autowired
    private GpmCgzjlyfsMapper cgzjlyfsMapper;

    @Override
    public List<CgzjlyfsListVO> getCgzjlyfsPageList(CgzjlyfsPageQueryDTO queryDTO) {
        try {
            log.info("分页查询专家来源方式，查询条件：{}", queryDTO);
            
            // 设置默认值
            if (StringUtils.isBlank(queryDTO.getGsdm())) {
                queryDTO.setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(queryDTO.getKjnd())) {
                queryDTO.setKjnd(LoginInfo.getCurrAccountantYear());
            }
            
            // 设置分页参数
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();
            
            List<CgzjlyfsListVO> result = cgzjlyfsMapper.getCgzjlyfsPageList(queryDTO);
            
            log.info("查询专家来源方式列表成功，返回记录数：{}", result.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("分页查询专家来源方式失败，参数：{}，错误：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询专家来源方式列表失败：" + e.getMessage(), e);
        }
    }
} 