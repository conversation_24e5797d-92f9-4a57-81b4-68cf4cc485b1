package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.IndexParams;
import com.gg.grp.hkycg.model.vo.IndexVO;
import com.gg.grp.hkycg.model.pojo.OerYszb;

import java.util.List;

/**
 * 预算指标服务接口
 */
public interface IndexService extends IService<OerYszb> {

    
    /**
     * 指标查询（返回VO）
     * @param indexParams 查询参数
     * @return 预算指标VO列表
     */
    List<IndexVO> qryIndex(IndexParams indexParams);
} 