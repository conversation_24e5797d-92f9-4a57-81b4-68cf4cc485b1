package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.model.pojo.Employee;
import com.gg.grp.hkycg.mapper.EmployeeMapper;
import com.gg.grp.hkycg.service.EmployeeService;
import org.springframework.stereotype.Service;

@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements EmployeeService {

    @Override
    public Employee getByEmployeeCode(String employeeCode) {
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Employee::getEmployeeCode, employeeCode);
        return this.getOne(queryWrapper);
    }
}
