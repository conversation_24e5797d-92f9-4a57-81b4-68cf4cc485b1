package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.mapper.GnflMapper;
import com.gg.grp.hkycg.model.vo.UserGnflUrlVO;
import com.gg.grp.hkycg.model.pojo.GpmGnfl;
import com.gg.grp.hkycg.service.GnflService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功能服务实现类
 */
@Service
public class GnflServiceImpl extends ServiceImpl<GnflMapper, GpmGnfl> implements GnflService {

    @Override
    public GpmGnfl getFunctionByUrl(String url) {
        LambdaQueryWrapper<GpmGnfl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmGnfl::getUrl, url);
        return this.getOne(queryWrapper);
    }

    @Override
    public GpmGnfl getFunctionByCode(String code) {
        LambdaQueryWrapper<GpmGnfl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmGnfl::getCode, code);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<GpmGnfl> getAllFunctions() {
        return this.list();
    }

    @Override
    public List<GpmGnfl> getFunctionsByRoleCode(String roleCode) {
        return this.baseMapper.getFunctionsByRoleCode(roleCode);
    }

    @Override
    public Result getUserGNFL() {
        List<UserGnflUrlVO> userGnflUrl = baseMapper.getUserGnflUrl(
                LoginInfo.getCurrCorpCode(),
                LoginInfo.getCurrAccountantYear(),
                LoginInfo.getCurrEmployeeCode()
        );
        return Result.success("查询成功!",userGnflUrl);
    }
} 