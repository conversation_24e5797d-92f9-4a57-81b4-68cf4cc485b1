package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.model.pojo.GpmRolegnfl;
import com.gg.grp.hkycg.mapper.CimRolegnflMapper;
import com.gg.grp.hkycg.service.RolegnflService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色功能关联服务实现类
 */
@Service
public class RolegnflServiceImpl extends ServiceImpl<CimRolegnflMapper, GpmRolegnfl> implements RolegnflService {

    @Override
    public List<String> getFunctionCodesByRole(String roleCode) {
        LambdaQueryWrapper<GpmRolegnfl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmRolegnfl::getRolecode, roleCode);
        return this.list(queryWrapper)
                .stream()
                .map(GpmRolegnfl::getGnflcode)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getRoleCodesByFunction(String gnflCode) {
        LambdaQueryWrapper<GpmRolegnfl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmRolegnfl::getGnflcode, gnflCode);
        return this.list(queryWrapper)
                .stream()
                .map(GpmRolegnfl::getRolecode)
                .collect(Collectors.toList());
    }

    @Override
    public boolean hasPermission(String roleCode, String gnflCode) {
        LambdaQueryWrapper<GpmRolegnfl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmRolegnfl::getRolecode, roleCode)
                .eq(GpmRolegnfl::getGnflcode, gnflCode);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public boolean addPermission(String roleCode, String gnflCode) {
        // 检查是否已存在
        if (hasPermission(roleCode, gnflCode)) {
            return true;
        }
        
        GpmRolegnfl rolegnfl = new GpmRolegnfl();
        rolegnfl.setRolecode(roleCode);
        rolegnfl.setGnflcode(gnflCode);
        return this.save(rolegnfl);
    }

    @Override
    public boolean removePermission(String roleCode, String gnflCode) {
        LambdaQueryWrapper<GpmRolegnfl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmRolegnfl::getRolecode, roleCode)
                .eq(GpmRolegnfl::getGnflcode, gnflCode);
        return this.remove(queryWrapper);
    }
} 