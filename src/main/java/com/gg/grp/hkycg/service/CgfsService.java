package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.CgfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgfsListVO;
import com.gg.grp.hkycg.model.vo.CgfsVO;
import com.gg.grp.hkycg.model.pojo.GpmCgfs;

import java.util.List;

/**
 * 采购方式Service接口
 */
public interface CgfsService extends IService<GpmCgfs> {

    /**
     * 查询所有采购方式
     * @return 采购方式列表
     */
    List<CgfsVO> getAllCgfs();

    /**
     * 分页查询采购方式列表
     * @param queryDTO 查询参数
     * @return 采购方式列表
     */
    List<CgfsListVO> getCgfsPageList(CgfsPageQueryDTO queryDTO);
} 