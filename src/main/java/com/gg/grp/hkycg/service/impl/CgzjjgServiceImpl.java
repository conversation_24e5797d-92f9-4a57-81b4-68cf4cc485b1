package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.GpmCgzjjgMapper;
import com.gg.grp.hkycg.model.dto.CgzjjgPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjjgListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzjjg;
import com.gg.grp.hkycg.service.CgzjjgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采购中介机构Service实现类
 */
@Slf4j
@Service
public class CgzjjgServiceImpl extends ServiceImpl<GpmCgzjjgMapper, GpmCgzjjg> implements CgzjjgService {

    @Autowired
    private GpmCgzjjgMapper cgzjjgMapper;

    @Override
    public List<CgzjjgListVO> getCgzjjgPageList(CgzjjgPageQueryDTO queryDTO) {
        try {
            log.info("分页查询采购中介机构，查询条件：{}", queryDTO);
            
            // 设置默认值
            if (StringUtils.isBlank(queryDTO.getGsdm())) {
                queryDTO.setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(queryDTO.getKjnd())) {
                queryDTO.setKjnd(LoginInfo.getCurrAccountantYear());
            }
            
            List<CgzjjgListVO> result = cgzjjgMapper.getCgzjjgPageList(queryDTO);
            
            log.info("查询采购中介机构列表成功，返回记录数：{}", result.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("分页查询采购中介机构失败，参数：{}，错误：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询采购中介机构列表失败：" + e.getMessage(), e);
        }
    }
} 