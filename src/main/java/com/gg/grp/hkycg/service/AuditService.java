package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.common.PageResult;
import com.gg.grp.hkycg.model.dto.UnifiedAuditQueryDTO;
import com.gg.grp.hkycg.model.vo.UnifiedAuditListVO;

/**
 * 统一审核服务接口
 */
public interface AuditService {

    /**
     * 获取统一的待办审核列表
     * @param queryDTO 查询参数
     * @return 分页的审核列表
     */
    PageResult<UnifiedAuditListVO> getUnifiedAuditList(UnifiedAuditQueryDTO queryDTO);
}