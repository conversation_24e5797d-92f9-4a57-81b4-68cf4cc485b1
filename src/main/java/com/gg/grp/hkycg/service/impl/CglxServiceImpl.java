package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.GpmCglxMapper;
import com.gg.grp.hkycg.model.dto.CglxPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CglxListVO;
import com.gg.grp.hkycg.model.vo.CglxVO;
import com.gg.grp.hkycg.model.pojo.GpmCglx;
import com.gg.grp.hkycg.service.CglxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购类型Service实现类
 */
@Slf4j
@Service
public class CglxServiceImpl extends ServiceImpl<GpmCglxMapper, GpmCglx> implements CglxService {

    @Autowired
    private GpmCglxMapper cglxMapper;

    @Override
    public List<CglxVO> getAllCglx() {
        try {
            // 构建查询条件
            LambdaQueryWrapper<GpmCglx> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCglx::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmCglx::getKjnd, LoginInfo.getCurrAccountantYear())
                    .ne(GpmCglx::getSyzt, "停用")
                    .ne(GpmCglx::getSyzt, "0")
                    .orderByAsc(GpmCglx::getPxh); // 按排序号升序

            // 查询数据
            List<GpmCglx> cglxList = cglxMapper.selectList(queryWrapper);

            // 转换为VO对象
            List<CglxVO> cglxVOList = cglxList.stream()
                    .map(cglx -> {
                        CglxVO cglxVO = new CglxVO();
                        BeanUtils.copyProperties(cglx, cglxVO);
                        return cglxVO;
                    })
                    .collect(Collectors.toList());

            log.info("查询采购类型成功，共{}条记录", cglxVOList.size());
            return cglxVOList;

        } catch (Exception e) {
            log.error("查询采购类型失败", e);
            throw new RuntimeException("查询采购类型失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CglxListVO> getCglxPageList(CglxPageQueryDTO queryDTO) {
        try {
            log.info("分页查询采购类型，查询条件：{}", queryDTO);

            // 设置默认公司代码和年度
            if (StringUtils.isBlank(queryDTO.getGsdm())) {
                queryDTO.setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(queryDTO.getKjnd())) {
                queryDTO.setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 调用Mapper查询
            List<CglxListVO> resultList = cglxMapper.getCglxPageList(queryDTO);

            // 状态转换
            resultList.forEach(item -> {
                // 使用状态转换：1->启用，0->停用
                if ("1".equals(item.getSyzt())) {
                    item.setSyztmc("启用");
                } else if ("0".equals(item.getSyzt())) {
                    item.setSyztmc("停用");
                } else {
                    item.setSyztmc(item.getSyzt());
                }
            });

            log.info("分页查询采购类型完成，返回记录数：{}", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("分页查询采购类型失败，查询条件：{}", queryDTO, e);
            throw new RuntimeException("分页查询采购类型失败：" + e.getMessage(), e);
        }
    }
} 