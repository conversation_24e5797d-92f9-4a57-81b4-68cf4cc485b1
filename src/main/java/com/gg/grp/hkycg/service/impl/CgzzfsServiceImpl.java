package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.GpmCgzzfsMapper;
import com.gg.grp.hkycg.model.dto.CgzzfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzzfsListVO;
import com.gg.grp.hkycg.model.vo.CgzzfsVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzzfs;
import com.gg.grp.hkycg.service.CgzzfsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购组织方式Service实现类
 */
@Slf4j
@Service
public class CgzzfsServiceImpl extends ServiceImpl<GpmCgzzfsMapper, GpmCgzzfs> implements CgzzfsService {

    @Autowired
    private GpmCgzzfsMapper cgzzfsMapper;

    @Override
    public List<CgzzfsVO> getAllCgzzfs() {
        try {
            // 构建查询条件
            LambdaQueryWrapper<GpmCgzzfs> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzzfs::getGsdm, LoginInfo.getCurrCorpCode())
                       .eq(GpmCgzzfs::getKjnd, LoginInfo.getCurrAccountantYear())
                       .ne(GpmCgzzfs::getSyzt, "0")
                       .eq(GpmCgzzfs::getSfxs, "1") // 只查询显示的记录
                       .orderByAsc(GpmCgzzfs::getPxh); // 按排序号升序

            // 查询数据
            List<GpmCgzzfs> cgzzfsList = cgzzfsMapper.selectList(queryWrapper);

            // 转换为VO对象
            List<CgzzfsVO> cgzzfsVOList = cgzzfsList.stream()
                    .map(cgzzfs -> {
                        CgzzfsVO cgzzfsVO = new CgzzfsVO();
                        BeanUtils.copyProperties(cgzzfs, cgzzfsVO);
                        return cgzzfsVO;
                    })
                    .collect(Collectors.toList());

            log.info("查询采购组织方式成功，共{}条记录", cgzzfsVOList.size());
            return cgzzfsVOList;

        } catch (Exception e) {
            log.error("查询采购组织方式失败", e);
            throw new RuntimeException("查询采购组织方式失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgzzfsListVO> getCgzzfsPageList(CgzzfsPageQueryDTO queryDTO) {
        try {
            log.info("分页查询采购组织方式，查询条件：{}", queryDTO);

            // 设置默认公司代码和年度
            if (StringUtils.isBlank(queryDTO.getGsdm())) {
                queryDTO.setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(queryDTO.getKjnd())) {
                queryDTO.setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 调用Mapper查询
            List<CgzzfsListVO> resultList = cgzzfsMapper.getCgzzfsPageList(queryDTO);

            // 状态转换
            resultList.forEach(item -> {
                // 使用状态转换：1->启用，0->停用
                if ("1".equals(item.getSyzt())) {
                    item.setSyztmc("启用");
                } else if ("0".equals(item.getSyzt())) {
                    item.setSyztmc("停用");
                } else {
                    item.setSyztmc(item.getSyzt());
                }
                
                // 是否显示转换：1->是，0->否
                if ("1".equals(item.getSfxs())) {
                    item.setSfxsmc("是");
                } else if ("0".equals(item.getSfxs())) {
                    item.setSfxsmc("否");
                } else {
                    item.setSfxsmc(item.getSfxs());
                }
            });

            log.info("分页查询采购组织方式完成，返回记录数：{}", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("分页查询采购组织方式失败，查询条件：{}", queryDTO, e);
            throw new RuntimeException("分页查询采购组织方式失败：" + e.getMessage(), e);
        }
    }
} 