package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.mapper.PubObjFlowTempMapper;
import com.gg.grp.hkycg.mapper.DynamicQueryMapper;
import com.gg.grp.hkycg.model.dto.FlowDto;
import com.gg.grp.hkycg.model.dto.FlowTemplateDTO;
import com.gg.grp.hkycg.model.vo.ObjflowVO;
import com.gg.grp.hkycg.model.pojo.FitCheckNode;
import com.gg.grp.hkycg.model.pojo.PubObjFlow;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.BeanConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流程模板服务实现类
 */
@Service
public class PubObjFlowTempServiceImpl extends ServiceImpl<PubObjFlowTempMapper, PubObjFlowTemp> implements PubObjFlowTempService {

    @Autowired
    private PubObjFlowTempMapper pubObjFlowTempMapper;
    @Autowired
    private DynamicQueryMapper dynamicQueryMapper;


    @Override
    public List<PubObjFlowTemp> selectListByDjlxid(Integer djlxid) {
        return pubObjFlowTempMapper.selectListByDjlxid(djlxid);
    }

    @Override
    public List<PubObjFlowTemp> selectByCondition(String gsdm, String kjnd, String flowcode, Integer jddm) {
        return pubObjFlowTempMapper.selectByCondition(gsdm, kjnd, flowcode, jddm);
    }

    @Override
    public List<FlowTemplateDTO> queryBillTypeNodes(Integer billTypeId) {
        List<PubObjFlowTemp> flowTemps = this.selectListByDjlxid(billTypeId);
        return BeanConvertUtils.convertListTo
                (flowTemps, FlowTemplateDTO::new, (template, templateDto) ->
                        templateDto.setDynamicAcquisition(StringUtils.isBlank(template.getShrdm())));
    }

    @Override
    public List<ObjflowVO> commitTemporary(List<FlowTemplateDTO> flowTemplateDtoList, FlowDto flowDto) {
        String djh = flowDto.getDjh();
        BillnoType billType = flowDto.getBillType();
        List<FitCheckNode> fitCheckNodes = getFitCheckNodes(djh, billType, flowTemplateDtoList);
        // 此处模拟了OER系统的部分逻辑，未来可以根据实际情况进行调整
        List<PubObjFlow> objflowModelList = mockCommitFlowResult(djh, billType, fitCheckNodes);
        Map<Integer, String> jdDmMcMap = flowTemplateDtoList.stream()
                .collect(Collectors.toMap(FlowTemplateDTO::getJddm, FlowTemplateDTO::getJdmc, (dm1, dm2) -> dm2));
        return BeanConvertUtils
                .convertListTo(objflowModelList, ObjflowVO::new, (flowModel, flowVo) -> {
//                            Integer auditFlag = Integer.valueOf(flowModel.getAUDIT_FLAG());
//                            flowVo.setCurrNodeName(jdDmMcMap.get(auditFlag));
//                            flowVo.setShrmc(flowModel.getSHRMC());
//                            flowVo.setAuditFlag(auditFlag);
                        }
                );
    }

    private List<FitCheckNode> getFitCheckNodes(String djh, BillnoType billType, List<FlowTemplateDTO> templateDTOList) {
//        List<FitCheckNode> fitCheckNodes = new ArrayList<>();
//        String tableName = billType.getTableName();
//        String billNoColumn = billType.getBillNoColumn();
//
//        for (FlowTemplateDTO template : templateDTOList) {
//            StringBuilder sqlBuilder = new StringBuilder();
//            sqlBuilder.append("SELECT COUNT(*) FROM ").append(tableName).append(" WHERE ").append(billNoColumn).append(" = '").append(djh).append("'");
//            if (StringUtils.isNotBlank(template.getShtj())) {
//                sqlBuilder.append(" AND (").append(template.getShtj()).append(")");
//            }
//            if (StringUtils.isNotBlank(template.getJdshtj())) {
//                sqlBuilder.append(" AND (").append(template.getJdshtj()).append(")");
//            }
//
//            int count = dynamicQueryMapper.checkCondition(sqlBuilder.toString());
//            if (count > 0) {
//                FitCheckNode node = new FitCheckNode();
//                node.setJd(template.getJddm());
//                node.setShrdm(template.getShrdm());
//                node.setShrmc(template.getShrmc());
//                // ... 其他属性设置
//                fitCheckNodes.add(node);
//            }
//        }
//        return fitCheckNodes;
        return null;
    }

    private List<PubObjFlow> mockCommitFlowResult(String djh, BillnoType billType, List<FitCheckNode> fitCheckNodes) {
        List<PubObjFlow> objflowList = new ArrayList<>();
        int sxh = 1;
        for (FitCheckNode node : fitCheckNodes) {
            PubObjFlow model = new PubObjFlow();
            model.setDjh(djh);
            model.setDjlx(billType.getCode().toString());
            objflowList.add(model);
        }
        return objflowList;
    }
} 