package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.mapper.GpmDataMapper;
import com.gg.grp.hkycg.mapper.GpmUserdataMapper;
import com.gg.grp.hkycg.model.pojo.GpmData;
import com.gg.grp.hkycg.model.pojo.GpmUserdata;
import com.gg.grp.hkycg.service.GpmUserdataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * GPM_USERDATA表服务实现类
 */
@Service
public class GpmUserdataServiceImpl extends ServiceImpl<GpmUserdataMapper, GpmUserdata> implements GpmUserdataService {
    // 可以在这里实现自定义的服务方法
    
    @Autowired
    private GpmDataMapper gpmDataMapper;
    
    /**
     * 根据职员代码查询其拥有的数据权限
     * @param zydm 职员代码
     * @return 数据权限信息
     */
    @Override
    public GpmData getUserDataPermission(String zydm) {
        // 查询用户数据权限关联表
        LambdaQueryWrapper<GpmUserdata> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmUserdata::getZydm, zydm);
        GpmUserdata userdata = this.getOne(queryWrapper);
        
        if (userdata == null) {
            return null;
        }
        
        // 获取数据权限详情
        String datacode = userdata.getDatacode();
        LambdaQueryWrapper<GpmData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(GpmData::getDatacode, datacode);
        return gpmDataMapper.selectOne(dataWrapper);
    }
    
    /**
     * 为职员分配数据权限
     * @param zydm 职员代码
     * @param datacode 数据权限代码
     * @return 是否分配成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignDataPermission(String zydm, String datacode) {
        // 首先检查该数据权限是否存在
        LambdaQueryWrapper<GpmData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(GpmData::getDatacode, datacode);
        GpmData gpmData = gpmDataMapper.selectOne(dataWrapper);
        if (gpmData == null) {
            return false;
        }
        
        // 检查用户是否已有数据权限
        LambdaQueryWrapper<GpmUserdata> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(GpmUserdata::getZydm, zydm);
        GpmUserdata existingUserdata = this.getOne(userWrapper);
        
        if (existingUserdata != null) {
            // 更新已有的数据权限
            existingUserdata.setDatacode(datacode);
            return this.updateById(existingUserdata);
        } else {
            // 创建新的数据权限关联
            GpmUserdata newUserdata = new GpmUserdata();
            newUserdata.setZydm(zydm);
            newUserdata.setDatacode(datacode);
            return this.save(newUserdata);
        }
    }
    
    /**
     * 批量为职员分配相同的数据权限
     * @param zydmList 职员代码列表
     * @param datacode 数据权限代码
     * @return 操作结果，包含成功数量和失败数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchAssignResult batchAssignDataPermission(List<String> zydmList, String datacode) {
        // 首先检查该数据权限是否存在
        LambdaQueryWrapper<GpmData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(GpmData::getDatacode, datacode);
        GpmData gpmData = gpmDataMapper.selectOne(dataWrapper);
        if (gpmData == null) {
            return new BatchAssignResult(0, zydmList.size());
        }
        
        int successCount = 0;
        int failCount = 0;
        
        // 批量处理每个职员的数据权限分配
        for (String zydm : zydmList) {
            try {
                boolean result = assignDataPermission(zydm, datacode);
                if (result) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                failCount++;
            }
        }
        
        return new BatchAssignResult(successCount, failCount);
    }
} 