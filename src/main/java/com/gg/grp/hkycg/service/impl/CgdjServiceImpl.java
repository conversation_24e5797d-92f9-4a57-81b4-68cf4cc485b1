package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.consts.StaticValue;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.StatusName;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.CgdjService;
import com.gg.grp.hkycg.service.PubObjFlowService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import com.gg.grp.hkycg.utils.ConvertUtils;
import com.gg.grp.hkycg.utils.DataPermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购结果Service实现类
 */
@Slf4j
@Service
public class CgdjServiceImpl extends ServiceImpl<GpmCgdjmlMapper, GpmCgdjml> implements CgdjService {

    @Autowired
    private GpmCgdjmlMapper cgjgmlMapper;

    @Autowired
    private GpmCgdjnrMapper cgjgnrMapper;

    @Autowired
    private PubObjFlowService pubObjFlowService;

    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;
    @Autowired
    private PubAuditLogMapper pubAuditLogMapper;

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Autowired
    private PubWorkflowMapper pubWorkflowMapper;

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;

    @Autowired
    private DataPermissionUtils dataPermissionUtils;

    @Override
    public CgjgSaveResponseVO saveCgjg(CgjgSaveDTO cgjgSaveDTO) {
        try {
            // 设置默认值
            if (StringUtils.isBlank(cgjgSaveDTO.getBaseInfo().getGsdm())) {
                cgjgSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(cgjgSaveDTO.getBaseInfo().getKjnd())) {
                cgjgSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
            }
            for (CgjgDetailDTO detail : cgjgSaveDTO.getCgjgDetails()){
                if (detail.getSfcgsqyr()==detail.getSfcgzbyr()){
                    throw new GlobalException("招标申请必须引入一个");
                }
                if (StringUtils.isBlank(detail.getSqmxxh()) && StringUtils.isBlank(detail.getZbmxxh())){
                    throw new GlobalException("招标申请明细的明细序号不能为空");
                }
            }

            // 生成结果登记编号
            String jgdjbh = generateJgdjbh();
            cgjgSaveDTO.getBaseInfo().setJgdjbh(jgdjbh);

            // 保存主表
            GpmCgdjml cgjgml = new GpmCgdjml();
            BeanUtils.copyProperties(cgjgSaveDTO.getBaseInfo(), cgjgml);
            
            cgjgml.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            cgjgml.setCreateUserDm(LoginInfo.getCurrEmployeeCode());
            cgjgml.setCreateUser(LoginInfo.getCurrEmployeeName());
            cgjgml.setZt("1"); // 默认保存状态

            cgjgmlMapper.insert(cgjgml);

            // 保存明细
            if (cgjgSaveDTO.getCgjgDetails() != null && !cgjgSaveDTO.getCgjgDetails().isEmpty()) {
                for (int i = 0; i < cgjgSaveDTO.getCgjgDetails().size(); i++) {
                    CgjgDetailDTO detail = cgjgSaveDTO.getCgjgDetails().get(i);
                    String jgnrbh = generateJgnrbh(i);
                    
                    GpmCgdjnr cgjgnr = new GpmCgdjnr();
                    BeanUtils.copyProperties(detail, cgjgnr);
                    cgjgnr.setJgnrbh(jgnrbh);
                    cgjgnr.setJgdjbh(jgdjbh);
                    cgjgnr.setSfcgsqyr(ConvertUtils.convertBooleanToString(detail.getSfcgsqyr()));
                    cgjgnr.setSfcgzbyr(ConvertUtils.convertBooleanToString(detail.getSfcgzbyr()));
                    cgjgnr.setZfcg(ConvertUtils.convertBooleanToString(detail.getZfcg()));
                    cgjgnr.setJkcp(ConvertUtils.convertBooleanToString(detail.getJkcp()));
                    cgjgnrMapper.insert(cgjgnr);
                }
            }

            log.info("保存采购登记成功，jgdjbh：{}", jgdjbh);
            return buildSaveResponseVO(jgdjbh);

        } catch (Exception e) {
            log.error("保存采购登记失败", e);
            throw new GlobalException("保存采购登记失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjgSaveResponseVO updateCgjgByJgdjbh(String jgdjbh, CgjgSaveDTO cgjgSaveDTO) {
        try {
            // 检查是否存在
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml existingCgjgml = cgjgmlMapper.selectOne(queryWrapper);
            if (existingCgjgml == null) {
                throw new GlobalException("采购结果不存在，jgdjbh: " + jgdjbh);
            }

            if (!existingCgjgml.getZt().equals("1") && !existingCgjgml.getZt().equals("5")){
                throw new GlobalException("采购结果不是保存或退回，无法修改");
            }

            // 更新主表
            GpmCgdjml cgjgml = new GpmCgdjml();
            BeanUtils.copyProperties(cgjgSaveDTO.getBaseInfo(), cgjgml);
            
            cgjgml.setUpdateTime(new Date());
            cgjgml.setUpdateUserDm(LoginInfo.getCurrEmployeeCode());
            cgjgml.setUpdateUser(LoginInfo.getCurrEmployeeName());
            cgjgmlMapper.updateById(cgjgml);

            // 删除旧明细
            LambdaQueryWrapper<GpmCgdjnr> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(GpmCgdjnr::getJgdjbh, jgdjbh);
            cgjgnrMapper.delete(deleteWrapper);

            // 保存新明细
            if (cgjgSaveDTO.getCgjgDetails() != null && !cgjgSaveDTO.getCgjgDetails().isEmpty()) {
                for (int i = 0; i < cgjgSaveDTO.getCgjgDetails().size(); i++) {
                    CgjgDetailDTO detail = cgjgSaveDTO.getCgjgDetails().get(i);
                    String jgnrbh = generateJgnrbh(i);
                    
                    GpmCgdjnr cgjgnr = new GpmCgdjnr();
                    BeanUtils.copyProperties(detail, cgjgnr);
                    cgjgnr.setSfcgsqyr(ConvertUtils.convertBooleanToString(detail.getSfcgsqyr()));
                    cgjgnr.setSfcgzbyr(ConvertUtils.convertBooleanToString(detail.getSfcgzbyr()));
                    cgjgnr.setZfcg(ConvertUtils.convertBooleanToString(detail.getZfcg()));
                    cgjgnr.setJkcp(ConvertUtils.convertBooleanToString(detail.getJkcp()));
                    cgjgnr.setJgnrbh(jgnrbh);
                    cgjgnr.setJgdjbh(jgdjbh);
                    cgjgnrMapper.insert(cgjgnr);
                }
            }
            if (existingCgjgml.getZt().equals("5")){
                updateStatusByJgdjbh(jgdjbh,1);
            }

            log.info("更新采购结果成功，jgdjbh：{}", jgdjbh);
            return buildSaveResponseVO(jgdjbh);

        } catch (Exception e) {
            log.error("更新采购结果失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("更新采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteByJgdjbh(String jgdjbh) {
        try {
            // 删除主表
            LambdaQueryWrapper<GpmCgdjml> mlQueryWrapper = new LambdaQueryWrapper<>();
            mlQueryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            int mlResult = cgjgmlMapper.delete(mlQueryWrapper);

            // 删除明细
            LambdaQueryWrapper<GpmCgdjnr> nrQueryWrapper = new LambdaQueryWrapper<>();
            nrQueryWrapper.eq(GpmCgdjnr::getJgdjbh, jgdjbh);
            int nrResult = cgjgnrMapper.delete(nrQueryWrapper);

            log.info("删除采购结果成功，jgdjbh：{}，主表删除数：{}，明细删除数：{}", jgdjbh, mlResult, nrResult);
            return mlResult > 0;

        } catch (Exception e) {
            log.error("删除采购结果失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("删除采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjgSaveResponseVO getCgjgDetailByJgdjbh(String jgdjbh) {
        try {
            return buildSaveResponseVO(jgdjbh);
        } catch (Exception e) {
            log.error("查询采购结果详情失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("查询采购结果详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgjgListVO> getCgjgPageList(CgjgPageQueryDTO queryDTO) {
        try {
            // 设置默认值
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            String gsdm = StringUtils.isNotBlank(queryDTO.getGsdm()) ? queryDTO.getGsdm() : LoginInfo.getCurrCorpCode();
            String kjnd = StringUtils.isNotBlank(queryDTO.getKjnd()) ? queryDTO.getKjnd() : LoginInfo.getCurrAccountantYear();
            Integer offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取数据权限信息
            String currEmployeeCode = LoginInfo.getCurrEmployeeCode();
            String dataPermission = dataPermissionUtils.getCurrentUserDataPermission();
            String currDeptCode = LoginInfo.getCurrDeptCode(); // 使用LoginInfo.getCurrDeptCode()获取部门代码

            log.info("执行采购结果分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, offset={}, size={}, 用户={}, 权限={}, 部门={}",
                    gsdm, kjnd, queryDTO.getCondition(), queryDTO.getZt(), offset, queryDTO.getSize(), currEmployeeCode, dataPermission, currDeptCode);

            // 调用Mapper方法
            List<CgjgListVO> resultList = cgjgmlMapper.getCgjgPageList(
                    gsdm, kjnd, queryDTO.getCondition(), queryDTO.getZt(),
                    queryDTO.getStartDate(), queryDTO.getEndDate(),
                    offset, queryDTO.getSize(),
                    currEmployeeCode, dataPermission, currDeptCode
            );

            log.info("分页查询采购结果成功，记录数：{}", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("分页查询采购结果失败", e);
            throw new GlobalException("分页查询采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Map<Integer, List<PubObjFlowTemp>> commitProxyByJgdjbh(BillnoType billnoType, String jgdjbh, Double money, String auditor) {
        try {
            log.info("开始提交采购结果，jgdjbh：{}，金额：{}，提交人：{}", jgdjbh, money, auditor);

            // 1. 校验采购结果状态，只有保存状态（1）才能提交
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);
            
            if (cgjgml == null) {
                throw new GlobalException("采购结果不存在，无法提交，jgdjbh: " + jgdjbh);
            }
            
            // 2. 权限校验：只有创建人才能提交
            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = cgjgml.getCreateUserDm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 提交操作，当前用户：{}，录入人：{}，jgdjbh：{}", currentUser, originalInputUser, jgdjbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能提交此采购计划");
            }
            log.info("权限校验通过 - 提交操作，当前用户：{}，jgdjbh：{}", currentUser, jgdjbh);

            // 3. 检查状态 - 只有保存状态（1、5）才能提交
            if (cgjgml.getZt() == null || !"1".equals(cgjgml.getZt()) && !"5".equals(cgjgml.getZt())) {
                String statusName = StatusName.findByStatus(cgjgml.getZt());
                throw new GlobalException("只有保存状态的采购结果才能提交，当前状态为：" + statusName);
            }
            
            // 4. 获取工作流程模板节点
            List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new HashMap<>();
            
            if (templates == null || templates.isEmpty()) {
                // 如果没有配置审核模板，创建默认审核节点
                log.warn("未找到审核流程模板，将创建默认审核节点，单据类型：{}", billnoType.getCode());

                PubObjFlowTemp defaultTemplate = new PubObjFlowTemp();
                defaultTemplate.setShrdm(StaticValue.getZydm());
                defaultTemplate.setShrxm(StaticValue.getZymc());
                defaultTemplate.setJdmc("超级管理员审核");
                defaultTemplate.setJddm(1314);
                defaultTemplate.setGsdm(StaticValue.getGsdm());
                defaultTemplate.setDjlxid(BillnoType.CGJG.getCode());
//                defaultTemplate.setFlowcode("CGSQ_FLOW_001");
                defaultTemplate.setXh("1");
//                defaultTemplate.setKjnd(String.valueOf(Year.now().getValue()));

                List<PubObjFlowTemp> defaultList = new ArrayList<>();
                defaultList.add(defaultTemplate);
                nodeMap.put(1, defaultList);

                log.info("已创建默认审核节点，审核人：{}", defaultTemplate.getShrxm());
            } else {
                // 按节点序号分组
                for (PubObjFlowTemp template : templates) {
                    Integer nodeSeq = template.getXh() != null ? Integer.parseInt(template.getXh()) : 1;
                    if (template.getDynamicAcquisition().equals("1") && StringUtils.isNotBlank(template.getShtj())){
                        JSONObject auditorInfo = cgjhmlMapper.findAuditorInfo(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear(),
                                jgdjbh, template.getShtj());
                        template.setShrxm(auditorInfo.getString("shrxm"));
                        template.setShrdm(auditorInfo.getString("shrdm"));
                    }
                    nodeMap.computeIfAbsent(nodeSeq, k -> new ArrayList<>()).add(template);
                }
                log.info("获取审核流程模板成功，共{}个节点，模板数量：{}", nodeMap.size(), templates.size());
            }
            
            log.info("采购结果提交成功，jgdjbh：{}", jgdjbh);
            return nodeMap;
            
        } catch (Exception e) {
            log.error("提交采购结果失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("提交采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void checkByJgdjbh(BillnoType billType, String jgdjbh, String opinion, String auditor, BigDecimal money) {
        try {
            log.info("开始审核采购结果，jgdjbh：{}，审核人：{}，意见：{}", jgdjbh, auditor, opinion);
            
            // 1. 查询采购结果详情
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);

            if (cgjgml == null) {
                throw new GlobalException("采购结果不存在，无法审核，jgdjbh: " + jgdjbh);
            }

            // 2. 检查状态 - 只有已提交（2）或审核中（3）的采购结果才能审核
            if (cgjgml.getZt() == null || 
                (!"2".equals(cgjgml.getZt()) && !"3".equals(cgjgml.getZt()))) {
                String statusName = StatusName.findByStatus(cgjgml.getZt());
                throw new GlobalException("只有已提交或审核中状态的采购结果才能审核，当前状态为：" + statusName);
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 获取审批流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billType.getModCode(), String.valueOf(billType.getCode()));

            if (workflow == null) {
                log.warn("未找到审核流程配置");
                throw new GlobalException("未找到审核流程配置");
            }

            // 4. 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    String.valueOf(billType.getCode()), 
                    jgdjbh
            );

            // 5. 权限验证
            if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("该节点已被其他人审核或您无权限审核");
            }

            // 6. 执行审核
            int nowNote = Integer.parseInt(nowNodeInfo.getAuditFlag());
            int nextNote = Integer.parseInt(nowNodeInfo.getAuditAftFlag());
            
            // 判断是否为终审（nextNote == -1 表示最后一个节点）
            boolean isLastNode = (nextNote == -1);
            
            log.info("审核节点信息 - 当前节点：{}，下一节点：{}，是否终审：{}", nowNote, nextNote, isLastNode);

            // 更新当前节点为已审核
            nowNodeInfo.setIsaudit("1");
            nowNodeInfo.setSpecificCheckPerson(LoginInfo.getCurrEmployeeName());

            // 更新当前节点状态
            LambdaUpdateWrapper<PubObjFlow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PubObjFlow::getModcode, billType.getModCode())
                    .eq(PubObjFlow::getDjlx, String.valueOf(billType.getCode()))
                    .eq(PubObjFlow::getDjh, jgdjbh)
                    .eq(PubObjFlow::getAuditFlag, String.valueOf(nowNote))
                    .set(PubObjFlow::getIsaudit, "1")
                    .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

            pubObjFlowMapper.update(null, updateWrapper);

            // 7. 根据终审判断结果更新采购计划状态
            int status = isLastNode ? 4 : 3; // 4-已审核（终审），3-审核中
            updateStatusByJgdjbh(jgdjbh, status);
            if (status==4){
                CacheStatisticsUtils.clearStatisticsCache();
            }

            // 8. 记录审核日志
            createAuditLog(jgdjbh, billType, nowNote, "审核", opinion, LoginInfo.getCurrEmployeeName(), money,nowNodeInfo.getNodeName());

            log.info("采购结果审核成功，jgdjbh：{}，审核人：{}，是否终审：{}", jgdjbh, auditor, isLastNode);
            
        } catch (Exception e) {
            log.error("审核采购结果失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("审核采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void updateStatusByJgdjbh(String jgdjbh, Integer status) {
        try {
            LambdaUpdateWrapper<GpmCgdjml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            
            GpmCgdjml updateEntity = new GpmCgdjml();
            // 状态字段需要转换为字符串
            updateEntity.setZt(String.valueOf(status));
            int result = cgjgmlMapper.update(updateEntity, updateWrapper);
            log.info("更新采购结果状态成功，jgdjbh：{}，状态：{}，影响行数：{}", jgdjbh, status, result);
        } catch (Exception e) {
            log.error("更新采购结果状态失败，jgdjbh: {}，状态：{}", jgdjbh, status, e);
            throw new GlobalException("更新状态失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjgSaveResponseVO callBackByJgdjbh(String jgdjbh) {
        try {
            log.info("开始收回采购结果，jgdjbh：{}", jgdjbh);
            
            // 1. 查询采购结果
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);
            
            if (cgjgml == null) {
                throw new GlobalException("采购结果不存在，jgdjbh: " + jgdjbh);
            }
            
            // 2. 检查状态是否可以收回
            String currentStatus = cgjgml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购结果状态异常，无法收回");
            }

            // 权限校验：只有录入人才能收回
            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = cgjgml.getCreateUserDm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 收回操作，当前用户：{}，录入人：{}", currentUser, originalInputUser);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能收回此采购计划");
            }
            log.info("权限校验通过 - 收回操作，当前用户：{}", currentUser);
            
            // 只有已提交(2)的状态才能收回
            if (!"2".equals(currentStatus)) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法收回。只有已提交的采购结果才能收回");
            }
            
            // 3. 检查是否已经有人开始审核
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(jgdjbh);
            boolean hasStartedAudit = flowNodes.stream()
                    .anyMatch(node -> "1".equals(node.getIsaudit()));
            
            if (hasStartedAudit) {
                throw new GlobalException("该采购结果已开始审核，无法收回");
            }
            
            // 4. 删除工作流程节点
            try {
                // 删除工作流程实例
                if (pubObjFlowService != null) {
                    pubObjFlowService.deleteByCon(BillnoType.CGJG.getModCode(),
                            String.valueOf(BillnoType.CGJG.getCode()), jgdjbh);
                }
            } catch (Exception e) {
                log.warn("清理工作流程数据时出现异常，但不影响收回操作，单据编号：{}，异常：{}", jgdjbh, e.getMessage());
            }

            updateStatusByJgdjbh(jgdjbh, 1);
            log.info("采购结果收回成功，jgdjbh：{}", jgdjbh);
            
            return buildSaveResponseVO(jgdjbh);
            
        } catch (Exception e) {
            log.error("收回采购结果失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("收回采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjgSaveResponseVO checkCallBackByJgdjbh(String jgdjbh, String opinion, String auditor) {
        try {
            log.info("开始退审采购结果，jgdjbh：{}，退审人：{}", jgdjbh, auditor);
            // 1. 查询采购计划
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);

            if (cgjgml == null) {
                throw new GlobalException("采购计划不存在，单据编号：" + jgdjbh);
            }

            // 2. 检查状态是否可以退审
            String currentStatus = cgjgml.getZt();
            if (StringUtils.isBlank(currentStatus)) {
                throw new GlobalException("采购计划状态异常，无法退审");
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 1. 查询当前审核节点
            PubObjFlow currentNode = pubObjFlowService.selectNowNodeByDjh(
                    BillnoType.CGJG.getModCode(),
                    String.valueOf(BillnoType.CGJG.getCode()),
                    jgdjbh
            );
            
            if (currentNode == null) {
                throw new GlobalException("未找到当前审核节点，无法退审");
            }
            
            // 2. 检查退审权限
            if (!currentNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("您无权限对该采购结果进行退审操作");
            }

            // 3. 处理工作流程退审逻辑
            int currentNodeSeq = Integer.parseInt(currentNode.getAuditFlag());

            if (currentStatus.equals("2")) {
                updateStatusByJgdjbh(jgdjbh, 5);
                createAuditLog(jgdjbh, BillnoType.CGJG, currentNodeSeq, "退审", opinion, LoginInfo.getCurrEmployeeName(), null,currentNode.getNodeName());
                return buildSaveResponseVO(jgdjbh);
            }

            // 只有审核中(3)的状态才能退审
            if (!currentStatus.equals("3")) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgjgml.getZt()) + "，无法退审。只有审核中的采购计划才能退审");
            }

            // 查询所有工作流程节点，找到上一个节点
            List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(jgdjbh);
            PubObjFlow previousNode = null;

            for (PubObjFlow node : allNodes) {
                int nodeSeq = Integer.parseInt(node.getAuditFlag());
                if (nodeSeq < currentNodeSeq && (previousNode == null ||
                        Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                    previousNode = node;
                }
            }

            // 4. 更新工作流程状态
            if (previousNode != null) {
                LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGJG.getModCode())
                        .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGJG.getCode()))
                        .eq(PubObjFlow::getDjh, jgdjbh)
                        .eq(PubObjFlow::getAuditFlag, previousNode.getAuditFlag())
                        .set(PubObjFlow::getIsaudit, "0")
                        .set(PubObjFlow::getSpecificCheckPerson, "");

                pubObjFlowMapper.update(null, updatePreviousWrapper);
                if (previousNode == allNodes.get(0)) {
                    updateStatusByJgdjbh(jgdjbh, 5);
                } else {
                    // 更新采购计划状态为审核中(3)
                    updateStatusByJgdjbh(jgdjbh, 3);
                }
            }
            
            // 5. 记录退审日志
            createAuditLog(jgdjbh, BillnoType.CGJG, currentNodeSeq, "退审", opinion, LoginInfo.getCurrEmployeeName(), null,currentNode.getNodeName());
            
            log.info("采购结果退审成功，jgdjbh：{}，退审人：{}", jgdjbh, LoginInfo.getCurrEmployeeName());
            
            return buildSaveResponseVO(jgdjbh);
            
        } catch (Exception e) {
            log.error("退审采购结果失败，jgdjbh: {}，退审人：{}", jgdjbh, LoginInfo.getCurrEmployeeName(), e);
            throw new GlobalException("退审采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjgSaveResponseVO noAuditByJgdjbh(String jgdjbh) {
        try {
            log.info("开始销审采购结果，jgdjbh：{}", jgdjbh);
            
            // 1. 查询采购结果
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);
            
            if (cgjgml == null) {
                throw new GlobalException("采购结果不存在，jgdjbh: " + jgdjbh);
            }

            // 2. 检查状态是否可以销审
            String currentStatus = cgjgml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购结果状态异常，无法销审");
            }

            int status = Integer.parseInt(currentStatus);
            // 只有审核中(3)、已审核(4)的状态才能销审
            if (status != 3 && status != 4) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法销审。只有已提交、审核中或已审核的采购结果才能销审");
            }
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            // 查询最后已审核节点
            PubObjFlow lastAuditNode = pubObjFlowService.selectLastAuditNodeByDjh(
                    BillnoType.CGJG.getModCode(),
                    String.valueOf(BillnoType.CGJG.getCode()),
                    jgdjbh
            );

            // 3. 销审逻辑
            if (status == 3 || status == 4) {
                // 如果当前状态是审核中(3)或已审核(4)，需要退回到上一个节点
                
                // 查询所有工作流程节点
                List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(jgdjbh);
                if (allNodes.isEmpty()) {
                    // 如果没有工作流程节点，直接设置为已提交状态
                    updateStatusByJgdjbh(jgdjbh, 2);
                    log.info("采购结果没有工作流程节点，销审为已提交状态，jgdjbh：{}", jgdjbh);
                } else {
                    // 找到最后一个已审核的节点
                    PubObjFlow lastAuditedNode = null;
                    int maxAuditedNodeSeq = -1;

                    for (PubObjFlow node : allNodes) {
                        if ("1".equals(node.getIsaudit())) { // 已审核的节点
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq > maxAuditedNodeSeq) {
                                maxAuditedNodeSeq = nodeSeq;
                                lastAuditedNode = node;
                            }
                        }
                    }

                    if (lastAuditedNode != null) {
                        if (!lastAuditedNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())){
                            throw new GlobalException("您无权限对该采购结果进行销审操作");
                        }

                        LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                        updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGJG.getModCode())
                                .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGJG.getCode()))
                                .eq(PubObjFlow::getDjh, jgdjbh)
                                .eq(PubObjFlow::getAuditFlag, lastAuditedNode.getAuditFlag())
                                .set(PubObjFlow::getIsaudit, "0")
                                .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());
                        pubObjFlowMapper.update(null, updatePreviousWrapper);
                        
                        // 有已审核的节点，找到它的上一个节点
                        PubObjFlow previousNode = null;
                        int previousNodeSeq = -1;

                        for (PubObjFlow node : allNodes) {
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq < maxAuditedNodeSeq && nodeSeq > previousNodeSeq) {
                                previousNodeSeq = nodeSeq;
                                previousNode = node;
                            }
                        }

                        if (previousNode != null) {
                            // 更新采购结果状态为审核中(3)
                            updateStatusByJgdjbh(jgdjbh, 3);
                            log.info("采购结果销审成功，退回到上一个节点，jgdjbh：{}，上一节点序号：{}", 
                                    jgdjbh, previousNode.getAuditFlag());
                        } else {
                            // 更新采购结果状态为已提交(2)
                            updateStatusByJgdjbh(jgdjbh, 2);
                            log.info("采购结果销审成功，第一个审核节点已重置为未审核状态，jgdjbh：{}，第一节点序号：{}", 
                                    jgdjbh, maxAuditedNodeSeq);
                        }
                    } else {
                        updateStatusByJgdjbh(jgdjbh, 2);
                        log.info("采购结果没有已审核节点，销审为已提交状态，jgdjbh：{}", jgdjbh);
                    }
                }
            }
            
            // 4. 记录销审日志
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    BillnoType.CGJG.getModCode(), String.valueOf(BillnoType.CGJG.getCode()));

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }
            if (lastAuditNode != null) {
                createAuditLog(jgdjbh, BillnoType.CGJG, Integer.parseInt(lastAuditNode.getAuditFlag()), "销审", "", LoginInfo.getCurrEmployeeName(), cgjgml.getYyje(),lastAuditNode.getNodeName());
            }

            if (status==4){
                CacheStatisticsUtils.clearStatisticsCache();
            }
            
            log.info("采购结果销审成功，jgdjbh：{}", jgdjbh);
            
            return buildSaveResponseVO(jgdjbh);
            
        } catch (Exception e) {
            log.error("销审采购结果失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("销审采购结果失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjgAuditLogVO findCheckLogByJgdjbh(BillnoType billnoType, String jgdjbh) {
        try {
            log.info("开始查询采购结果审核日志，结果登记编号：{}", jgdjbh);
            
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 1. 检查采购结果是否存在
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);
            if (cgjgml == null) {
                throw new GlobalException("采购结果不存在，jgdjbh: " + jgdjbh);
            }
            
            // 2. 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    billnoType.getModCode(), String.valueOf(billnoType.getCode()));
            
            String flowcode = "CGJG_FLOW_001"; // 默认流程代码
            if (workflow != null) {
                flowcode = workflow.getFlowcode();
            }
            
            // 3. 查询审核日志（使用JGDJBH作为单据号）
            List<PubAuditLog> logList = pubAuditLogMapper.selectByBillId(gsdm, kjnd, jgdjbh, flowcode);
            
            // 4. 转换为VO
            CgjgAuditLogVO auditLogVO = new CgjgAuditLogVO();
            auditLogVO.setJgdjbh(jgdjbh);
            auditLogVO.setBillType(billnoType.getComment());
            
            // 转换日志列表
            List<CgjgAuditLogVO.AuditLogItem> logItems = logList.stream().map(log -> {
                CgjgAuditLogVO.AuditLogItem item = new CgjgAuditLogVO.AuditLogItem();
                item.setGsdm(log.getGsdm());
                item.setKjnd(log.getKjnd());
                item.setLogid(log.getLogid());
                item.setBillid(log.getBillid());
                item.setBillname(log.getBillname());
                item.setFlowcode(log.getFlowcode());
                item.setFlowname(log.getFlowname());
                item.setModname(log.getModname());
                item.setBizname(log.getBizname());
                item.setNodeseq(log.getNodeseq());
                item.setNodename(log.getNodename());
                item.setAuditorid(log.getAuditorid());
                item.setAuditor(log.getAuditor());
                item.setCertigierid(log.getCertigierid());
                item.setCertigier(log.getCertigier());
                item.setAdatetime(log.getAdatetime());
                item.setAmt(log.getAmt());
                item.setRemark(log.getRemark());
                item.setAtype(log.getAtype());
                item.setLogseq(log.getLogseq());
                item.setServDateTime(log.getServDateTime());
                item.setComputerName(log.getComputerName());
                return item;
            }).collect(Collectors.toList());
            
            // 5. 创建日志标题列表
            List<CgjgAuditLogVO.LogTitleItem> logTitleList = new ArrayList<>();
            
            // 添加提交节点
            CgjgAuditLogVO.LogTitleItem titleNode = new CgjgAuditLogVO.LogTitleItem();
            titleNode.setNodename("提交审核");
            titleNode.setGsdm(gsdm);
            titleNode.setNodeseq(-9); // 提交节点编号
            titleNode.setKjnd(kjnd);
            titleNode.setIsaudit(logList.isEmpty() ? "0" : "1");
            titleNode.setAuditStatus(logList.isEmpty() ? "未提交" : "已提交");
            
            // 如果有提交日志，设置提交时间
            logList.stream()
                .filter(log -> log.getNodeseq() != null && log.getNodeseq() == -9)
                .findFirst()
                .ifPresent(log -> titleNode.setAuditTime(log.getServDateTime()));
            
            logTitleList.add(titleNode);
            
            // 查询工作流程节点，添加审核节点标题
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(jgdjbh);
            
            // 查询工作流程模板节点（用于获取节点名称）
            List<PubObjFlowTemp> flowTemplates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, String> nodeNameMap = new HashMap<>();
            for (PubObjFlowTemp template : flowTemplates) {
                nodeNameMap.put(Integer.valueOf(template.getXh()), template.getJdmc());
            }
            
            for (PubObjFlow node : flowNodes) {
                CgjgAuditLogVO.LogTitleItem flowTitleNode = new CgjgAuditLogVO.LogTitleItem();
                
                // 通过节点序号获取正确的节点名称
                Integer nodeSeq = null;
                try {
                    nodeSeq = Integer.valueOf(node.getAuditFlag());
                    // 从工作流程模板中获取节点名称，如果没有则使用审核人名称作为备选
                    String nodeName = nodeNameMap.get(nodeSeq);
                    if (nodeName != null && !nodeName.trim().isEmpty()) {
                        flowTitleNode.setNodename(nodeName);
                    } else {
                        // 如果模板中没有节点名称，使用审核人名称作为备选
                        flowTitleNode.setNodename(node.getShrmc() != null ? node.getShrmc() : "审核节点");
                    }
                } catch (NumberFormatException e) {
                    flowTitleNode.setNodename("审核节点");
                    nodeSeq = 1;
                }
                
                flowTitleNode.setGsdm(node.getGsdm());
                flowTitleNode.setNodeseq(nodeSeq);
                flowTitleNode.setKjnd(node.getKjnd());
                flowTitleNode.setIsaudit("1".equals(node.getIsaudit()) ? "1" : "0");
                flowTitleNode.setAuditStatus("1".equals(node.getIsaudit()) ? "已审核" : "待审核");
                
                // 查找对应的审核日志设置审核时间
                logList.stream()
                    .filter(log -> log.getNodeseq() != null && log.getNodeseq().equals(flowTitleNode.getNodeseq()))
                    .findFirst()
                    .ifPresent(log -> flowTitleNode.setAuditTime(log.getServDateTime()));
                
                logTitleList.add(flowTitleNode);
            }
            
            auditLogVO.setLogList(logItems);
            auditLogVO.setLogTitleList(logTitleList);
            
            log.info("查询采购结果审核日志成功，结果登记编号：{}，日志数量：{}", jgdjbh, logItems.size());
            
            return auditLogVO;
            
        } catch (Exception e) {
            log.error("查询采购结果审核日志失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("查询审核日志失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Object isCheckedByAuthorityByJgdjbh(String jgdjbh) {
        try {
            JSONObject resultJson = new JSONObject();
            String currentUser = LoginInfo.getCurrEmployeeName();
            
            // 查询采购结果
            LambdaQueryWrapper<GpmCgdjml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(queryWrapper);
            
            if (cgjgml == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", true);
                resultJson.put("result", "此流程单据已经被删除，当前审核人" + currentUser + "无法查看");
                return resultJson;
            }
            
            String status = cgjgml.getZt();
            if ("2".equals(status) || "3".equals(status)) {
                // 查询当前审核节点
                PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                        BillnoType.CGJG.getModCode(),
                        String.valueOf(BillnoType.CGJG.getCode()),
                        jgdjbh
                );
                
                if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "此流程当前节点已被审批或当前审核人" + currentUser + "无权审批!");
                } else {
                    resultJson.put("isCheck", false);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "");
                }
            } else {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "当前状态无需审核");
            }
            
            return resultJson;
            
        } catch (Exception e) {
            log.error("检查采购结果审核权限异常，jgdjbh：{}", jgdjbh, e);
            
            JSONObject errorResult = new JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    @Override
    public PageResult<CgjgSaveResponseVO> getApprovedCgzbsqList(CgjgPageQueryDTO queryDTO) {
        try {
            // 固定状态为已审核
            queryDTO.setZt("4");

            // 调用分页查询获取基础信息和总数
            List<CgjgListVO> basicList = getCgjgPageList(queryDTO);

            long totalCount = 0;
            if (!basicList.isEmpty()) {
                totalCount = basicList.get(0).getTotalCount();
            }

            // 获取详情
            List<CgjgSaveResponseVO> detailList = new ArrayList<>();
            for (CgjgListVO basicItem : basicList) {
                try {
                    CgjgSaveResponseVO detail = getCgjgDetailByJgdjbh(basicItem.getJgdjbh());
                    if (detail != null) {
                        detailList.add(detail);
                    }
                } catch (Exception e) {
                    log.warn("获取已审核采购结果详情失败，zbsqbh: {}, 错误: {}", basicItem.getJgdjbh(), e.getMessage());
                    // 继续处理下一个
                }
            }

            return new PageResult<>(detailList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

        } catch (Exception e) {
            log.error("查询已审核招标申请列表失败", e);
            throw new GlobalException("查询已审核招标申请列表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 构建保存响应VO
     */
    private CgjgSaveResponseVO buildSaveResponseVO(String jgdjbh) {
        try {
            // 查询主表
            LambdaQueryWrapper<GpmCgdjml> mlQueryWrapper = new LambdaQueryWrapper<>();
            mlQueryWrapper.eq(GpmCgdjml::getJgdjbh, jgdjbh);
            GpmCgdjml cgjgml = cgjgmlMapper.selectOne(mlQueryWrapper);

            if (cgjgml == null) {
                throw new GlobalException("采购结果不存在，jgdjbh: " + jgdjbh);
            }

            // 查询明细
            LambdaQueryWrapper<GpmCgdjnr> nrQueryWrapper = new LambdaQueryWrapper<>();
            nrQueryWrapper.eq(GpmCgdjnr::getJgdjbh, jgdjbh);
            List<GpmCgdjnr> cgjgnrList = cgjgnrMapper.selectList(nrQueryWrapper);

            // 构建响应VO
            CgjgSaveResponseVO responseVO = new CgjgSaveResponseVO();

            // 构建基础信息
            CgjgSaveResponseVO.CgjgBaseInfoResponseVO baseInfoVO = new CgjgSaveResponseVO.CgjgBaseInfoResponseVO();
            BeanUtils.copyProperties(cgjgml, baseInfoVO);
            baseInfoVO.setZtmc(StatusName.findByStatus(cgjgml.getZt()));
            
            responseVO.setBaseInfo(baseInfoVO);

            // 构建明细信息列表
            List<CgjgSaveResponseVO.CgjgDetailResponseVO> detailList = new ArrayList<>();
            if (!cgjgnrList.isEmpty()) {
                for (GpmCgdjnr cgjgnr : cgjgnrList) {
                    CgjgSaveResponseVO.CgjgDetailResponseVO detailVO = new CgjgSaveResponseVO.CgjgDetailResponseVO();
                    BeanUtils.copyProperties(cgjgnr, detailVO);
                    if (cgjgnr.getZbsj()!=null){
                        detailVO.setZbsj(cgjgnr.getZbsj().toString());
                    }
                    // 字符串转布尔值
                    detailVO.setSfcgsqyr(ConvertUtils.convertStringToBoolean(cgjgnr.getSfcgsqyr()));
                    detailVO.setSfcgzbyr(ConvertUtils.convertStringToBoolean(cgjgnr.getSfcgzbyr()));
                    detailVO.setZfcg(ConvertUtils.convertStringToBoolean(cgjgnr.getZfcg()));
                    detailVO.setJkcp(ConvertUtils.convertStringToBoolean(cgjgnr.getJkcp()));

                    detailList.add(detailVO);
                }
            }
            responseVO.setCgjgDetails(detailList);
            
            return responseVO;

        } catch (Exception e) {
            log.error("构建采购结果响应VO失败，jgdjbh: {}", jgdjbh, e);
            throw new GlobalException("构建响应数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 生成结果登记编号
     */
    private String generateJgdjbh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        // 查询当天最大序号
        String prefix = "CGDJ" + dateStr;
        String maxJgdjbh = cgjgmlMapper.selectMaxJgdjbhByPrefix(prefix);
        
        int sequenceNumber = 1;
        if (maxJgdjbh != null && maxJgdjbh.length() >= prefix.length() + 4) {
            // 提取序列号部分
            String sequence = maxJgdjbh.substring(prefix.length());
            try {
                sequenceNumber = Integer.parseInt(sequence) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析序列号失败，使用默认值1: {}", sequence, e);
            }
        }
        
        // 格式化为4位数字，例如：0001, 0002, ..., 9999
        return prefix + String.format("%04d", sequenceNumber);
    }
    
    /**
     * 生成明细编号，格式：CGDJMX年月日001, CGDJMX年月日002, ...
     * @param detailIndex 明细索引，从0开始
     * @return 明细编号
     */
    private String generateJgnrbh(int detailIndex) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String mxPrefix = "CGDJMX" + dateStr;
        
        // 序号从1开始，加上传入的索引值
        int sequenceNumber = 1 + detailIndex;
        
        // 格式化为3位数字，例如：001, 002, ..., 999
        return mxPrefix + String.format("%03d", sequenceNumber);
    }

    /**
     * 创建审核日志
     */
    private void createAuditLog(String jgdjbh, BillnoType billType, int nodeSeq, String auditType, String opinion, String auditor, BigDecimal money,String nodename) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 获取工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billType.getModCode(), String.valueOf(billType.getCode()));
            
            String flowCode = workflow != null ? workflow.getFlowcode() : "CGJG_FLOW_001";
            String flowName = workflow != null ? workflow.getFlowname() : "采购结果审核流程";
            
            PubAuditLog auditLog = new PubAuditLog();
            auditLog.setGsdm(gsdm);
            auditLog.setKjnd(kjnd);
            
            // 获取新的日志ID
            Integer newLogID = pubAuditLogMapper.selectMaxLogID();
            if (newLogID == null) {
                newLogID = 1;
            }
            auditLog.setLogid(newLogID.longValue());
            
            // 设置单据信息
            auditLog.setBillid(jgdjbh);
            if (workflow != null) {
                auditLog.setBillname(workflow.getBizname());
            }
            auditLog.setFlowcode(flowCode);
            auditLog.setFlowname(flowName);
            auditLog.setModname(billType.getModCode());
            if (workflow != null) {
                auditLog.setBizname(workflow.getBizname());
            }

            // 设置审核节点信息
            auditLog.setNodeseq(nodeSeq);
            auditLog.setNodename(nodename);
            
            // 设置审核人信息
            auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
            auditLog.setAuditor(auditor);
            auditLog.setCertigierid(0);
            auditLog.setCertigier("");
            
            // 设置审核时间
            SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setAdatetime(df1.format(new Date()));
            
            // 设置金额和备注
            auditLog.setAmt(money != null ? money : BigDecimal.ZERO);
            auditLog.setRemark(opinion != null ? opinion : "");
            auditLog.setAtype(auditType);
            
            // 设置服务器时间
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setServDateTime(df2.format(new Date()));
            
            // 设置计算机信息
            try {
                String computerMsg = java.net.InetAddress.getLocalHost().getHostName()
                        + "/" + java.net.InetAddress.getLocalHost().getHostAddress();
                auditLog.setComputerName(computerMsg);
            } catch (Exception e) {
                auditLog.setComputerName("Unknown");
            }
            
            // 保存审核日志
            pubAuditLogMapper.insert(auditLog);
            
            log.info("创建采购结果审核日志成功，jgdjbh：{}，审核类型：{}，审核人：{}", jgdjbh, auditType, auditor);
            
        } catch (Exception e) {
            log.error("创建采购结果审核日志失败，jgdjbh：{}", jgdjbh, e);
            // 不抛出异常，避免影响主流程
        }
    }

} 