package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.pojo.GpmData;
import com.gg.grp.hkycg.model.pojo.GpmUserdata;

import java.util.List;

/**
 * GPM_USERDATA表服务接口
 */
public interface GpmUserdataService extends IService<GpmUserdata> {
    // 可以在这里添加自定义的服务方法
    
    /**
     * 根据职员代码查询其拥有的数据权限
     * @param zydm 职员代码
     * @return 数据权限信息
     */
    GpmData getUserDataPermission(String zydm);
    
    /**
     * 为职员分配数据权限
     * @param zydm 职员代码
     * @param datacode 数据权限代码
     * @return 是否分配成功
     */
    boolean assignDataPermission(String zydm, String datacode);
    
    /**
     * 批量为职员分配相同的数据权限
     * @param zydmList 职员代码列表
     * @param datacode 数据权限代码
     * @return 操作结果，包含成功数量和失败数量
     */
    BatchAssignResult batchAssignDataPermission(List<String> zydmList, String datacode);
    
    /**
     * 批量操作结果
     */
    class BatchAssignResult {
        /**
         * 成功数量
         */
        private int successCount;
        
        /**
         * 失败数量
         */
        private int failCount;

        public BatchAssignResult(int successCount, int failCount) {
            this.successCount = successCount;
            this.failCount = failCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailCount() {
            return failCount;
        }
    }
} 