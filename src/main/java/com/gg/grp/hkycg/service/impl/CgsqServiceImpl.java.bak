package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.consts.StaticValue;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.StatusName;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.CgjhBudgetIndexDTO;
import com.gg.grp.hkycg.model.dto.CgsqAuditQueryDTO;
import com.gg.grp.hkycg.model.dto.CgsqCheckParams;
import com.gg.grp.hkycg.model.dto.CgsqJhmxWithBudgetDTO;
import com.gg.grp.hkycg.model.dto.CgsqPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgsqSaveDTO;
import com.gg.grp.hkycg.model.vo.CgsqAuditListVO;
import com.gg.grp.hkycg.model.vo.CgsqAuditLogVO;
import com.gg.grp.hkycg.model.vo.CgsqListVO;
import com.gg.grp.hkycg.model.vo.CgsqSaveResponseVO;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.CgsqService;
import com.gg.grp.hkycg.service.PubObjFlowService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.ConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 采购申请Service实现类
 */
@Slf4j
@Service
public class CgsqServiceImpl implements CgsqService {

    @Autowired
    private GpmCgsqmlMapper cgsqmlMapper;

    @Autowired
    private GpmCgsqnrMapper cgsqJhmxMapper;

    @Autowired
    private IndexMapper indexMapper;

    @Autowired
    private GbiZbsyrecMapper gbiZbsyrecMapper;

    @Autowired
    private PubAuditLogMapper pubAuditLogMapper;

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Autowired
    private PubWorkflowMapper pubWorkflowMapper;

    @Autowired
    private PubObjFlowService pubObjFlowService;

    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;

    @Override
    public CgsqSaveResponseVO saveCgsq(CgsqSaveDTO cgsqSaveDTO) {
        try {
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, cgsqSaveDTO.getBaseInfo().getJhbh());
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);
            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在");
            }

            // 设置默认值
            if (StringUtils.isBlank(cgsqSaveDTO.getBaseInfo().getGsdm())) {
                cgsqSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(cgsqSaveDTO.getBaseInfo().getKjnd())) {
                cgsqSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 生成申请编号
            String sqbh = generateSqbh();
            cgsqSaveDTO.getBaseInfo().setSqbh(sqbh);

            // 1. 保存采购申请主表
            GpmCgsqml cgsqml = new GpmCgsqml();
            BeanUtils.copyProperties(cgsqSaveDTO.getBaseInfo(), cgsqml);

            // 处理布尔值字段
            cgsqml.setLscgjh(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getLscgjh()));
            cgsqml.setScydsxlxsp(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getScydsxlxsp()));
            cgsqml.setSjbmlxsp(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getSjbmlxsp()));
            cgsqml.setSzfcg(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getSzfcg()));
            cgsqml.setJwbmlxsp(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getJwbmlxsp()));
            cgsqml.setSfcgjhyr(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getSfcgjhyr()));

            // 设置系统字段
            cgsqml.setCjrq(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            cgsqml.setCjrmc(LoginInfo.getCurrEmployeeName());
            cgsqml.setCjrdm(LoginInfo.getCurrEmployeeCode());
            cgsqml.setZt("1"); // 保存状态
            cgsqmlMapper.insert(cgsqml);
            log.info("采购申请主表保存成功，sqbh：{}", sqbh);

            // 2. 保存采购申请计划明细及预算指标
            boolean hasSqmx = cgsqSaveDTO.getCgsqDetails() != null && !cgsqSaveDTO.getCgsqDetails().isEmpty();
            if (hasSqmx) {
                Integer maxMlid = getMaxMlidFromOerYszb(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear());
                Integer maxBnxid = getMaxBnxidFromOerYszb(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear());
                Integer maxDataId = getMaxDataIdFromOerYszb(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear());

                int budgetIndexCounter = 0;

                for (int i = 0; i < cgsqSaveDTO.getCgsqDetails().size(); i++) {
                    CgsqJhmxWithBudgetDTO jhmxWithBudget = cgsqSaveDTO.getCgsqDetails().get(i);
                    
                    // 使用新的生成方式创建明细编号
                    String sqmxxh = generateSqmxxh(i);

                    GpmCgsqnr cgsqJhmx = new GpmCgsqnr();
                    BeanUtils.copyProperties(jhmxWithBudget.getJhmx(), cgsqJhmx);

                    cgsqJhmx.setSqbh(sqbh);
                    cgsqJhmx.setSqmxxh(sqmxxh);
                    cgsqJhmx.setGsdm(LoginInfo.getCurrCorpCode());
                    cgsqJhmx.setKjnd(LoginInfo.getCurrAccountantYear());
                    cgsqJhmx.setCjsj(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    cgsqJhmx.setCjrdm(LoginInfo.getCurrEmployeeCode());
                    cgsqJhmx.setCjrmc(LoginInfo.getCurrEmployeeName());
                    cgsqJhmx.setZfcg(ConvertUtils.convertBooleanToString(jhmxWithBudget.getJhmx().getZfcg()));
                    cgsqJhmx.setJkcp(ConvertUtils.convertBooleanToString(jhmxWithBudget.getJhmx().getJkcp()));

                    cgsqJhmxMapper.insert(cgsqJhmx);
                    log.info("保存计划明细成功，sqbh：{}，sqmxxh：{}", sqbh, sqmxxh);

                    CgjhBudgetIndexDTO budgetIndex = jhmxWithBudget.getBudgetIndex();
                    if (budgetIndex != null) {
                        budgetIndexCounter++;
                        budgetIndex.setMxxh(sqmxxh);

                        OerYszb oerYszb = new OerYszb();
                        oerYszb.setMlid(maxMlid + budgetIndexCounter);
                        oerYszb.setBnxid(maxBnxid + budgetIndexCounter);
                        oerYszb.setDataId(maxDataId + budgetIndexCounter);
                        oerYszb.setXh(budgetIndex.getIndex() != null ? budgetIndex.getIndex() : budgetIndexCounter);
                        oerYszb.setDjbh(sqbh);
                        oerYszb.setMxxh(sqmxxh);
                        oerYszb.setGsdm(LoginInfo.getCurrCorpCode());
                        oerYszb.setKjnd(LoginInfo.getCurrAccountantYear());
                        if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                            oerYszb.setJe(new BigDecimal(budgetIndex.getAmount()));
                        }
                        oerYszb.setZbid(Integer.valueOf(budgetIndex.getZbid()));
                        oerYszb.setXm(budgetIndex.getProject());
                        oerYszb.setZbgnkmmc(budgetIndex.getFuncSubject());
                        oerYszb.setZbjjkmmc(budgetIndex.getEconomicSubject());
                        oerYszb.setYskjkmmc(budgetIndex.getAccountTitle());
                        oerYszb.setZbfz7dm(budgetIndex.getFZ7DM());
                        oerYszb.setZbfz7mc(budgetIndex.getFZ7MC());
                        oerYszb.setZbfz8dm(budgetIndex.getFZ8DM());
                        oerYszb.setZbfz8mc(budgetIndex.getFZ8MC());
                        oerYszb.setZbfz9dm(budgetIndex.getFZ9DM());
                        oerYszb.setZbfz9mc(budgetIndex.getFZ9MC());
                        oerYszb.setZbdm(budgetIndex.getZbdm());
                        indexMapper.insert(oerYszb);

                        GbiZbsyrec zbsyrec = new GbiZbsyrec();
                        zbsyrec.setGsdm(LoginInfo.getCurrCorpCode());
                        zbsyrec.setKjnd(LoginInfo.getCurrAccountantYear());
                        zbsyrec.setDjid(sqbh); // 使用单据编号作为外键
                        zbsyrec.setDjlx(String.valueOf(BillnoType.CGSQ.getCode()));
                        zbsyrec.setModule(BillnoType.CGSQ.getModCode());
                        zbsyrec.setDjzt("1");
                        if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                            zbsyrec.setDjje(new BigDecimal(budgetIndex.getAmount()));
                        }
                        zbsyrec.setMxxh(sqmxxh);

                        gbiZbsyrecMapper.insert(zbsyrec);
                        log.info("保存预算指标成功，sqbh：{}，sqmxxh：{}，金额：{}", sqbh, sqmxxh, budgetIndex.getAmount());
                    }
                }
            }

            log.info("采购申请保存成功，sqbh：{}，计划明细数量：{}",
                    sqbh,
                    hasSqmx ? cgsqSaveDTO.getCgsqDetails().size() : 0);

            return buildSaveResponseVO(sqbh);

        } catch (Exception e) {
            log.error("保存采购申请失败", e);
            throw new GlobalException("保存采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO updateCgsq(String sqbh, CgsqSaveDTO cgsqSaveDTO) {
        try {
            // 参数校验
            if (cgsqSaveDTO == null || cgsqSaveDTO.getBaseInfo() == null) {
                throw new GlobalException("采购申请基础信息不能为空");
            }
            
            // 检查明细信息
            if (cgsqSaveDTO.getCgsqDetails() == null || cgsqSaveDTO.getCgsqDetails().isEmpty()) {
                throw new GlobalException("采购申请明细不能为空");
            }
            
            // 检查采购申请是否存在
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml existingCgsqml = cgsqmlMapper.selectOne(queryWrapper);
            if (existingCgsqml == null) {
                throw new GlobalException("采购申请不存在!");
            }

            if (!existingCgsqml.getZt().equals("1") && !existingCgsqml.getZt().equals("5")){
                throw new GlobalException("采购申请不是保存或退回，无法修改");
            }
            
            // 权限校验：只有创建人才能更新
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = existingCgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 更新操作，当前用户：{}，创建人：{}，sqbh：{}", currentUser, originalUser, sqbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能修改此采购申请");
            }
            log.info("权限校验通过 - 更新操作，当前用户：{}，sqbh：{}", currentUser, sqbh);
            
            // 获取基础信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 1. 更新采购申请主表
            GpmCgsqml cgsqml = new GpmCgsqml();
            BeanUtils.copyProperties(cgsqSaveDTO.getBaseInfo(), cgsqml);
            
            // 处理布尔值字段
            cgsqml.setLscgjh(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getLscgjh()));
            cgsqml.setScydsxlxsp(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getScydsxlxsp()));
            cgsqml.setSjbmlxsp(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getSjbmlxsp()));
            cgsqml.setSzfcg(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getSzfcg()));
            cgsqml.setJwbmlxsp(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getJwbmlxsp()));
            cgsqml.setSfcgjhyr(ConvertUtils.convertBooleanToString(cgsqSaveDTO.getBaseInfo().getSfcgjhyr()));
            
            // 保留原有的创建信息
            cgsqml.setCjrq(existingCgsqml.getCjrq());
            cgsqml.setCjrmc(existingCgsqml.getCjrmc());
            cgsqml.setCjrdm(existingCgsqml.getCjrdm());

            log.info("准备更新主表数据，申请名称：{}", cgsqml.getSqmc());

            // 更新主表
            LambdaUpdateWrapper<GpmCgsqml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgsqml::getJhbh, sqbh);
            int result = cgsqmlMapper.update(cgsqml, updateWrapper);
            log.info("主表更新结果：{}", result);
            
            // 2. 删除原有的明细和预算指标记录
            log.info("开始删除原有明细和预算指标，sqbh：{}", sqbh);
            
            // 删除计划明细记录（支持基于SQBH和DJBH删除）
            LambdaUpdateWrapper<GpmCgsqnr> deleteJhmxWrapper = new LambdaUpdateWrapper<>();
            if (StringUtils.isNotBlank(existingCgsqml.getSqbh())) {
                deleteJhmxWrapper.eq(GpmCgsqnr::getSqbh, existingCgsqml.getSqbh());
            }
            int deletedJhmx = cgsqJhmxMapper.delete(deleteJhmxWrapper);
            log.info("删除计划明细记录{}条", deletedJhmx);

            // 删除预算指标记录（OER_YSZB表）
            LambdaUpdateWrapper<OerYszb> deleteOerYszbWrapper = new LambdaUpdateWrapper<>();
            deleteOerYszbWrapper.eq(OerYszb::getDjbh, sqbh);
            int deletedOerYszb = indexMapper.delete(deleteOerYszbWrapper);
            log.info("删除OER_YSZB预算指标记录{}条", deletedOerYszb);

            // 删除指标使用记录
            LambdaUpdateWrapper<GbiZbsyrec> deleteZbsyrecWrapper = new LambdaUpdateWrapper<>();
            deleteZbsyrecWrapper.eq(GbiZbsyrec::getDjid, sqbh)
                    .eq(GbiZbsyrec::getModule, BillnoType.CGSQ.getModCode())
                    .eq(GbiZbsyrec::getDjlx, BillnoType.CGSQ.getCode());
            int deletedZbsyrec = gbiZbsyrecMapper.delete(deleteZbsyrecWrapper);
            log.info("删除GBI_ZBSYREC指标使用记录{}条", deletedZbsyrec);

            // 3. 重新保存新的明细和预算指标
            // 获取最大序号用于生成必填字段
            Integer maxMlid = getMaxMlidFromOerYszb(gsdm, kjnd);
            Integer maxBnxid = getMaxBnxidFromOerYszb(gsdm, kjnd);
            Integer maxDataId = getMaxDataIdFromOerYszb(gsdm, kjnd);
            
            // 处理null值
            if (maxMlid == null) maxMlid = 0;
            if (maxBnxid == null) maxBnxid = 0;
            if (maxDataId == null) maxDataId = 0;
            
            int budgetIndexCounter = 0;
            
            for (int i = 0; i < cgsqSaveDTO.getCgsqDetails().size(); i++) {
                CgsqJhmxWithBudgetDTO jhmxWithBudget = cgsqSaveDTO.getCgsqDetails().get(i);
                // 生成明细唯一ID
                String sqmxxh = generateSqmxxh(i);
                
                // 保存计划明细记录
                GpmCgsqnr cgsqJhmx = new GpmCgsqnr();
                BeanUtils.copyProperties(jhmxWithBudget.getJhmx(), cgsqJhmx);
                
                // 设置关联信息
                cgsqJhmx.setSqbh(existingCgsqml.getSqbh()); // 设置申请编号作为外键
                cgsqJhmx.setSqmxxh(sqmxxh);
                cgsqJhmx.setGsdm(gsdm);
                cgsqJhmx.setKjnd(kjnd);
                cgsqJhmx.setCjsj(existingCgsqml.getCjrq());
                cgsqJhmx.setCjrdm(LoginInfo.getCurrEmployeeCode());
                cgsqJhmx.setCjrmc(LoginInfo.getCurrEmployeeName());
                cgsqJhmx.setGxsj(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                cgsqJhmx.setGxrdm(LoginInfo.getCurrEmployeeCode());
                cgsqJhmx.setGxrmc(LoginInfo.getCurrEmployeeName());
                cgsqJhmx.setZfcg(ConvertUtils.convertBooleanToString(jhmxWithBudget.getJhmx().getZfcg()));
                cgsqJhmx.setJkcp(ConvertUtils.convertBooleanToString(jhmxWithBudget.getJhmx().getJkcp()));
                
                cgsqJhmxMapper.insert(cgsqJhmx);
                log.info("保存计划明细成功，sqbh：{}，sqmxxh：{}", sqbh, sqmxxh);
                
                // 保存该明细对应的预算指标
                CgjhBudgetIndexDTO budgetIndex = jhmxWithBudget.getBudgetIndex();
                if (budgetIndex != null) {
                    budgetIndexCounter++;
                    
                    // 设置明细序号
                    budgetIndex.setMxxh(sqmxxh);
                    
                    // 保存预算指标到OER_YSZB表
                    OerYszb oerYszb = new OerYszb();
                    
                    // 设置必填字段
                    oerYszb.setMlid(maxMlid + budgetIndexCounter);
                    oerYszb.setBnxid(maxBnxid + budgetIndexCounter);
                    oerYszb.setDataId(maxDataId + budgetIndexCounter);
                    oerYszb.setXh(budgetIndex.getIndex() != null ? budgetIndex.getIndex() : budgetIndexCounter);
                    
                    // 设置基本字段
                    oerYszb.setDjbh(sqbh);
                    oerYszb.setMxxh(sqmxxh);
                    oerYszb.setGsdm(gsdm);
                    oerYszb.setKjnd(kjnd);
                    
                    // 设置预算指标数据
                    if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                        oerYszb.setJe(new BigDecimal(budgetIndex.getAmount()));
                    }
                    oerYszb.setZbid(Integer.valueOf(budgetIndex.getZbid()));
                    oerYszb.setZy(budgetIndex.getProject());
                    oerYszb.setZbgnkmmc(budgetIndex.getFuncSubject());
                    oerYszb.setZbjjkmmc(budgetIndex.getEconomicSubject());
                    oerYszb.setYskjkmmc(budgetIndex.getAccountTitle());
                    oerYszb.setZbfz7dm(budgetIndex.getFZ7DM());
                    oerYszb.setZbfz7mc(budgetIndex.getFZ7MC());
                    oerYszb.setZbfz8dm(budgetIndex.getFZ8DM());
                    oerYszb.setZbfz8mc(budgetIndex.getFZ8MC());
                    oerYszb.setZbfz9dm(budgetIndex.getFZ9DM());
                    oerYszb.setZbfz9mc(budgetIndex.getFZ9MC());
                    oerYszb.setXm(budgetIndex.getProject());
                    oerYszb.setZbdm(budgetIndex.getZbdm());
                    
                    indexMapper.insert(oerYszb);
                    
                    // 保存指标使用记录到GBI_ZBSYREC表
                    GbiZbsyrec zbsyrec = new GbiZbsyrec();
                    zbsyrec.setGsdm(gsdm);
                    zbsyrec.setKjnd(kjnd);
                    zbsyrec.setDjid(sqbh);
                    zbsyrec.setDjlx(String.valueOf(BillnoType.CGSQ.getCode()));
                    zbsyrec.setModule(BillnoType.CGSQ.getModCode());
                    zbsyrec.setDjzt("1");
                    if (StringUtils.isNotBlank(budgetIndex.getAmount())){
                        zbsyrec.setDjje(new BigDecimal(budgetIndex.getAmount()));
                    }
                    zbsyrec.setMxxh(sqmxxh);
                    
                    gbiZbsyrecMapper.insert(zbsyrec);
                    log.info("保存预算指标成功，sqbh：{}，sqmxxh：{}，金额：{}", sqbh, sqmxxh, budgetIndex.getAmount());
                }
            }
            if (existingCgsqml.getZt().equals("5")){
                updateStatus(sqbh,1);
            }
            log.info("更新采购申请成功，sqbh：{}，明细数量：{}", sqbh, cgsqSaveDTO.getCgsqDetails().size());
            
            // 4. 构建并返回响应数据
            CgsqSaveResponseVO responseVO = buildSaveResponseVO(sqbh);
            log.info("返回更新后的采购申请详情，申请名称：{}", responseVO.getBaseInfo().getSqmc());
            
            return responseVO;
            
        } catch (Exception e) {
            log.error("更新采购申请失败，sqbh: {}", sqbh, e);
            throw new GlobalException("更新采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteByDjbh(String djbh) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 权限校验：查询采购申请的创建人信息
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getJhbh, djbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，无法删除，djbh: " + djbh);
            }
            
            // 权限校验：只有创建人才能删除
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = cgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 删除操作，当前用户：{}，创建人：{}，djbh：{}", currentUser, originalUser, djbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能删除此采购申请");
            }
            log.info("权限校验通过 - 删除操作，当前用户：{}，djbh：{}", currentUser, djbh);
            
            // 1. 删除主表
            LambdaUpdateWrapper<GpmCgsqml> mlWrapper = new LambdaUpdateWrapper<>();
            mlWrapper.eq(GpmCgsqml::getJhbh, djbh);
            int deleteml = cgsqmlMapper.delete(mlWrapper);
            
            // 2. 删除计划明细表（支持基于SQBH和DJBH删除）
            LambdaUpdateWrapper<GpmCgsqnr> jhmxWrapper = new LambdaUpdateWrapper<>();
            if (StringUtils.isNotBlank(cgsqml.getSqbh())) {
                jhmxWrapper.eq(GpmCgsqnr::getSqbh, cgsqml.getSqbh());
            }
            int deleteJhmx = cgsqJhmxMapper.delete(jhmxWrapper);
            
            // 3. 删除预算指标表
            LambdaUpdateWrapper<OerYszb> yszbWrapper = new LambdaUpdateWrapper<>();
            yszbWrapper.eq(OerYszb::getDjbh, djbh);
            indexMapper.delete(yszbWrapper);
            
            // 4. 删除指标使用记录
            LambdaUpdateWrapper<GbiZbsyrec> zbsyrecWrapper = new LambdaUpdateWrapper<>();
            zbsyrecWrapper.eq(GbiZbsyrec::getDjid, djbh)
                    .eq(GbiZbsyrec::getModule, BillnoType.CGSQ.getModCode())
                    .eq(GbiZbsyrec::getDjlx, BillnoType.CGSQ.getCode());
            gbiZbsyrecMapper.delete(zbsyrecWrapper);
            
            log.info("删除采购申请成功，djbh：{}", djbh);
            return deleteml > 0;
            
        } catch (Exception e) {
            log.error("删除采购申请失败，djbh: {}", djbh, e);
            throw new GlobalException("删除采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO getCgsqDetailByDjbh(String djbh) {
        try {
            // 检查采购申请是否存在
            LambdaQueryWrapper<GpmCgsqml> mlQueryWrapper = new LambdaQueryWrapper<>();
            mlQueryWrapper.eq(GpmCgsqml::getJhbh, djbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(mlQueryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，djbh: " + djbh);
            }
            
            // 构建响应数据
            CgsqSaveResponseVO responseVO = buildSaveResponseVO(djbh);
            log.info("查询采购申请详情成功，djbh: {}, 申请名称: {}", djbh, responseVO.getBaseInfo().getSqmc());
            return responseVO;
            
        } catch (Exception e) {
            log.error("查询采购申请详情失败，djbh: {}", djbh, e);
            throw new GlobalException("查询采购申请详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgsqListVO> getCgsqPageList(CgsqPageQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 设置默认查询条件
            String gsdm = queryDTO.getGsdm();
            if (!StringUtils.isNotBlank(gsdm)) {
                gsdm = LoginInfo.getCurrCorpCode();
            }

            String kjnd = queryDTO.getKjnd();
            if (!StringUtils.isNotBlank(kjnd)) {
                kjnd = LoginInfo.getCurrAccountantYear();
            }

            // 处理模糊查询条件
            String condition = null;
            if (StringUtils.isNotBlank(queryDTO.getCondition())) {
                condition = "%" + queryDTO.getCondition().trim() + "%";
            }

            // 计算分页偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 准备其他查询条件
            String zt = queryDTO.getZt();
            String startDate = queryDTO.getStartDate();
            String endDate = queryDTO.getEndDate();

            log.info("执行采购申请分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, offset={}, size={}",
                    gsdm, kjnd, condition, zt, offset, queryDTO.getSize());

            // 执行查询
            List<CgsqListVO> resultList = cgsqmlMapper.getCgsqPageList(
                    gsdm, kjnd, condition, zt, startDate, endDate, offset, queryDTO.getSize()
            );

            log.info("采购申请分页查询完成，返回{}条记录", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("采购申请分页查询失败，参数：{}，错误：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询采购申请列表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 构建保存响应VO（支持基于SQBH或DJBH查询）
     */
    private CgsqSaveResponseVO buildSaveResponseVO(String sqbh) {
        CgsqSaveResponseVO responseVO = new CgsqSaveResponseVO();
        
        // 1. 查询主表信息
        LambdaQueryWrapper<GpmCgsqml> mlWrapper = new LambdaQueryWrapper<>();
        mlWrapper.eq(GpmCgsqml::getSqbh, sqbh);
        GpmCgsqml cgsqml = cgsqmlMapper.selectOne(mlWrapper);
        if (cgsqml == null) {
            return null;
        }
        CgsqSaveResponseVO.CgsqBaseInfoResponseVO baseInfo = new CgsqSaveResponseVO.CgsqBaseInfoResponseVO();
        BeanUtils.copyProperties(cgsqml, baseInfo);
        // 手动转换布尔值
        baseInfo.setLscgjh(ConvertUtils.convertStringToBoolean(cgsqml.getLscgjh()));
        baseInfo.setScydsxlxsp(ConvertUtils.convertStringToBoolean(cgsqml.getScydsxlxsp()));
        baseInfo.setSjbmlxsp(ConvertUtils.convertStringToBoolean(cgsqml.getSjbmlxsp()));
        baseInfo.setSzfcg(ConvertUtils.convertStringToBoolean(cgsqml.getSzfcg()));
        baseInfo.setJwbmlxsp(ConvertUtils.convertStringToBoolean(cgsqml.getJwbmlxsp()));
        baseInfo.setSfcgjhyr(ConvertUtils.convertStringToBoolean(cgsqml.getSfcgjhyr()));
        baseInfo.setZtmc(StatusName.findByStatus(cgsqml.getZt()));
        responseVO.setBaseInfo(baseInfo);
        
        // 2. 查询计划明细及预算指标
        List<CgsqSaveResponseVO.CgsqJhmxWithBudgetResponseVO> jhmxListWithBudget = new ArrayList<>();
        
        // 查询计划明细
        LambdaQueryWrapper<GpmCgsqnr> jhmxWrapper = new LambdaQueryWrapper<>();
        jhmxWrapper.eq(GpmCgsqnr::getSqbh, sqbh);
        List<GpmCgsqnr> jhmxList = cgsqJhmxMapper.selectList(jhmxWrapper);
        
        for (GpmCgsqnr jhmx : jhmxList) {
            CgsqSaveResponseVO.CgsqJhmxWithBudgetResponseVO jhmxWithBudget = new CgsqSaveResponseVO.CgsqJhmxWithBudgetResponseVO();
            
            // 设置明细信息
            CgsqSaveResponseVO.CgsqJhmxResponseVO jhmxVO = new CgsqSaveResponseVO.CgsqJhmxResponseVO();
            BeanUtils.copyProperties(jhmx, jhmxVO);
            jhmxVO.setZfcg(ConvertUtils.convertStringToBoolean(jhmx.getZfcg()));
            jhmxVO.setJkcp(ConvertUtils.convertStringToBoolean(jhmx.getJkcp()));
            jhmxWithBudget.setJhmx(jhmxVO);
            
            // 查询对应的预算指标
            LambdaQueryWrapper<OerYszb> yszbWrapper = new LambdaQueryWrapper<>();
            yszbWrapper.eq(OerYszb::getDjbh, sqbh)
                       .eq(OerYszb::getMxxh, jhmx.getSqmxxh());
            OerYszb oerYszb = indexMapper.selectOne(yszbWrapper);
            
            if (oerYszb != null) {
                CgjhBudgetIndexDTO budgetIndex = new CgjhBudgetIndexDTO();
                budgetIndex.setMxxh(oerYszb.getMxxh());
                budgetIndex.setIndex(oerYszb.getXh());
                budgetIndex.setZbid(String.valueOf(oerYszb.getZbid()));
                budgetIndex.setAmount(oerYszb.getJe() != null ? oerYszb.getJe().toString() : null);
                budgetIndex.setProject(oerYszb.getXm());
                budgetIndex.setFuncSubject(oerYszb.getZbgnkmmc());
                budgetIndex.setEconomicSubject(oerYszb.getZbjjkmmc());
                budgetIndex.setAccountTitle(oerYszb.getYskjkmmc());
                budgetIndex.setFZ7DM(oerYszb.getZbfz7dm());
                budgetIndex.setFZ7MC(oerYszb.getZbfz7mc());
                budgetIndex.setFZ8DM(oerYszb.getZbfz8dm());
                budgetIndex.setFZ8MC(oerYszb.getZbfz8mc());
                budgetIndex.setFZ9DM(oerYszb.getZbfz9dm());
                budgetIndex.setFZ9MC(oerYszb.getZbfz9mc());
                jhmxWithBudget.setBudgetIndex(budgetIndex);
            }
            jhmxListWithBudget.add(jhmxWithBudget);
        }
        responseVO.setCgsqDetails(jhmxListWithBudget);
        return responseVO;
    }

    /**
     * 获取OER_YSZB表中的最大MLID
     */
    private Integer getMaxMlidFromOerYszb(String gsdm, String kjnd) {
        Integer maxMlid = indexMapper.selectMaxMlid(gsdm, kjnd);
        return maxMlid != null ? maxMlid : 0;
    }

    /**
     * 获取OER_YSZB表中的最大BNXID
     */
    private Integer getMaxBnxidFromOerYszb(String gsdm, String kjnd) {
        Integer maxBnxid = indexMapper.selectMaxBnxid(gsdm, kjnd);
        return maxBnxid != null ? maxBnxid : 0;
    }

    /**
     * 获取OER_YSZB表中的最大DataID
     */
    private Integer getMaxDataIdFromOerYszb(String gsdm, String kjnd) {
        Integer maxDataId = indexMapper.selectMaxDataId(gsdm, kjnd);
        return maxDataId != null ? maxDataId : 0;
    }

    private String convertBooleanStringTo10(String bool) {
        return bool != null && !bool.isEmpty() && Boolean.parseBoolean(bool) ? "1" : "0";
    }

    /**
     * 生成申请编号
     * @return 申请编号
     */
    private String generateSqbh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        // 查询当天最大序号
        String prefix = "CGSQ" + dateStr;
        String maxSqbh = cgsqmlMapper.selectMaxSqbhByPrefix(prefix);
        
        int sequenceNumber = 1;
        if (maxSqbh != null && maxSqbh.length() >= prefix.length() + 4) {
            // 提取序列号部分
            String sequence = maxSqbh.substring(prefix.length());
            try {
                sequenceNumber = Integer.parseInt(sequence) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析序列号失败，使用默认值1: {}", sequence, e);
            }
        }
        
        // 格式化为4位数字，例如：0001, 0002, ..., 9999
        return prefix + String.format("%04d", sequenceNumber);
    }

    /**
     * 生成单据编号（外键）
     * @return 单据编号
     */
    private String generateDjbh() {
        // 复用SQBH的生成逻辑
        return generateSqbh();
    }
    
    /**
     * 生成明细编号，格式：CGSQMX年月日001, CGSQMX年月日002, ...
     * @param detailIndex 明细索引，从0开始
     * @return 明细编号
     */
    private String generateSqmxxh(int detailIndex) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String mxPrefix = "CGSQMX" + dateStr;
        
        // 序号从1开始，加上传入的索引值
        int sequenceNumber = 1 + detailIndex;
        
        // 格式化为3位数字，例如：001, 002, ..., 999
        return mxPrefix + String.format("%03d", sequenceNumber);
    }

    @Override
    public Map<Integer, List<PubObjFlowTemp>> commitProxy(BillnoType billnoType, String djbh, Double money, String auditor) {
        try {

            // 1. 校验采购申请状态，只有保存状态（1）才能提交
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getJhbh, djbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，无法提交");
            }
            
            // 权限校验：只有创建人才能提交
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = cgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 提交操作，当前用户：{}，创建人：{}，djbh：{}", currentUser, originalUser, djbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能提交此采购申请");
            }
            log.info("权限校验通过 - 提交操作，当前用户：{}，djbh：{}", currentUser, djbh);
            
            // 2. 检查状态 - 只有保存状态（1）才能提交
            if (cgsqml.getZt() == null || !"1".equals(cgsqml.getZt())) {
                throw new GlobalException("只有保存状态的采购申请才能提交，当前状态为：" + StatusName.findByStatus(cgsqml.getZt()));
            }
            
            // 3. 获取工作流程模板节点
            List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new HashMap<>();
            
            if (templates.isEmpty()) {
                // 如果没有配置审核模板，创建默认审核节点
                log.warn("未找到审核流程模板，将创建默认审核节点，单据类型：{}", billnoType.getCode());

                PubObjFlowTemp defaultTemplate = new PubObjFlowTemp();
                defaultTemplate.setShrdm(StaticValue.getZydm());
                defaultTemplate.setShrxm(StaticValue.getZymc());
                defaultTemplate.setJdmc("超级管理员审核");
                defaultTemplate.setJddm(1314);
                defaultTemplate.setGsdm(StaticValue.getGsdm());
                defaultTemplate.setDjlxid(BillnoType.CGSQ.getCode());
                defaultTemplate.setFlowcode("CGSQ_FLOW_001");
                defaultTemplate.setXh("1");
                defaultTemplate.setKjnd(String.valueOf(Year.now().getValue()));

                List<PubObjFlowTemp> defaultList = new ArrayList<>();
                defaultList.add(defaultTemplate);
                nodeMap.put(1, defaultList);

                log.info("已创建默认审核节点，审核人：{}", defaultTemplate.getShrxm());
            } else {
                // 按节点序号分组
                for (PubObjFlowTemp template : templates) {
                    Integer nodeSeq = template.getXh() != null ? Integer.parseInt(template.getXh()) : 1;
                    nodeMap.computeIfAbsent(nodeSeq, k -> new ArrayList<>()).add(template);
                }
                log.info("获取审核流程模板成功，共{}个节点，模板数量：{}", nodeMap.size(), templates.size());
            }
            
            log.info("采购申请提交Service层处理完成，djbh：{}，节点数量：{}", djbh, nodeMap.size());
            
            return nodeMap;
            
        } catch (Exception e) {
            log.error("采购申请提交Service层处理失败，djbh: {}", djbh, e);
            throw new GlobalException("提交采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO findByLid(String djbh) {
        // 复用已有的getCgsqDetailByDjbh方法
        return getCgsqDetailBySqbh(djbh);
    }

    @Override
    public void updateStatus(String sqbh, Integer status) {
        try {
            LambdaUpdateWrapper<GpmCgsqml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgsqml::getSqbh, sqbh)
                         .set(GpmCgsqml::getZt, status.toString());
            
            int result = cgsqmlMapper.update(null, updateWrapper);
            if (result == 0) {
                throw new GlobalException("更新采购申请状态失败，采购申请不存在：" + sqbh);
            }
            
            // 同步更新GBI_ZBSYREC表的状态，确保与采购申请主表状态保持一致
            updateGbiZbsyrecStatus(sqbh, status.toString());
            
            log.info("更新采购申请状态成功，djbh: {}, status: {}", sqbh, status);
            
        } catch (Exception e) {
            log.error("更新采购申请状态失败，djbh: {}, status: {}", sqbh, status, e);
            throw new GlobalException("更新采购申请状态失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Object isCheckedByAuthority(CgsqCheckParams checkParams) {
        try {
            // 修改为优先使用SQBH查询，如果没有SQBH则使用DJBH查询
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            
            // 检查参数中是否有SQBH，优先使用SQBH
            String sqbh = null;
            String djbh = checkParams.getDjbh();
            
            // 如果传入的是SQBH格式（以SQBH开头），则作为SQBH处理
            if (djbh != null && djbh.startsWith("SQBH")) {
                sqbh = djbh;
                queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            } else {
                // 否则按DJBH处理
                queryWrapper.eq(GpmCgsqml::getJhbh, djbh);
            }
            
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);

            JSONObject resultJson = new JSONObject();

            // 检查采购申请是否存在
            if (cgsqml == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", true);
                resultJson.put("result", "此流程单据已经被删除，当前审核人" + LoginInfo.getCurrEmployeeName() + "无法查看");
                return resultJson;
            }

            // 检查采购申请状态
            // 状态：1-保存，2-已提交，3-审核中，4-已审核，5-退回
            if (cgsqml.getZt() == null || 
                "1".equals(cgsqml.getZt()) || // 1-保存状态
                "5".equals(cgsqml.getZt())) { // 5-退回状态
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程当前节点已被审批，当前审核人" + LoginInfo.getCurrEmployeeName() + "无权审批!\n是否进入详情查看单据信息");
                return resultJson;
            }

            // 获取审批流程信息
            BillnoType billType = BillnoType.CGSQ;

            // 查询当前审核节点（使用SQBH或DJBH）
            String billId = sqbh != null ? sqbh : djbh;
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    billType.getCode().toString(), 
                    billId
            );

            // 检查当前用户是否有审核权限
            if (nowNodeInfo == null || 
                !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程当前节点已被审批，当前审核人" + LoginInfo.getCurrEmployeeName() + "无权审批!\n是否进入详情查看单据信息");
            } else {
                resultJson.put("isCheck", false);
                resultJson.put("isDelete", false);
                resultJson.put("result", "");
            }

            return resultJson;

        } catch (Exception e) {
            log.error("检查采购申请审核权限失败，参数: {}", checkParams, e);
            
            JSONObject errorResult = new JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 同步更新GBI_ZBSYREC表的单据状态
     * @param sqbh 单据编号
     * @param djzt 新的单据状态
     */
    private void updateGbiZbsyrecStatus(String sqbh, String djzt) {
        try {
            log.info("开始同步更新GBI_ZBSYREC表状态，单据编号：{}，新状态：{}", sqbh, djzt);
            
            // 更新GBI_ZBSYREC表中对应记录的DJZT字段
            LambdaUpdateWrapper<GbiZbsyrec> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GbiZbsyrec::getDjid, sqbh)
                        .eq(GbiZbsyrec::getModule, BillnoType.CGSQ.getModCode())
                        .eq(GbiZbsyrec::getDjlx, BillnoType.CGSQ.getCode())
                        .set(GbiZbsyrec::getDjzt, djzt);
            
            int updateCount = gbiZbsyrecMapper.update(null, updateWrapper);
            
            log.info("GBI_ZBSYREC表状态同步完成，单据编号：{}，更新记录数：{}，新状态：{}", sqbh, updateCount, djzt);
            
        } catch (Exception e) {
            log.error("同步更新GBI_ZBSYREC表状态失败，单据编号：{}，新状态：{}，异常：{}", sqbh, djzt, e.getMessage());
            // 这里不抛出异常，避免影响主流程，但会记录日志
        }
    }

    @Override
    public void check(BillnoType billType, String jhbh, String opinion, String auditor, BigDecimal money) {
        try {
            // 1. 查询采购申请详情
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getJhbh, jhbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);

            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，无法审核");
            }

            // 2. 检查状态 - 只有已提交（2）或审核中（3）的采购申请才能审核
            if (cgsqml.getZt() == null || 
                (!"2".equals(cgsqml.getZt()) && !"3".equals(cgsqml.getZt()))) {
                throw new GlobalException("只有已提交或审核中状态的采购申请才能审核，当前状态为：" + StatusName.findByStatus(cgsqml.getZt()));
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 获取审批流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    billType.getModCode(), billType.getCode().toString());

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }

            // 4. 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    billType.getCode().toString(), 
                    jhbh
            );

            if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("该节点已被其他人审核或您无权限审核");
            }

            // 5. 关键修正：在更新节点之前先获取终审判断所需的信息
            int nowNote = Integer.parseInt(nowNodeInfo.getAuditFlag());
            int nextNote = Integer.parseInt(nowNodeInfo.getAuditAftFlag());
            
            // 判断是否为终审（nextNote == -1 表示最后一个节点）
            boolean isLastNode = (nextNote == -1);
            
            log.info("审核节点信息 - 当前节点：{}，下一节点：{}，是否终审：{}", nowNote, nextNote, isLastNode);

            // 6. 更新当前节点为已审核
            nowNodeInfo.setIsaudit("1");
            nowNodeInfo.setSpecificCheckPerson(LoginInfo.getCurrEmployeeName());

            // 更新当前节点状态
            LambdaUpdateWrapper<PubObjFlow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PubObjFlow::getModcode, billType.getModCode())
                    .eq(PubObjFlow::getDjlx, billType.getCode().toString())
                    .eq(PubObjFlow::getDjh, jhbh)
                    .eq(PubObjFlow::getAuditFlag, String.valueOf(nowNote))
                    .set(PubObjFlow::getIsaudit, "1")
                    .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

            pubObjFlowMapper.update(null, updateWrapper);

            // 7. 根据终审判断结果更新采购申请状态
            int status = isLastNode ? 4 : 3; // 4-已审核（终审），3-审核中
            updateStatus(jhbh, status);
            
            // 8. 记录审核日志（统一使用djbh）
            createAuditLog(billType, jhbh, opinion, LoginInfo.getCurrEmployeeName(), money, workflow, nowNote, nowNodeInfo.getNodeName(), "审核");

            log.info("采购申请审核成功，jhbh：{}，审核人：{}，是否终审：{}", jhbh, LoginInfo.getCurrEmployeeName(), isLastNode);

        } catch (Exception e) {
            log.error("审核采购申请失败，jhbh: {}", jhbh, e);
            throw new GlobalException("审核采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO callBack(String sqbh) {
        try {
            log.info("开始收回采购申请，单据编号：{}", sqbh);
            
            // 1. 查询采购申请
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，单据编号：" + sqbh);
            }
            
            // 权限校验：只有创建人才能收回
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = cgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 收回操作，当前用户：{}，创建人：{}，djbh：{}", currentUser, originalUser, sqbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能收回此采购申请");
            }
            log.info("权限校验通过 - 收回操作，当前用户：{}，djbh：{}", currentUser, sqbh);
            
            // 2. 检查状态是否可以收回
            String currentStatus = cgsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购申请状态异常，无法收回");
            }
            
            // 只有已提交(2)、审核中(3)的状态才能收回
            if (!"2".equals(currentStatus) && !"3".equals(currentStatus)) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgsqml.getZt()) + "，无法收回。只有已提交或审核中的采购申请才能收回");
            }
            
            // 3. 更新状态为保存状态(1)
            updateStatus(sqbh, 1);
            
            // 4. 删除相关的工作流程数据
            try {
                // 删除工作流程实例
                if (pubObjFlowService != null) {
                    pubObjFlowService.deleteByCon(BillnoType.CGSQ.getModCode(), 
                                                BillnoType.CGSQ.getCode().toString(), sqbh);
                }
                
                log.info("已清理工作流程数据，单据编号：{}", sqbh);
                
            } catch (Exception e) {
                log.warn("清理工作流程数据时出现异常，但不影响收回操作，单据编号：{}，异常：{}", sqbh, e.getMessage());
            }
            
            log.info("采购申请收回成功，单据编号：{}", sqbh);
            
            // 5. 查询并返回收回后的采购申请详情
            return getCgsqDetailBySqbh(sqbh);
            
        } catch (Exception e) {
            log.error("收回采购申请失败，单据编号：{}，异常：{}", sqbh, e.getMessage(), e);
            throw new GlobalException("收回采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO checkCallBack(String djbh, String opinion, String auditor) {
        try {
            log.info("开始退审采购申请，单据编号：{}，退审人：{}", djbh, LoginInfo.getCurrEmployeeName());
            
            // 1. 查询采购申请
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getJhbh, djbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，单据编号：" + djbh);
            }
            
            // 2. 检查状态是否可以退审
            String currentStatus = cgsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购申请状态异常，无法退审");
            }
            
            // 只有审核中(3)的状态才能退审
            if (!"3".equals(currentStatus)) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgsqml.getZt()) + "，无法退审。只有审核中的采购申请才能退审");
            }
            
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 3. 查询当前审核节点
            PubObjFlow currentNode = pubObjFlowService.selectNowNodeByDjh(
                    BillnoType.CGSQ.getModCode(),
                    BillnoType.CGSQ.getCode().toString(), 
                    djbh
            );
            
            if (currentNode == null) {
                throw new GlobalException("未找到当前审核节点，无法退审");
            }
            
            // 4. 检查退审权限
            if (!currentNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("您无权限对该采购申请进行退审操作");
            }
            
            // 5. 退回到上一个节点或已提交状态
            updateStatus(djbh, 2); // 设置为已提交状态
            
            // 6. 删除工作流程节点
            pubObjFlowService.deleteByCon(BillnoType.CGSQ.getModCode(), 
                                        BillnoType.CGSQ.getCode().toString(), djbh);
            
            // 7. 记录退审日志
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    BillnoType.CGSQ.getModCode(), BillnoType.CGSQ.getCode().toString());
            
            if (workflow != null) {
                createAuditLog(BillnoType.CGSQ, djbh, opinion, LoginInfo.getCurrEmployeeName(),
                             cgsqml.getSqje(), workflow, Integer.parseInt(currentNode.getAuditFlag()), 
                             currentNode.getNodeName(), "退审");
            }
            
            log.info("采购申请退审成功，单据编号：{}，退审人：{}", djbh, LoginInfo.getCurrEmployeeName());
            
            // 8. 查询并返回退审后的采购申请详情
            return getCgsqDetailBySqbh(djbh);
            
        } catch (Exception e) {
            log.error("退审采购申请失败，单据编号：{}，退审人：{}，异常：{}", djbh, LoginInfo.getCurrEmployeeName(), e.getMessage(), e);
            throw new GlobalException("退审采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO noAudit(String sqbh) {
        try {
            log.info("开始销审采购申请，单据编号：{}", sqbh);
            
            // 1. 查询采购申请
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getJhbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，单据编号：" + sqbh);
            }
            
            // 2. 检查状态是否可以销审
            String currentStatus = cgsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购申请状态异常，无法销审");
            }
            
            int status = Integer.parseInt(currentStatus);
            // 只有已提交(2)、审核中(3)、已审核(4)的状态才能销审
            if (status != 3 && status != 4) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgsqml.getZt()) + "，无法销审。只有已提交、审核中或已审核的采购申请才能销审");
            }
            
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 查询最后已审核节点
            PubObjFlow lastAuditNode = pubObjFlowService.selectLastAuditNodeByDjh(
                    BillnoType.CGSQ.getModCode(),
                    BillnoType.CGSQ.getCode().toString(),
                    sqbh
            );

            // 3. 销审逻辑：参考采购计划的销审逻辑
            if (status == 3 || status == 4) {
                // 如果当前状态是审核中(3)或已审核(4)，需要退回到上一个节点
                
                // 查询所有工作流程节点
                List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(sqbh);
                if (allNodes.isEmpty()) {
                    // 如果没有工作流程节点，直接设置为已提交状态
                    updateStatus(sqbh, 2);
                    log.info("采购申请没有工作流程节点，销审为已提交状态，单据编号：{}", sqbh);
                } else {
                    // 找到最后一个已审核的节点（销审应该退回到上一个节点）
                    PubObjFlow lastAuditedNode = null;
                    int maxAuditedNodeSeq = -1;
                    
                    for (PubObjFlow node : allNodes) {
                        if ("1".equals(node.getIsaudit())) { // 已审核的节点
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq > maxAuditedNodeSeq) {
                                maxAuditedNodeSeq = nodeSeq;
                                lastAuditedNode = node;
                            }
                        }
                    }
                    
                    if (lastAuditedNode != null) {
                        if (!lastAuditedNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                            throw new GlobalException("您无权限对该采购验收进行销审操作");
                        }
                        // 有已审核的节点，找到它的上一个节点
                        PubObjFlow previousNode = null;
                        
                        for (PubObjFlow node : allNodes) {
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq < maxAuditedNodeSeq && (previousNode == null || 
                                Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                                previousNode = node;
                            }
                        }

                        // 将上一个节点设为未审核，删除当前节点及之后的节点
                        LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                        updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGSQ.getModCode())
                                .eq(PubObjFlow::getDjlx, BillnoType.CGSQ.getCode().toString())
                                .eq(PubObjFlow::getDjh, sqbh)
                                .eq(PubObjFlow::getAuditFlag, previousNode.getAuditFlag())
                                .set(PubObjFlow::getIsaudit, "0")
                                .set(PubObjFlow::getSpecificCheckPerson, "");

                        pubObjFlowMapper.update(null, updatePreviousWrapper);

                        if (previousNode != null) {
                            // 更新采购验收状态为审核中(3)
                            updateStatus(sqbh, 3);
                            log.info("采购申请销审成功，退回到上一个节点，ysbh：{}，上一节点序号：{}",
                                    sqbh, previousNode.getAuditFlag());
                        } else {
                            // 更新采购验收状态为已提交(2)
                            updateStatus(sqbh, 2);
                            log.info("采购申请销审成功，第一个审核节点已重置为未审核状态，ysbh：{}，第一节点序号：{}",
                                    sqbh, maxAuditedNodeSeq);
                        }
                    } else {
                        updateStatus(sqbh, 2);
                        log.info("采购申请没有已审核节点，销审为已提交状态，单据编号：{}", sqbh);
                    }
                }
            }
            
            // 4. 记录销审操作日志
            try {
                // 查询工作流程配置获取流程代码
                PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                        BillnoType.CGSQ.getModCode(), BillnoType.CGSQ.getCode().toString());
                
                // 记录销审操作日志
                if (workflow != null) {
                    createAuditLog(BillnoType.CGSQ, sqbh, "",
                                 LoginInfo.getCurrEmployeeName(), cgsqml.getSqje(), 
                                 workflow, -99, lastAuditNode.getAuditFlag() , "销审");
                }
                
                log.info("已记录销审操作日志，单据编号：{}", sqbh);
                
            } catch (Exception e) {
                log.warn("记录销审日志时出现异常，但不影响销审操作，单据编号：{}，异常：{}", sqbh, e.getMessage());
            }
            
            log.info("采购申请销审成功，单据编号：{}", sqbh);
            
            // 5. 查询并返回销审后的采购申请详情
            return getCgsqDetailBySqbh(sqbh);
            
        } catch (Exception e) {
            log.error("销审采购申请失败，单据编号：{}，异常：{}", sqbh, e.getMessage(), e);
            throw new GlobalException("销审采购申请失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建审核日志
     */
    private void createAuditLog(BillnoType billType, String djbh, String opinion, String auditor, 
                               BigDecimal money, PubWorkflow workflow, int nodeseq, String nodename, String atype) {
        try {
            PubAuditLog auditLog = new PubAuditLog();
            
            // 设置基础信息
            auditLog.setGsdm(LoginInfo.getCurrCorpCode());
            auditLog.setKjnd(LoginInfo.getCurrAccountantYear());

            // 获取新的日志ID
            Integer newLogID = pubAuditLogMapper.selectMaxLogID();
            if (newLogID == null) {
                newLogID = 1;
            }
            auditLog.setLogid(newLogID.longValue());

            // 设置单据信息
            auditLog.setBillid(djbh);
            auditLog.setBillname(workflow.getBizname());
            auditLog.setFlowcode(workflow.getFlowcode());
            auditLog.setFlowname(workflow.getFlowname());
            auditLog.setModname(billType.getModCode());
            auditLog.setBizname(workflow.getBizname());

            // 设置审核节点信息
            auditLog.setNodeseq(nodeseq);
            auditLog.setNodename(nodename);

            // 设置审核人信息
            auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
            auditLog.setAuditor(LoginInfo.getCurrEmployeeName());
            auditLog.setCertigierid(0);
            auditLog.setCertigier("");

            // 设置审核时间
            SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmss");
            auditLog.setAdatetime(df1.format(new Date()));

            // 设置金额和备注
            auditLog.setAmt(money != null ? money : BigDecimal.ZERO);
            auditLog.setRemark(opinion != null ? opinion : "");
            auditLog.setAtype(atype);

            // 设置服务器时间
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setServDateTime(df2.format(new Date()));

            // 设置计算机信息
            try {
                String computerMsg = InetAddress.getLocalHost().getHostName()
                        + "/" + InetAddress.getLocalHost().getHostAddress();
                auditLog.setComputerName(computerMsg);
            } catch (Exception e) {
                auditLog.setComputerName("Unknown");
            }

            // 保存审核日志
            pubAuditLogMapper.insert(auditLog);
            
        } catch (Exception e) {
            log.error("创建审核日志失败，djbh: {}", djbh, e);
            // 审核日志失败不影响主流程
        }
    }

    @Override
    public boolean deleteBySqbh(String sqbh) {
        try {
            // 权限校验：查询采购申请的创建人信息
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，无法删除，sqbh: " + sqbh);
            }
            
            // 权限校验：只有创建人才能删除
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = cgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 删除操作，当前用户：{}，创建人：{}，sqbh：{}", currentUser, originalUser, sqbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能删除此采购申请");
            }
            log.info("权限校验通过 - 删除操作，当前用户：{}，sqbh：{}", currentUser, sqbh);
            
            // 1. 删除主表
            LambdaUpdateWrapper<GpmCgsqml> mlWrapper = new LambdaUpdateWrapper<>();
            mlWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            int deleteml = cgsqmlMapper.delete(mlWrapper);
            
            // 2. 删除计划明细表
            LambdaUpdateWrapper<GpmCgsqnr> jhmxWrapper = new LambdaUpdateWrapper<>();
            jhmxWrapper.eq(GpmCgsqnr::getSqbh, sqbh);
            int deleteJhmx = cgsqJhmxMapper.delete(jhmxWrapper);
            
            // 3. 删除预算指标表
            LambdaUpdateWrapper<OerYszb> yszbWrapper = new LambdaUpdateWrapper<>();
            yszbWrapper.eq(OerYszb::getDjbh, sqbh);
            int deletedOerYszb = indexMapper.delete(yszbWrapper);
            
            // 4. 删除指标使用记录
            LambdaUpdateWrapper<GbiZbsyrec> zbsyrecWrapper = new LambdaUpdateWrapper<>();
            zbsyrecWrapper.eq(GbiZbsyrec::getDjid, sqbh)
                    .eq(GbiZbsyrec::getModule, BillnoType.CGSQ.getModCode())
                    .eq(GbiZbsyrec::getDjlx, BillnoType.CGSQ.getCode());
            int deletedZbsyrec = gbiZbsyrecMapper.delete(zbsyrecWrapper);
            
            log.info("删除采购申请成功，sqbh：{}，主表：{}条，明细：{}条，预算指标：{}条，指标使用记录：{}条", 
                    sqbh, deleteml, deleteJhmx, deletedOerYszb, deletedZbsyrec);
            return deleteml > 0;
            
        } catch (Exception e) {
            log.error("删除采购申请失败，sqbh: {}", sqbh, e);
            throw new GlobalException("删除采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO getCgsqDetailBySqbh(String sqbh) {
        try {
            // 构建响应数据
            CgsqSaveResponseVO responseVO = buildSaveResponseVO(sqbh);
            if (responseVO == null) {
                throw new GlobalException("采购申请不存在，sqbh: " + sqbh);
            }
            log.info("查询采购申请详情成功，sqbh: {}, 申请名称: {}", sqbh, responseVO.getBaseInfo().getSqmc());
            return responseVO;
        } catch (Exception e) {
            log.error("查询采购申请详情失败，sqbh: {}", sqbh, e);
            throw new GlobalException("查询采购申请详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Map<Integer, List<PubObjFlowTemp>> commitProxyBySqbh(BillnoType billnoType, String sqbh, Double money, String auditor) {
        try {
            // 1. 校验采购申请状态，只有保存状态（1、5）才能提交
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，无法提交，sqbh: " + sqbh);
            }
            
            // 权限校验：只有创建人才能提交
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = cgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 提交操作，当前用户：{}，创建人：{}，sqbh：{}", currentUser, originalUser, sqbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能提交此采购申请");
            }
            log.info("权限校验通过 - 提交操作，当前用户：{}，sqbh：{}", currentUser, sqbh);

            // 2. 检查状态 - 只有保存状态（1）(5)才能提交
            if (!cgsqml.getZt().equals("1") && !cgsqml.getZt().equals("5")) {
                throw new GlobalException("只有保存状态的采购计划才能提交，当前状态为：" + StatusName.findByStatus(cgsqml.getZt()));
            }
            
            // 3. 获取工作流程模板节点
            List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new HashMap<>();
            
            if (templates == null || templates.isEmpty()) {
                // 如果没有配置审核模板，创建默认审核节点
                log.warn("未找到审核流程模板，将创建默认审核节点，单据类型：{}", billnoType.getCode());
                
                PubObjFlowTemp defaultTemplate = new PubObjFlowTemp();
                defaultTemplate.setShrdm(StaticValue.getZydm());
                defaultTemplate.setShrxm(StaticValue.getZymc());
                defaultTemplate.setJdmc("超级管理员审核");
                defaultTemplate.setJddm(1314);
                defaultTemplate.setGsdm(StaticValue.getGsdm());
                defaultTemplate.setDjlxid(BillnoType.CGSQ.getCode());
                defaultTemplate.setFlowcode("CGSQ_FLOW_001");
                defaultTemplate.setXh("1");
                defaultTemplate.setKjnd(String.valueOf(Year.now().getValue()));
                
                List<PubObjFlowTemp> defaultList = new ArrayList<>();
                defaultList.add(defaultTemplate);
                nodeMap.put(1, defaultList);
                
                log.info("已创建默认审核节点，审核人：{}", defaultTemplate.getShrxm());
            } else {
                // 按节点序号分组
                for (PubObjFlowTemp template : templates) {
                    Integer nodeSeq = template.getXh() != null ? Integer.parseInt(template.getXh()) : 1;
                    nodeMap.computeIfAbsent(nodeSeq, k -> new ArrayList<>()).add(template);
                }
                
                log.info("获取审核流程模板成功，共{}个节点，模板数量：{}", nodeMap.size(), templates.size());
            }
            
            return nodeMap;
            
        } catch (Exception e) {
            log.error("提交采购申请失败，sqbh: {}", sqbh, e);
            throw new GlobalException("提交采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO findBySqbh(String sqbh) {
        return getCgsqDetailBySqbh(sqbh);
    }

    @Override
    public void updateStatusBySqbh(String sqbh, Integer status) {
        try {
            // 直接基于SQBH更新状态
            LambdaUpdateWrapper<GpmCgsqml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgsqml::getSqbh, sqbh)
                         .set(GpmCgsqml::getZt, status.toString());
            
            int result = cgsqmlMapper.update(null, updateWrapper);
            if (result == 0) {
                throw new GlobalException("更新采购申请状态失败，采购申请不存在：" + sqbh);
            }
            
            // 同步更新GBI_ZBSYREC表的状态，确保与采购申请主表状态保持一致
            updateGbiZbsyrecStatusBySqbh(sqbh, status.toString());
            
            log.info("更新采购申请状态成功，sqbh: {}, status: {}", sqbh, status);
            
        } catch (Exception e) {
            log.error("更新采购申请状态失败，sqbh: {}, status: {}", sqbh, status, e);
            throw new GlobalException("更新采购申请状态失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void checkBySqbh(BillnoType billType, String sqbh, String opinion, String auditor, BigDecimal money) {
        try {
            // 1. 查询采购申请详情
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);

            // 2. 检查状态 - 只有已提交（2）或审核中（3）的采购申请才能审核
            if (cgsqml.getZt() == null || 
                (!"2".equals(cgsqml.getZt()) && !"3".equals(cgsqml.getZt()))) {
                throw new GlobalException("只有已提交或审核中状态的采购申请才能审核，当前状态为：" + StatusName.findByStatus(cgsqml.getZt()));
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 获取审批流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    billType.getModCode(), billType.getCode().toString());

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }

            // 4. 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    billType.getCode().toString(), 
                    sqbh
            );

            if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("该节点已被其他人审核或您无权限审核");
            }

            // 5. 关键修正：在更新节点之前先获取终审判断所需的信息
            int nowNote = Integer.parseInt(nowNodeInfo.getAuditFlag());
            int nextNote = Integer.parseInt(nowNodeInfo.getAuditAftFlag());
            
            // 判断是否为终审（nextNote == -1 表示最后一个节点）
            boolean isLastNode = (nextNote == -1);
            
            log.info("审核节点信息 - 当前节点：{}，下一节点：{}，是否终审：{}", nowNote, nextNote, isLastNode);

            // 6. 更新当前节点为已审核
            nowNodeInfo.setIsaudit("1");
            nowNodeInfo.setSpecificCheckPerson(LoginInfo.getCurrEmployeeName());

            // 更新当前节点状态
            LambdaUpdateWrapper<PubObjFlow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PubObjFlow::getModcode, billType.getModCode())
                    .eq(PubObjFlow::getDjlx, billType.getCode().toString())
                    .eq(PubObjFlow::getDjh, sqbh)
                    .eq(PubObjFlow::getAuditFlag, String.valueOf(nowNote))
                    .set(PubObjFlow::getIsaudit, "1")
                    .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

            pubObjFlowMapper.update(null, updateWrapper);

            // 7. 根据终审判断结果更新采购申请状态
            int status = isLastNode ? 4 : 3; // 4-已审核（终审），3-审核中
            updateStatusBySqbh(sqbh, status);
            
            // 8. 记录审核日志
            createAuditLogBySqbh(billType, sqbh, opinion, LoginInfo.getCurrEmployeeName(), money, workflow, nowNote, nowNodeInfo.getNodeName(), "审核");

            log.info("采购申请审核成功，sqbh：{}，审核人：{}，是否终审：{}", sqbh, auditor, isLastNode);

        } catch (Exception e) {
            log.error("审核采购申请失败，sqbh: {}", sqbh, e);
            throw new GlobalException("审核采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqAuditLogVO findCheckLogBySqbh(BillnoType billnoType, String sqbh) {
        try {
            log.info("开始查询采购申请审核日志，申请编号：{}", sqbh);
            
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 1. 检查采购申请是否存在
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，sqbh: " + sqbh);
            }
            
            // 2. 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd, 
                    billnoType.getModCode(), billnoType.getCode().toString());
            
            String flowcode = "CGSQ_FLOW_001"; // 默认流程代码
            if (workflow != null) {
                flowcode = workflow.getFlowcode();
            }
            
            // 3. 查询审核日志（使用SQBH作为单据号）
            List<PubAuditLog> logList = pubAuditLogMapper.selectByBillId(gsdm, kjnd, sqbh, flowcode);
            
            // 4. 转换为VO
            CgsqAuditLogVO auditLogVO = new CgsqAuditLogVO();
            
            // 转换日志列表
            List<CgsqAuditLogVO.AuditLogItem> logItems = logList.stream().map(log -> {
                CgsqAuditLogVO.AuditLogItem item = new CgsqAuditLogVO.AuditLogItem();
                item.setGsdm(log.getGsdm());
                item.setKjnd(log.getKjnd());
                item.setLogid(log.getLogid());
                item.setBillid(log.getBillid());
                item.setBillname(log.getBillname());
                item.setFlowcode(log.getFlowcode());
                item.setFlowname(log.getFlowname());
                item.setModname(log.getModname());
                item.setBizname(log.getBizname());
                item.setNodeseq(log.getNodeseq());
                item.setNodename(log.getNodename());
                item.setAuditorid(log.getAuditorid());
                item.setAuditor(log.getAuditor());
                item.setCertigierid(log.getCertigierid());
                item.setCertigier(log.getCertigier());
                item.setAdatetime(log.getAdatetime());
                item.setAmt(log.getAmt());
                item.setRemark(log.getRemark());
                item.setAtype(log.getAtype());
                item.setLogseq(log.getLogseq());
                item.setServDateTime(log.getServDateTime());
                item.setComputerName(log.getComputerName());
                return item;
            }).collect(Collectors.toList());
            
            // 5. 创建日志标题列表
            List<CgsqAuditLogVO.LogTitleItem> logTitleList = new ArrayList<>();
            
            // 添加提交节点
            CgsqAuditLogVO.LogTitleItem titleNode = new CgsqAuditLogVO.LogTitleItem();
            titleNode.setNodename("提交审核");
            titleNode.setGsdm(gsdm);
            titleNode.setNodeseq(-9); // 提交节点编号
            titleNode.setKjnd(kjnd);
            titleNode.setIsaudit(logList.isEmpty() ? "0" : "1");
            titleNode.setAuditStatus(logList.isEmpty() ? "未提交" : "已提交");
            
            // 如果有提交日志，设置提交时间
            logList.stream()
                .filter(log -> log.getNodeseq() != null && log.getNodeseq() == -9)
                .findFirst()
                .ifPresent(log -> titleNode.setAuditTime(log.getServDateTime()));
            
            logTitleList.add(titleNode);
            
            // 查询工作流程节点，添加审核节点标题（使用SQBH）
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(sqbh);
            
            // 查询工作流程模板节点（用于获取节点名称）
            List<PubObjFlowTemp> flowTemplates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, String> nodeNameMap = new HashMap<>();
            for (PubObjFlowTemp template : flowTemplates) {
                nodeNameMap.put(Integer.valueOf(template.getXh()), template.getJdmc());
            }
            
            for (PubObjFlow node : flowNodes) {
                CgsqAuditLogVO.LogTitleItem flowTitleNode = new CgsqAuditLogVO.LogTitleItem();
                
                // 通过节点序号获取正确的节点名称
                Integer nodeSeq = null;
                try {
                    nodeSeq = Integer.valueOf(node.getAuditFlag());
                    // 从工作流程模板中获取节点名称，如果没有则使用审核人名称作为备选
                    String nodeName = nodeNameMap.get(nodeSeq);
                    if (nodeName != null && !nodeName.trim().isEmpty()) {
                        flowTitleNode.setNodename(nodeName);
                    } else {
                        // 如果模板中没有节点名称，使用审核人名称作为备选
                        flowTitleNode.setNodename(node.getShrmc() != null ? node.getShrmc() : "审核节点");
                    }
                } catch (NumberFormatException e) {
                    flowTitleNode.setNodename("审核节点");
                    nodeSeq = 1;
                }
                
                flowTitleNode.setGsdm(node.getGsdm());
                flowTitleNode.setNodeseq(nodeSeq);
                flowTitleNode.setKjnd(node.getKjnd());
                flowTitleNode.setIsaudit("1".equals(node.getIsaudit()) ? "1" : "0");
                flowTitleNode.setAuditStatus("1".equals(node.getIsaudit()) ? "已审核" : "待审核");
                
                // 查找对应的审核日志设置审核时间
                logList.stream()
                    .filter(log -> log.getNodeseq() != null && log.getNodeseq().equals(flowTitleNode.getNodeseq()))
                    .findFirst()
                    .ifPresent(log -> flowTitleNode.setAuditTime(log.getServDateTime()));
                
                logTitleList.add(flowTitleNode);
            }
            
            auditLogVO.setLogList(logItems);
            auditLogVO.setLogTitleList(logTitleList);
            
            log.info("查询采购申请审核日志成功，申请编号：{}，日志数量：{}", sqbh, logItems.size());
            
            return auditLogVO;
            
        } catch (Exception e) {
            log.error("查询采购申请审核日志失败，sqbh: {}", sqbh, e);
            throw new GlobalException("查询审核日志失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO callBackBySqbh(String sqbh) {
        try {
            log.info("开始收回采购申请，申请编号：{}", sqbh);
            
            // 1. 查询采购申请
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，申请编号：" + sqbh);
            }
            
            // 2. 权限校验：只有创建人才能收回
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalUser = cgsqml.getCjrmc();
            if (!currentUser.equals(originalUser)) {
                log.warn("权限校验失败 - 收回操作，当前用户：{}，创建人：{}，sqbh：{}", currentUser, originalUser, sqbh);
                throw new GlobalException("无权限操作：只有创建人（" + originalUser + "）才能收回此采购申请");
            }
            log.info("权限校验通过 - 收回操作，当前用户：{}，sqbh：{}", currentUser, sqbh);
            
            // 3. 检查状态是否可以收回
            String currentStatus = cgsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购申请状态异常，无法收回");
            }
            
            // 只有已提交(2)的状态才能收回
            if (!"2".equals(currentStatus)) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgsqml.getZt()) + "，无法收回。只有已提交的采购申请才能收回");
            }
            
            // 4. 更新状态为保存状态(1)
            updateStatusBySqbh(sqbh, 1);
            
            // 5. 删除相关的工作流程数据
            try {
                // 删除工作流程实例（使用SQBH作为单据号）
                if (pubObjFlowService != null) {
                    pubObjFlowService.deleteByCon(BillnoType.CGSQ.getModCode(), 
                                                BillnoType.CGSQ.getCode().toString(), sqbh);
                }
                
                log.info("已清理工作流程数据，申请编号：{}", sqbh);
                
            } catch (Exception e) {
                log.warn("清理工作流程数据时出现异常，但不影响收回操作，申请编号：{}，异常：{}", sqbh, e.getMessage());
            }
            
            log.info("采购申请收回成功，申请编号：{}", sqbh);
            
            // 6. 查询并返回收回后的采购申请详情
            return getCgsqDetailBySqbh(sqbh);
            
        } catch (Exception e) {
            log.error("收回采购申请失败，申请编号：{}，异常：{}", sqbh, e.getMessage(), e);
            throw new GlobalException("收回采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO checkCallBackBySqbh(String sqbh, String opinion, String auditor) {
        try {
            log.info("开始退审采购申请，申请编号：{}，退审人：{}", sqbh, auditor);
            
            // 1. 查询采购申请
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，申请编号：" + sqbh);
            }
            
            // 2. 检查状态是否可以退审
            String currentStatus = cgsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购申请状态异常，无法退审");
            }
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 3. 查询当前审核节点
            PubObjFlow currentNode = pubObjFlowService.selectNowNodeByDjh(
                    BillnoType.CGSQ.getModCode(),
                    BillnoType.CGSQ.getCode().toString(), 
                    sqbh
            );

            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    BillnoType.CGSQ.getModCode(), BillnoType.CGSQ.getCode().toString());
            
            // 4. 检查退审权限
            if (!currentNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("您无权限对该采购申请进行退审操作");
            }
            
            // 5. 处理工作流程退审逻辑
            int currentNodeSeq = Integer.parseInt(currentNode.getAuditFlag());

            if (currentStatus.equals("2")){
                if (workflow != null) {
                    createAuditLogBySqbh(BillnoType.CGSQ, sqbh, opinion, LoginInfo.getCurrEmployeeName(),
                            cgsqml.getSqje(), workflow, currentNodeSeq,
                            currentNode.getNodeName(), "退审");
                }
                updateStatus(sqbh,5);
                return buildSaveResponseVO(sqbh);
            }

            // 只有审核中(3)的状态才能退审
            if (!"3".equals(currentStatus)) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(currentStatus) + "，无法退审。只有审核中的采购申请才能退审");
            }
            
            // 查询所有工作流程节点，找到上一个节点
            List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(sqbh);
            PubObjFlow previousNode = null;
            
            for (PubObjFlow node : allNodes) {
                int nodeSeq = Integer.parseInt(node.getAuditFlag());
                if (nodeSeq < currentNodeSeq && (previousNode == null || 
                    Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                    previousNode = node;
                }
            }
            
            // 6. 更新工作流程状态
            if (previousNode != null) {
                // 如果有上一个节点，将上一个节点设为未审核
                LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGSQ.getModCode())
                        .eq(PubObjFlow::getDjlx, BillnoType.CGSQ.getCode().toString())
                        .eq(PubObjFlow::getDjh, sqbh)
                        .eq(PubObjFlow::getAuditFlag, previousNode.getAuditFlag())
                        .set(PubObjFlow::getIsaudit, "0")
                        .set(PubObjFlow::getSpecificCheckPerson, "");
                
                pubObjFlowMapper.update(null, updatePreviousWrapper);

                if (previousNode == allNodes.get(0)) {
                    updateStatus(sqbh, 5);
                } else {
                    // 更新采购计划状态为审核中(3)
                    updateStatus(sqbh, 3);
                }
                
                log.info("采购申请退审成功，退回到上一个节点，申请编号：{}，上一节点序号：{}", 
                        sqbh, previousNode.getAuditFlag());
            }
            
            // 7. 记录退审日志
            if (workflow != null) {
                createAuditLogBySqbh(BillnoType.CGSQ, sqbh, opinion, LoginInfo.getCurrEmployeeName(),
                             cgsqml.getSqje(), workflow, currentNodeSeq, 
                             currentNode.getNodeName(), "退审");
            }
            
            log.info("采购申请退审成功，申请编号：{}，退审人：{}", sqbh, auditor);
            
            // 8. 查询并返回退审后的采购申请详情
            return getCgsqDetailBySqbh(sqbh);
            
        } catch (Exception e) {
            log.error("退审采购申请失败，申请编号：{}，退审人：{}，异常：{}", sqbh, auditor, e.getMessage(), e);
            throw new GlobalException("退审采购申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgsqSaveResponseVO noAuditBySqbh(String sqbh) {
        try {
            log.info("开始销审采购申请，申请编号：{}", sqbh);
            
            // 1. 查询采购申请
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);
            
            if (cgsqml == null) {
                throw new GlobalException("采购申请不存在，申请编号：" + sqbh);
            }
            
            // 2. 检查状态是否可以销审
            String currentStatus = cgsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购申请状态异常，无法销审");
            }
            
            int status = Integer.parseInt(currentStatus);
            // 只有审核中(3)、已审核(4)的状态才能销审
            if (status != 3 && status != 4) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(currentStatus) + "，无法销审。只有已提交、审核中或已审核的采购申请才能销审");
            }
            
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            PubObjFlow lastAuditNode = pubObjFlowService.selectLastAuditNodeByDjh(
                    BillnoType.CGSQ.getModCode(),
                    BillnoType.CGSQ.getCode().toString(),
                    sqbh
            );
            
            // 3. 销审逻辑：参考采购计划的销审逻辑
            if (status == 3 || status == 4) {
                // 如果当前状态是审核中(3)或已审核(4)，需要退回到上一个节点
                
                // 查询所有工作流程节点
                List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(sqbh);
                if (allNodes.isEmpty()) {
                    // 如果没有工作流程节点，直接设置为已提交状态
                    updateStatusBySqbh(sqbh, 2);
                    log.info("采购申请没有工作流程节点，销审为已提交状态，申请编号：{}", sqbh);
                } else {
                    // 找到最后一个已审核的节点（销审应该退回到上一个节点）
                    PubObjFlow lastAuditedNode = null;
                    int maxAuditedNodeSeq = -1;
                    
                    for (PubObjFlow node : allNodes) {
                        if ("1".equals(node.getIsaudit())) { // 已审核的节点
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq > maxAuditedNodeSeq) {
                                maxAuditedNodeSeq = nodeSeq;
                                lastAuditedNode = node;
                            }
                        }
                    }
                    
                    if (lastAuditedNode != null) {
                        if (!lastAuditedNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                            throw new GlobalException("您无权限对该采购验收进行销审操作");
                        }
                        LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                        updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGSQ.getModCode())
                                .eq(PubObjFlow::getDjlx, BillnoType.CGSQ.getCode().toString())
                                .eq(PubObjFlow::getDjh, sqbh)
                                .eq(PubObjFlow::getAuditFlag, lastAuditedNode.getAuditFlag())
                                .set(PubObjFlow::getIsaudit, "0");

                        pubObjFlowMapper.update(null, updatePreviousWrapper);
                        // 有已审核的节点，找到它的上一个节点
                        PubObjFlow previousNode = null;
                        
                        for (PubObjFlow node : allNodes) {
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq < maxAuditedNodeSeq && (previousNode == null || 
                                Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                                previousNode = node;
                            }
                        }
                        if (previousNode != null) {
                            // 更新采购验收状态为审核中(3)
                            updateStatus(sqbh, 3);
                            log.info("采购申请销审成功，退回到上一个节点，申请编号：{}，上一节点序号：{}", 
                                    sqbh, previousNode.getAuditFlag());
                        } else {
                            // 更新采购申请状态为已提交(2)，这样第一个人可以继续审核
                            updateStatusBySqbh(sqbh, 2);
                            log.info("采购申请销审成功，第一个审核节点已重置为未审核状态，申请编号：{}，第一节点序号：{}", 
                                    sqbh, maxAuditedNodeSeq);
                        }
                    } else {
                        updateStatusBySqbh(sqbh, 2);
                        log.info("采购申请没有已审核节点，销审为已提交状态，申请编号：{}", sqbh);
                    }
                }
            }

            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    BillnoType.CGSQ.getModCode(), String.valueOf(BillnoType.CGSQ.getCode()));

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }
            if (lastAuditNode!=null){
                createAuditLog(BillnoType.CGSQ, sqbh, "", LoginInfo.getCurrEmployeeName(),
                        cgsqml.getSqje(), workflow, Integer.parseInt(lastAuditNode.getAuditFlag()),lastAuditNode.getNodeName(),"销审");
            }
            
            log.info("采购申请销审成功，申请编号：{}", sqbh);
            
            // 5. 查询并返回销审后的采购申请详情
            return getCgsqDetailBySqbh(sqbh);
            
        } catch (Exception e) {
            log.error("销审采购申请失败，申请编号：{}，异常：{}", sqbh, e.getMessage(), e);
            throw new GlobalException("销审采购申请失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建审核日志
     */
    private void createAuditLogBySqbh(BillnoType billType, String sqbh, String opinion, String auditor, 
                               BigDecimal money, PubWorkflow workflow, int nodeseq, String nodename, String atype) {
        try {
            PubAuditLog auditLog = new PubAuditLog();
            
            // 设置基础信息
            auditLog.setGsdm(LoginInfo.getCurrCorpCode());
            auditLog.setKjnd(LoginInfo.getCurrAccountantYear());

            // 获取新的日志ID
            Integer newLogID = pubAuditLogMapper.selectMaxLogID();
            if (newLogID == null) {
                newLogID = 1;
            }
            auditLog.setLogid(newLogID.longValue());

            // 设置单据信息（使用SQBH）
            auditLog.setBillid(sqbh);
            auditLog.setBillname(workflow.getBizname());
            auditLog.setFlowcode(workflow.getFlowcode());
            auditLog.setFlowname(workflow.getFlowname());
            auditLog.setModname(billType.getModCode());
            auditLog.setBizname(workflow.getBizname());

            // 设置审核节点信息
            auditLog.setNodeseq(nodeseq);
            auditLog.setNodename(nodename);

            // 设置审核人信息
            auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
            auditLog.setAuditor(LoginInfo.getCurrEmployeeName());
            auditLog.setCertigierid(0);
            auditLog.setCertigier("");

            // 设置审核时间
            SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setAdatetime(df1.format(new Date()));

            // 设置金额和备注
            auditLog.setAmt(money != null ? money : BigDecimal.ZERO);
            auditLog.setRemark(StringUtils.isNotBlank(opinion)? opinion : "");
            auditLog.setAtype(atype);

            // 设置服务器时间
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setServDateTime(df2.format(new Date()));

            // 设置计算机信息
            try {
                String computerMsg = InetAddress.getLocalHost().getHostName()
                        + "/" + InetAddress.getLocalHost().getHostAddress();
                auditLog.setComputerName(computerMsg);
            } catch (Exception e) {
                auditLog.setComputerName("Unknown");
            }

            // 保存审核日志
            pubAuditLogMapper.insert(auditLog);
            
        } catch (Exception e) {
            log.error("创建审核日志失败，sqbh: {}", sqbh, e);
            // 审核日志失败不影响主流程
        }
    }

    /**
     * 同步更新GBI_ZBSYREC表的单据状态
     * @param sqbh 申请编号
     * @param djzt 新的单据状态
     */
    private void updateGbiZbsyrecStatusBySqbh(String sqbh, String djzt) {
        try {
            log.info("开始同步更新GBI_ZBSYREC表状态，申请编号：{}，新状态：{}", sqbh, djzt);
            
            // 更新GBI_ZBSYREC表中对应记录的DJZT字段
            LambdaUpdateWrapper<GbiZbsyrec> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GbiZbsyrec::getDjid, sqbh)
                        .eq(GbiZbsyrec::getModule, BillnoType.CGSQ.getModCode())
                        .eq(GbiZbsyrec::getDjlx, BillnoType.CGSQ.getCode())
                        .set(GbiZbsyrec::getDjzt, djzt);
            
            int updateCount = gbiZbsyrecMapper.update(null, updateWrapper);
            
            log.info("GBI_ZBSYREC表状态同步完成，申请编号：{}，更新记录数：{}，新状态：{}", sqbh, updateCount, djzt);
            
        } catch (Exception e) {
            log.error("同步更新GBI_ZBSYREC表状态失败，申请编号：{}，新状态：{}，异常：{}", sqbh, djzt, e.getMessage());
            // 这里不抛出异常，避免影响主流程，但会记录日志
        }
    }

    /**
     * 检查采购申请是否有权限审核（基于SQBH，无参数版本）
     * 注意：此方法需要在前端调用时通过某种方式传递SQBH，比如通过请求头、Session等
     * @param sqbh 申请编号
     * @return 权限检查结果
     */
    public Object isCheckedByAuthorityBySqbh(String sqbh) {
        try {
            log.info("开始检查采购申请审核权限，sqbh：{}", sqbh);
            
            // 参数校验
            if (StringUtils.isBlank(sqbh)) {
                throw new GlobalException("采购申请编号不能为空");
            }
            
            // 查询采购申请信息
            LambdaQueryWrapper<GpmCgsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqml::getSqbh, sqbh);
            GpmCgsqml cgsqml = cgsqmlMapper.selectOne(queryWrapper);

            JSONObject resultJson = new JSONObject();
            String currentUser = LoginInfo.getCurrEmployeeName();

            // 1. 检查采购申请是否存在
            if (cgsqml == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", true);
                resultJson.put("result", "此流程单据已经被删除，当前审核人" + currentUser + "无法查看");
                log.warn("采购申请不存在，sqbh：{}，当前用户：{}", sqbh, currentUser);
                return resultJson;
            }

            // 2. 检查采购申请状态
            String status = cgsqml.getZt();
            String statusName = StatusName.findByStatus(status);
            
            log.info("采购申请状态检查，sqbh：{}，状态：{}（{}），当前用户：{}", sqbh, status, statusName, currentUser);
            
            // 状态：1-保存，2-已提交，3-审核中，4-已审核，5-退回
            if (status == null || "1".equals(status) || "5".equals(status)) {
                // 保存状态或退回状态，无需审核
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程当前状态为：" + statusName + "，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                log.info("采购申请状态不允许审核，sqbh：{}，状态：{}", sqbh, statusName);
                return resultJson;
            }

            if ("4".equals(status)) {
                // 已审核状态
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程已审核完成，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                log.info("采购申请已审核完成，sqbh：{}，当前用户：{}", sqbh, currentUser);
                return resultJson;
            }

            // 3. 检查工作流审核权限（对于已提交或审核中状态）
            if ("2".equals(status) || "3".equals(status)) {
            // 获取审批流程信息
            BillnoType billType = BillnoType.CGSQ;

                // 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    billType.getCode().toString(), 
                    sqbh
            );

            // 检查当前用户是否有审核权限
                if (nowNodeInfo == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                    resultJson.put("result", "未找到当前审核节点信息，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                    log.warn("未找到审核节点信息，sqbh：{}，当前用户：{}", sqbh, currentUser);
                    return resultJson;
                }
                
                // 检查当前用户是否在审核人列表中
                String currentEmployeeCode = LoginInfo.getCurrEmployeeCode();
                String auditors = nowNodeInfo.getShr1();
                
                if (StringUtils.isBlank(auditors) || !auditors.contains(currentEmployeeCode)) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "此流程当前节点已被审批或当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                    log.info("用户无审核权限，sqbh：{}，当前用户：{}，当前节点审核人：{}", sqbh, currentUser, auditors);
                    return resultJson;
                }
                
                // 检查节点是否已经被审核
                if ("1".equals(nowNodeInfo.getIsaudit())) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "此流程当前节点已被审批，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                    log.info("当前节点已被审核，sqbh：{}，当前用户：{}", sqbh, currentUser);
                    return resultJson;
                }

                // 4. 用户有审核权限
                resultJson.put("isCheck", false);
                resultJson.put("isDelete", false);
                resultJson.put("result", "");
                log.info("用户有审核权限，sqbh：{}，当前用户：{}，节点：{}", sqbh, currentUser, nowNodeInfo.getShrmc());
                return resultJson;
            }

            // 5. 未知状态
            resultJson.put("isCheck", true);
            resultJson.put("isDelete", false);
            resultJson.put("result", "采购申请状态异常（" + statusName + "），当前审核人" + currentUser + "无法操作");
            log.warn("采购申请状态异常，sqbh：{}，状态：{}，当前用户：{}", sqbh, status, currentUser);

            return resultJson;

        } catch (Exception e) {
            log.error("检查采购申请审核权限失败，sqbh: {}", sqbh, e);
            
            JSONObject errorResult = new JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取权限信息：" + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public List<CgsqListVO> getApprovedCgsqList(CgsqPageQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }
            
            // 设置默认查询条件
            String gsdm = queryDTO.getGsdm();
            if (!StringUtils.isNotBlank(gsdm)) {
                gsdm = LoginInfo.getCurrCorpCode();
            }
            
            String kjnd = queryDTO.getKjnd();
            if (!StringUtils.isNotBlank(kjnd)) {
                kjnd = LoginInfo.getCurrAccountantYear();
            }
            
            // 处理模糊查询条件
            String condition = null;
            if (StringUtils.isNotBlank(queryDTO.getCondition())) {
                condition = "%" + queryDTO.getCondition().trim() + "%";
            }
            
            // 计算分页偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();
            
            // 准备其他查询条件 - 固定状态为已审核(4)
            String zt = "4"; // 已审核状态
            String startDate = queryDTO.getStartDate();
            String endDate = queryDTO.getEndDate();
            
            log.info("执行已审核采购申请分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, offset={}, size={}", 
                    gsdm, kjnd, condition, zt, offset, queryDTO.getSize());
            
            // 先查询基础列表（包含总记录数）
            List<CgsqListVO> basicList = cgsqmlMapper.getCgsqApprovedPageList(
                    gsdm, kjnd, condition, zt, startDate, endDate, offset, queryDTO.getSize(), queryDTO.getCgfssfzb()
            );
            
            // 转换为详细响应列表
//            List<CgsqSaveResponseVO> detailList = new ArrayList<>();
//            for (CgsqListVO basicItem : basicList) {
//                try {
//                    String identifier = basicItem.getSqbh();
//                    CgsqSaveResponseVO detail = buildSaveResponseVO(identifier);
//                    detailList.add(detail);
//                } catch (Exception e) {
//                    log.warn("构建采购申请详情失败，sqbh: {}, 错误: {}",
//                            basicItem.getSqbh(), e.getMessage());
//                    // 继续处理其他记录，不中断整个查询
//                }
//            }
            
//            log.info("已审核采购申请分页查询完成，返回{}条详细记录", detailList.size());
            return basicList;
            
        } catch (Exception e) {
            log.error("已审核采购申请分页查询失败，参数：{}，错误：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询已审核采购申请列表失败：" + e.getMessage(), e);
        }
    }
} 