package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 招标申请Service接口
 */
public interface CgzbsqService {

    /**
     * 保存招标申请
     * @param cgzbsqSaveDTO 招标申请保存DTO
     * @return 保存的招标申请详情
     */
    CgzbsqSaveResponseVO saveCgzbsq(CgzbsqSaveDTO cgzbsqSaveDTO);

    /**
     * 更新招标申请（使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @param cgzbsqSaveDTO 招标申请保存DTO
     * @return 更新后的招标申请详情
     */
    CgzbsqSaveResponseVO updateCgzbsqByZbsqbh(String zbsqbh, CgzbsqSaveDTO cgzbsqSaveDTO);

    /**
     * 根据招标申请编号删除招标申请
     * @param zbsqbh 招标申请编号
     * @return 是否删除成功
     */
    boolean deleteByZbsqbh(String zbsqbh);

    /**
     * 根据招标申请编号查询招标申请详情
     * @param zbsqbh 招标申请编号
     * @return 招标申请详情VO
     */
    CgzbsqSaveResponseVO getCgzbsqDetailByZbsqbh(String zbsqbh);

    /**
     * 分页查询招标申请列表
     * @param queryDTO 查询参数
     * @return 招标申请列表
     */
    List<CgzbsqListVO> getCgzbsqPageList(CgzbsqPageQueryDTO queryDTO);

    /**
     * 提交招标申请（使用招标申请编号）
     * @param billnoType 单据类型
     * @param zbsqbh 招标申请编号
     * @param money 金额
     * @param auditor 审核人
     * @return 工作流程节点映射
     */
    Map<Integer, List<PubObjFlowTemp>> commitProxyByZbsqbh(BillnoType billnoType, String zbsqbh, Double money, String auditor);

    /**
     * 查询审核日志（使用招标申请编号）
     * @param billnoType 单据类型
     * @param zbsqbh 招标申请编号
     * @return 审核日志VO
     */
    CgzbsqAuditLogVO findCheckLogByZbsqbh(BillnoType billnoType, String zbsqbh);

    /**
     * 检查招标申请是否有权限审核（基于ZBSQBH）
     * @param zbsqbh 招标申请编号
     * @return 权限检查结果
     */
    Object isCheckedByAuthorityByZbsqbh(String zbsqbh);

    /**
     * 审核招标申请（使用招标申请编号）
     * @param billType 单据类型
     * @param zbsqbh 招标申请编号
     * @param opinion 审核意见
     * @param auditor 审核人
     * @param money 金额
     */
    void checkByZbsqbh(BillnoType billType, String zbsqbh, String opinion, String auditor, BigDecimal money);

    /**
     * 更新招标申请状态（使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @param status 状态
     */
    void updateStatusByZbsqbh(String zbsqbh, Integer status);

    /**
     * 收回招标申请（使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 收回后的招标申请详情
     */
    CgzbsqSaveResponseVO callBackByZbsqbh(String zbsqbh);

    /**
     * 退审招标申请（使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @param opinion 退审意见
     * @param auditor 退审人
     * @return 退审后的招标申请详情
     */
    CgzbsqSaveResponseVO checkCallBackByZbsqbh(String zbsqbh, String opinion, String auditor);

    /**
     * 销审招标申请（使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 销审后的招标申请详情
     */
    CgzbsqSaveResponseVO noAuditByZbsqbh(String zbsqbh);

    /**
     * 分页查询已审核的招标申请
     * @param queryDTO 查询参数
     * @return 已审核的招标申请列表
     */
    PageResult<CgzbsqSaveResponseVO> getApprovedCgzbsqList(CgzbsqPageQueryDTO queryDTO);
} 