package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.ApprovalReminderMapper;
import com.gg.grp.hkycg.mapper.PubObjFlowMapper;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.ApprovalReminder;
import com.gg.grp.hkycg.model.pojo.PubObjFlow;
import com.gg.grp.hkycg.service.ApprovalReminderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;



/**
 * 审批催办Service实现类
 */
@Slf4j
@Service
public class ApprovalReminderServiceImpl implements ApprovalReminderService {

    @Autowired
    private ApprovalReminderMapper approvalReminderMapper;
    
    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Override
    public PageResult<ApprovalReminderVO> queryRemindableApprovals(ApprovalReminderQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 计算偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 查询需要催办的单据
            List<ApprovalReminderVO> approvals = approvalReminderMapper.queryRemindableApprovals(
                    gsdm, kjnd, queryDTO.getBillNo(), queryDTO.getBillType(),
                    queryDTO.getStatus(), queryDTO.getNodeName(), queryDTO.getStartDate(),
                    queryDTO.getEndDate(), queryDTO.getStagnationStartTime(),
                    offset, queryDTO.getSize(), queryDTO.getShowReminded()
            );

            //填充催办次数和最后催办时间
            for (ApprovalReminderVO approval : approvals) {
                // 查询最后一次催办记录
                ApprovalReminder lastReminder = approvalReminderMapper.getLastReminderByBill(
                        gsdm, kjnd, approval.getBillNo(), approval.getBillType(),
                        BillnoType.findByBillType(approval.getBillType()).getModCode(),
                        approval.getNodeName()
                );
                if (lastReminder != null) {
                    approval.setLastReminderTime(lastReminder.getReminderTime());
                    approval.setLastReminderResultZTMC(lastReminder.getReminderResult().equals("0")?"未处理":"已处理");
                    approval.setLastReminderResult(lastReminder.getReminderResult());
                    approval.setCfcs(lastReminder.getCfcs());
                }
            }

            // 获取总记录数
            long totalCount = 0;
            if (!approvals.isEmpty()) {
                totalCount = approvals.get(0).getTotal();
            }

            // 创建分页结果
            return new PageResult<>(approvals, totalCount, queryDTO.getCurrent(), queryDTO.getSize());
        } catch (Exception e) {
            log.error("查询需要催办的单据异常", e);
            throw new GlobalException("查询需要催办的单据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean sendReminder(List<ReminderSendItemVO> reminderSendDTO) {
        try {
            // 参数校验
            if (reminderSendDTO == null || reminderSendDTO.isEmpty()) {
                throw new GlobalException("单据号列表不能为空");
            }
            
            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String employeeCode = LoginInfo.getCurrEmployeeCode();
            String employeeName = LoginInfo.getCurrEmployeeName();

            // 创建催办记录计数
            int successCount = 0;
            
            // 逐个处理每个单据
            for (ReminderSendItemVO bill : reminderSendDTO) {
                try {
                    // 获取模块代码
                    String modCode = BillnoType.findByBillType(bill.getDjlx()).getModCode();
                    
                    // 获取单据当前审核节点
                    PubObjFlow currentNode = pubObjFlowMapper.selectNowNodeByDjh(modCode, String.valueOf(BillnoType.findByBillType(bill.getDjlx()).getCode()), bill.getDjbh());
                    if (currentNode == null) {
                        log.warn("未找到单据当前审核节点，单据号：{}", bill);
                        continue; // 跳过此单据，继续处理下一个
                    }
                    
                    // 查询是否已有催办记录
                    ApprovalReminder lastReminder = approvalReminderMapper.getLastReminderByBill(
                            gsdm, kjnd, bill.getDjbh(), bill.getDjlx(), modCode, currentNode.getNodeName()
                    );
                    
                    int result = 0;
                    
                    if (lastReminder != null) {
                        // 已有催办记录，更新催办次数
                        int cfcs = lastReminder.getCfcs() != null ? lastReminder.getCfcs() : 0;

                        // 更新催办次数
                        LambdaUpdateWrapper<ApprovalReminder> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(ApprovalReminder::getGrid, lastReminder.getGrid())
                                .set(ApprovalReminder::getCfcs, String.valueOf(cfcs + 1))
                                .set(ApprovalReminder::getUpdateTime, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                                .set(ApprovalReminder::getReminderTime, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                                .set(ApprovalReminder::getReminderResult, "0"); // 重置为未处理状态
                                
                        result = approvalReminderMapper.update(null, updateWrapper);
                        log.info("更新催办记录，单据号：{}，类型：{}，催办次数：{}", bill.getDjbh(), bill.getDjlx(), cfcs + 1);
                    } else {
                        // 无催办记录，创建新记录
                        ApprovalReminder reminder = new ApprovalReminder();
                        reminder.setGrid(UUID.randomUUID().toString().replace("-", ""));
                        reminder.setGsdm(gsdm);
                        reminder.setKjnd(kjnd);
                        reminder.setDjbh(bill.getDjbh());
                        reminder.setDjlx(bill.getDjlx());
                        reminder.setReminderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                        reminder.setReminderUserDm(employeeCode);
                        reminder.setReminderUser(employeeName);
                        
                        // 从审核人列表中提取审核人信息
                        String[] auditors = currentNode.getShr1().split(",");
                        String receiver = "";
                        String receiverDm = "";
                        if (auditors.length > 1) {
                            receiverDm = auditors[1]; // 去掉第一个空元素
                            receiver = currentNode.getShrmc();
                        }
                        reminder.setReceiverDm(receiverDm);
                        reminder.setReceiver(receiver);
                        reminder.setReminderContent("催办");
                        reminder.setReminderResult("0"); // 默认未处理
                        reminder.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                        reminder.setNodeName(currentNode.getNodeName());
                        reminder.setCfcs(1); // 初始催办次数为1
                        
                        // 保存催办记录
                        result = approvalReminderMapper.insert(reminder);
                        log.info("创建催办记录，单据号：{}，类型：{}", bill.getDjbh(), bill.getDjlx());
                    }
                    
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理单据催办失败，单据号：{}", bill, e);
                    // 单个单据处理失败不影响其他单据，继续处理
                }
            }
            
            // 只要有一个单据处理成功就返回true
            return successCount > 0;
        } catch (Exception e) {
            log.error("发送催办通知异常", e);
            throw new GlobalException("发送催办通知失败：" + e.getMessage(), e);
        }
    }

    @Override
    public PageResult<ApprovalReminder> getReminderHistory(String billNo, String billType, Integer current, Integer size) {
        try {
            // 参数校验和默认值设置
            if (current == null || current < 1) {
                current = 1;
            }
            if (size == null || size < 1) {
                size = 10;
            }

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 构建查询条件
            LambdaQueryWrapper<ApprovalReminder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ApprovalReminder::getGsdm, gsdm)
                    .eq(ApprovalReminder::getKjnd, kjnd)
                    .eq(ApprovalReminder::getDjbh, billNo)
                    .eq(ApprovalReminder::getDjlx, billType)
                    .orderByDesc(ApprovalReminder::getReminderTime);

            // 执行分页查询
            Page<ApprovalReminder> page = new Page<>(current, size);
            Page<ApprovalReminder> resultPage = approvalReminderMapper.selectPage(page, queryWrapper);

            // 创建分页结果
            return new PageResult<>(resultPage.getRecords(), resultPage.getTotal(), current, size);
        } catch (Exception e) {
            log.error("查询催办记录异常", e);
            throw new GlobalException("查询催办记录失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsProcessed(Long grid) {
        try {
            // 参数校验
            if (grid == null) {
                throw new GlobalException("催办记录GRID不能为空");
            }

            // 更新催办记录状态
            LambdaUpdateWrapper<ApprovalReminder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ApprovalReminder::getGrid, grid)
                    .set(ApprovalReminder::getReminderResult, "1");

            int result = approvalReminderMapper.update(null, updateWrapper);
            return result > 0;
        } catch (Exception e) {
            log.error("标记催办为已处理异常", e);
            throw new GlobalException("标记催办为已处理失败：" + e.getMessage(), e);
        }
    }
    
    @Override
    public PageResult<MyReminderVO> queryMyReminders(MyReminderQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 计算偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String employeeCode = LoginInfo.getCurrEmployeeCode();

            // 查询当前登录人被催办的单据
            List<MyReminderVO> myReminders = approvalReminderMapper.queryMyReminders(
                    gsdm, kjnd, employeeCode,
                    queryDTO.getBillNo(), queryDTO.getBillType(),
                    queryDTO.getReminderResult(), queryDTO.getStartDate(),
                    queryDTO.getEndDate(), offset, queryDTO.getSize()
            );

            // 获取总记录数
            long totalCount = 0;
            if (!myReminders.isEmpty()) {
                totalCount = myReminders.get(0).getTotalCount();
            }

            // 创建分页结果
            return new PageResult<>(myReminders, totalCount, queryDTO.getCurrent(), queryDTO.getSize());
        } catch (Exception e) {
            log.error("查询被催办单据异常", e);
            throw new GlobalException("查询被催办单据失败：" + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean updateReminderStatusAfterAudit(String billNo, String billType, String nodeName) {
        try {
            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            // 查询该单据是否有催办记录
            ApprovalReminder lastReminder = approvalReminderMapper.getLastReminderByBill(
                    gsdm, kjnd, billNo, billType, 
                    BillnoType.findByBillType(billType).getModCode(), 
                    nodeName
            );
            
            if (lastReminder != null && "0".equals(lastReminder.getReminderResult())) {
                // 如果存在未处理的催办记录，则更新为已处理(3)
                LambdaUpdateWrapper<ApprovalReminder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ApprovalReminder::getGrid, lastReminder.getGrid())
                        .set(ApprovalReminder::getReminderResult, "3")
                        .set(ApprovalReminder::getUpdateTime, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                
                int result = approvalReminderMapper.update(null, updateWrapper);
                
                if (result > 0) {
                    log.info("单据审核后自动更新催办状态成功，单据号：{}，类型：{}，节点：{}", billNo, billType, nodeName);
                    return true;
                } else {
                    log.warn("单据审核后更新催办状态失败，单据号：{}，类型：{}，节点：{}", billNo, billType, nodeName);
                    return false;
                }
            } else {
                log.debug("单据无催办记录或已处理，无需更新，单据号：{}，类型：{}，节点：{}", billNo, billType, nodeName);
                return true;
            }
        } catch (Exception e) {
            log.error("单据审核后更新催办状态异常，单据号：{}，类型：{}，节点：{}", billNo, billType, nodeName, e);
            // 不抛出异常，避免影响主业务流程
            return false;
        }
    }

    @Override
    public List<BusinessReminderVO> queryBillsNearDeadline(Integer days) {
        try {
            // 参数校验
            if (days==null || days <= 0) {
                throw new GlobalException("天数参数不能为空且必须大于0");
            }

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String employeeCode = LoginInfo.getCurrEmployeeCode();

            if (employeeCode == null || employeeCode.isEmpty()) {
                throw new GlobalException("当前用户未登录或登录信息无效");
            }

            // 执行查询
            List<BusinessReminderVO> result = approvalReminderMapper.queryBillsNearDeadline(
                    gsdm, kjnd, employeeCode, days
            );

            log.info("查询当前登录人离截止日期{}天内的单据成功，共{}条记录", days, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询离截止日期内的单据异常，天数：{}，异常信息：{}",
                    days, e.getMessage());
            throw new GlobalException("查询离截止日期内的单据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public PageResult<Object> queryApprovedBills(ApprovedBillsQueryDTO queryDTO) {
        try {
            // 参数校验
            if (queryDTO == null) {
                throw new GlobalException("查询条件不能为空");
            }
            if (queryDTO.getBillType() == null || queryDTO.getBillType().trim().isEmpty()) {
                throw new GlobalException("单据类型不能为空");
            }

            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 计算偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 根据单据类型查询不同的数据
            switch (queryDTO.getBillType().toUpperCase()) {
                case "CGJH":
                    // 查询已审核的采购计划
                    List<ApprovedPlanVO> plans = approvalReminderMapper.queryApprovedPlans(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate(), offset, queryDTO.getSize());
                    long planTotal = approvalReminderMapper.countApprovedPlans(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate());
                    log.info("查询已审核采购计划成功，共{}条记录", planTotal);
                    @SuppressWarnings("unchecked")
                    PageResult<Object> planResult = new PageResult<>((List<Object>)(List<?>)plans, planTotal, queryDTO.getCurrent(), queryDTO.getSize());
                    return planResult;

                case "CGSQ":
                    // 查询已审核的采购申请
                    List<ApprovedApplicationVO> applications = approvalReminderMapper.queryApprovedApplications(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate(), offset, queryDTO.getSize());
                    long applicationTotal = approvalReminderMapper.countApprovedApplications(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate());
                    log.info("查询已审核采购申请成功，共{}条记录", applicationTotal);
                    @SuppressWarnings("unchecked")
                    PageResult<Object> applicationResult = new PageResult<>((List<Object>)(List<?>)applications, applicationTotal, queryDTO.getCurrent(), queryDTO.getSize());
                    return applicationResult;

                case "CGZB":
                    // 查询已审核的采购招标
                    List<ApprovedBiddingVO> biddings = approvalReminderMapper.queryApprovedBiddings(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate(), offset, queryDTO.getSize());
                    long biddingTotal = approvalReminderMapper.countApprovedBiddings(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate());
                    log.info("查询已审核采购招标成功，共{}条记录", biddingTotal);
                    @SuppressWarnings("unchecked")
                    PageResult<Object> biddingResult = new PageResult<>((List<Object>)(List<?>)biddings, biddingTotal, queryDTO.getCurrent(), queryDTO.getSize());
                    return biddingResult;

                case "CGDJ":
                    // 查询已审核的采购结果
                    List<ApprovedResultVO> results = approvalReminderMapper.queryApprovedResults(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate(), offset, queryDTO.getSize());
                    long resultTotal = approvalReminderMapper.countApprovedResults(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate());
                    log.info("查询已审核采购结果成功，共{}条记录", resultTotal);
                    @SuppressWarnings("unchecked")
                    PageResult<Object> resultResult = new PageResult<>((List<Object>)(List<?>)results, resultTotal, queryDTO.getCurrent(), queryDTO.getSize());
                    return resultResult;

                case "CGYS":
                    // 查询已审核的采购验收
                    List<ApprovedAcceptanceVO> acceptances = approvalReminderMapper.queryApprovedAcceptances(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate(), offset, queryDTO.getSize());
                    long acceptanceTotal = approvalReminderMapper.countApprovedAcceptances(
                            gsdm, kjnd, queryDTO.getStartDate(), queryDTO.getEndDate());
                    log.info("查询已审核采购验收成功，共{}条记录", acceptanceTotal);
                    @SuppressWarnings("unchecked")
                    PageResult<Object> acceptanceResult = new PageResult<>((List<Object>)(List<?>)acceptances, acceptanceTotal, queryDTO.getCurrent(), queryDTO.getSize());
                    return acceptanceResult;

                default:
                    throw new GlobalException("不支持的单据类型：" + queryDTO.getBillType());
            }

        } catch (Exception e) {
            log.error("查询已审核状态单据异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询已审核状态单据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public PageResult<AllApprovedBillVO> queryAllApprovedBills(AllApprovedBillsQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO == null) {
                queryDTO = new AllApprovedBillsQueryDTO();
            }
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 计算偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 查询所有已审核的单据
            List<AllApprovedBillVO> bills = approvalReminderMapper.queryAllApprovedBills(
                    gsdm, kjnd, offset, queryDTO.getSize());

            // 查询总数
            long total = approvalReminderMapper.countAllApprovedBills(gsdm, kjnd);

            log.info("查询所有已审核单据成功，共{}条记录", total);
            return new PageResult<>(bills, total, queryDTO.getCurrent(), queryDTO.getSize());

        } catch (Exception e) {
            log.error("查询所有已审核状态单据异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询所有已审核状态单据失败：" + e.getMessage(), e);
        }
    }

}