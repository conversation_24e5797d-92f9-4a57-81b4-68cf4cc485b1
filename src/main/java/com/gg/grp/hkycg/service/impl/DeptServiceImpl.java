package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.model.dto.DeptPageQueryDTO;
import com.gg.grp.hkycg.model.vo.DeptListVO;
import com.gg.grp.hkycg.model.pojo.Dept;
import com.gg.grp.hkycg.mapper.DeptMapper;
import com.gg.grp.hkycg.service.DeptService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门服务实现类
 */
@Service
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {

    @Override
    public Dept getByDeptCode(String deptCode) {
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dept::getGsdm, LoginInfo.getCurrCorpCode());
        queryWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear());
        queryWrapper.eq(Dept::getBmdm, deptCode);
        return this.getOne(queryWrapper);
    }

    @Override
    public String getDeptNameByCode(String deptCode) {
        Dept dept = getByDeptCode(deptCode);
        return dept != null ? dept.getBmmc() : null;
    }

    @Override
    public Dept qryDeptByCode(String deptCode) {
        Dept dept = getByDeptCode(deptCode);
        if (dept == null) {
            return null;
        }

        // 转换为DeptModel
        Dept deptModel = new Dept();
        deptModel.setGsdm(dept.getGsdm());
        deptModel.setKjnd(dept.getKjnd());
        deptModel.setBmdm(dept.getBmdm());
        deptModel.setBmmc(dept.getBmmc());
        deptModel.setFzrdm(dept.getFzrdm());

        return deptModel;
    }

    @Override
    public IPage<DeptListVO> queryAllDept(DeptPageQueryDTO queryDTO) {
        // 创建分页对象
        Page<Dept> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

        // 构建查询条件
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear());
        queryWrapper.eq(Dept::getGsdm, LoginInfo.getCurrCorpCode());
        queryWrapper.ne(Dept::getBmmc, "停用");

        // 部门代码模糊查询
        if (StringUtils.hasText(queryDTO.getDeptCode())) {
            queryWrapper.like(Dept::getBmdm, queryDTO.getDeptCode());
        }

        // 部门名称模糊查询
        if (StringUtils.hasText(queryDTO.getDeptName())) {
            queryWrapper.like(Dept::getBmmc, queryDTO.getDeptName());
        }

        if (StringUtils.hasText(queryDTO.getCondition())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(Dept::getBmdm, queryDTO.getCondition())
                            .or()
                            .like(Dept::getBmmc, queryDTO.getCondition())
            );
        }

        // 按部门代码排序
        queryWrapper.orderByAsc(Dept::getBmdm);

        // 执行分页查询
        IPage<Dept> deptPage = page(page, queryWrapper);

        // 转换为VO对象
        Page<DeptListVO> resultPage = new Page<>(deptPage.getCurrent(), deptPage.getSize(), deptPage.getTotal());
        List<DeptListVO> voList = new ArrayList<>();
        deptPage.getRecords().forEach(dept -> {
            DeptListVO vo = new DeptListVO();
            BeanUtils.copyProperties(dept, vo);
            vo.setDeptName(dept.getBmmc());
            vo.setDeptCode(dept.getBmdm());
            vo.setFzrdm(dept.getFzrdm());
            voList.add(vo);
        });
        resultPage.setRecords(voList);
        return resultPage;
    }

    @Override
    public IPage<DeptListVO> queryAllDeptWithResponsiblePerson(DeptPageQueryDTO queryDTO) {
        // 创建分页对象
        Page<DeptListVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        
        // 调用Mapper方法进行联表查询
        return this.baseMapper.selectDeptWithResponsiblePerson(page, 
                LoginInfo.getCurrCorpCode(), 
                LoginInfo.getCurrAccountantYear(), 
                queryDTO.getCondition());
    }

}