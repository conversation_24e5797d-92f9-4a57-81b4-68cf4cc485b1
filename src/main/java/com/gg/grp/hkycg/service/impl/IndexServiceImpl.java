package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.IndexMapper;
import com.gg.grp.hkycg.model.dto.IndexParams;
import com.gg.grp.hkycg.model.vo.IndexVO;
import com.gg.grp.hkycg.model.pojo.Dept;
import com.gg.grp.hkycg.model.pojo.OerYszb;
import com.gg.grp.hkycg.service.DeptService;
import com.gg.grp.hkycg.service.IndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 预算指标服务实现类
 */
@Slf4j
@Service
public class IndexServiceImpl extends ServiceImpl<IndexMapper, OerYszb> implements IndexService {

    @Autowired
    private DeptService deptService;
    @Autowired
    private IndexMapper indexMapper;

    @Override
    public List<IndexVO> qryIndex(IndexParams indexParams) {
        Map<String, Object> params = new HashMap<>();
        
        // 基础参数 - 确保不为null
        String gsdm = LoginInfo.getCurrCorpCode();
        String kjnd = LoginInfo.getCurrAccountantYear();
        String endDate = LoginInfo.getCurrBusiDate().replace("-", "");
        String employeeCode = LoginInfo.getCurrEmployeeCode();
        
        if (gsdm == null || kjnd == null || endDate == null || employeeCode == null) {
            throw new RuntimeException("登录信息不完整，无法执行查询");
        }
        
        params.put("gsdm", gsdm);
        params.put("kjnd", kjnd);
        params.put("endDate", endDate);
        params.put("employeeCode", employeeCode);
        
        // 分页参数 - 确保不为null且为正数
        Integer pageSize = indexParams.getPageNum() != null && indexParams.getPageNum() > 0 ? indexParams.getPageNum() : 10;
        Integer pageNo = indexParams.getPages() != null && indexParams.getPages() > 0 ? indexParams.getPages() : 1;
        params.put("pageSize", pageSize);
        params.put("pageNo", pageNo);
        
        // 查询条件 - 只有非空且非空白时才添加
        if (StringUtils.hasText(indexParams.getCondition())) {
            params.put("condition", "%" + indexParams.getCondition().trim() + "%");
        }
        
        // 部门负责人权限检查
        try {
            Dept dept = deptService.qryDeptByCode(LoginInfo.getCurrDeptCode());
            if (dept != null && dept.getFzrdm() != null && dept.getFzrdm().equals(employeeCode)) {
                // 如果是负责人，需要查出公用账号下的授权指标
                params.put("deptfzrdm", LoginInfo.getCurrDeptCode() + "001");
            }
        } catch (Exception e) {
            log.warn("获取部门信息失败，跳过部门负责人权限检查：{}", e.getMessage());
        }
        
        log.info("执行指标查询，参数：{}", params);

        try {
            List<JSONObject> jsonList = baseMapper.qryIndex(params);

            // 转换JSONObject为IndexVO
            List<IndexVO> indexVOList = new ArrayList<>();
            for (JSONObject jsonObject : jsonList) {
                IndexVO indexVO = new IndexVO();

                // 设置基本字段
                indexVO.setZbid(getIntegerFromJson(jsonObject, "indexID"));
                indexVO.setGsdm(getStringFromJson(jsonObject, "corpCode"));
                indexVO.setKjnd(getStringFromJson(jsonObject, "accountantYear"));
                indexVO.setZbdm(getStringFromJson(jsonObject, "indexCode"));
                indexVO.setZblx(getStringFromJson(jsonObject, "indexType"));
                indexVO.setRealIndexCode(getStringFromJson(jsonObject, "realIndexCode"));
                indexVO.setNote(getStringFromJson(jsonObject, "note"));

                // 设置部门相关字段
                indexVO.setBmdm(getStringFromJson(jsonObject, "deptCode"));
                indexVO.setBmmc(getStringFromJson(jsonObject, "deptName"));
                indexVO.setZydm(getStringFromJson(jsonObject, "employeeCode"));
                indexVO.setZymc(getStringFromJson(jsonObject, "employeeName"));

                // 设置项目相关字段
                indexVO.setXmdm(getStringFromJson(jsonObject, "projectCode"));
                indexVO.setXmmc(getStringFromJson(jsonObject, "projectName"));
                indexVO.setProjectClassifyCode(getStringFromJson(jsonObject, "projectClassifyCode"));
                indexVO.setProjectClassifyName(getStringFromJson(jsonObject, "projectClassifyName"));

                // 设置科目相关字段
                indexVO.setFuncSubjectCode(getStringFromJson(jsonObject, "funcSubjectCode"));
                indexVO.setFuncSubjectName(getStringFromJson(jsonObject, "funcSubjectName"));
                indexVO.setEconomicSubjectCode(getStringFromJson(jsonObject, "economicSubjectCode"));
                indexVO.setEconomicSubjectName(getStringFromJson(jsonObject, "economicSubjectName"));

                // 设置指标来源字段
                indexVO.setIndexSourceCode(getStringFromJson(jsonObject, "indexSourceCode"));
                indexVO.setIndexSourceName(getStringFromJson(jsonObject, "indexSourceName"));

                // 设置金额字段
                indexVO.setResidual(getBigDecimalFromJson(jsonObject, "residual"));
                indexVO.setAmt(getBigDecimalFromJson(jsonObject, "amt"));
                indexVO.setYSJE(getBigDecimalFromJson(jsonObject, "YSJE"));
                indexVO.setNCYSY(getBigDecimalFromJson(jsonObject, "NCYSY"));
                indexVO.setDJJE(getBigDecimalFromJson(jsonObject, "DJJE"));

                // 设置状态字段
                indexVO.setState(getStringFromJson(jsonObject, "state"));
                indexVO.setModule(getStringFromJson(jsonObject, "module"));

                // 设置录入相关字段
                indexVO.setInputCode(getIntegerFromJson(jsonObject, "inputCode"));
                indexVO.setInputName(getStringFromJson(jsonObject, "inputName"));
                indexVO.setInputDate(getStringFromJson(jsonObject, "inputDate"));
                indexVO.setInputTime(getStringFromJson(jsonObject, "inputTime"));

                // 设置审核相关字段
                indexVO.setApproverCode(getIntegerFromJson(jsonObject, "approverCode"));
                indexVO.setApproverName(getStringFromJson(jsonObject, "approverName"));
                indexVO.setApproverDate(getStringFromJson(jsonObject, "approverDate"));
                indexVO.setApproverTime(getStringFromJson(jsonObject, "approverTime"));

                // 设置批复相关字段
                indexVO.setPFRID(getIntegerFromJson(jsonObject, "PFRID"));
                indexVO.setPFR(getStringFromJson(jsonObject, "PFR"));
                indexVO.setPFRQ(getStringFromJson(jsonObject, "PFRQ"));
                indexVO.setPFSJ(getStringFromJson(jsonObject, "PFSJ"));

                // 设置预算方案字段
                indexVO.setYSFADM(getStringFromJson(jsonObject, "YSFADM"));
                indexVO.setYSFAMC(getStringFromJson(jsonObject, "YSFAMC"));

                // 设置支出类型字段
                indexVO.setExpenditureTypeCode(getStringFromJson(jsonObject, "expenditureTypeCode"));
                indexVO.setExpenditureTypeName(getStringFromJson(jsonObject, "expenditureTypeName"));

                // 设置文号字段
                indexVO.setWHDM(getStringFromJson(jsonObject, "WHDM"));
                indexVO.setWHMC(getStringFromJson(jsonObject, "WHMC"));

                // 设置资金来源字段
                indexVO.setZJLYDM(getStringFromJson(jsonObject, "ZJLYDM"));
                indexVO.setZJLYMC(getStringFromJson(jsonObject, "ZJLYMC"));

                // 设置资金性质字段
                indexVO.setZJXZDM(getStringFromJson(jsonObject, "ZJXZDM"));
                indexVO.setZJXZMC(getStringFromJson(jsonObject, "ZJXZMC"));

                // 设置结算方式字段
                indexVO.setJSFSDM(getStringFromJson(jsonObject, "JSFSDM"));
                indexVO.setJSFSMC(getStringFromJson(jsonObject, "JSFSMC"));

                // 设置支付方式字段
                indexVO.setZFFSDM(getStringFromJson(jsonObject, "ZFFSDM"));
                indexVO.setZFFSMC(getStringFromJson(jsonObject, "ZFFSMC"));

                // 设置预算单位字段
                indexVO.setYSDWDM(getStringFromJson(jsonObject, "YSDWDM"));
                indexVO.setYSDWMC(getStringFromJson(jsonObject, "YSDWMC"));

                // 设置扩展字段
                indexVO.setFZ6DM(getStringFromJson(jsonObject, "FZ6DM"));
                indexVO.setFZ6MC(getStringFromJson(jsonObject, "FZ6MC"));
                indexVO.setFZ7DM(getStringFromJson(jsonObject, "FZ7DM"));
                indexVO.setFZ7MC(getStringFromJson(jsonObject, "FZ7MC"));
                indexVO.setFZ8DM(getStringFromJson(jsonObject, "FZ8DM"));
                indexVO.setFZ8MC(getStringFromJson(jsonObject, "FZ8MC"));
                indexVO.setFZ9DM(getStringFromJson(jsonObject, "FZ9DM"));
                indexVO.setFZ9MC(getStringFromJson(jsonObject, "FZ9MC"));
                indexVO.setFZADM(getStringFromJson(jsonObject, "FZADM"));
                indexVO.setFZAMC(getStringFromJson(jsonObject, "FZAMC"));

                // 设置其他字段
                indexVO.setYJBFB(getBigDecimalFromJson(jsonObject, "YJBFB"));
                indexVO.setCYSKZFS(getStringFromJson(jsonObject, "CYSKZFS"));
                indexVO.setSFJZ(getStringFromJson(jsonObject, "SFJZ"));
                indexVO.setIDZBBH(getStringFromJson(jsonObject, "IDZBBH"));
                indexVO.setXFZT(getStringFromJson(jsonObject, "XFZT"));
                indexVO.setRealSource(getIntegerFromJson(jsonObject, "realSource"));
                indexVO.setBYGKZ(getStringFromJson(jsonObject, "BYGKZ"));
                indexVO.setRemarks(getStringFromJson(jsonObject, "remarks"));

                // 设置分页字段
                indexVO.setRownumber(getIntegerFromJson(jsonObject, "rownumber"));
                indexVO.setTotalCount(getLongFromJson(jsonObject, "totalCount"));

                indexVOList.add(indexVO);
            }

            return indexVOList;
        } catch (Exception e) {
            log.error("执行指标查询失败，参数：{}，错误：{}", params, e.getMessage());
            throw new RuntimeException("查询指标数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从JSONObject中获取String值
     */
    private String getStringFromJson(JSONObject jsonObject, String key) {
        Object value = jsonObject.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 从JSONObject中获取Integer值
     */
    private Integer getIntegerFromJson(JSONObject jsonObject, String key) {
        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从JSONObject中获取BigDecimal值
     */
    private BigDecimal getBigDecimalFromJson(JSONObject jsonObject, String key) {
        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从JSONObject中获取Long值
     */
    private Long getLongFromJson(JSONObject jsonObject, String key) {
        Object value = jsonObject.get(key);
        if (value == null) {
            return null;
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}