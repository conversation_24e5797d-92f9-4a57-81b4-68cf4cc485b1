package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.CgmlPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgmlVO;
import com.gg.grp.hkycg.model.pojo.HbgZfcgml;

/**
 * 采购品目服务接口
 */
public interface CgmlService extends IService<HbgZfcgml> {
    
    /**
     * 分页查询采购品目
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<CgmlVO> pageQuery(CgmlPageQueryDTO queryDTO);
}
