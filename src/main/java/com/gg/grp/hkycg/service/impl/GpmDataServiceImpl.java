package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.mapper.GpmDataMapper;
import com.gg.grp.hkycg.model.pojo.GpmData;
import com.gg.grp.hkycg.service.GpmDataService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * GPM_DATA表服务实现类
 */
@Service
public class GpmDataServiceImpl extends ServiceImpl<GpmDataMapper, GpmData> implements GpmDataService {
    // 可以在这里实现自定义的服务方法
    
    /**
     * 查询所有数据权限代码和名称
     * @return 数据权限列表
     */
    @Override
    public List<GpmData> listAllDataPermission() {
        LambdaQueryWrapper<GpmData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(GpmData::getDatacode, GpmData::getDataname);
        return this.list(queryWrapper);
    }
} 