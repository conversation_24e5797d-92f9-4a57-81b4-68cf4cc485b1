package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.consts.StaticValue;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.StatusName;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.PubAuditLogMapper;
import com.gg.grp.hkycg.mapper.PubObjFlowMapper;
import com.gg.grp.hkycg.mapper.PubWorkflowMapper;
import com.gg.grp.hkycg.mapper.IndexMapper;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.service.CgjhService;
import com.gg.grp.hkycg.service.PubObjFlowService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import com.gg.grp.hkycg.utils.DataPermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.util.stream.Collectors;

/**
 * 采购计划Service实现类
 */
@Slf4j
@Service
public class CgjhServiceImpl extends ServiceImpl<GpmCgjhmlMapper, GpmCgjhml> implements CgjhService {

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;

    @Autowired
    private GpmCgjhnrMapper cgjhnrMapper;

    @Autowired
    private GpmCgjhYszbMapper cgjhYszbMapper;

    @Autowired
    private GbiZbsyrecMapper gbiZbsyrecMapper;

    @Autowired
    private GbiZbxmbMapper zbxmbMapper;

    @Autowired
    private PubWorkflowMapper pubWorkflowMapper;

    @Autowired
    private PubAuditLogMapper pubAuditLogMapper;

    @Autowired
    private PubObjFlowService pubObjFlowService;

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    @Autowired
    private IndexMapper indexMapper;

    @Autowired
    private DataPermissionUtils dataPermissionUtils;

    @Override
    public CgjhSaveResponseVO saveCgjh(CgjhSaveDTO cgjhSaveDTO) {
        try {
            // 获取基础信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 数据校验
            checkBeforeSave(cgjhSaveDTO);

            // 预算指标存在性校验
            checkBudgetIndexExists(cgjhSaveDTO, gsdm, kjnd);

            // 1. 生成计划编号和单据编号
            String jhbh = generateJhbh();

            // 2. 保存采购计划主表
            GpmCgjhml cgjhml = new GpmCgjhml();
            BeanUtils.copyProperties(cgjhSaveDTO.getBaseInfo(), cgjhml);

            // 设置系统字段
            cgjhml.setGsdm(gsdm);
            cgjhml.setKjnd(kjnd);
            cgjhml.setJhbh(jhbh);
            cgjhml.setZt(BigDecimal.ONE); // 状态：1-已保存
            cgjhml.setStamp(BigDecimal.valueOf(Instant.now().getEpochSecond()));

            // 设置录入信息
            cgjhml.setLrrId(new BigDecimal(LoginInfo.getCurrEmployeeCode() != null ? LoginInfo.getCurrEmployeeCode() : "0"));
            cgjhml.setLrr(LoginInfo.getCurrEmployeeName());
            cgjhml.setLrRq(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            log.info("准备保存主表数据：{}", cgjhml);

            // 保存主表
            int result = cgjhmlMapper.insert(cgjhml);
            log.info("主表保存结果：{}", result);

            saveCgjhDetails(jhbh, cgjhSaveDTO);

            // 3. 构建并返回响应数据
            CgjhSaveResponseVO responseVO = buildSaveResponseVO(jhbh);
            log.info("返回保存的采购计划详情，计划名称：{}", responseVO.getBaseInfo().getJhmc());

            return responseVO;

        } catch (Exception e) {
            log.error("保存采购计划失败", e);
            throw new GlobalException("保存采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjhSaveResponseVO updateCgjh(String jhbh, CgjhSaveDTO cgjhSaveDTO) {
        try {
            // 检查采购计划是否存在
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml existingCgjhml = cgjhmlMapper.selectOne(queryWrapper);
            if (existingCgjhml == null) {
                throw new GlobalException("采购计划不存在!");
            }
            if (existingCgjhml.getZt().compareTo(new BigDecimal(1)) != 0 && existingCgjhml.getZt().compareTo(new BigDecimal(5)) != 0) {
                throw new GlobalException("采购计划状态不是保存或退回，不能更新");
            }

            // 权限校验：只有录入人才能更新
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalInputUser = existingCgjhml.getLrr();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 更新操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, jhbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能修改此采购计划");
            }
            log.info("权限校验通过 - 更新操作，当前用户：{}，jhbh：{}", currentUser, jhbh);

            // 获取基础信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 数据校验
            checkBeforeSave(cgjhSaveDTO);

            // 预算指标存在性校验
            checkBudgetIndexExists(cgjhSaveDTO, gsdm, kjnd);

            // 1. 更新采购计划主表
            GpmCgjhml cgjhml = new GpmCgjhml();
            BeanUtils.copyProperties(cgjhSaveDTO.getBaseInfo(), cgjhml);

            log.info("准备更新主表数据，计划名称：{}", cgjhml.getJhmc());

            // 更新主表
            LambdaUpdateWrapper<GpmCgjhml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            int result = cgjhmlMapper.update(cgjhml, updateWrapper);
            log.info("主表更新结果：{}", result);

            // 2. 删除原有的明细和预算指标记录
            log.info("开始删除原有明细和预算指标，jhbh：{}", jhbh);

            // 删除明细记录
            LambdaUpdateWrapper<GpmCgjhnr> deleteDetailWrapper = new LambdaUpdateWrapper<>();
            deleteDetailWrapper.eq(GpmCgjhnr::getJhbh, jhbh);
            int deletedDetails = cgjhnrMapper.delete(deleteDetailWrapper);
            log.info("删除明细记录{}条", deletedDetails);

            // 删除预算指标记录（OER_YSZB表）
            LambdaUpdateWrapper<OerYszb> deleteOerYszbWrapper = new LambdaUpdateWrapper<>();
            deleteOerYszbWrapper.eq(OerYszb::getDjbh, jhbh);
            int deletedOerYszb = indexMapper.delete(deleteOerYszbWrapper);
            log.info("删除OER_YSZB预算指标记录{}条", deletedOerYszb);

            // 删除指标使用记录
            LambdaUpdateWrapper<GbiZbsyrec> deleteZbsyrecWrapper = new LambdaUpdateWrapper<>();
            deleteZbsyrecWrapper.eq(GbiZbsyrec::getDjid, jhbh)
                    .eq(GbiZbsyrec::getModule, BillnoType.CGJH.getModCode())
                    .eq(GbiZbsyrec::getDjlx, BillnoType.CGJH.getCode());
            int deletedZbsyrec = gbiZbsyrecMapper.delete(deleteZbsyrecWrapper);
            log.info("删除GBI_ZBSYREC指标使用记录{}条", deletedZbsyrec);

            // 3. 重新保存新的明细和预算指标（按照保存模式的逻辑）
            // 获取最大序号用于生成必填字段
            Integer maxMlid = getMaxMlidFromOerYszb(gsdm, kjnd);
            Integer maxBnxid = getMaxBnxidFromOerYszb(gsdm, kjnd);
            Integer maxDataId = getMaxDataIdFromOerYszb(gsdm, kjnd);

            // 处理null值
            if (maxMlid == null) maxMlid = 0;
            if (maxBnxid == null) maxBnxid = 0;
            if (maxDataId == null) maxDataId = 0;

            int budgetIndexCounter = 0; // 预算指标计数器
            
            // 生成明细编号的日期前缀
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String dateStr = sdf.format(new Date());
            String mxPrefix = "CGJHMX" + dateStr;
            
            // 查询当前前缀下的最大序号
            Integer maxSequence = cgjhnrMapper.selectMaxSequenceByPrefix(mxPrefix);
            int sequenceStart = (maxSequence != null) ? maxSequence + 1 : 1;

            for (int i = 0; i < cgjhSaveDTO.getDetailsWithBudget().size(); i++) {
                CgjhDetailWithBudgetDTO detailWithBudget = cgjhSaveDTO.getDetailsWithBudget().get(i);

                // 生成明细编号，格式：CGJHMX年月日001, CGJHMX年月日002, ...
                String jhmxxh = mxPrefix + String.format("%03d", sequenceStart + i);

                // 保存明细记录
                GpmCgjhnr cgjhnr = new GpmCgjhnr();
                BeanUtils.copyProperties(detailWithBudget.getDetail(), cgjhnr);

                cgjhnr.setJhbh(jhbh);
                cgjhnr.setJhmxxh(jhmxxh);
                cgjhnr.setGsdm(gsdm);
                cgjhnr.setKjnd(kjnd);
                cgjhnr.setCjsj(existingCgjhml.getLrRq());
                cgjhnr.setGxsj(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

                cgjhnrMapper.insert(cgjhnr);
                log.info("保存明细成功，jhbh：{}，jhmxxh：{}", jhbh, jhmxxh);

                // 保存该明细对应的预算指标
                CgjhBudgetIndexDTO budgetIndex = detailWithBudget.getBudgetIndex();
                if (budgetIndex != null) {
                    budgetIndexCounter++;

                    // 设置明细序号
                    budgetIndex.setMxxh(jhmxxh);

                    // 保存预算指标到OER_YSZB表
                    OerYszb oerYszb = new OerYszb();

                    // 设置必填字段
                    oerYszb.setMlid(maxMlid + budgetIndexCounter);
                    oerYszb.setBnxid(maxBnxid + budgetIndexCounter);
                    oerYszb.setDataId(maxDataId + budgetIndexCounter);
                    oerYszb.setXh(budgetIndex.getIndex() != null ? budgetIndex.getIndex() : budgetIndexCounter);

                    // 设置基本字段
                    oerYszb.setDjbh(jhbh);
                    oerYszb.setMxxh(jhmxxh); // 设置明细序号关联
                    oerYszb.setGsdm(gsdm);
                    oerYszb.setKjnd(kjnd);

                    // 设置预算指标数据
                    if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                        oerYszb.setJe(new BigDecimal(budgetIndex.getAmount()));
                    }
                    oerYszb.setZbid(Integer.valueOf(budgetIndex.getZbid()));
                    oerYszb.setXm(budgetIndex.getProject());
                    oerYszb.setZy(budgetIndex.getProject());
                    oerYszb.setZbgnkmmc(budgetIndex.getFuncSubject());
                    oerYszb.setZbjjkmmc(budgetIndex.getEconomicSubject());
                    oerYszb.setYskjkmmc(budgetIndex.getAccountTitle());
                    oerYszb.setZbfz7dm(budgetIndex.getFZ7DM());
                    oerYszb.setZbfz7mc(budgetIndex.getFZ7MC());
                    oerYszb.setZbfz8dm(budgetIndex.getFZ8DM());
                    oerYszb.setZbfz8mc(budgetIndex.getFZ8MC());
                    oerYszb.setZbfz9dm(budgetIndex.getFZ9DM());
                    oerYszb.setZbfz9mc(budgetIndex.getFZ9MC());
                    oerYszb.setZbdm(budgetIndex.getZbdm());

                    // 使用indexMapper的insert方法
                    indexMapper.insert(oerYszb);

                    // 保存指标使用记录到GBI_ZBSYREC表
                    GbiZbsyrec zbsyrec = new GbiZbsyrec();
                    zbsyrec.setGsdm(gsdm);
                    zbsyrec.setKjnd(kjnd);
                    zbsyrec.setDjid(jhbh);
                    zbsyrec.setDjlx(String.valueOf(BillnoType.CGJH.getCode()));
                    zbsyrec.setModule(BillnoType.CGSQ.getModCode());
                    zbsyrec.setDjzt("1"); // 单据状态
                    // 设置预算金额
                    if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                        zbsyrec.setDjje(new BigDecimal(budgetIndex.getAmount()));
                    }
                    zbsyrec.setMxxh(jhmxxh); // 设置明细ID关联

                    gbiZbsyrecMapper.insert(zbsyrec);

                    if (existingCgjhml.getZt().compareTo(new BigDecimal(5)) == 0) {
                        updateStatus(jhbh, 1);
                    }

                    log.info("保存预算指标成功，jhbh：{}，jhmxxh：{}，金额：{}", jhbh, jhmxxh, budgetIndex.getAmount());
                }
            }

            log.info("更新采购计划成功，jhbh：{}，明细数量：{}", jhbh, cgjhSaveDTO.getDetailsWithBudget().size());

            // 4. 构建并返回响应数据（直接使用djbh）
            CgjhSaveResponseVO responseVO = buildSaveResponseVO(jhbh);
            log.info("返回更新后的采购计划详情，计划名称：{}", responseVO.getBaseInfo().getJhmc());

            return responseVO;

        } catch (Exception e) {
            log.error("更新采购计划失败，jhbh: {}", jhbh, e);
            throw new GlobalException("更新采购计划失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取OER_YSZB表中的最大MLID
     */
    private Integer getMaxMlidFromOerYszb(String gsdm, String kjnd) {
        return indexMapper.selectMaxMlid(gsdm, kjnd);
    }

    /**
     * 获取OER_YSZB表中的最大BNXID
     */
    private Integer getMaxBnxidFromOerYszb(String gsdm, String kjnd) {
        return indexMapper.selectMaxBnxid(gsdm, kjnd);
    }

    /**
     * 获取OER_YSZB表中的最大DataID
     */
    private Integer getMaxDataIdFromOerYszb(String gsdm, String kjnd) {
        return indexMapper.selectMaxDataId(gsdm, kjnd);
    }

    @Override
    public boolean deleteByIdCgjh(String jhid) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 权限校验：查询采购计划的录入人信息
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，无法删除，jhid: " + jhid);
            }

            // 权限校验：只有录入人才能删除
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalInputUser = cgjhml.getLrr();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 删除操作，当前用户：{}，录入人：{}，jhid：{}", currentUser, originalInputUser, jhid);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能删除此采购计划");
            }
            log.info("权限校验通过 - 删除操作，当前用户：{}，jhid：{}", currentUser, jhid);

            // 1. 删除主表
            LambdaUpdateWrapper<GpmCgjhml> mlWrapper = new LambdaUpdateWrapper<>();
            int deleteml = cgjhmlMapper.delete(mlWrapper);

            // 2. 删除明细表
            LambdaUpdateWrapper<GpmCgjhnr> nrWrapper = new LambdaUpdateWrapper<>();
            nrWrapper.eq(GpmCgjhnr::getJhbh, jhid);
            int deletenr = cgjhnrMapper.delete(nrWrapper);

            // 3. 删除预算指标表
            if (cgjhml.getJhbh() != null) {
                LambdaUpdateWrapper<GpmCgjhYszb> yszbWrapper = new LambdaUpdateWrapper<>();
                yszbWrapper.eq(GpmCgjhYszb::getDjbh, cgjhml.getJhbh());
                cgjhYszbMapper.delete(yszbWrapper);
            }

            // 4. 删除指标使用记录
            if (cgjhml.getJhbh() != null) {
                LambdaUpdateWrapper<GbiZbsyrec> zbsyrecWrapper = new LambdaUpdateWrapper<>();
                zbsyrecWrapper.eq(GbiZbsyrec::getDjid, cgjhml.getJhbh())
                        .eq(GbiZbsyrec::getModule, BillnoType.CGJH.getModCode())
                        .eq(GbiZbsyrec::getDjlx, BillnoType.CGJH.getCode());
                gbiZbsyrecMapper.delete(zbsyrecWrapper);
            }

            log.info("删除采购计划成功，jhid：{}", jhid);
            return deleteml > 0 && deletenr > 0;

        } catch (Exception e) {
            log.error("删除采购计划失败，jhid: {}", jhid, e);
            throw new GlobalException("删除采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteByJhbh(String jhbh) {
        try {
            // 权限校验：查询采购计划的录入人信息
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，无法删除，jhbh: " + jhbh);
            }
            if (cgjhml.getZt().compareTo(BigDecimal.ONE) != 0) {
                throw new GlobalException("采购计划不是保存状态，无法删除，jhbh: " + jhbh);
            }

            // 权限校验：只有录入人才能删除
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalInputUser = cgjhml.getLrr();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 删除操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, jhbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能删除此采购计划");
            }
            log.info("权限校验通过 - 删除操作，当前用户：{}，jhbh：{}", currentUser, jhbh);

            // 删除主表
            LambdaUpdateWrapper<GpmCgjhml> mlWrapper = new LambdaUpdateWrapper<>();
            mlWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            int deleteml = cgjhmlMapper.delete(mlWrapper);

            // 删除明细表
            LambdaUpdateWrapper<GpmCgjhnr> nrWrapper = new LambdaUpdateWrapper<>();
            nrWrapper.eq(GpmCgjhnr::getJhbh, jhbh);
            int deletenr = cgjhnrMapper.delete(nrWrapper);

            // 删除预算指标表
            LambdaUpdateWrapper<GpmCgjhYszb> yszbWrapper = new LambdaUpdateWrapper<>();
            yszbWrapper.eq(GpmCgjhYszb::getDjbh, jhbh);
            cgjhYszbMapper.delete(yszbWrapper);

            // 删除指标使用记录
            LambdaUpdateWrapper<GbiZbsyrec> zbsyrecWrapper = new LambdaUpdateWrapper<>();
            zbsyrecWrapper.eq(GbiZbsyrec::getDjid, jhbh)
                    .eq(GbiZbsyrec::getModule, BillnoType.CGJH.getModCode())
                    .eq(GbiZbsyrec::getDjlx, BillnoType.CGJH.getCode());
            gbiZbsyrecMapper.delete(zbsyrecWrapper);

            log.info("删除采购计划成功，jhbh：{}", jhbh);
            return deleteml > 0;

        } catch (Exception e) {
            log.error("删除采购计划失败，jhbh: {}", jhbh, e);
            throw new GlobalException("删除采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjhSaveResponseVO getCgjhDetailByJhid(String jhid) {
        try {
            // 检查采购计划是否存在
            LambdaQueryWrapper<GpmCgjhml> mlQueryWrapper = new LambdaQueryWrapper<>();
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(mlQueryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，jhid: " + jhid);
            }

            // 获取djbh，然后使用djbh构建响应数据
            CgjhSaveResponseVO responseVO = buildSaveResponseVO(cgjhml.getJhbh());
            log.info("查询采购计划详情成功，jhid: {}, djbh: {}, 计划名称: {}", jhid, cgjhml.getJhbh(), responseVO.getBaseInfo().getJhmc());
            return responseVO;

        } catch (Exception e) {
            log.error("查询采购计划详情失败，jhid: {}", jhid, e);
            throw new GlobalException("查询采购计划详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjhSaveResponseVO getCgjhDetailByJhbh(String jhbh) {
        try {
            // 检查采购计划是否存在（使用djbh查询）
            LambdaQueryWrapper<GpmCgjhml> mlQueryWrapper = new LambdaQueryWrapper<>();
            mlQueryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(mlQueryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，jhbh: " + jhbh);
            }

            // 直接使用djbh构建响应数据
            CgjhSaveResponseVO responseVO = buildSaveResponseVO(jhbh);
            log.info("查询采购计划详情成功，jhbh: {}, 计划名称: {}", jhbh, responseVO.getBaseInfo().getJhmc());
            return responseVO;

        } catch (Exception e) {
            log.error("查询采购计划详情失败，jhbh: {}", jhbh, e);
            throw new GlobalException("查询采购计划详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgjhListVO> getCgjhPageList(CgjhPageQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 设置默认查询条件
            String gsdm = queryDTO.getGsdm();
            if (!StringUtils.isNotBlank(gsdm)) {
                gsdm = LoginInfo.getCurrCorpCode();
            }

            String kjnd = queryDTO.getKjnd();
            if (!StringUtils.isNotBlank(kjnd)) {
                kjnd = LoginInfo.getCurrAccountantYear();
            }

            // 处理模糊查询条件
            String condition = null;
            if (StringUtils.isNotBlank(queryDTO.getCondition())) {
                condition = "%" + queryDTO.getCondition().trim() + "%";
            }

            // 计算分页偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 准备其他查询条件
            String zt = queryDTO.getZt();
            String startDate = queryDTO.getStartDate();
            String endDate = queryDTO.getEndDate();

            // 获取数据权限信息
            String currEmployeeCode = LoginInfo.getCurrEmployeeCode();
            String dataPermission = dataPermissionUtils.getCurrentUserDataPermission();

            log.info("执行采购计划分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, offset={}, size={}, 用户={}, 权限={}, 部门={}",
                    gsdm, kjnd, condition, zt, offset, queryDTO.getSize(), currEmployeeCode, dataPermission, LoginInfo.getCurrDeptCode());

            // 执行查询，直接传递各个参数
            List<CgjhListVO> resultList = cgjhmlMapper.getCgjhPageList(
                    gsdm, kjnd, condition, zt, startDate, endDate, offset, queryDTO.getSize(),
                    currEmployeeCode, dataPermission, LoginInfo.getCurrDeptCode()
            );

            log.info("采购计划分页查询完成，返回{}条记录", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("采购计划分页查询失败，参数：{}，错误：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询采购计划列表失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgjhListVO> getApprovedCgjhList(CgjhPageQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 设置默认查询条件
            String gsdm = queryDTO.getGsdm();
            if (!StringUtils.isNotBlank(gsdm)) {
                gsdm = LoginInfo.getCurrCorpCode();
            }

            String kjnd = queryDTO.getKjnd();
            if (!StringUtils.isNotBlank(kjnd)) {
                kjnd = LoginInfo.getCurrAccountantYear();
            }

            // 处理模糊查询条件
            String condition = null;
            if (StringUtils.isNotBlank(queryDTO.getCondition())) {
                condition = "%" + queryDTO.getCondition().trim() + "%";
            }

            // 计算分页偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 准备其他查询条件 - 固定状态为已审核(4)
            String zt = "4"; // 已审核状态
            String startDate = queryDTO.getStartDate();
            String endDate = queryDTO.getEndDate();

            // 获取数据权限信息
            String currEmployeeCode = LoginInfo.getCurrEmployeeCode();
            String dataPermission = dataPermissionUtils.getCurrentUserDataPermission();

            log.info("执行已审核采购计划分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, offset={}, size={}, 用户={}, 权限={}, 部门={}",
                    gsdm, kjnd, condition, zt, offset, queryDTO.getSize(), currEmployeeCode, dataPermission, LoginInfo.getCurrCorpCode());

            // 先查询基础列表（包含总记录数）
            List<CgjhListVO> basicList = cgjhmlMapper.getApprovedCgjhList(
                    gsdm, kjnd, condition, zt, startDate, endDate, offset, queryDTO.getSize(),
                    currEmployeeCode, dataPermission, LoginInfo.getCurrDeptCode()
            );

            return basicList;

        } catch (Exception e) {
            log.error("已审核采购计划分页查询失败，参数：{}，错误：{}", queryDTO, e.getMessage());
            throw new GlobalException("查询已审核采购计划列表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 保存前数据校验
     * @param cgjhSaveDTO 采购计划保存DTO
     */
    private void checkBeforeSave(CgjhSaveDTO cgjhSaveDTO) {
        // 基础信息校验
        if (cgjhSaveDTO.getBaseInfo().getJhje() == null || cgjhSaveDTO.getBaseInfo().getJhje().compareTo(BigDecimal.ZERO) <= 0) {
            throw new GlobalException("计划金额必须大于0");
        }

        // 多明细信息校验（一对一关系）
        if (cgjhSaveDTO.getDetailsWithBudget() != null && !cgjhSaveDTO.getDetailsWithBudget().isEmpty()) {
            BigDecimal totalDetailAmount = BigDecimal.ZERO;

            for (int i = 0; i < cgjhSaveDTO.getDetailsWithBudget().size(); i++) {
                CgjhDetailWithBudgetDTO detailWithBudget = cgjhSaveDTO.getDetailsWithBudget().get(i);
                final int detailIndex = i + 1; // 创建final变量供Lambda使用

                // 校验明细信息
                if (detailWithBudget.getDetail() == null) {
                    throw new GlobalException("第" + detailIndex + "个明细信息不能为空");
                }

                BigDecimal detailAmount = detailWithBudget.getDetail().getJhje();
                if (detailAmount == null || detailAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new GlobalException("第" + detailIndex + "个明细的金额必须大于0");
                }

                totalDetailAmount = totalDetailAmount.add(detailAmount);

                // 校验预算指标信息（一对一关系）
                if (detailWithBudget.getBudgetIndex() == null) {
                    throw new GlobalException("第" + detailIndex + "个明细的预算指标不能为空");
                }

                // 校验预算指标金额是否等于明细金额
                CgjhBudgetIndexDTO budgetIndex = detailWithBudget.getBudgetIndex();
//                if (budgetIndex.getAmount() == null) {
//                    throw new GlobalException("第" + detailIndex + "个明细的预算指标金额不能为空");
//                }

                BigDecimal budgetAmount = null;
                try {
                    if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                        budgetAmount = new BigDecimal(budgetIndex.getAmount());
                    }
                } catch (NumberFormatException e) {
                    throw new GlobalException("第" + detailIndex + "个明细的预算指标金额格式不正确");
                }

                if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                    if (budgetAmount.compareTo(detailAmount) != 0) {
                        throw new GlobalException("第" + detailIndex + "个明细的预算指标金额必须等于明细金额");
                    }
                }
            }

            // 校验明细金额总和是否等于主表计划金额
            if (totalDetailAmount.compareTo(cgjhSaveDTO.getBaseInfo().getJhje()) != 0) {
                throw new GlobalException("所有明细金额总和必须等于计划总金额");
            }
        }
    }

    /**
     * 校验预算指标是否存在
     * @param cgjhSaveDTO 采购计划保存DTO
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     */
    private void checkBudgetIndexExists(CgjhSaveDTO cgjhSaveDTO, String gsdm, String kjnd) {
        if (cgjhSaveDTO.getDetailsWithBudget() != null && !cgjhSaveDTO.getDetailsWithBudget().isEmpty()) {
            for (int i = 0; i < cgjhSaveDTO.getDetailsWithBudget().size(); i++) {
                CgjhDetailWithBudgetDTO detailWithBudget = cgjhSaveDTO.getDetailsWithBudget().get(i);
                final int detailIndex = i + 1; // 创建final变量供Lambda使用

                // 校验预算指标存在性
                if (detailWithBudget.getBudgetIndex() != null && detailWithBudget.getBudgetIndex().getZbid() != null) {
                    if (StringUtils.isBlank(detailWithBudget.getBudgetIndex().getZbid().trim())){
                        throw new GlobalException("第" + detailIndex + "个明细的预算指标ID不能为空");
                    }
                    Integer indexID = Integer.valueOf(detailWithBudget.getBudgetIndex().getZbid());

                    // 检查预算指标是否存在
                    Integer exists = indexMapper.checkBudgetIndexExists(gsdm, kjnd, indexID);
                    if (exists == null || exists == 0) {
                        throw new GlobalException("第" + detailIndex + "个明细的预算指标ID（" + indexID + "）不存在或不可用");
                    }

                    log.debug("预算指标存在性校验通过，明细序号：{}，指标ID：{}", detailIndex, indexID);
                }
            }
        }
        log.info("预算指标存在性校验完成，公司：{}，年度：{}", gsdm, kjnd);
    }

    /**
     * 生成计划编号
     * 格式：当前年份后2位 + 时间戳后6位，确保在NUMERIC(9)范围内
     */
    private String generateJhbh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        // 查询当天最大序号
        String prefix = "CGJH" + dateStr;
        String maxJhbh = cgjhmlMapper.selectMaxJhbhByPrefix(prefix);
        
        int sequenceNumber = 1;
        if (maxJhbh != null && maxJhbh.length() >= prefix.length() + 4) {
            // 提取序列号部分
            String sequence = maxJhbh.substring(prefix.length());
            try {
                sequenceNumber = Integer.parseInt(sequence) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析序列号失败，使用默认值1: {}", sequence, e);
            }
        }
        
        // 格式化为4位数字，例如：0001, 0002, ..., 9999
        return prefix + String.format("%04d", sequenceNumber);
    }

    /**
     * 构建保存响应VO（多明细模式）
     * @param jhbh 单据编号
     * @return CgjhSaveResponseVO
     */
    private CgjhSaveResponseVO buildSaveResponseVO(String jhbh) {
        try {
            CgjhSaveResponseVO responseVO = new CgjhSaveResponseVO();

            // 1. 查询并构建基础信息（使用djbh）
            LambdaQueryWrapper<GpmCgjhml> mlQueryWrapper = new LambdaQueryWrapper<>();
            mlQueryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(mlQueryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，jhbh: " + jhbh);
            }

            CgjhBaseInfoResponseVO baseInfo = new CgjhBaseInfoResponseVO();
            BeanUtils.copyProperties(cgjhml, baseInfo);
            baseInfo.setJhbh(cgjhml.getJhbh());
            baseInfo.setZtmc(StatusName.findByStatus(cgjhml.getZt()));
            responseVO.setBaseInfo(baseInfo);

            // 2. 查询所有明细记录
            LambdaQueryWrapper<GpmCgjhnr> nrQueryWrapper = new LambdaQueryWrapper<>();
            nrQueryWrapper.eq(GpmCgjhnr::getJhbh, jhbh)
                    .orderByAsc(GpmCgjhnr::getJhmxxh); // 按明细序号排序

            List<GpmCgjhnr> cgjhnrList = cgjhnrMapper.selectList(nrQueryWrapper);

            // 3. 构建多明细与预算指标组合结构
            List<CgjhDetailWithBudgetDTO> detailsWithBudget = new ArrayList<>();

            if (cgjhnrList != null && !cgjhnrList.isEmpty()) {
                for (GpmCgjhnr cgjhnr : cgjhnrList) {
                    // 构建明细信息
                    CgjhDetailDTO detailDTO = new CgjhDetailDTO();
                    BeanUtils.copyProperties(cgjhnr, detailDTO);

                    // 查询该明细对应的预算指标（一对一关系）
                    CgjhBudgetIndexDTO budgetIndex = null;

                    // 一对一关系：查询该明细的第一条预算指标记录
                    LambdaQueryWrapper<OerYszb> yszbQueryWrapper = new LambdaQueryWrapper<>();
                    yszbQueryWrapper.eq(OerYszb::getDjbh, jhbh)
                            .eq(OerYszb::getMxxh, cgjhnr.getJhmxxh())
                            .orderByAsc(OerYszb::getXh);

                    OerYszb oerYszb = indexMapper.selectOne(yszbQueryWrapper);

                    if (oerYszb != null) {
                        // 构建预算指标DTO
                        budgetIndex = new CgjhBudgetIndexDTO();
                        budgetIndex.setZbid(String.valueOf(oerYszb.getZbid()));
                        budgetIndex.setProject(oerYszb.getXm());
                        // TODO: 预算指标的剩余金额
//                        budgetIndex.setResidual(oerYszb.getJe());
                        budgetIndex.setMxxh(oerYszb.getMxxh());
                        if (oerYszb.getJe() != null) {
                            budgetIndex.setAmount(oerYszb.getJe().toString());
                        }
                        budgetIndex.setZbdm(oerYszb.getZbdm());
                        budgetIndex.setProject(oerYszb.getXm());
                        budgetIndex.setFuncSubject(oerYszb.getZbgnkmmc());
                        budgetIndex.setEconomicSubject(oerYszb.getZbjjkmmc());
                        budgetIndex.setAccountTitle(oerYszb.getYskjkmmc());
                        budgetIndex.setFZ7DM(oerYszb.getZbfz7dm());
                        budgetIndex.setFZ7MC(oerYszb.getZbfz7mc());
                        budgetIndex.setFZ8DM(oerYszb.getZbfz8dm());
                        budgetIndex.setFZ8MC(oerYszb.getZbfz8mc());
                        budgetIndex.setFZ9DM(oerYszb.getZbfz9dm());
                        budgetIndex.setFZ9MC(oerYszb.getZbfz9mc());
                        budgetIndex.setIndex(oerYszb.getXh());
                    }

                    // 构建明细与预算指标组合（一对一关系）
                    CgjhDetailWithBudgetDTO detailWithBudget = new CgjhDetailWithBudgetDTO();
                    detailWithBudget.setDetail(detailDTO);
                    detailWithBudget.setBudgetIndex(budgetIndex);

                    detailsWithBudget.add(detailWithBudget);
                }

                log.info("查询到{}条明细记录，jhbh: {}", cgjhnrList.size(), jhbh);
            } else {
                log.warn("未查询到明细记录，jhbh: {}", jhbh);
            }

            // 设置多明细结构
            responseVO.setDetailsWithBudget(detailsWithBudget);

            return responseVO;

        } catch (Exception e) {
            log.error("构建保存响应VO失败，jhbh: {}", jhbh, e);
            throw new GlobalException("构建响应数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Map<Integer, List<PubObjFlowTemp>> commitProxy(BillnoType billnoType, String jhbh, Double money) {
        try {
            // 1. 校验采购计划状态，只有保存状态（1）才能提交
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，无法提交");
            }

            // 权限校验：只有录入人才能提交
            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = String.valueOf(cgjhml.getLrrId());
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 提交操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, jhbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能提交此采购计划");
            }
            log.info("权限校验通过 - 提交操作，当前用户：{}，jhbh：{}", currentUser, jhbh);

            // 2. 检查状态 - 只有保存状态（1）(5)才能提交
            if (cgjhml.getZt().intValue() != 1 && cgjhml.getZt().intValue() != 5) {
                throw new GlobalException("只有保存状态的采购计划才能提交，当前状态为：" + StatusName.findByStatus(cgjhml.getZt()));
            }

            List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(BillnoType.CGJH.getCode());
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new HashMap<>();

            if (templates.isEmpty()) {
                // 如果没有配置审核模板，创建默认审核节点
                log.warn("未找到审核流程模板，将创建默认审核节点，单据类型：{}", billnoType.getCode());

                PubObjFlowTemp defaultTemplate = new PubObjFlowTemp();
                defaultTemplate.setShrdm(StaticValue.getZydm());
                defaultTemplate.setShrxm(StaticValue.getZymc());
                defaultTemplate.setJdmc("超级管理员审核");
                defaultTemplate.setJddm(1314);
                defaultTemplate.setGsdm(StaticValue.getGsdm());
                defaultTemplate.setDjlxid(BillnoType.CGSQ.getCode());
//                defaultTemplate.setFlowcode("CGSQ_FLOW_001");
                defaultTemplate.setXh("1");
//                defaultTemplate.setKjnd(String.valueOf(Year.now().getValue()));

                List<PubObjFlowTemp> defaultList = new ArrayList<>();
                defaultList.add(defaultTemplate);
                nodeMap.put(1, defaultList);

                log.info("已创建默认审核节点，审核人：{}", defaultTemplate.getShrxm());
            } else {
                // 按节点序号分组
                for (PubObjFlowTemp template : templates) {
                    Integer nodeSeq = template.getXh() != null ? Integer.parseInt(template.getXh()) : 1;
                    if (template.getDynamicAcquisition().equals("1") && StringUtils.isNotBlank(template.getShtj())){
                        JSONObject auditorInfo = cgjhmlMapper.findAuditorInfo(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear(),
                                jhbh, template.getShtj());
                        if (auditorInfo!=null){
                            template.setShrxm(auditorInfo.getString("shrxm"));
                            template.setShrdm(auditorInfo.getString("shrdm"));
                        }
                    }
                    nodeMap.computeIfAbsent(nodeSeq, k -> new ArrayList<>()).add(template);
                }
                log.info("获取审核流程模板成功，共{}个节点，模板数量：{}", nodeMap.size(), templates.size());
            }

            log.info("采购计划提交Service层处理完成，jhbh：{}，节点数量：{}", jhbh, nodeMap.size());

            return nodeMap;

        } catch (Exception e) {
            log.error("采购计划提交Service层处理失败，jhbh: {}", jhbh, e);
            throw new GlobalException("提交采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjhSaveResponseVO findByLid(String jhbh) {
        // 复用已有的getCgjhDetailByDjbh方法
        return getCgjhDetailByJhbh(jhbh);
    }

    @Override
    public CgjhAuditLogVO findCheckLog(BillnoType billnoType, String jhbh) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billnoType.getModCode(), String.valueOf(billnoType.getCode()));

            String flowcode = "CGJH_FLOW_001"; // 默认流程代码
            if (workflow != null) {
                flowcode = workflow.getFlowcode();
            }

            // 查询审核日志
            List<PubAuditLog> logList = pubAuditLogMapper.selectByBillId(gsdm, kjnd, jhbh, flowcode);

            // 转换为VO
            CgjhAuditLogVO auditLogVO = new CgjhAuditLogVO();

            // 转换日志列表
            List<CgjhAuditLogVO.AuditLogItem> logItems = logList.stream().map(log -> {
                CgjhAuditLogVO.AuditLogItem item = new CgjhAuditLogVO.AuditLogItem();
                item.setGsdm(log.getGsdm());
                item.setKjnd(log.getKjnd());
                item.setLogid(log.getLogid());
                item.setBillid(log.getBillid());
                item.setBillname(log.getBillname());
                item.setFlowcode(log.getFlowcode());
                item.setFlowname(log.getFlowname());
                item.setModname(log.getModname());
                item.setBizname(log.getBizname());
                item.setNodeseq(log.getNodeseq());
                item.setNodename(log.getNodename());
                item.setAuditorid(log.getAuditorid());
                item.setAuditor(log.getAuditor());
                item.setCertigierid(log.getCertigierid());
                item.setCertigier(log.getCertigier());
                item.setAdatetime(log.getAdatetime());
                item.setAmt(log.getAmt());
                item.setRemark(log.getRemark());
                item.setAtype(log.getAtype());
                item.setLogseq(log.getLogseq());
                item.setServDateTime(log.getServDateTime());
                item.setComputerName(log.getComputerName());
                return item;
            }).collect(Collectors.toList());

            // 创建日志标题列表
            List<CgjhAuditLogVO.LogTitleItem> logTitleList = new ArrayList<>();

            // 添加提交节点
            CgjhAuditLogVO.LogTitleItem titleNode = new CgjhAuditLogVO.LogTitleItem();
            titleNode.setNodename("提交审核");
            titleNode.setGsdm(gsdm);
            titleNode.setNodeseq(-9); // 提交节点编号
            titleNode.setKjnd(kjnd);
            titleNode.setIsaudit(logList.isEmpty() ? "0" : "1");
            titleNode.setAuditStatus(logList.isEmpty() ? "未提交" : "已提交");

            // 如果有提交日志，设置提交时间
            logList.stream()
                    .filter(log -> log.getNodeseq() != null && log.getNodeseq() == -9)
                    .findFirst()
                    .ifPresent(log -> titleNode.setAuditTime(log.getServDateTime()));

            logTitleList.add(titleNode);

            // 查询工作流程节点，添加审核节点标题
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(jhbh);

            // 查询工作流程模板节点
            List<PubObjFlowTemp> flowTemplates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, String> nodeNameMap = new HashMap<>();
            for (PubObjFlowTemp template : flowTemplates) {
                nodeNameMap.put(Integer.valueOf(template.getXh()), template.getJdmc());
            }

            for (PubObjFlow node : flowNodes) {
                CgjhAuditLogVO.LogTitleItem flowTitleNode = new CgjhAuditLogVO.LogTitleItem();

                // 通过节点序号获取正确的节点名称
                Integer nodeSeq = null;
                try {
                    nodeSeq = Integer.valueOf(node.getAuditFlag());
                    // 从工作流程模板中获取节点名称，如果没有则使用审核人名称作为备选
                    String nodeName = nodeNameMap.get(nodeSeq);
                    if (nodeName != null && !nodeName.trim().isEmpty()) {
                        flowTitleNode.setNodename(nodeName);
                    } else {
                        // 如果模板中没有节点名称，使用审核人名称作为备选
                        flowTitleNode.setNodename(node.getShrmc() != null ? node.getNodeName() : "审核节点");
                    }
                } catch (NumberFormatException e) {
                    flowTitleNode.setNodename("审核节点");
                    nodeSeq = 1;
                }

                flowTitleNode.setGsdm(node.getGsdm());
                flowTitleNode.setNodeseq(nodeSeq);
                flowTitleNode.setKjnd(node.getKjnd());
                flowTitleNode.setIsaudit("1".equals(node.getIsaudit()) ? "1" : "0");
                flowTitleNode.setAuditStatus("1".equals(node.getIsaudit()) ? "已审核" : "待审核");

                // 查找对应的审核日志设置审核时间
                logList.stream()
                        .filter(log -> log.getNodeseq() != null && log.getNodeseq().equals(flowTitleNode.getNodeseq()))
                        .findFirst()
                        .ifPresent(log -> flowTitleNode.setAuditTime(log.getServDateTime()));

                logTitleList.add(flowTitleNode);
            }

            auditLogVO.setLogList(logItems);
            auditLogVO.setLogTitleList(logTitleList);

            return auditLogVO;

        } catch (Exception e) {
            log.error("查询采购计划审核日志失败，jhbh: {}", jhbh, e);
            throw new GlobalException("查询审核日志失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Object isCheckedByAuthority(String jhbh) {
        try {
            // 查询采购计划详情
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            JSONObject resultJson = new JSONObject();

            // 检查采购计划是否存在
            if (cgjhml == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", true);
                resultJson.put("result", "此流程单据已经被删除，当前审核人" + LoginInfo.getCurrEmployeeName() + "无法查看");
                return resultJson;
            }

            // 检查采购计划状态
            // 状态：1-保存，2-已提交，3-审核中，4-已审核，5-退回
            if (cgjhml.getZt() == null ||
                    cgjhml.getZt().intValue() == 1 || // 1-保存状态
                    cgjhml.getZt().intValue() == 5) { // 5-退回状态
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程当前节点已被审批，当前审核人" + LoginInfo.getCurrEmployeeName() + "无权审批!\n是否进入详情查看单据信息");
                return resultJson;
            }

            // 获取审批流程信息
            BillnoType billType = BillnoType.CGJH;

            // 查询当前审核节点（使用djbh）
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    String.valueOf(billType.getCode()),
                    jhbh  // 直接使用djbh
            );

            // 检查当前用户是否有审核权限
            if (nowNodeInfo == null ||
                    !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程当前节点已被审批，当前审核人" + LoginInfo.getCurrEmployeeName() + "无权审批!\n是否进入详情查看单据信息");
            } else {
                resultJson.put("isCheck", false);
                resultJson.put("isDelete", false);
                resultJson.put("result", "");
            }

            return resultJson;

        } catch (Exception e) {
            log.error("检查采购计划审核权限失败，djbh: {}", jhbh, e);

            JSONObject errorResult = new JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    @Override
    public void check(BillnoType billType, String jhbh, String opinion, String auditor, BigDecimal money) {
        try {
            // 1. 查询采购计划详情
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            // 2. 检查状态 - 只有已提交（2）或审核中（3）的采购计划才能审核
            if (cgjhml.getZt() == null ||
                    (cgjhml.getZt().intValue() != 2 && cgjhml.getZt().intValue() != 3)) {
                throw new GlobalException("只有已提交或审核中状态的采购计划才能审核，当前状态为：" + StatusName.findByStatus(cgjhml.getZt()));
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 获取审批流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billType.getModCode(), String.valueOf(billType.getCode()));

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }

            // 4. 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    String.valueOf(billType.getCode()),
                    jhbh
            );

            if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("该节点已被其他人审核或您无权限审核");
            }

            // 5. 在更新节点之前先获取终审判断所需的信息
            int nowNote = Integer.parseInt(nowNodeInfo.getAuditFlag());
            int nextNote = Integer.parseInt(nowNodeInfo.getAuditAftFlag());

            // 判断是否为终审（nextNote == -1 表示最后一个节点）
            boolean isLastNode = (nextNote == -1);

            log.info("审核节点信息 - 当前节点：{}，下一节点：{}，是否终审：{}", nowNote, nextNote, isLastNode);

            // 6. 更新当前节点为已审核
            nowNodeInfo.setIsaudit("1");
            nowNodeInfo.setSpecificCheckPerson(LoginInfo.getCurrEmployeeName());

            // 更新当前节点状态
            LambdaUpdateWrapper<PubObjFlow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PubObjFlow::getModcode, billType.getModCode())
                    .eq(PubObjFlow::getDjlx, String.valueOf(billType.getCode()))
                    .eq(PubObjFlow::getDjh, jhbh)
                    .eq(PubObjFlow::getAuditFlag, String.valueOf(nowNote))
                    .set(PubObjFlow::getIsaudit, "1")
                    .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

            pubObjFlowMapper.update(null, updateWrapper);

            // 7. 根据终审判断结果更新采购计划状态
            int status = isLastNode ? 4 : 3; // 4-已审核（终审），3-审核中
            updateStatus(jhbh, status);

            // 8. 如果不是终审，需要确保下一个审核节点存在且状态正确
            if (!isLastNode) {
                // 查询下一个审核节点是否存在
                LambdaQueryWrapper<PubObjFlow> nextNodeQuery = new LambdaQueryWrapper<>();
                nextNodeQuery.eq(PubObjFlow::getModcode, billType.getModCode())
                        .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGJH.getCode()))
                        .eq(PubObjFlow::getDjh, jhbh)
                        .eq(PubObjFlow::getAuditFlag, String.valueOf(nextNote));

                PubObjFlow nextNode = pubObjFlowMapper.selectOne(nextNodeQuery);

                if (nextNode == null) {
                    log.warn("下一个审核节点不存在，jhbh：{}，下一节点序号：{}，需要创建", jhbh, nextNote);

                    // 如果下一个节点不存在，尝试从工作流程模板创建
                    List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(billType.getCode());
                    PubObjFlowTemp nextTemplate = templates.stream()
                            .filter(t -> t.getJddm() == nextNote)
                            .findFirst()
                            .orElse(null);

                    if (nextTemplate != null) {
                        // 创建下一个审核节点
                        // 直接使用已存在的gsdm、kjnd和workflow变量，不重复定义
                        String nextFlowCode = workflow != null ? workflow.getFlowcode() : "CGJH_FLOW_001";

                        PubObjFlow newNextNode = new PubObjFlow();
                        newNextNode.setPofId(UUID.randomUUID().toString().replace("-", "").substring(0, 20));
                        newNextNode.setGsdm(gsdm);
                        newNextNode.setKjnd(kjnd);
                        newNextNode.setModcode(billType.getModCode());
                        newNextNode.setDjlx(String.valueOf(BillnoType.CGJH.getCode()));
                        newNextNode.setDjh(jhbh);
                        newNextNode.setFlowcode(nextFlowCode);
                        newNextNode.setShr1("," + nextTemplate.getShrdm() + ",");
                        newNextNode.setShrmc(nextTemplate.getShrxm());
                        newNextNode.setAuditFlag(String.valueOf(nextNote));
                        newNextNode.setAuditAftFlag("-1");
                        newNextNode.setIsaudit("0");
                        newNextNode.setSpecificCheckPerson("");

                        pubObjFlowMapper.insert(newNextNode);

                        log.info("已创建下一个审核节点，jhbh：{}，节点序号：{}，审核人：{}",
                                jhbh, nextNote, nextTemplate.getShrxm());
                    } else {
                        log.warn("未找到下一个节点的模板配置，jhbh：{}，节点序号：{}", jhbh, nextNote);
                    }
                } else {
                    // 下一个节点存在，确保其状态为未审核
                    if (!"0".equals(nextNode.getIsaudit())) {
                        LambdaUpdateWrapper<PubObjFlow> resetNextNodeWrapper = new LambdaUpdateWrapper<>();
                        resetNextNodeWrapper.eq(PubObjFlow::getPofId, nextNode.getPofId())
                                .set(PubObjFlow::getIsaudit, "0")
                                .set(PubObjFlow::getSpecificCheckPerson, "");

                        pubObjFlowMapper.update(null, resetNextNodeWrapper);

                        log.info("已重置下一个审核节点状态为未审核，jhbh：{}，节点序号：{}", jhbh, nextNote);
                    }
                }
            }

            // 9. 如果是终审，同时更新采购计划的审核相关字段
            if (isLastNode) {
                CacheStatisticsUtils.clearStatisticsCache();
                LambdaUpdateWrapper<GpmCgjhml> cgjhUpdateWrapper = new LambdaUpdateWrapper<>();
                cgjhUpdateWrapper.eq(GpmCgjhml::getJhbh, jhbh)
                        .set(GpmCgjhml::getAshjd, nowNote) // 当前审核节点
                        .set(GpmCgjhml::getAxshjd, -1) // 下一审核节点设为-1（终审）
                        .set(GpmCgjhml::getAshr, LoginInfo.getCurrEmployeeName()) // 审核人
                        .set(GpmCgjhml::getAshrId, Integer.parseInt(LoginInfo.getCurrEmployeeCode() != null ? LoginInfo.getCurrEmployeeCode() : "0")) // 审核人ID
                        .set(GpmCgjhml::getAshRq, new SimpleDateFormat("yyyy-MM-dd").format(new Date())); // 审核日期

                cgjhmlMapper.update(null, cgjhUpdateWrapper);

                log.info("终审完成，已更新采购计划审核信息，jhbh：{}", jhbh);
            } else {
                // 非终审，设置下一个审核节点信息
                LambdaUpdateWrapper<GpmCgjhml> cgjhUpdateWrapper = new LambdaUpdateWrapper<>();
                cgjhUpdateWrapper.eq(GpmCgjhml::getJhbh, jhbh)
                        .set(GpmCgjhml::getAshjd, nowNote) // 当前审核节点
                        .set(GpmCgjhml::getAxshjd, nextNote) // 下一审核节点
                        .set(GpmCgjhml::getAshr, "") // 清空审核人
                        .set(GpmCgjhml::getAshrId, -1) // 清空审核人ID
                        .set(GpmCgjhml::getAshRq, ""); // 清空审核日期

                cgjhmlMapper.update(null, cgjhUpdateWrapper);

                log.info("中间审核完成，已设置下一审核节点，jhbh：{}，下一节点：{}", jhbh, nextNote);
            }

            // 10. 记录审核日志
            createAuditLog(billType, jhbh, opinion, LoginInfo.getCurrEmployeeName(), money, workflow, nowNote, nowNodeInfo.getNodeName(), "审核");

            log.info("采购计划审核成功，jhbh：{}，审核人：{}，是否终审：{}", jhbh, auditor, isLastNode);

        } catch (Exception e) {
            log.error("审核采购计划失败，jhbh: {}", jhbh, e);
            throw new GlobalException("审核采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void updateStatus(String jhbh, Integer status) {
        try {
            LambdaUpdateWrapper<GpmCgjhml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgjhml::getJhbh, jhbh)
                    .set(GpmCgjhml::getZt, new BigDecimal(status));

            int result = cgjhmlMapper.update(null, updateWrapper);
            if (result == 0) {
                throw new GlobalException("更新采购计划状态失败，采购计划不存在：" + jhbh);
            }

            // 同步更新GBI_ZBSYREC表的状态，确保与采购计划主表状态保持一致
            updateGbiZbsyrecStatus(jhbh, status.toString());

            log.info("更新采购计划状态成功，jhbh: {}, status: {}", jhbh, status);

        } catch (Exception e) {
            log.error("更新采购计划状态失败，jhbh: {}, status: {}", jhbh, status, e);
            throw new GlobalException("更新采购计划状态失败：" + e.getMessage(), e);
        }
    }

    /**
     * 同步更新GBI_ZBSYREC表的单据状态
     * 确保与采购计划主表状态保持一致，这样IndexController的余额计算才能正确扣除在途和使用金额
     * @param jhbh 单据编号
     * @param djzt 新的单据状态（1-保存，2-已提交，3-审核中，4-已审核）
     */
    private void updateGbiZbsyrecStatus(String jhbh, String djzt) {
        try {
            log.info("开始同步更新GBI_ZBSYREC表状态，单据编号：{}，新状态：{}", jhbh, djzt);

            // 更新GBI_ZBSYREC表中对应记录的DJZT字段
            LambdaUpdateWrapper<GbiZbsyrec> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GbiZbsyrec::getDjid, jhbh)
                    .eq(GbiZbsyrec::getModule, BillnoType.CGJH.getModCode())
                    .eq(GbiZbsyrec::getDjlx, BillnoType.CGJH.getCode())
                    .set(GbiZbsyrec::getDjzt, djzt);

            int updateCount = gbiZbsyrecMapper.update(null, updateWrapper);

            log.info("GBI_ZBSYREC表状态同步完成，单据编号：{}，更新记录数：{}，新状态：{}", jhbh, updateCount, djzt);

        } catch (Exception e) {
            log.error("同步更新GBI_ZBSYREC表状态失败，单据编号：{}，新状态：{}，异常：{}", jhbh, djzt, e.getMessage());
            // 这里不抛出异常，避免影响主流程，但会记录日志
        }
    }


    @Override
    public CgjhSaveResponseVO callBack(String jhbh) {
        try {
            log.info("开始收回采购计划，单据编号：{}", jhbh);

            // 1. 查询采购计划
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，单据编号：" + jhbh);
            }

            // 权限校验：只有录入人才能收回
            String currentUser = LoginInfo.getCurrEmployeeName();
            String originalInputUser = cgjhml.getLrr();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 收回操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, jhbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能收回此采购计划");
            }
            log.info("权限校验通过 - 收回操作，当前用户：{}，jhbh：{}", currentUser, jhbh);

            // 2. 检查状态是否可以收回
            BigDecimal currentStatus = cgjhml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购计划状态异常，无法收回");
            }

            int status = currentStatus.intValue();
            // 只有已提交(2)的状态才能收回
            if (status != 2) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgjhml.getZt()) + "，无法收回。只有已提交或审核中的采购计划才能收回");
            }

            // 3. 检查是否已经有人开始审核
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(jhbh);
            boolean hasStartedAudit = flowNodes.stream()
                    .anyMatch(node -> "1".equals(node.getIsaudit()));

            if (hasStartedAudit) {
                throw new GlobalException("该采购验收已开始审核，无法收回");
            }

            // 4. 删除相关的工作流程数据
            try {
                // 删除工作流程实例
                if (pubObjFlowService != null) {
                    pubObjFlowService.deleteByCon(BillnoType.CGJH.getModCode(),
                            String.valueOf(BillnoType.CGJH.getCode()), jhbh);
                }
            } catch (Exception e) {
                log.warn("清理工作流程数据时出现异常，但不影响收回操作，单据编号：{}，异常：{}", jhbh, e.getMessage());
            }

            updateStatus(jhbh, 1);
            log.info("采购计划收回成功，单据编号：{}", jhbh);

            // 5. 查询并返回收回后的采购计划详情
            return getCgjhDetailByJhbh(jhbh);

        } catch (Exception e) {
            log.error("收回采购计划失败，单据编号：{}，异常：{}", jhbh, e.getMessage(), e);
            throw new GlobalException("收回采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjhSaveResponseVO checkCallBack(String jhbh, String opinion, String auditor) {
        try {
            log.info("开始退审采购计划，单据编号：{}，退审人：{}", jhbh, auditor);

            // 1. 查询采购计划
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，单据编号：" + jhbh);
            }

            // 2. 检查状态是否可以退审
            BigDecimal currentStatus = cgjhml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购计划状态异常，无法退审");
            }

            int status = currentStatus.intValue();
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 查询当前审核节点
            PubObjFlow currentNode = pubObjFlowService.selectNowNodeByDjh(
                    BillnoType.CGJH.getModCode(),
                    String.valueOf(BillnoType.CGJH.getCode()),
                    jhbh
            );

            if (currentNode == null) {
                throw new GlobalException("未找到当前审核节点，无法退审");
            }

            // 4. 检查退审权限
            if (!currentNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("您无权限对该采购计划进行退审操作");
            }

            // 5. 处理工作流程退审逻辑
            int currentNodeSeq = Integer.parseInt(currentNode.getAuditFlag());
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    BillnoType.CGJH.getModCode(), String.valueOf(BillnoType.CGJH.getCode()));

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }

            if (status == 2) {
                updateStatus(jhbh, 5);
                createAuditLog(BillnoType.CGJH, jhbh, opinion, LoginInfo.getCurrEmployeeName(),
                        cgjhml.getJhje(), workflow, currentNodeSeq,
                        currentNode.getNodeName(), "退审");
                return getCgjhDetailByJhbh(jhbh);
            }

            // 只有审核中(3)的状态才能退审
            if (status != 3) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgjhml.getZt()) + "，无法退审。只有审核中的采购计划才能退审");
            }

            // 查询所有工作流程节点，找到上一个节点
            List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(jhbh);
            PubObjFlow previousNode = null;

            for (PubObjFlow node : allNodes) {
                int nodeSeq = Integer.parseInt(node.getAuditFlag());
                if (nodeSeq < currentNodeSeq && (previousNode == null ||
                        Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                    previousNode = node;
                }
            }

            // 6. 更新工作流程状态
            if (previousNode != null) {
                LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGJH.getModCode())
                        .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGJH.getCode()))
                        .eq(PubObjFlow::getDjh, jhbh)
                        .eq(PubObjFlow::getAuditFlag, previousNode.getAuditFlag())
                        .set(PubObjFlow::getIsaudit, "0")
                        .set(PubObjFlow::getSpecificCheckPerson, "");

                pubObjFlowMapper.update(null, updatePreviousWrapper);
                if (previousNode == allNodes.get(0)) {
                    updateStatus(jhbh, 5);
                } else {
                    // 更新采购计划状态为审核中(3)
                    updateStatus(jhbh, 3);
                }
            }

            // 7. 记录退审日志
            createAuditLog(BillnoType.CGJH, jhbh, opinion, LoginInfo.getCurrEmployeeName(),
                    cgjhml.getJhje(), workflow, currentNodeSeq,
                    currentNode.getNodeName(), "退审");


            log.info("采购计划退审成功，单据编号：{}，退审人：{}", jhbh, auditor);

            // 8. 查询并返回退审后的采购计划详情
            return getCgjhDetailByJhbh(jhbh);

        } catch (Exception e) {
            log.error("退审采购计划失败，单据编号：{}，退审人：{}，异常：{}", jhbh, auditor, e.getMessage(), e);
            throw new GlobalException("退审采购计划失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgjhSaveResponseVO noAudit(String jhbh) {
        try {
            log.info("开始销审采购计划，单据编号：{}", jhbh);

            // 1. 查询采购计划
            LambdaQueryWrapper<GpmCgjhml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhml::getJhbh, jhbh);
            GpmCgjhml cgjhml = cgjhmlMapper.selectOne(queryWrapper);

            if (cgjhml == null) {
                throw new GlobalException("采购计划不存在，单据编号：" + jhbh);
            }

            // 2. 检查状态是否可以销审
            BigDecimal currentStatus = cgjhml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购计划状态异常，无法销审");
            }

            int status = currentStatus.intValue();
            // 只有审核中(3)、已审核(4)的状态才能销审
            if (status != 3 && status != 4) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgjhml.getZt()) + "，无法销审。只有已提交、审核中或已审核的采购计划才能销审");
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 查询最后已审核节点
            PubObjFlow lastAuditNode = pubObjFlowService.selectLastAuditNodeByDjh(
                    BillnoType.CGJH.getModCode(),
                    String.valueOf(BillnoType.CGJH.getCode()),
                    jhbh
            );

            // 3. 销审逻辑
            if (status == 3 || status == 4) {
                // 如果当前状态是审核中(3)或已审核(4)，需要退回到上一个节点

                // 查询所有工作流程节点
                List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(jhbh);
                if (allNodes.isEmpty()) {
                    // 如果没有工作流程节点，直接设置为已提交状态
                    updateStatus(jhbh, 2);
                    log.info("采购验收没有工作流程节点，销审为已提交状态，jhbh：{}", jhbh);
                } else {
                    // 找到最后一个已审核的节点
                    PubObjFlow lastAuditedNode = null;
                    int maxAuditedNodeSeq = -1;

                    for (PubObjFlow node : allNodes) {
                        if ("1".equals(node.getIsaudit())) { // 已审核的节点
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq > maxAuditedNodeSeq) {
                                maxAuditedNodeSeq = nodeSeq;
                                lastAuditedNode = node;
                            }
                        }
                    }

                    if (lastAuditedNode != null) {
                        if (!lastAuditedNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                            throw new GlobalException("您无权限对该采购验收进行销审操作");
                        }
                        LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                        updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGJH.getModCode())
                                .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGJH.getCode()))
                                .eq(PubObjFlow::getDjh, jhbh)
                                .eq(PubObjFlow::getAuditFlag, lastAuditedNode.getAuditFlag())
                                .set(PubObjFlow::getIsaudit, "0");

                        pubObjFlowMapper.update(null, updatePreviousWrapper);
                        // 有已审核的节点，找到它的上一个节点
                        PubObjFlow previousNode = null;
                        int previousNodeSeq = -1;

                        for (PubObjFlow node : allNodes) {
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq < maxAuditedNodeSeq && nodeSeq > previousNodeSeq) {
                                previousNodeSeq = nodeSeq;
                                previousNode = node;
                            }
                        }
                        if (previousNode != null) {
                            // 更新采购验收状态为审核中(3)
                            updateStatus(jhbh, 3);
                            log.info("采购验收销审成功，退回到上一个节点，ysbh：{}，上一节点序号：{}",
                                    jhbh, previousNode.getAuditFlag());
                        } else {
                            // 更新采购验收状态为已提交(2)
                            updateStatus(jhbh, 2);
                            log.info("采购计划销审成功，第一个审核节点已重置为未审核状态，ysbh：{}，第一节点序号：{}",
                                    jhbh, maxAuditedNodeSeq);
                        }
                    } else {
                        updateStatus(jhbh, 2);
                        log.info("采购验收没有已审核节点，销审为已提交状态，ysbh：{}", jhbh);
                    }
                }
                PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                        BillnoType.CGJH.getModCode(), String.valueOf(BillnoType.CGJH.getCode()));

                if (workflow == null) {
                    throw new GlobalException("未找到审核流程配置");
                }
                if (lastAuditNode != null) {
                    createAuditLog(BillnoType.CGJH, jhbh, "", LoginInfo.getCurrEmployeeName(),
                            cgjhml.getJhje(), workflow, Integer.parseInt(lastAuditNode.getAuditFlag()), lastAuditNode.getNodeName(), "销审");
                }
            }

            // 5. 查询并返回销审后的采购计划详情
            return getCgjhDetailByJhbh(jhbh);

        } catch (Exception e) {
            log.error("销审采购计划失败，单据编号：{}，异常：{}", jhbh, e.getMessage(), e);
            throw new GlobalException("销审采购计划失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建审核日志
     */
    private void createAuditLog(BillnoType billType, String jhbh, String opinion, String auditor,
                                BigDecimal money, PubWorkflow workflow, int nodeseq, String nodename, String atype) {
        try {
            PubAuditLog auditLog = new PubAuditLog();

            // 设置基础信息
            auditLog.setGsdm(LoginInfo.getCurrCorpCode());
            auditLog.setKjnd(LoginInfo.getCurrAccountantYear());

            // 获取新的日志ID
            Integer newLogID = pubAuditLogMapper.selectMaxLogID();
            if (newLogID == null) {
                newLogID = 1;
            }
            auditLog.setLogid(newLogID.longValue());

            // 设置单据信息
            auditLog.setBillid(jhbh);
            auditLog.setBillname(workflow.getBizname());
            auditLog.setFlowcode(workflow.getFlowcode());
            auditLog.setFlowname(workflow.getFlowname());
            auditLog.setModname(billType.getModCode());
            auditLog.setBizname(workflow.getBizname());

            // 设置审核节点信息
            auditLog.setNodeseq(nodeseq);
            auditLog.setNodename(nodename);

            // 设置审核人信息
            auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
            auditLog.setAuditor(auditor);
            auditLog.setCertigierid(0);
            auditLog.setCertigier("");

            // 设置审核时间
            SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setAdatetime(df1.format(new Date()));

            // 设置金额和备注
            auditLog.setAmt(money != null ? money : BigDecimal.ZERO);
            auditLog.setRemark(opinion != null ? opinion : "");
            auditLog.setAtype(atype);

            // 设置服务器时间
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setServDateTime(df2.format(new Date()));

            // 设置计算机信息
            try {
                String computerMsg = InetAddress.getLocalHost().getHostName()
                        + "/" + InetAddress.getLocalHost().getHostAddress();
                auditLog.setComputerName(computerMsg);
            } catch (Exception e) {
                auditLog.setComputerName("Unknown");
            }

            // 保存审核日志
            pubAuditLogMapper.insert(auditLog);

        } catch (Exception e) {
            log.error("创建审核日志失败，jhbh: {}", jhbh, e);
            // 审核日志失败不影响主流程
        }
    }

    @Override
    public void saveCgjhDetails(String jhbh, CgjhSaveDTO cgjhSaveDTO) {
        // 2. 保存多个明细及其对应的预算指标
        // 获取最大序号用于生成必填字段
        Integer maxMlid = getMaxMlidFromOerYszb(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear());
        Integer maxBnxid = getMaxBnxidFromOerYszb(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear());
        Integer maxDataId = getMaxDataIdFromOerYszb(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear());

        int budgetIndexCounter = 0; // 预算指标计数器
        
        // 生成明细编号的日期前缀
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String mxPrefix = "CGJHMX" + dateStr;
        
        // 从数据库查询当前前缀下的最大序号
        Integer maxSequence = cgjhnrMapper.selectMaxSequenceByPrefix(mxPrefix);
        int sequenceStart = (maxSequence != null) ? maxSequence + 1 : 1;
        
        log.info("查询到明细前缀{}的最大序号为{}，新序号从{}开始", mxPrefix, maxSequence, sequenceStart);

        for (int i = 0; i < cgjhSaveDTO.getDetailsWithBudget().size(); i++) {
            CgjhDetailWithBudgetDTO detailWithBudget = cgjhSaveDTO.getDetailsWithBudget().get(i);

            // 生成明细编号，格式：CGJHMX年月日001, CGJHMX年月日002, ...
            String jhmxxh = mxPrefix + String.format("%03d", sequenceStart + i);

            // 保存明细记录
            GpmCgjhnr cgjhnr = new GpmCgjhnr();
            BeanUtils.copyProperties(detailWithBudget.getDetail(), cgjhnr);

            // 设置关联信息
            cgjhnr.setJhbh(jhbh);
            cgjhnr.setJhmxxh(jhmxxh);
            cgjhnr.setGsdm(LoginInfo.getCurrCorpCode());
            cgjhnr.setKjnd(LoginInfo.getCurrAccountantYear());

            // 保存明细
            cgjhnrMapper.insert(cgjhnr);
            log.info("保存明细成功，jhbh：{}，jhmxxh：{}", jhbh, jhmxxh);

            // 保存该明细对应的预算指标（一对一关系）
            CgjhBudgetIndexDTO budgetIndex = detailWithBudget.getBudgetIndex();
            if (budgetIndex != null) {
                budgetIndexCounter++;

                // 设置明细序号
                budgetIndex.setMxxh(jhmxxh);

                // 保存预算指标到OER_YSZB表
                OerYszb oerYszb = new OerYszb();

                // 设置必填字段
                oerYszb.setMlid(maxMlid + budgetIndexCounter);
                oerYszb.setBnxid(maxBnxid + budgetIndexCounter);
                oerYszb.setDataId(maxDataId + budgetIndexCounter);
                oerYszb.setXh(budgetIndex.getIndex() != null ? budgetIndex.getIndex() : budgetIndexCounter);

                // 设置基本字段
                oerYszb.setDjbh(jhbh);
                oerYszb.setMxxh(jhmxxh); // 设置明细序号关联
                oerYszb.setGsdm(LoginInfo.getCurrCorpCode());
                oerYszb.setKjnd(LoginInfo.getCurrAccountantYear());

                // 设置预算指标数据
                if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                    oerYszb.setJe(new BigDecimal(budgetIndex.getAmount()));
                }
                oerYszb.setXm(budgetIndex.getProject());
                oerYszb.setZbid(budgetIndex.getZbid() != null ? Integer.valueOf(budgetIndex.getZbid()) : null);
                oerYszb.setZy(budgetIndex.getProject());
                oerYszb.setZbgnkmmc(budgetIndex.getFuncSubject());
                oerYszb.setZbjjkmmc(budgetIndex.getEconomicSubject());
                oerYszb.setYskjkmmc(budgetIndex.getAccountTitle());
                oerYszb.setZbfz7dm(budgetIndex.getFZ7DM());
                oerYszb.setZbfz7mc(budgetIndex.getFZ7MC());
                oerYszb.setZbfz8dm(budgetIndex.getFZ8DM());
                oerYszb.setZbfz8mc(budgetIndex.getFZ8MC());
                oerYszb.setZbfz9dm(budgetIndex.getFZ9DM());
                oerYszb.setZbfz9mc(budgetIndex.getFZ9MC());
                oerYszb.setZbdm(budgetIndex.getZbdm());

                // 使用indexMapper的insert方法
                indexMapper.insert(oerYszb);

                // 保存指标使用记录到GBI_ZBSYREC表
                GbiZbsyrec zbsyrec = new GbiZbsyrec();
                zbsyrec.setGsdm(LoginInfo.getCurrCorpCode());
                zbsyrec.setKjnd(LoginInfo.getCurrAccountantYear());
                zbsyrec.setDjid(jhbh);
                zbsyrec.setDjlx(String.valueOf(BillnoType.CGJH.getCode()));
                zbsyrec.setModule(BillnoType.CGJH.getModCode());
                zbsyrec.setDjzt("1"); // 单据状态
                // 设置预算金额
                if (StringUtils.isNotBlank(budgetIndex.getAmount())) {
                    zbsyrec.setDjje(new BigDecimal(budgetIndex.getAmount()));
                }
                zbsyrec.setMxxh(jhmxxh); // 设置明细ID关联

                gbiZbsyrecMapper.insert(zbsyrec);

                log.info("保存预算指标成功，jhbh：{}，jhmxxh：{}，金额：{}", jhbh, jhmxxh, budgetIndex.getAmount());
            }
        }

        log.info("多明细采购计划保存成功，jhbh：{}，明细数量：{}", jhbh, cgjhSaveDTO.getDetailsWithBudget().size());
    }

} 