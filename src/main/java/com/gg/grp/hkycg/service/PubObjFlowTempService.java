package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.FlowDto;
import com.gg.grp.hkycg.model.dto.FlowTemplateDTO;
import com.gg.grp.hkycg.model.vo.ObjflowVO;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.util.List;

/**
 * 工作流程模板服务接口
 */
public interface PubObjFlowTempService extends IService<PubObjFlowTemp> {

    /**
     * 根据单据类型ID查询工作流程模板列表
     * @param djlxid 单据类型ID
     * @return 工作流程模板列表
     */
    List<PubObjFlowTemp> selectListByDjlxid(Integer djlxid);

    /**
     * 根据条件查询工作流程模板
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param flowcode 流程代码
     * @param jddm 节点代码
     * @return 工作流程模板列表
     */
    List<PubObjFlowTemp> selectByCondition(String gsdm, String kjnd, String flowcode, Integer jddm);

    /**
     * 查询单据类型对应的节点列表
     * @param billTypeId 单据类型id
     * @return 单据类型对应的节点列表
     */
    List<FlowTemplateDTO> queryBillTypeNodes(Integer billTypeId);

    List<ObjflowVO> commitTemporary(List<FlowTemplateDTO> flowTemplateDtoList, FlowDto flowDto);
}