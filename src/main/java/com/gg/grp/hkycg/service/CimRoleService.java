package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.pojo.GpmRole;

import java.util.List;

/**
 * 角色服务接口
 */
public interface CimRoleService extends IService<GpmRole> {
    
    /**
     * 根据部门代码获取角色
     * @param bmdm 部门代码
     * @return 角色信息
     */
    GpmRole getRoleByBmdm(String bmdm);
    
    /**
     * 获取所有角色列表
     * @return 角色列表
     */
    List<GpmRole> getAllRoles();
    
    /**
     * 根据角色代码获取角色
     * @param roleCode 角色代码
     * @return 角色信息
     */
    GpmRole getRoleByCode(String roleCode);
} 