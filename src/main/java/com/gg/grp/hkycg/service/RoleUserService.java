package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.RoleDto;
import com.gg.grp.hkycg.model.vo.ModuleGivenDTO;
import com.gg.grp.hkycg.model.pojo.GpmRoleUser;

import java.util.List;

public interface RoleUserService extends IService<GpmRoleUser> {
    Result deleteRoleIndex(RoleDto roleDto);

    Result saveRoleIndex(RoleDto roleDto);

    Result queryRoleIndexList(ModuleGivenDTO moduleGivenDTO);

    Result deleteRoleUser(RoleDto roleDto);

    Result saveRoleUser(RoleDto roleDto);

    Result queryRoleUserList(ModuleGivenDTO moduleGivenDTO);

    Result saveRolePermissions(RoleDto roleDto);

    Result queryRolePermissions(RoleDto params);

    Result getAllIndexList(ModuleGivenDTO moduleGivenDTO);

    Result getAllPersonAndDeptList(ModuleGivenDTO moduleGivenDTO);

    List<String> queryUserAuthorizeList();
}
