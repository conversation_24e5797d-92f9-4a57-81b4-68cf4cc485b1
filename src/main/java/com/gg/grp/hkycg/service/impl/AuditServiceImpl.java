package com.gg.grp.hkycg.service.impl;

import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.PageResult;
import com.gg.grp.hkycg.mapper.AuditMapper;
import com.gg.grp.hkycg.model.dto.UnifiedAuditQueryDTO;
import com.gg.grp.hkycg.model.vo.UnifiedAuditListVO;
import com.gg.grp.hkycg.service.AuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 统一审核服务实现类
 */
@Service
@Slf4j
public class AuditServiceImpl implements AuditService {

    @Autowired
    private AuditMapper auditMapper;

    @Override
    public PageResult<UnifiedAuditListVO> getUnifiedAuditList(UnifiedAuditQueryDTO queryDTO) {
        String gsdm = LoginInfo.getCurrCorpCode();
        String kjnd = LoginInfo.getCurrAccountantYear();
        String auditorCode = LoginInfo.getCurrEmployeeCode();

        Integer offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

        List<UnifiedAuditListVO> records = auditMapper.getUnifiedAuditList(
                gsdm,
                kjnd,
                auditorCode,
                queryDTO.getCondition(),
                getBillTypeToString(queryDTO.getDjlx()),
                queryDTO.getStartDate(),
                queryDTO.getEndDate(),
                offset,
                queryDTO.getSize(),
                queryDTO.getZt() != null ? queryDTO.getZt().toString() : null
        );

        if (CollectionUtils.isEmpty(records)) {
            return new PageResult<>(0, Collections.emptyList());
        }

        long total = records.get(0).getTotalCount();
        return new PageResult<>(total, records);
    }

    public String getBillTypeToString(String billType) {
        if (StringUtils.isBlank(billType)){
            return null;
        }
        switch (billType) {
            case "CGJH":
                return "采购计划";
            case "CGSQ":
                return "采购申请";
            case "CGDJ":
                return "采购登记";
            case "CGZB":
                return "采购招标";
            case "CGYS":
                return "采购验收";
            default:
                return "";
        }
    }

} 