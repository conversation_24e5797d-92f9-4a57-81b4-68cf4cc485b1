package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.model.pojo.GpmGnfl;

import java.util.List;
import java.util.Map;

/**
 * URL授权管理服务接口
 */
public interface UrlAuthorizationService {
    
    /**
     * 检查用户是否有访问指定URL的权限
     * @param employeeCode 员工代码
     * @param url 前端URL
     * @return 是否有权限
     */
    boolean hasUrlPermission(String employeeCode, String url);
    
    /**
     * 根据员工代码获取其有权限访问的所有URL列表
     * @param employeeCode 员工代码
     * @return URL列表
     */
    List<String> getAuthorizedUrls(String employeeCode);
    
    /**
     * 根据员工代码获取其有权限的功能列表
     * @param employeeCode 员工代码
     * @return 功能列表
     */
    List<GpmGnfl> getAuthorizedFunctions(String employeeCode);
    
    /**
     * 根据部门代码获取角色代码
     * @param bmdm 部门代码
     * @return 角色代码
     */
    String getRoleCodeByDeptCode(String bmdm);
    
    /**
     * 获取用户权限信息摘要
     * @param employeeCode 员工代码
     * @return 权限摘要信息
     */
    Map<String, Object> getUserPermissionSummary(String employeeCode);
    
    /**
     * 刷新用户权限缓存
     * @param employeeCode 员工代码
     */
    void refreshUserPermissions(String employeeCode);
} 