package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.model.dto.AttachmentUploadDTO;
import com.gg.grp.hkycg.model.vo.AttachmentVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 附件服务接口
 */
public interface AttachmentService {
    
    /**
     * 上传附件
     * @param file 文件
     * @param dto 上传参数
     * @return 附件信息
     */
    AttachmentVO uploadAttachment(MultipartFile file, AttachmentUploadDTO dto);
    
    /**
     * 批量上传附件
     * @param files 文件列表
     * @param dto 上传参数
     * @return 附件信息列表
     */
    List<AttachmentVO> batchUploadAttachments(List<MultipartFile> files, AttachmentUploadDTO dto);
    
    /**
     * 查询单据关联的附件列表
     * @param djlx 单据类型
     * @param djh 单据号
     * @param sfmx 是否明细
     * @return 附件列表
     */
    List<AttachmentVO> queryAttachmentsByBill(String djlx, String djh, String sfmx);
    
    /**
     * 查询单据的目录、明细和所有相关附件
     * 先查询出目录和明细，再查询所有相关附件
     * @param djlx 单据类型
     * @param djh 单据号
     * @return 包含目录明细和附件列表的结果
     */
    Map<String, Object> queryAttachmentsWithDirectoryAndDetail(String djlx, String djh);
    
    /**
     * 删除附件
     * @param fjid 附件ID
     * @return 是否成功
     */
    boolean deleteAttachment(String fjid);
    
    /**
     * 下载附件
     * @param fjid 附件ID
     * @param response HTTP响应
     */
    void downloadAttachment(String fjid, HttpServletResponse response);
    
    /**
     * 预览附件
     * @param fjid 附件ID
     * @param response HTTP响应
     */
    void previewAttachment(String fjid, HttpServletResponse response);
} 