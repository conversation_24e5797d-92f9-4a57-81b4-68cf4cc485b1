package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.pojo.GpmRolegnfl;

import java.util.List;

/**
 * 角色功能关联服务接口
 */
public interface RolegnflService extends IService<GpmRolegnfl> {
    
    /**
     * 根据角色代码获取功能代码列表
     * @param roleCode 角色代码
     * @return 功能代码列表
     */
    List<String> getFunctionCodesByRole(String roleCode);
    
    /**
     * 根据功能代码获取角色代码列表
     * @param gnflCode 功能代码
     * @return 角色代码列表
     */
    List<String> getRoleCodesByFunction(String gnflCode);
    
    /**
     * 检查角色是否有指定功能权限
     * @param roleCode 角色代码
     * @param gnflCode 功能代码
     * @return 是否有权限
     */
    boolean hasPermission(String roleCode, String gnflCode);
    
    /**
     * 为角色添加功能权限
     * @param roleCode 角色代码
     * @param gnflCode 功能代码
     * @return 是否成功
     */
    boolean addPermission(String roleCode, String gnflCode);
    
    /**
     * 移除角色功能权限
     * @param roleCode 角色代码
     * @param gnflCode 功能代码
     * @return 是否成功
     */
    boolean removePermission(String roleCode, String gnflCode);
} 