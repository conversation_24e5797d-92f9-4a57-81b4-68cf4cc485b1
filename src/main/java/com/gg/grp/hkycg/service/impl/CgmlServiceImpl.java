package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.mapper.HbgZfcgmlMapper;
import com.gg.grp.hkycg.model.dto.CgmlPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgmlVO;
import com.gg.grp.hkycg.model.pojo.HbgZfcgml;
import com.gg.grp.hkycg.service.CgmlService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 采购品目服务实现类
 */
@Service
public class CgmlServiceImpl extends ServiceImpl<HbgZfcgmlMapper, HbgZfcgml> implements CgmlService {
    
    @Override
    public IPage<CgmlVO> pageQuery(CgmlPageQueryDTO queryDTO) {
        // 创建分页对象
        Page<HbgZfcgml> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        
        // 构建查询条件
        LambdaQueryWrapper<HbgZfcgml> queryWrapper = new LambdaQueryWrapper<>();
        
        // 采购品目代码模糊查询
        if (StringUtils.isNotBlank(queryDTO.getCgmldm())) {
            queryWrapper.like(HbgZfcgml::getCgmldm, queryDTO.getCgmldm());
        }
        
        // 采购品目名称模糊查询
        if (StringUtils.isNotBlank(queryDTO.getCgmlmc())) {
            queryWrapper.like(HbgZfcgml::getCgmlmc, queryDTO.getCgmlmc());
        }
        
        // 按采购品目代码排序
        queryWrapper.orderByAsc(HbgZfcgml::getCgmldm);
        
        // 执行分页查询
        IPage<HbgZfcgml> pageResult = this.page(page, queryWrapper);
        
        // 转换为VO对象
        Page<CgmlVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(pageResult.getRecords().stream().map(this::convertToVO).collect(java.util.stream.Collectors.toList()));
        
        return voPage;
    }
    
    /**
     * 实体转换为VO
     * @param entity 实体对象
     * @return VO对象
     */
    private CgmlVO convertToVO(HbgZfcgml entity) {
        CgmlVO vo = new CgmlVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
