package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.model.pojo.GpmRole;
import com.gg.grp.hkycg.mapper.CimRoleMapper;
import com.gg.grp.hkycg.service.CimRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色服务实现类
 */
@Service
public class CimRoleServiceImpl extends ServiceImpl<CimRoleMapper, GpmRole> implements CimRoleService {

    @Override
    public GpmRole getRoleByBmdm(String bmdm) {
        LambdaQueryWrapper<GpmRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmRole::getBmdm, bmdm);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<GpmRole> getAllRoles() {
        return this.list();
    }

    @Override
    public GpmRole getRoleByCode(String roleCode) {
        LambdaQueryWrapper<GpmRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GpmRole::getRoleCode, roleCode);
        return this.getOne(queryWrapper);
    }
} 