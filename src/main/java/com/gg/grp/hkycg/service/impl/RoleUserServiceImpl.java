package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.RoleDto;
import com.gg.grp.hkycg.model.vo.AllPersonAndDeptListVO;
import com.gg.grp.hkycg.model.vo.ModuleGivenDTO;
import com.gg.grp.hkycg.model.vo.RoleUserIndexListVO;
import com.gg.grp.hkycg.model.vo.RoleUserListVO;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.RoleUserService;
import com.gg.grp.hkycg.service.RolezbService;
import com.gg.grp.hkycg.service.SqbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RoleUserServiceImpl extends ServiceImpl<RoleUserMapper, GpmRoleUser> implements RoleUserService {


    @Autowired
    private RolezbMapper rolezbMapper;

    @Autowired
    private SqbMapper sqbMapper;

    @Autowired
    private SqbService sqbService;

    @Autowired
    private RolezbService rolezbService;

    @Autowired
    private RoleGnflMapper roleGnflMapper;

    @Autowired
    private GbiZbxmbMapper gbiZbxmbMapper;

    @Override
    public Result deleteRoleIndex(RoleDto roleDto) {
        try {
            JSONArray indexLists = JSONArray.parseArray(roleDto.getIndexLists());

            List<String> zbidList = indexLists.stream().map(e -> JSONObject.parseObject(String.valueOf(e)).getString("ZBID")).collect(Collectors.toList());

            LambdaQueryWrapper<GpmRolezb> rolezbWrapper = new LambdaQueryWrapper<>();
            rolezbWrapper.eq(GpmRolezb::getGsdm,LoginInfo.getCurrCorpCode())
                    .eq(GpmRolezb::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRolezb::getRolecode,roleDto.getRoleCode())
                    .in(GpmRolezb::getZbid,zbidList);
            rolezbMapper.delete(rolezbWrapper);

            //通过角色查找角色人员
            LambdaQueryWrapper<GpmRoleUser> roleUserWrapper = new LambdaQueryWrapper<>();
            roleUserWrapper.eq(GpmRoleUser::getGsdm,LoginInfo.getCurrCorpCode())
                    .eq(GpmRoleUser::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRoleUser::getRolecode,roleDto.getRoleCode());
            List<GpmRoleUser> roleUsers = baseMapper.selectList(roleUserWrapper);

            //遍历授权人 批量删除对应的zbid
            for (GpmRoleUser roleUser :roleUsers){
                LambdaQueryWrapper<GpmSqb> sqbWrapper = new LambdaQueryWrapper<>();
                sqbWrapper.eq(GpmSqb::getGsdm,LoginInfo.getCurrCorpCode())
                        .eq(GpmSqb::getKjnd, LoginInfo.getCurrAccountantYear())
                        .eq(GpmSqb::getRolecode,roleDto.getRoleCode())
                        .eq(GpmSqb::getSqren,"1")
                        .eq(GpmSqb::getBsqren,roleUser.getZydm())
                        .in(GpmSqb::getZbid,zbidList);
                        sqbMapper.delete(sqbWrapper);
            }

            return Result.success("删除成功!");
        }catch (Exception ex){
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public Result saveRoleIndex(RoleDto roleDto) {
        try {

            JSONArray indexLists = JSONArray.parseArray(roleDto.getIndexLists());

            //通过角色查找角色人员
            LambdaQueryWrapper<GpmRoleUser> roleUserWrapper = new LambdaQueryWrapper<>();
            roleUserWrapper.eq(GpmRoleUser::getGsdm,LoginInfo.getCurrCorpCode())
                    .eq(GpmRoleUser::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRoleUser::getRolecode,roleDto.getRoleCode());
            List<GpmRoleUser> roleUsers = baseMapper.selectList(roleUserWrapper);

            List<GpmRolezb> roleZbList = new ArrayList<>();

            List<GpmSqb> sqbList = new ArrayList<>();

            for (int i = 0; i < indexLists.size(); i++) {
                JSONObject json = indexLists.getJSONObject(i);

                GpmRolezb rolezb = new GpmRolezb();
                rolezb.setGsdm(LoginInfo.getCurrCorpCode());
                rolezb.setKjnd(LoginInfo.getCurrAccountantYear());
                rolezb.setRolecode(roleDto.getRoleCode());
                if(roleDto.getSqr()!=null){
                    rolezb.setSqren(roleDto.getSqr());
                }else {
                    rolezb.setSqren("1");
                }
                rolezb.setJzrq("");
                //暂定前端传我
                rolezb.setSyqx(json.getString("syqx"));
                rolezb.setZbid(json.getInteger("indexID"));

                roleZbList.add(rolezb);


                for (int j = 0; j < roleUsers.size(); j++) {
                    //保存CIM_SQB
                    GpmSqb sqb = new GpmSqb();
                    sqb.setGsdm(LoginInfo.getCurrCorpCode());
                    sqb.setKjnd(LoginInfo.getCurrAccountantYear());
                    if(roleDto.getSqr()!=null){
                        sqb.setSqren(roleDto.getSqr());
                    }else {
                        sqb.setSqren("1");
                    }

                    sqb.setBmdm(json.getString("deptCode"));
                    sqb.setBz("公共指标");
                    sqb.setSyqx(json.getString("syqx"));
                    //暂定CIM_SQB中保存角色代码
                    sqb.setRolecode(roleDto.getRoleCode());
                    //指标ID
                    sqb.setZbid(json.getString("indexID"));
                    sqb.setBsqren(roleUsers.get(j).getZydm());

                    sqbList.add(sqb);
                }

            }

            //批量插入角色指标
            if (!roleZbList.isEmpty()){
                rolezbService.saveBatch(roleZbList);
            }

            //批量插入授权表
            if (!sqbList.isEmpty()){
                sqbService.saveBatch(sqbList);
            }

            return Result.success("保存成功!");
        }catch (Exception ex){
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public Result queryRoleIndexList(ModuleGivenDTO moduleGivenDTO) {
        moduleGivenDTO.setKjnd(LoginInfo.getCurrAccountantYear());
        moduleGivenDTO.setGsdm(LoginInfo.getCurrCorpCode());
        List<RoleUserIndexListVO> resultList = rolezbMapper.queryRoleIndexList(moduleGivenDTO);
        return Result.success(resultList);
    }

    @Override
    public Result deleteRoleUser(RoleDto roleDto) {
        try {
            JSONArray roleUserLists = JSONArray.parseArray(roleDto.getRoleUserLists());

            List<String> zydmList = roleUserLists.stream().map(e -> JSONObject.parseObject(String.valueOf(e)).getString("zydm")).collect(Collectors.toList());

            LambdaQueryWrapper<GpmRoleUser> roleUserWrapper = new LambdaQueryWrapper<>();
            roleUserWrapper.eq(GpmRoleUser::getGsdm,LoginInfo.getCurrCorpCode())
                    .eq(GpmRoleUser::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRoleUser::getRolecode,roleDto.getRoleCode())
                    .in(GpmRoleUser::getZydm,zydmList);
            baseMapper.delete(roleUserWrapper);

            //通过角色查找授权指标zbidList
            LambdaQueryWrapper<GpmRolezb> rolezbWrapper = new LambdaQueryWrapper<>();
            rolezbWrapper.eq(GpmRolezb::getGsdm,LoginInfo.getCurrCorpCode())
                    .eq(GpmRolezb::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRolezb::getRolecode,roleDto.getRoleCode());
            List<GpmRolezb> rolezbs = rolezbMapper.selectList(rolezbWrapper);

            List<Integer> zbidList = rolezbs.stream().map(GpmRolezb::getZbid).collect(Collectors.toList());

            //遍历授权人 批量删除对应的zbid
            if (!zbidList.isEmpty()){
                for (String zydm : zydmList) {
                    int batchCount = 500;
                    int batchLastIndex = batchCount;
                    for (int index = 0; index < zbidList.size(); ) {
                        if (batchLastIndex >= zbidList.size()) {
                            batchLastIndex = zbidList.size();
                            LambdaQueryWrapper<GpmSqb> sqbWrapper = new LambdaQueryWrapper<>();
                            sqbWrapper.eq(GpmSqb::getGsdm,LoginInfo.getCurrCorpCode())
                                    .eq(GpmSqb::getKjnd, LoginInfo.getCurrAccountantYear())
                                    .eq(GpmSqb::getRolecode,roleDto.getRoleCode())
                                    .eq(GpmSqb::getSqren,"1")
                                    .eq(GpmSqb::getBsqren,zydm)
                                    .in(GpmSqb::getZbid,zbidList.subList(
                                            index, batchLastIndex));
                            sqbService.remove(sqbWrapper);
                            break;// 数据删除完毕，退出循环
                        } else {
                            LambdaQueryWrapper<GpmSqb> sqbWrapper = new LambdaQueryWrapper<>();
                            sqbWrapper.eq(GpmSqb::getGsdm,LoginInfo.getCurrCorpCode())
                                    .eq(GpmSqb::getKjnd, LoginInfo.getCurrAccountantYear())
                                    .eq(GpmSqb::getRolecode,roleDto.getRoleCode())
                                    .eq(GpmSqb::getSqren,"1")
                                    .eq(GpmSqb::getBsqren,zydm)
                                    .in(GpmSqb::getZbid,zbidList.subList(
                                            index, batchLastIndex));
                            sqbService.remove(sqbWrapper);
                            index = batchLastIndex;// 设置下一批下标
                            batchLastIndex = index + batchCount;
                        }
                    }
                }
            }
            return Result.success("删除成功!");
        }catch (Exception ex){
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public Result saveRoleUser(RoleDto roleDto) {
        try {

            JSONArray roleUserLists = JSONArray.parseArray(roleDto.getRoleUserLists());

            //通过角色查找授权指标zbidList
            LambdaQueryWrapper<GpmRolezb> rolezbWrapper = new LambdaQueryWrapper<>();
            rolezbWrapper.eq(GpmRolezb::getGsdm,LoginInfo.getCurrCorpCode())
                    .eq(GpmRolezb::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRolezb::getRolecode,roleDto.getRoleCode());

            List<GpmRolezb> rolezbs = rolezbMapper.selectList(rolezbWrapper);

            List<GpmRoleUser> roleUserList = new ArrayList<>();

            List<GpmSqb> sqbList = new ArrayList<>();


            for (int i = 0; i < roleUserLists.size(); i++) {
                JSONObject json = roleUserLists.getJSONObject(i);

                GpmRoleUser model = new GpmRoleUser();
                model.setGsdm(LoginInfo.getCurrCorpCode());
                model.setKjnd(LoginInfo.getCurrAccountantYear());
                model.setRolecode(roleDto.getRoleCode());
                model.setZydm(json.getString("zydm"));

                roleUserList.add(model);

                for (GpmRolezb rolezb : rolezbs) {

                    //保存CIM_SQB
                    GpmSqb sqb = new GpmSqb();
                    sqb.setGsdm(LoginInfo.getCurrCorpCode());
                    sqb.setKjnd(LoginInfo.getCurrAccountantYear());
                    sqb.setSqren("1");
                    sqb.setBmdm(json.getString("deptCode"));
                    sqb.setBz("公共指标");
                    //暂定CIM_SQB中保存角色代码
                    sqb.setRolecode(roleDto.getRoleCode());
                    //职员代码
                    sqb.setBsqren(json.getString("zydm"));
                    sqb.setZbid(String.valueOf(rolezb.getZbid()));
                    sqb.setSyqx(rolezb.getSyqx());

                    sqbList.add(sqb);
                }


            }
            //批量插入角色人员
            if (!roleUserList.isEmpty()){
                this.saveBatch(roleUserList);
            }
            //批量插入授权表
            if (!sqbList.isEmpty()){
                sqbService.saveBatch(sqbList);
            }


            return Result.success("批量保存成功!");
        }catch (Exception ex){
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public Result queryRoleUserList(ModuleGivenDTO moduleGivenDTO) {
        moduleGivenDTO.setKjnd(LoginInfo.getCurrAccountantYear());
        moduleGivenDTO.setGsdm(LoginInfo.getCurrCorpCode());

        List<RoleUserListVO> resultList = baseMapper.queryRoleUserList(moduleGivenDTO);
        return Result.success(resultList);
    }

    @Override
    public Result saveRolePermissions(RoleDto roleDto) {
        try {
            LambdaQueryWrapper<GpmRolegnfl> rolegnflWrapper = new LambdaQueryWrapper<>();
            rolegnflWrapper.eq(GpmRolegnfl::getRolecode, roleDto.getRoleCode());
            roleGnflMapper.delete(rolegnflWrapper);
            JSONArray gnflLists = JSONArray.parseArray(roleDto.getGnflLists());

            GpmRolegnfl rolegnfl = new GpmRolegnfl();
            rolegnfl.setRolecode(roleDto.getRoleCode());
            for (int i = 0; i < gnflLists.size(); i++) {
                JSONObject json = gnflLists.getJSONObject(i);
                rolegnfl.setGnflcode(json.getString("id"));
                roleGnflMapper.insert(rolegnfl);
            }
            return Result.success("保存成功!");
        }catch (Exception ex){
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public Result queryRolePermissions(RoleDto roleDto) {
        List<JSONObject> gnflLists = baseMapper.getGNFLByRole(roleDto);
        //过滤角色权限项
        List<JSONObject> newGnflLists = gnflLists.stream().filter(e -> e.getInteger("CODE") != 8013).collect(Collectors.toList());


        List<JSONObject> resultList = new ArrayList<>();

        newGnflLists.forEach(e->{

            JSONObject json = new JSONObject();
            json.put("pId",e.getString("pCode"));
            json.put("id",e.getString("CODE"));
            json.put("codeName","["+e.getString("CODE")+"]"+e.getString("NAME"));
            if (StringUtils.isEmpty(e.getString("GNFLCODE"))){
                json.put("isCheck",false);
            }else {
                json.put("isCheck",true);
            }
            resultList.add(json);

        });

        return Result.success(resultList);
    }

    @Override
    public Result getAllIndexList(ModuleGivenDTO moduleGivenDTO) {
        Map<String, Object> temp = new HashMap<>();
        temp.put("gsdm", LoginInfo.getCurrCorpCode());
        temp.put("kjnd", LoginInfo.getCurrAccountantYear());
        temp.put("pageSize", moduleGivenDTO.getPageNum());
        temp.put("pageNo", moduleGivenDTO.getPages());
        temp.put("endDate",LoginInfo.getCurrBusiDate().replace("-" ,""));
        temp.put("roleCode", moduleGivenDTO.getRoleCode());
        if (StringUtils.isNotEmpty(moduleGivenDTO.getCondition())) {
            temp.put("condition", "%" + moduleGivenDTO.getCondition() + "%");
        }

        List<JSONObject> returnList = gbiZbxmbMapper.getAllIndexList(temp);

        return Result.success(returnList);
    }

    @Override
    public Result getAllPersonAndDeptList(ModuleGivenDTO moduleGivenDTO) {
        moduleGivenDTO.setGsdm(LoginInfo.getCurrCorpCode());
        moduleGivenDTO.setKjnd(LoginInfo.getCurrAccountantYear());
        if (StringUtils.isNotEmpty(moduleGivenDTO.getCondition())){
            moduleGivenDTO.setCondition("%"+moduleGivenDTO.getCondition()+"%");
        }

        List<AllPersonAndDeptListVO> resultList = baseMapper.getAllPersonAndDeptList(
                moduleGivenDTO.getGsdm(),
                moduleGivenDTO.getKjnd(),
                moduleGivenDTO.getRoleCode(),
                moduleGivenDTO.getCondition(),
                moduleGivenDTO.getPages(),
                moduleGivenDTO.getPageNum()
        );

        return Result.success(resultList);
    }

    @Override
    public List<String> queryUserAuthorizeList() {
        return baseMapper.queryUserAuthorizeList(
                LoginInfo.getCurrCorpCode(),
                LoginInfo.getCurrAccountantYear(),
                LoginInfo.getCurrEmployeeCode()
        );
    }
}
