package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.GpmCgfsMapper;
import com.gg.grp.hkycg.model.dto.CgfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgfsListVO;
import com.gg.grp.hkycg.model.vo.CgfsVO;
import com.gg.grp.hkycg.model.pojo.GpmCgfs;
import com.gg.grp.hkycg.service.CgfsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购方式Service实现类
 */
@Slf4j
@Service
public class CgfsServiceImpl extends ServiceImpl<GpmCgfsMapper, GpmCgfs> implements CgfsService {

    @Autowired
    private GpmCgfsMapper cgfsMapper;

    @Override
    public List<CgfsVO> getAllCgfs() {
        try {
            // 构建查询条件
            LambdaQueryWrapper<GpmCgfs> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgfs::getGsdm, LoginInfo.getCurrCorpCode())
                       .eq(GpmCgfs::getKjnd, LoginInfo.getCurrAccountantYear())
                       .ne(GpmCgfs::getSyzt, "0")
                       .orderByAsc(GpmCgfs::getPxh); // 按排序号升序

            // 查询数据
            List<GpmCgfs> cgfsList = cgfsMapper.selectList(queryWrapper);

            // 转换为VO对象
            List<CgfsVO> cgfsVOList = cgfsList.stream()
                    .map(cgfs -> {
                        CgfsVO cgfsVO = new CgfsVO();
                        BeanUtils.copyProperties(cgfs, cgfsVO);
                        return cgfsVO;
                    })
                    .collect(Collectors.toList());

            log.info("查询采购方式成功，共{}条记录", cgfsVOList.size());
            return cgfsVOList;

        } catch (Exception e) {
            log.error("查询采购方式失败", e);
            throw new RuntimeException("查询采购方式失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgfsListVO> getCgfsPageList(CgfsPageQueryDTO queryDTO) {
        try {
            log.info("分页查询采购方式，查询条件：{}", queryDTO);

            // 设置默认公司代码和年度
            if (StringUtils.isBlank(queryDTO.getGsdm())) {
                queryDTO.setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(queryDTO.getKjnd())) {
                queryDTO.setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 调用Mapper查询
            List<CgfsListVO> resultList = cgfsMapper.getCgfsPageList(queryDTO);

            // 状态转换
            resultList.forEach(item -> {
                // 使用状态转换：1->启用，0->停用
                if ("1".equals(item.getSyzt())) {
                    item.setSyztmc("启用");
                } else if ("0".equals(item.getSyzt())) {
                    item.setSyztmc("停用");
                } else {
                    item.setSyztmc(item.getSyzt());
                }
            });

            log.info("分页查询采购方式完成，返回记录数：{}", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("分页查询采购方式失败，查询条件：{}", queryDTO, e);
            throw new RuntimeException("分页查询采购方式失败：" + e.getMessage(), e);
        }
    }
} 