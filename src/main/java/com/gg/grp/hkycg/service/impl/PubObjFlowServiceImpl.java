package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.mapper.PubObjFlowMapper;
import com.gg.grp.hkycg.model.pojo.PubObjFlow;
import com.gg.grp.hkycg.service.PubObjFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工作流程实例服务实现类
 */
@Service
public class PubObjFlowServiceImpl extends ServiceImpl<PubObjFlowMapper, PubObjFlow> implements PubObjFlowService {

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Override
    public int insert(PubObjFlow pubObjFlow) {
        return pubObjFlowMapper.insert(pubObjFlow);
    }

    @Override
    public void deleteByCon(String modCode, String djlx, String djh) {
        pubObjFlowMapper.deleteByCon(modCode, djlx, djh);
    }

    @Override
    public List<PubObjFlow> selectByBillno(String djh) {
        return pubObjFlowMapper.selectByBillno(djh);
    }

    @Override
    public PubObjFlow selectNowNodeByDjh(String modCode, String djlx, String djh) {
        return pubObjFlowMapper.selectNowNodeByDjh(modCode, djlx, djh);
    }

    @Override
    public PubObjFlow selectLastAuditNodeByDjh(String modCode, String djlx, String djh) {
        return pubObjFlowMapper.selectLastAuditNodeByDjh(modCode, djlx, djh);
    }
} 