package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.CglxPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CglxListVO;
import com.gg.grp.hkycg.model.vo.CglxVO;
import com.gg.grp.hkycg.model.pojo.GpmCglx;

import java.util.List;

/**
 * 采购类型Service接口
 */
public interface CglxService extends IService<GpmCglx> {

    /**
     * 查询所有采购类型
     * @return 采购类型列表
     */
    List<CglxVO> getAllCglx();

    /**
     * 分页查询采购类型列表
     * @param queryDTO 查询参数
     * @return 采购类型列表
     */
    List<CglxListVO> getCglxPageList(CglxPageQueryDTO queryDTO);
} 