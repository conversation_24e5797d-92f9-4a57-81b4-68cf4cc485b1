package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.BudgetIndexDTO;
import com.gg.grp.hkycg.model.pojo.BudgetIndex;

import java.util.List;

/**
 * 预算指标服务接口
 */
public interface BudgetIndexService extends IService<BudgetIndex> {

    /**
     * 保存预算指标列表
     * @param budgetIndexDTOList 预算指标DTO列表
     * @param lid 业务ID
     * @return 保存后的预算指标DTO列表
     */
    List<BudgetIndexDTO> saveList(List<BudgetIndexDTO> budgetIndexDTOList, String lid);

    /**
     * 更新预算指标列表
     * @param budgetIndexDTOList 预算指标DTO列表
     * @param lid 业务ID
     * @return 更新后的预算指标DTO列表
     */
    List<BudgetIndexDTO> updateList(List<BudgetIndexDTO> budgetIndexDTOList, String lid);

    /**
     * 根据业务ID查询预算指标列表
     * @param lid 业务ID
     * @return 预算指标DTO列表
     */
    List<BudgetIndexDTO> findByBid(String lid);

    /**
     * 根据业务ID删除预算指标
     * @param lid 业务ID
     */
    void del(String lid);
} 