package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.DeptPageQueryDTO;
import com.gg.grp.hkycg.model.vo.DeptListVO;
import com.gg.grp.hkycg.model.pojo.Dept;

/**
 * 部门服务接口
 */
public interface DeptService extends IService<Dept> {
    
    /**
     * 根据部门代码获取部门信息
     * @param deptCode 部门代码
     * @return 部门信息
     */
    Dept getByDeptCode(String deptCode);
    
    /**
     * 根据部门代码获取部门名称
     * @param deptCode 部门代码
     * @return 部门名称
     */
    String getDeptNameByCode(String deptCode);
    
    /**
     * 根据部门代码查询部门信息
     * @param deptCode 部门代码
     * @return 部门模型
     */
    Dept qryDeptByCode(String deptCode);

    IPage<DeptListVO> queryAllDept(DeptPageQueryDTO queryDTO);
    
    /**
     * 查询部门信息并关联负责人姓名
     * @param queryDTO 查询参数
     * @return 部门列表VO分页结果
     */
    IPage<DeptListVO> queryAllDeptWithResponsiblePerson(DeptPageQueryDTO queryDTO);
}