package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.StatusName;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.CgzbsqService;
import com.gg.grp.hkycg.service.PubObjFlowService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.ConvertUtils;
import com.gg.grp.hkycg.utils.DataPermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 招标申请Service实现类
 */
@Slf4j
@Service
public class CgzbsqServiceImpl extends ServiceImpl<GpmCgzbsqmlMapper, GpmCgzbsqml> implements CgzbsqService {

    @Autowired
    private GpmCgzbsqmlMapper cgzbsqmlMapper;

    @Autowired
    private GpmCgzbsqnrMapper cgzbsqnrMapper;

    @Autowired
    private PubAuditLogMapper pubAuditLogMapper;

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Autowired
    private PubWorkflowMapper pubWorkflowMapper;

    @Autowired
    private PubObjFlowService pubObjFlowService;

    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    @Autowired
    private GpmCgsqnrMapper cgsqnrMapper;

    @Autowired
    private GpmCgsqmlMapper cgsqmlMapper;

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;

    @Autowired
    private DataPermissionUtils dataPermissionUtils;


    @Override
    public CgzbsqSaveResponseVO saveCgzbsq(CgzbsqSaveDTO cgzbsqSaveDTO) {
        try {
            for (CgzbsqDetailDTO detail : cgzbsqSaveDTO.getCgzbsqDetails()) {
                if (!detail.getSfcgsqyr() || StringUtils.isBlank(detail.getSqmxxh())) {
                    throw new GlobalException("招标申请明细的申请明细序号不能为空");
                }
                LambdaQueryWrapper<GpmCgsqnr> nrWrapper = new LambdaQueryWrapper<>();
                nrWrapper.eq(GpmCgsqnr::getSqmxxh, detail.getSqmxxh());
                nrWrapper.eq(GpmCgsqnr::getSqbh, detail.getSqbh());
                if (cgsqnrMapper.selectOne(nrWrapper) == null) {
                    throw new GlobalException("招标申请明细不存在");
                }
                LambdaQueryWrapper<GpmCgsqml> mlWrapper = new LambdaQueryWrapper<>();
                mlWrapper.eq(GpmCgsqml::getSqbh, detail.getSqbh());
                GpmCgsqml cgsqml = cgsqmlMapper.selectOne(mlWrapper);
                if (cgsqml == null || !cgsqml.getZt().equals("4")) {
                    throw new GlobalException("招标申请明细不存在或招标申请未审核");
                }
                 // TODO: 检查明细是否已经被引入过
//                LambdaQueryWrapper<GpmCgzbsqnr> zbsqnrWrapper = new LambdaQueryWrapper<>();
//                zbsqnrWrapper.eq(GpmCgzbsqnr::getSqmxxh, detail.getSqmxxh())
//                        .eq(GpmCgzbsqnr::getGsdm, LoginInfo.getCurrCorpCode())
//                        .eq(GpmCgzbsqnr::getKjnd, LoginInfo.getCurrAccountantYear());
//                List<GpmCgzbsqnr> zbsqnrs = cgzbsqnrMapper.selectList(zbsqnrWrapper);
//                for (GpmCgzbsqnr zbsqnr : zbsqnrs){
//
//                }
//                if (zbsqnr!=null || detail.getBccgje().compareTo(zbsqnr.getBccgje())==0 || !detail.getBccgsl().equals(zbsqnr.getBccgsl())){
//                    throw new GlobalException("采购申请此条明细已经被引入过了");
//                }
            }
            // 设置默认值
            if (StringUtils.isBlank(cgzbsqSaveDTO.getBaseInfo().getGsdm())) {
                cgzbsqSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(cgzbsqSaveDTO.getBaseInfo().getKjnd())) {
                cgzbsqSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 生成招标申请编号（主键）
            String zbsqbh = generateZbsqbh();
            cgzbsqSaveDTO.getBaseInfo().setZbsqbh(zbsqbh);

            // 1. 保存招标申请主表
            GpmCgzbsqml cgzbsqml = new GpmCgzbsqml();
            BeanUtils.copyProperties(cgzbsqSaveDTO.getBaseInfo(), cgzbsqml);

            // 设置系统字段
            String currentDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            cgzbsqml.setCjsj(currentDateTime);
            cgzbsqml.setCjrdm(LoginInfo.getCurrEmployeeCode());
            cgzbsqml.setCjrmc(LoginInfo.getCurrEmployeeName());
            cgzbsqml.setZt("1"); // 默认保存状态
            cgzbsqmlMapper.insert(cgzbsqml);
            log.info("招标申请主表保存成功，zbsqbh：{}", zbsqbh);

            // 2. 保存招标申请明细
            boolean hasDetails = cgzbsqSaveDTO.getCgzbsqDetails() != null && !cgzbsqSaveDTO.getCgzbsqDetails().isEmpty();
            if (hasDetails) {
                for (int i = 0; i < cgzbsqSaveDTO.getCgzbsqDetails().size(); i++) {
                    CgzbsqDetailDTO detail = cgzbsqSaveDTO.getCgzbsqDetails().get(i);
                    String zbmxxh = generateZbmxxh(i);

                    GpmCgzbsqnr cgzbsqnr = new GpmCgzbsqnr();
                    BeanUtils.copyProperties(detail, cgzbsqnr);

                    cgzbsqnr.setZbsqbh(zbsqbh);
                    cgzbsqnr.setZbmxxh(zbmxxh);
                    cgzbsqnr.setGsdm(LoginInfo.getCurrCorpCode());
                    cgzbsqnr.setKjnd(LoginInfo.getCurrAccountantYear());
                    cgzbsqnr.setCjsj(currentDateTime);
                    cgzbsqnr.setCjrdm(LoginInfo.getCurrEmployeeCode());
                    cgzbsqnr.setCjrmc(LoginInfo.getCurrEmployeeName());
                    cgzbsqnr.setSfcgsqyr(ConvertUtils.convertBooleanToString(detail.getSfcgsqyr()));
                    cgzbsqnr.setZfcg(ConvertUtils.convertBooleanToString(detail.getZfcg()));
                    cgzbsqnr.setJkcp(ConvertUtils.convertBooleanToString(detail.getJkcp()));

                    cgzbsqnrMapper.insert(cgzbsqnr);
                    log.info("保存招标申请明细成功，zbsqbh：{}，zbmxxh：{}", zbsqbh, zbmxxh);
                }
            }

            log.info("招标申请保存成功，zbsqbh：{}，明细数量：{}",
                    zbsqbh,
                    hasDetails ? cgzbsqSaveDTO.getCgzbsqDetails().size() : 0);

            return buildSaveResponseVO(zbsqbh);

        } catch (Exception e) {
            log.error("保存招标申请失败", e);
            throw new GlobalException("保存招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgzbsqSaveResponseVO updateCgzbsqByZbsqbh(String zbsqbh, CgzbsqSaveDTO cgzbsqSaveDTO) {
        try {
            // 参数校验
            if (cgzbsqSaveDTO == null || cgzbsqSaveDTO.getBaseInfo() == null) {
                throw new GlobalException("招标申请基础信息不能为空");
            }

            // 检查明细信息
            if (cgzbsqSaveDTO.getCgzbsqDetails() == null || cgzbsqSaveDTO.getCgzbsqDetails().isEmpty()) {
                throw new GlobalException("招标申请明细不能为空");
            }

            // 检查招标申请是否存在
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml existingCgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);
            if (existingCgzbsqml == null) {
                throw new GlobalException("招标申请不存在!");
            }
            if (!existingCgzbsqml.getZt().equals("5") && !existingCgzbsqml.getZt().equals("1")) {
                throw new GlobalException("采购招标状态不是保存或退回，不能更新");
            }

            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = existingCgzbsqml.getCjrdm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 更新操作，当前用户：{}，录入人：{}", currentUser, originalInputUser);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能修改此采购计划");
            }
            log.info("权限校验通过 - 更新操作，当前用户：{}，jhbh：{}", currentUser, existingCgzbsqml.getZbsqbh());

            // 1. 更新招标申请主表
            GpmCgzbsqml cgzbsqml = new GpmCgzbsqml();
            BeanUtils.copyProperties(cgzbsqSaveDTO.getBaseInfo(), cgzbsqml);

            // 保留原有的创建信息
            cgzbsqml.setCjsj(existingCgzbsqml.getCjsj());
            cgzbsqml.setXgsj(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            // 更新主表
            LambdaUpdateWrapper<GpmCgzbsqml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            int result = cgzbsqmlMapper.update(cgzbsqml, updateWrapper);
            log.info("主表更新结果：{}", result);

            // 2. 删除原有的明细记录
            LambdaUpdateWrapper<GpmCgzbsqnr> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.eq(GpmCgzbsqnr::getZbsqbh, zbsqbh);
            int deletedDetails = cgzbsqnrMapper.delete(deleteWrapper);
            log.info("删除招标申请明细记录{}条", deletedDetails);

            // 3. 重新保存新的明细
            String currentDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            for (int i = 0; i < cgzbsqSaveDTO.getCgzbsqDetails().size(); i++) {
                CgzbsqDetailDTO detail = cgzbsqSaveDTO.getCgzbsqDetails().get(i);
                String zbmxxh = generateZbmxxh(i);

                GpmCgzbsqnr cgzbsqnr = new GpmCgzbsqnr();
                BeanUtils.copyProperties(detail, cgzbsqnr);

                cgzbsqnr.setZbsqbh(zbsqbh);
                cgzbsqnr.setZbmxxh(zbmxxh);
                cgzbsqnr.setGsdm(LoginInfo.getCurrCorpCode());
                cgzbsqnr.setKjnd(LoginInfo.getCurrAccountantYear());
                cgzbsqnr.setCjsj(currentDateTime);
                cgzbsqnr.setCjrdm(LoginInfo.getCurrEmployeeCode());
                cgzbsqnr.setCjrmc(LoginInfo.getCurrEmployeeName());
                cgzbsqnr.setSfcgsqyr(ConvertUtils.convertBooleanToString(detail.getSfcgsqyr()));
                cgzbsqnr.setZfcg(ConvertUtils.convertBooleanToString(detail.getZfcg()));
                cgzbsqnr.setJkcp(ConvertUtils.convertBooleanToString(detail.getJkcp()));

                cgzbsqnrMapper.insert(cgzbsqnr);
                log.info("保存招标申请明细成功，zbsqbh：{}，zbmxxh：{}", zbsqbh, zbmxxh);
            }

            if (existingCgzbsqml.getZt().equals("5")) {
                updateStatusByZbsqbh(zbsqbh, 1);
            }

            log.info("更新招标申请成功，zbsqbh：{}，明细数量：{}", zbsqbh, cgzbsqSaveDTO.getCgzbsqDetails().size());

            return buildSaveResponseVO(zbsqbh);

        } catch (Exception e) {
            log.error("更新招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("更新招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteByZbsqbh(String zbsqbh) {
        try {
            // 查询招标申请是否存在
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，无法删除，zbsqbh: " + zbsqbh);
            }

            // 1. 删除主表
            LambdaUpdateWrapper<GpmCgzbsqml> mlWrapper = new LambdaUpdateWrapper<>();
            mlWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            int deleteml = cgzbsqmlMapper.delete(mlWrapper);

            // 2. 删除明细表
            LambdaUpdateWrapper<GpmCgzbsqnr> detailWrapper = new LambdaUpdateWrapper<>();
            detailWrapper.eq(GpmCgzbsqnr::getZbsqbh, zbsqbh);
            int deleteDetails = cgzbsqnrMapper.delete(detailWrapper);

            log.info("删除招标申请成功，zbsqbh：{}，删除主表记录：{}，删除明细记录：{}",
                    zbsqbh, deleteml, deleteDetails);

            return deleteml > 0;

        } catch (Exception e) {
            log.error("删除招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("删除招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgzbsqSaveResponseVO getCgzbsqDetailByZbsqbh(String zbsqbh) {
        try {
            return buildSaveResponseVO(zbsqbh);
        } catch (Exception e) {
            log.error("查询招标申请详情失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("查询招标申请详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgzbsqListVO> getCgzbsqPageList(CgzbsqPageQueryDTO queryDTO) {
        try {
            // 1. 参数校验和默认值设置
            if (queryDTO == null) {
                queryDTO = new CgzbsqPageQueryDTO();
            }

            // 校验分页参数
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 防止分页查询过大，限制每页最大记录数
            if (queryDTO.getSize() > 1000) {
                queryDTO.setSize(1000);
                log.warn("分页大小超过限制，自动调整为1000条");
            }

            // 2. 设置默认查询条件
            String gsdm = queryDTO.getGsdm();
            if (StringUtils.isBlank(gsdm)) {
                gsdm = LoginInfo.getCurrCorpCode();
                queryDTO.setGsdm(gsdm);
            }

            String kjnd = queryDTO.getKjnd();
            if (StringUtils.isBlank(kjnd)) {
                kjnd = LoginInfo.getCurrAccountantYear();
                queryDTO.setKjnd(kjnd);
            }

            // 3. 处理模糊查询条件
            String condition = queryDTO.getCondition();
            if (StringUtils.isNotBlank(condition)) {
                condition = condition.trim();
                // 防止SQL注入，移除特殊字符
                condition = condition.replaceAll("['\";\\\\]", "");
                queryDTO.setCondition(condition);
            }

            // 4. 日期参数校验
            String startDate = queryDTO.getStartDate();
            String endDate = queryDTO.getEndDate();

            // 简单的日期格式校验
            if (StringUtils.isNotBlank(startDate) && !startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new GlobalException("开始日期格式不正确，请使用yyyy-MM-dd格式");
            }
            if (StringUtils.isNotBlank(endDate) && !endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new GlobalException("结束日期格式不正确，请使用yyyy-MM-dd格式");
            }

            // 检查日期范围是否合理
            if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                if (startDate.compareTo(endDate) > 0) {
                    throw new GlobalException("开始日期不能大于结束日期");
                }
            }

            // 5. 计算分页偏移量
            Integer offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 6. 准备查询参数
            String zt = queryDTO.getZt();

            // 获取数据权限信息
            String currEmployeeCode = LoginInfo.getCurrEmployeeCode();
            String dataPermission = dataPermissionUtils.getCurrentUserDataPermission();
            String currDeptCode = LoginInfo.getCurrDeptCode(); // 使用LoginInfo.getCurrDeptCode()获取部门代码

            log.info("执行招标申请分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, startDate={}, endDate={}, current={}, size={}, 用户={}, 权限={}, 部门={}",
                    gsdm, kjnd, condition, zt, startDate, endDate, queryDTO.getCurrent(), queryDTO.getSize(), currEmployeeCode, dataPermission, currDeptCode);

            // 7. 执行查询
            List<CgzbsqListVO> resultList = cgzbsqmlMapper.getCgzbsqPageList(
                    gsdm,
                    kjnd,
                    condition,
                    zt,
                    startDate,
                    endDate,
                    offset,
                    queryDTO.getSize(),
                    currEmployeeCode,
                    dataPermission,
                    currDeptCode
            );

            // 8. 后处理：状态转换（Mapper已经处理了状态转换，这里保留兼容性）
            if (resultList != null) {
                resultList.forEach(item -> {
                    if (item.getZtmc() == null && item.getZt() != null) {
                        item.setZtmc(StatusName.findByStatus(item.getZt()));
                    }
                });
            }

            // 9. 查询结果日志
            int resultSize = resultList != null ? resultList.size() : 0;
            log.info("招标申请分页查询完成，返回{}条记录，查询条件：{}", resultSize, queryDTO);

            // 10. 如果是第一页且结果不为空，记录总数信息
            if (queryDTO.getCurrent() == 1 && resultSize > 0) {
                Integer totalCount = resultList.get(0).getTotalCount();
                log.info("查询总记录数：{}，当前页：{}，每页大小：{}", totalCount, queryDTO.getCurrent(), queryDTO.getSize());
            }

            return resultList != null ? resultList : new ArrayList<>();

        } catch (GlobalException e) {
            // 业务异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("招标申请分页查询失败，查询条件：{}，错误信息：{}", queryDTO, e.getMessage(), e);
            throw new GlobalException("查询招标申请列表失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Map<Integer, List<PubObjFlowTemp>> commitProxyByZbsqbh(BillnoType billnoType, String zbsqbh, Double money, String auditor) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            log.info("开始提交招标申请，zbsqbh：{}，金额：{}，提交人：{}", zbsqbh, money, auditor);

            // 1. 校验招标申请状态，只有保存状态（1）才能提交
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，无法提交，zbsqbh: " + zbsqbh);
            }

            // 2. 权限校验：只有创建人才能提交
            String currentUser = LoginInfo.getCurrEmployeeName();
            // 招标申请表没有创建人字段，暂时跳过权限校验
            // TODO: 可以考虑添加创建人字段或使用其他方式进行权限控制

            // 3. 检查状态 - 只有保存状态（1）才能提交
            if (cgzbsqml.getZt() == null || !"1".equals(cgzbsqml.getZt())) {
                String statusName = StatusName.findByStatus(cgzbsqml.getZt());
                throw new GlobalException("只有保存状态的招标申请才能提交，当前状态为：" + statusName);
            }

            // 注意：状态更新将由AOP统一处理，此处不再更新状态
            log.info("招标申请基础校验通过，zbsqbh：{}，等待AOP处理状态更新", zbsqbh);

            // 4. 获取工作流程模板节点
            List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new HashMap<>();

            if (templates == null || templates.isEmpty()) {
                // 如果没有配置审核模板，创建默认审核节点
                log.warn("未找到审核流程模板，将创建默认审核节点，单据类型：{}", billnoType.getCode());

                PubObjFlowTemp defaultTemplate = new PubObjFlowTemp();
                defaultTemplate.setShrdm(auditor != null ? auditor : LoginInfo.getCurrEmployeeCode());
                defaultTemplate.setShrxm(auditor != null ? "审核人" : LoginInfo.getCurrEmployeeName());
                defaultTemplate.setJddm(1); // 默认节点编号
                defaultTemplate.setJdmc("招标申请审核"); // 默认节点名称

                List<PubObjFlowTemp> defaultList = new ArrayList<>();
                defaultList.add(defaultTemplate);
                nodeMap.put(1, defaultList);

                log.info("已创建默认审核节点，审核人：{}", defaultTemplate.getShrxm());
            } else {
                // 按节点序号分组
                for (PubObjFlowTemp template : templates) {
                    Integer nodeSeq = template.getJddm() != null ? template.getJddm() : 1;
                    if (template.getDynamicAcquisition().equals("1") && StringUtils.isNotBlank(template.getShtj())) {
                        JSONObject auditorInfo = cgjhmlMapper.findAuditorInfo(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear(),
                                zbsqbh, template.getShtj());
                        template.setShrxm(auditorInfo.getString("shrxm"));
                        template.setShrdm(auditorInfo.getString("shrdm"));
                    }
                    nodeMap.computeIfAbsent(nodeSeq, k -> new ArrayList<>()).add(template);
                }

                log.info("获取审核流程模板成功，共{}个节点，模板数量：{}", nodeMap.size(), templates.size());
            }

            log.info("招标申请提交成功，zbsqbh：{}", zbsqbh);
            return nodeMap;

        } catch (Exception e) {
            log.error("提交招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("提交招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgzbsqAuditLogVO findCheckLogByZbsqbh(BillnoType billnoType, String zbsqbh) {
        try {
            log.info("开始查询招标申请审核日志，申请编号：{}", zbsqbh);

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 1. 检查招标申请是否存在
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);
            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，zbsqbh: " + zbsqbh);
            }

            // 2. 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billnoType.getModCode(), String.valueOf(billnoType.getCode()));

            String flowcode = "CGZBSQ_FLOW_001"; // 默认流程代码
            if (workflow != null) {
                flowcode = workflow.getFlowcode();
            }

            // 3. 查询审核日志（使用ZBSQBH作为单据号）
            List<PubAuditLog> logList = pubAuditLogMapper.selectByBillId(gsdm, kjnd, zbsqbh, flowcode);

            // 4. 转换为VO
            CgzbsqAuditLogVO auditLogVO = new CgzbsqAuditLogVO();
            auditLogVO.setZbsqbh(zbsqbh);
            auditLogVO.setBillType(billnoType.getComment());

            // 转换日志列表
            List<CgzbsqAuditLogVO.AuditLogItem> logItems = logList.stream().map(log -> {
                CgzbsqAuditLogVO.AuditLogItem item = new CgzbsqAuditLogVO.AuditLogItem();
                item.setGsdm(log.getGsdm());
                item.setKjnd(log.getKjnd());
                item.setLogid(log.getLogid());
                item.setBillid(log.getBillid());
                item.setBillname(log.getBillname());
                item.setFlowcode(log.getFlowcode());
                item.setFlowname(log.getFlowname());
                item.setModname(log.getModname());
                item.setBizname(log.getBizname());
                item.setNodeseq(log.getNodeseq());
                item.setNodename(log.getNodename());
                item.setAuditorid(log.getAuditorid());
                item.setAuditor(log.getAuditor());
                item.setCertigierid(log.getCertigierid());
                item.setCertigier(log.getCertigier());
                item.setAdatetime(log.getAdatetime());
                item.setAmt(log.getAmt());
                item.setRemark(log.getRemark());
                item.setAtype(log.getAtype());
                item.setLogseq(log.getLogseq());
                item.setServDateTime(log.getServDateTime());
                item.setComputerName(log.getComputerName());
                return item;
            }).collect(Collectors.toList());

            // 5. 创建日志标题列表
            List<CgzbsqAuditLogVO.LogTitleItem> logTitleList = new ArrayList<>();

            // 添加提交节点
            CgzbsqAuditLogVO.LogTitleItem titleNode = new CgzbsqAuditLogVO.LogTitleItem();
            titleNode.setNodename("提交审核");
            titleNode.setGsdm(gsdm);
            titleNode.setNodeseq(-9); // 提交节点编号
            titleNode.setKjnd(kjnd);
            titleNode.setIsaudit(logList.isEmpty() ? "0" : "1");
            titleNode.setAuditStatus(logList.isEmpty() ? "未提交" : "已提交");

            // 如果有提交日志，设置提交时间
            logList.stream()
                    .filter(log -> log.getNodeseq() != null && log.getNodeseq() == -9)
                    .findFirst()
                    .ifPresent(log -> titleNode.setAuditTime(log.getServDateTime()));

            logTitleList.add(titleNode);

            // 查询工作流程节点，添加审核节点标题
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(zbsqbh);

            // 查询工作流程模板节点（用于获取节点名称）
            List<PubObjFlowTemp> flowTemplates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, String> nodeNameMap = new HashMap<>();
            for (PubObjFlowTemp template : flowTemplates) {
                nodeNameMap.put(Integer.valueOf(template.getXh()), template.getJdmc());
            }

            for (PubObjFlow node : flowNodes) {
                CgzbsqAuditLogVO.LogTitleItem flowTitleNode = new CgzbsqAuditLogVO.LogTitleItem();

                // 通过节点序号获取正确的节点名称
                Integer nodeSeq = null;
                try {
                    nodeSeq = Integer.valueOf(node.getAuditFlag());
                    // 从工作流程模板中获取节点名称，如果没有则使用审核人名称作为备选
                    String nodeName = nodeNameMap.get(nodeSeq);
                    if (nodeName != null && !nodeName.trim().isEmpty()) {
                        flowTitleNode.setNodename(nodeName);
                    } else {
                        // 如果模板中没有节点名称，使用审核人名称作为备选
                        flowTitleNode.setNodename(node.getShrmc() != null ? node.getShrmc() : "审核节点");
                    }
                } catch (NumberFormatException e) {
                    flowTitleNode.setNodename("审核节点");
                    nodeSeq = 1;
                }

                flowTitleNode.setGsdm(node.getGsdm());
                flowTitleNode.setNodeseq(nodeSeq);
                flowTitleNode.setKjnd(node.getKjnd());
                flowTitleNode.setIsaudit("1".equals(node.getIsaudit()) ? "1" : "0");
                flowTitleNode.setAuditStatus("1".equals(node.getIsaudit()) ? "已审核" : "待审核");

                // 查找对应的审核日志设置审核时间
                logList.stream()
                        .filter(log -> log.getNodeseq() != null && log.getNodeseq().equals(flowTitleNode.getNodeseq()))
                        .findFirst()
                        .ifPresent(log -> flowTitleNode.setAuditTime(log.getServDateTime()));

                logTitleList.add(flowTitleNode);
            }

            auditLogVO.setLogList(logItems);
            auditLogVO.setLogTitleList(logTitleList);

            log.info("查询招标申请审核日志成功，申请编号：{}，日志数量：{}", zbsqbh, logItems.size());

            return auditLogVO;

        } catch (Exception e) {
            log.error("查询招标申请审核日志失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("查询审核日志失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Object isCheckedByAuthorityByZbsqbh(String zbsqbh) {
        try {
            log.info("开始检查招标申请审核权限，zbsqbh：{}", zbsqbh);

            // 参数校验
            if (StringUtils.isBlank(zbsqbh)) {
                throw new GlobalException("招标申请编号不能为空");
            }

            // 查询招标申请信息
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            JSONObject resultJson = new JSONObject();
            String currentUser = LoginInfo.getCurrEmployeeName();

            // 1. 检查招标申请是否存在
            if (cgzbsqml == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", true);
                resultJson.put("result", "此流程单据已经被删除，当前审核人" + currentUser + "无法查看");
                log.warn("招标申请不存在，zbsqbh：{}，当前用户：{}", zbsqbh, currentUser);
                return resultJson;
            }

            // 2. 检查招标申请状态
            String status = cgzbsqml.getZt();
            String statusName = StatusName.findByStatus(status);

            log.info("招标申请状态检查，zbsqbh：{}，状态：{}（{}），当前用户：{}", zbsqbh, status, statusName, currentUser);

            // 状态：1-保存，2-已提交，3-审核中，4-已审核，5-退回
            if (status == null || "1".equals(status) || "5".equals(status)) {
                // 保存状态或退回状态，无需审核
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程当前状态为：" + statusName + "，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                log.info("招标申请状态不允许审核，zbsqbh：{}，状态：{}", zbsqbh, statusName);
                return resultJson;
            }

            if ("4".equals(status)) {
                // 已审核状态
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "此流程已审核完成，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                log.info("招标申请已审核完成，zbsqbh：{}，当前用户：{}", zbsqbh, currentUser);
                return resultJson;
            }

            // 3. 检查工作流审核权限（对于已提交或审核中状态）
            if ("2".equals(status) || "3".equals(status)) {
                // 获取审批流程信息
                BillnoType billType = BillnoType.CGZB;

                // 查询当前审核节点
                PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                        billType.getModCode(),
                        String.valueOf(billType.getCode()),
                        zbsqbh
                );

                // 检查当前用户是否有审核权限
                if (nowNodeInfo == null) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "未找到当前审核节点信息，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                    log.warn("未找到审核节点信息，zbsqbh：{}，当前用户：{}", zbsqbh, currentUser);
                    return resultJson;
                }

                // 检查当前用户是否在审核人列表中
                String currentEmployeeCode = LoginInfo.getCurrEmployeeCode();
                String auditors = nowNodeInfo.getShr1();

                if (StringUtils.isBlank(auditors) || !auditors.contains(currentEmployeeCode)) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "此流程当前节点已被审批或当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                    log.info("用户无审核权限，zbsqbh：{}，当前用户：{}，当前节点审核人：{}", zbsqbh, currentUser, auditors);
                    return resultJson;
                }

                // 检查节点是否已经被审核
                if ("1".equals(nowNodeInfo.getIsaudit())) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "此流程当前节点已被审批，当前审核人" + currentUser + "无权审批!\n是否进入详情查看单据信息");
                    log.info("当前节点已被审核，zbsqbh：{}，当前用户：{}", zbsqbh, currentUser);
                    return resultJson;
                }

                // 4. 用户有审核权限
                resultJson.put("isCheck", false);
                resultJson.put("isDelete", false);
                resultJson.put("result", "");
                log.info("用户有审核权限，zbsqbh：{}，当前用户：{}，节点：{}", zbsqbh, currentUser, nowNodeInfo.getShrmc());
                return resultJson;
            }

            // 5. 未知状态
            resultJson.put("isCheck", true);
            resultJson.put("isDelete", false);
            resultJson.put("result", "招标申请状态异常（" + statusName + "），当前审核人" + currentUser + "无法操作");
            log.warn("招标申请状态异常，zbsqbh：{}，状态：{}，当前用户：{}", zbsqbh, status, currentUser);

            return resultJson;

        } catch (Exception e) {
            log.error("检查招标申请审核权限失败，zbsqbh: {}", zbsqbh, e);

            JSONObject errorResult = new JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取权限信息：" + e.getMessage());
            return errorResult;
        }
    }

    @Override
    public void checkByZbsqbh(BillnoType billType, String zbsqbh, String opinion, String auditor, BigDecimal money) {
        try {
            log.info("开始审核招标申请，zbsqbh：{}，审核人：{}，意见：{}", zbsqbh, auditor, opinion);

            // 1. 查询招标申请详情
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，无法审核，zbsqbh: " + zbsqbh);
            }

            // 2. 检查状态 - 只有已提交（2）或审核中（3）的招标申请才能审核
            if (cgzbsqml.getZt() == null ||
                    (!"2".equals(cgzbsqml.getZt()) && !"3".equals(cgzbsqml.getZt()))) {
                String statusName = StatusName.findByStatus(cgzbsqml.getZt());
                throw new GlobalException("只有已提交或审核中状态的招标申请才能审核，当前状态为：" + statusName);
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 获取审批流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billType.getModCode(), String.valueOf(billType.getCode()));

            if (workflow == null) {
                log.warn("未找到审核流程配置，使用默认配置");
                // 可以选择抛出异常或者使用默认配置
                throw new GlobalException("未找到审核流程配置");
            }

            // 4. 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    String.valueOf(billType.getCode()),
                    zbsqbh
            );

            if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("该节点已被其他人审核或您无权限审核");
            }

            // 5. 关键修正：在更新节点之前先获取终审判断所需的信息
            int nowNote = Integer.parseInt(nowNodeInfo.getAuditFlag());
            int nextNote = Integer.parseInt(nowNodeInfo.getAuditAftFlag());

            // 判断是否为终审（nextNote == -1 表示最后一个节点）
            boolean isLastNode = (nextNote == -1);

            log.info("审核节点信息 - 当前节点：{}，下一节点：{}，是否终审：{}", nowNote, nextNote, isLastNode);

            // 6. 更新当前节点为已审核
            nowNodeInfo.setIsaudit("1");
            nowNodeInfo.setSpecificCheckPerson(LoginInfo.getCurrEmployeeName());

            // 更新当前节点状态
            LambdaUpdateWrapper<PubObjFlow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PubObjFlow::getModcode, BillnoType.CGZB.getModCode())
                    .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGZB.getCode()))
                    .eq(PubObjFlow::getDjh, zbsqbh)
                    .eq(PubObjFlow::getAuditFlag, String.valueOf(nowNote))
                    .set(PubObjFlow::getIsaudit, "1")
                    .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

            pubObjFlowMapper.update(null, updateWrapper);

            // 7. 根据终审判断结果更新招标申请状态
            int status = isLastNode ? 4 : 3; // 4-已审核（终审），3-审核中
            updateStatusByZbsqbh(zbsqbh, status);

            // 8. 记录审核日志
            createAuditLogByZbsqbh(BillnoType.CGZB, zbsqbh, opinion, LoginInfo.getCurrEmployeeName(), money, workflow, nowNote, nowNodeInfo.getNodeName(), "审核");

            log.info("招标申请审核成功，zbsqbh：{}，审核人：{}，是否终审：{}", zbsqbh, auditor, isLastNode);

        } catch (Exception e) {
            log.error("审核招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("审核招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void updateStatusByZbsqbh(String zbsqbh, Integer status) {
        try {
            LambdaUpdateWrapper<GpmCgzbsqml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);

            GpmCgzbsqml updateEntity = new GpmCgzbsqml();
            // 状态字段需要转换为字符串
            updateEntity.setZt(String.valueOf(status));
            updateEntity.setXgsj(new SimpleDateFormat("yyyyMMdd").format(new Date()));

            int result = cgzbsqmlMapper.update(updateEntity, updateWrapper);
            log.info("更新招标申请状态成功，zbsqbh：{}，状态：{}，影响行数：{}", zbsqbh, status, result);
        } catch (Exception e) {
            log.error("更新招标申请状态失败，zbsqbh: {}，状态：{}", zbsqbh, status, e);
            throw new GlobalException("更新状态失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgzbsqSaveResponseVO callBackByZbsqbh(String zbsqbh) {
        try {
            log.info("开始收回招标申请，zbsqbh：{}", zbsqbh);

            // 1. 查询招标申请
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，zbsqbh: " + zbsqbh);
            }

            // 2. 权限校验：只有创建人才能收回（招标申请表暂无创建人字段，跳过权限校验）
            // String currentUser = LoginInfo.getCurrEmployeeName();
            // TODO: 可以考虑添加创建人字段或使用其他方式进行权限控制

            // 3. 检查状态是否可以收回
            String currentStatus = cgzbsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("招标申请状态异常，无法收回");
            }

            // 只有已提交(2)的状态才能收回
            if (!"2".equals(currentStatus)) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法收回。只有已提交的招标申请才能收回");
            }

            // 4. 检查是否已经有人开始审核
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(zbsqbh);
            boolean hasStartedAudit = flowNodes.stream()
                    .anyMatch(node -> "1".equals(node.getIsaudit()));

            if (hasStartedAudit) {
                throw new GlobalException("该招标申请已开始审核，无法收回");
            }

            // 5. 删除工作流程节点
            if (!flowNodes.isEmpty()) {
                LambdaUpdateWrapper<PubObjFlow> deleteWrapper = new LambdaUpdateWrapper<>();
                deleteWrapper.eq(PubObjFlow::getModcode, BillnoType.CGZB.getModCode())
                        .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGZB.getCode()))
                        .eq(PubObjFlow::getDjh, zbsqbh);
                pubObjFlowMapper.delete(deleteWrapper);
                log.info("删除工作流程节点成功，zbsqbh：{}，节点数量：{}", zbsqbh, flowNodes.size());
            }

            // 6. 更新状态为保存状态
            updateStatusByZbsqbh(zbsqbh, 1);

            log.info("招标申请收回成功，zbsqbh：{}", zbsqbh);
            return getCgzbsqDetailByZbsqbh(zbsqbh);

        } catch (Exception e) {
            log.error("收回招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("收回招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgzbsqSaveResponseVO checkCallBackByZbsqbh(String zbsqbh, String opinion, String auditor) {
        try {
            log.info("开始退审招标申请，zbsqbh：{}，退审人：{}", zbsqbh, auditor);

            // 1. 查询招标申请
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，zbsqbh: " + zbsqbh);
            }

            // 2. 检查状态是否可以退审
            String currentStatus = cgzbsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("招标申请状态异常，无法退审");
            }


            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 查询当前审核节点
            PubObjFlow currentNode = pubObjFlowService.selectNowNodeByDjh(
                    BillnoType.CGZB.getModCode(),
                    String.valueOf(BillnoType.CGZB.getCode()),
                    zbsqbh
            );

            if (currentNode == null) {
                throw new GlobalException("未找到当前审核节点，无法退审");
            }

            // 4. 检查退审权限
            if (!currentNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("您无权限对该招标申请进行退审操作");
            }

            // 5. 处理工作流程退审逻辑
            int currentNodeSeq = Integer.parseInt(currentNode.getAuditFlag());
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    BillnoType.CGZB.getModCode(), String.valueOf(BillnoType.CGZB.getCode()));

            if (currentStatus.equals("2")) {
                updateStatusByZbsqbh(zbsqbh, 5);
                if (workflow != null) {
                    createAuditLogByZbsqbh(BillnoType.CGZB, zbsqbh, opinion, LoginInfo.getCurrEmployeeName(),
                            cgzbsqml.getXmje(), workflow, currentNodeSeq,
                            currentNode.getNodeName(), "退审");
                }
                return getCgzbsqDetailByZbsqbh(zbsqbh);
            }


            // 只有审核中(3)的状态才能退审
            if (!"3".equals(currentStatus)) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法退审。只有审核中的招标申请才能退审");
            }


            // 查询所有工作流程节点，找到上一个节点
            List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(zbsqbh);
            PubObjFlow previousNode = null;

            for (PubObjFlow node : allNodes) {
                int nodeSeq = Integer.parseInt(node.getAuditFlag());
                if (nodeSeq < currentNodeSeq && (previousNode == null ||
                        Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                    previousNode = node;
                }
            }

            // 6. 更新工作流程状态
            if (previousNode != null) {
                // 如果有上一个节点，将上一个节点设为未审核
                LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGZB.getModCode())
                        .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGZB.getCode()))
                        .eq(PubObjFlow::getDjh, zbsqbh)
                        .eq(PubObjFlow::getAuditFlag, previousNode.getAuditFlag())
                        .set(PubObjFlow::getIsaudit, "0")
                        .set(PubObjFlow::getSpecificCheckPerson, "");

                pubObjFlowMapper.update(null, updatePreviousWrapper);
                if (previousNode == allNodes.get(0)) {
                    updateStatusByZbsqbh(zbsqbh, 5);
                } else {
                    // 更新采购计划状态为审核中(3)
                    updateStatusByZbsqbh(zbsqbh, 3);
                }

                log.info("招标申请退审成功，退回到上一个节点，zbsqbh：{}，上一节点序号：{}",
                        zbsqbh, previousNode.getAuditFlag());
            } else {
                // 更新招标申请状态为已提交(2)
                updateStatusByZbsqbh(zbsqbh, 2);
                log.info("招标申请退审成功，退回到已提交状态，zbsqbh：{}", zbsqbh);
            }

            // 7. 记录退审日志
            if (workflow != null) {
                createAuditLogByZbsqbh(BillnoType.CGZB, zbsqbh, opinion, LoginInfo.getCurrEmployeeName(),
                        cgzbsqml.getXmje(), workflow, currentNodeSeq,
                        currentNode.getNodeName(), "退审");
            }

            log.info("招标申请退审成功，zbsqbh：{}，退审人：{}", zbsqbh, auditor);

            // 8. 查询并返回退审后的招标申请详情
            return getCgzbsqDetailByZbsqbh(zbsqbh);

        } catch (Exception e) {
            log.error("退审招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("退审招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgzbsqSaveResponseVO noAuditByZbsqbh(String zbsqbh) {
        try {
            log.info("开始销审招标申请，zbsqbh：{}", zbsqbh);

            // 1. 参数校验
            if (StringUtils.isBlank(zbsqbh)) {
                throw new GlobalException("招标申请编号不能为空");
            }

            // 2. 查询招标申请
            LambdaQueryWrapper<GpmCgzbsqml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(queryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，zbsqbh: " + zbsqbh);
            }

            // 3. 检查状态是否可以销审
            String currentStatus = cgzbsqml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("招标申请状态异常，无法销审");
            }

            int status = Integer.parseInt(currentStatus);
            // 只有审核中(3)、已审核(4)状态才能销审
            if (status != 3 && status != 4) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法销审。只有已提交、审核中或已审核的招标申请才能销审");
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String currentUser = LoginInfo.getCurrEmployeeName();

            PubObjFlow lastAuditNode = pubObjFlowService.selectLastAuditNodeByDjh(
                    BillnoType.CGZB.getModCode(),
                    String.valueOf(BillnoType.CGZB.getCode()),
                    zbsqbh
            );

            // 5. 销审
            String finalStatus = null; // 记录最终状态
            if (status == 3 || status == 4) {
                // 如果当前状态是审核中(3)或已审核(4)，需要退回到上一个节点

                // 查询所有工作流程节点
                List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(zbsqbh);
                if (allNodes.isEmpty()) {
                    // 如果没有工作流程节点，直接设置为已提交状态
                    updateStatusByZbsqbh(zbsqbh, 2);
                    finalStatus = "2";
                    log.info("招标申请没有工作流程节点，销审为已提交状态，zbsqbh：{}", zbsqbh);
                } else {
                    // 找到最后一个已审核的节点（销审应该退回到上一个节点）
                    PubObjFlow lastAuditedNode = null;
                    int maxAuditedNodeSeq = -1;

                    for (PubObjFlow node : allNodes) {
                        if ("1".equals(node.getIsaudit())) { // 已审核的节点
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq > maxAuditedNodeSeq) {
                                maxAuditedNodeSeq = nodeSeq;
                                lastAuditedNode = node;
                            }
                        }
                    }

                    if (lastAuditedNode != null) {
                        if (!lastAuditedNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                            throw new GlobalException("您无权限对该采购验收进行销审操作");
                        }
                        // 将上一个节点设为未审核，删除当前节点及之后的节点
                        LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                        updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGZB.getModCode())
                                .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGZB.getCode()))
                                .eq(PubObjFlow::getDjh, zbsqbh)
                                .eq(PubObjFlow::getAuditFlag, lastAuditedNode.getAuditFlag())
                                .set(PubObjFlow::getIsaudit, "0")
                                .set(PubObjFlow::getSpecificCheckPerson, "");

                        pubObjFlowMapper.update(null, updatePreviousWrapper);
                        // 有已审核的节点，找到它的上一个节点
                        PubObjFlow previousNode = null;

                        for (PubObjFlow node : allNodes) {
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq < maxAuditedNodeSeq && (previousNode == null ||
                                    Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                                previousNode = node;
                            }
                        }
                        if (previousNode != null) {
                            // 更新招标申请状态为审核中(3)
                            updateStatusByZbsqbh(zbsqbh, 3);
                            log.info("招标申请销审成功，退回到上一个节点，zbsqbh：{}，上一节点序号：{}",
                                    zbsqbh, previousNode.getAuditFlag());
                        } else {
                            // 更新招标申请状态为已提交(2)，这样第一个人可以继续审核
                            updateStatusByZbsqbh(zbsqbh, 2);
                            log.info("招标申请销审成功，第一个审核节点已重置为未审核状态，zbsqbh：{}，第一节点序号：{}",
                                    zbsqbh, maxAuditedNodeSeq);
                        }
                    } else {
                        updateStatusByZbsqbh(zbsqbh, 2);
                        log.info("招标申请没有已审核节点，销审为已提交状态，zbsqbh：{}", zbsqbh);
                    }
                }
            }

            // 6. 记录销审操作日志
            try {
                // 查询工作流程配置获取流程代码
                PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                        BillnoType.CGZB.getModCode(), String.valueOf(BillnoType.CGZB.getCode()));

                // 记录销审操作日志
                if (workflow != null) {
                    createAuditLogByZbsqbh(BillnoType.CGZB, zbsqbh, "",
                            currentUser, cgzbsqml.getXmje(), workflow,
                            -99, lastAuditNode.getNodeName(), "销审");
                }

                log.info("已记录销审操作日志，zbsqbh：{}", zbsqbh);

            } catch (Exception e) {
                log.warn("记录销审日志时出现异常，但不影响销审操作，zbsqbh：{}，异常：{}", zbsqbh, e.getMessage());
            }

            log.info("招标申请销审成功，zbsqbh：{}，操作人：{}，原状态：{}→新状态：{}",
                    zbsqbh, currentUser, StatusName.findByStatus(currentStatus), finalStatus != null ? StatusName.findByStatus(finalStatus) : "无");

            return getCgzbsqDetailByZbsqbh(zbsqbh);

        } catch (Exception e) {
            log.error("销审招标申请失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("销审招标申请失败：" + e.getMessage(), e);
        }
    }

    @Override
    public PageResult<CgzbsqSaveResponseVO> getApprovedCgzbsqList(CgzbsqPageQueryDTO queryDTO) {
        try {
            // 固定状态为已审核
            queryDTO.setZt("4");

            // 调用分页查询获取基础信息和总数
            List<CgzbsqListVO> basicList = getCgzbsqPageList(queryDTO);

            long totalCount = 0;
            if (!basicList.isEmpty()) {
                totalCount = basicList.get(0).getTotalCount();
            }

            // 获取详情
            List<CgzbsqSaveResponseVO> detailList = new ArrayList<>();
            for (CgzbsqListVO basicItem : basicList) {
                try {
                    CgzbsqSaveResponseVO detail = getCgzbsqDetailByZbsqbh(basicItem.getZbsqbh());
                    if (detail != null) {
                        detailList.add(detail);
                    }
                } catch (Exception e) {
                    log.warn("获取已审核招标申请详情失败，zbsqbh: {}, 错误: {}", basicItem.getZbsqbh(), e.getMessage());
                    // 继续处理下一个
                }
            }

            return new PageResult<>(detailList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

        } catch (Exception e) {
            log.error("查询已审核招标申请列表失败", e);
            throw new GlobalException("查询已审核招标申请列表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 构建保存响应VO
     */
    private CgzbsqSaveResponseVO buildSaveResponseVO(String zbsqbh) {
        try {
            // 查询主表信息
            LambdaQueryWrapper<GpmCgzbsqml> mlQueryWrapper = new LambdaQueryWrapper<>();
            mlQueryWrapper.eq(GpmCgzbsqml::getZbsqbh, zbsqbh);
            GpmCgzbsqml cgzbsqml = cgzbsqmlMapper.selectOne(mlQueryWrapper);

            if (cgzbsqml == null) {
                throw new GlobalException("招标申请不存在，zbsqbh: " + zbsqbh);
            }

            // 查询明细信息
            LambdaQueryWrapper<GpmCgzbsqnr> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.eq(GpmCgzbsqnr::getZbsqbh, zbsqbh);
            List<GpmCgzbsqnr> cgzbsqnrList = cgzbsqnrMapper.selectList(detailQueryWrapper);

            // 构建响应VO
            CgzbsqSaveResponseVO responseVO = new CgzbsqSaveResponseVO();

            // 构建基础信息
            CgzbsqSaveResponseVO.CgzbsqBaseInfoResponseVO baseInfoVO = new CgzbsqSaveResponseVO.CgzbsqBaseInfoResponseVO();
            BeanUtils.copyProperties(cgzbsqml, baseInfoVO);
            baseInfoVO.setZtmc(StatusName.findByStatus(cgzbsqml.getZt()));
            responseVO.setBaseInfo(baseInfoVO);

            // 构建明细信息
            List<CgzbsqSaveResponseVO.CgzbsqDetailResponseVO> detailVOList = cgzbsqnrList.stream()
                    .map(detail -> {
                        CgzbsqSaveResponseVO.CgzbsqDetailResponseVO detailVO = new CgzbsqSaveResponseVO.CgzbsqDetailResponseVO();
                        BeanUtils.copyProperties(detail, detailVO);
                        detailVO.setSfcgsqyr(ConvertUtils.convertStringToBoolean(detail.getSfcgsqyr()));
                        detailVO.setZfcg(ConvertUtils.convertStringToBoolean(detail.getZfcg()));
                        detailVO.setJkcp(ConvertUtils.convertStringToBoolean(detail.getJkcp()));
                        return detailVO;
                    })
                    .collect(Collectors.toList());

            responseVO.setCgzbsqDetails(detailVOList);

            log.info("构建招标申请响应VO成功，zbsqbh：{}，明细数量：{}", zbsqbh, detailVOList.size());
            return responseVO;

        } catch (Exception e) {
            log.error("构建招标申请响应VO失败，zbsqbh: {}", zbsqbh, e);
            throw new GlobalException("构建响应数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 生成招标申请编号
     */
    private String generateZbsqbh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());

        // 查询当天最大序号
        String prefix = "CGZB" + dateStr;
        String maxZbsqbh = cgzbsqmlMapper.selectMaxZbsqbhByPrefix(prefix);

        int sequenceNumber = 1;
        if (maxZbsqbh != null && maxZbsqbh.length() >= prefix.length() + 4) {
            // 提取序列号部分
            String sequence = maxZbsqbh.substring(prefix.length());
            try {
                sequenceNumber = Integer.parseInt(sequence) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析序列号失败，使用默认值1: {}", sequence, e);
            }
        }

        // 格式化为4位数字，例如：0001, 0002, ..., 9999
        return prefix + String.format("%04d", sequenceNumber);
    }

    /**
     * 生成明细编号，格式：CGZBMX年月日001, CGZBMX年月日002, ...
     * @param detailIndex 明细索引，从0开始
     * @return 明细编号
     */
    private String generateZbmxxh(int detailIndex) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String mxPrefix = "CGZBMX" + dateStr;

        // 序号从1开始，加上传入的索引值
        int sequenceNumber = 1 + detailIndex;

        // 格式化为3位数字，例如：001, 002, ..., 999
        return mxPrefix + String.format("%03d", sequenceNumber);
    }

    /**
     * 创建审核日志
     */
    private void createAuditLogByZbsqbh(BillnoType billType, String zbsqbh, String opinion, String auditor,
                                        BigDecimal money, PubWorkflow workflow, int nodeseq, String nodename, String atype) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            PubAuditLog auditLog = new PubAuditLog();
            Integer newLogID = pubAuditLogMapper.selectMaxLogID();
            if (newLogID == null) {
                newLogID = 1;
            }
            auditLog.setLogid(newLogID.longValue());

            // 基本信息
            auditLog.setGsdm(gsdm);
            auditLog.setKjnd(kjnd);
            auditLog.setBillid(zbsqbh);
            auditLog.setBillname(workflow.getBizname());

            // 流程信息
            if (workflow != null) {
                auditLog.setFlowcode(workflow.getFlowcode());
                auditLog.setFlowname(workflow.getFlowname());
            } else {
                auditLog.setFlowcode("CGZBSQ_FLOW_001");
                auditLog.setFlowname("招标申请审核流程");
            }

            auditLog.setModname(billType.getModCode());
            auditLog.setBizname(billType.getComment());

            // 节点信息
            auditLog.setNodeseq(nodeseq);
            auditLog.setNodename(nodename);

            // 审核人信息
            auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
            auditLog.setAuditor(LoginInfo.getCurrEmployeeName());
            auditLog.setCertigierid(0);
            auditLog.setCertigier("");

            // 时间和金额
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            auditLog.setAdatetime(currentTime);
            auditLog.setServDateTime(currentTime);
            auditLog.setAmt(money);

            // 意见和类型
            auditLog.setRemark(opinion);
            auditLog.setAtype(atype);

            // 系统信息
            try {
                auditLog.setComputerName(InetAddress.getLocalHost().getHostName());
            } catch (Exception e) {
                auditLog.setComputerName("Unknown");
            }

            // 保存审核日志
            pubAuditLogMapper.insert(auditLog);

            log.info("创建招标申请审核日志成功，zbsqbh：{}，审核人：{}，节点：{}", zbsqbh, auditor, nodename);

        } catch (Exception e) {
            log.error("创建招标申请审核日志失败，zbsqbh: {}", zbsqbh, e);
            // 审核日志记录失败不应该影响主业务，所以这里只记录错误但不抛出异常
        }
    }
} 