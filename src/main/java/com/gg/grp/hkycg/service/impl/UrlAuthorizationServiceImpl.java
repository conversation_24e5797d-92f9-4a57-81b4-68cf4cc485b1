package com.gg.grp.hkycg.service.impl;

import com.gg.grp.hkycg.model.pojo.GpmGnfl;
import com.gg.grp.hkycg.model.pojo.GpmRole;
import com.gg.grp.hkycg.model.pojo.Employee;
import com.gg.grp.hkycg.service.*;
import com.gg.grp.hkycg.common.redis.RedisSessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * URL授权管理服务实现类
 */
@Service
public class UrlAuthorizationServiceImpl implements UrlAuthorizationService {

    @Autowired
    private EmployeeService employeeService;
    
    @Autowired
    private CimRoleService cimRoleService;
    
    @Autowired
    private GnflService gnflService;
    
    @Autowired
    private RolegnflService rolegnflService;
    
    @Autowired
    private RedisSessionManager redisSessionManager;

    @Override
    public boolean hasUrlPermission(String employeeCode, String url) {
        try {
            // 首先从缓存获取用户权限信息
            List<String> authorizedUrls = getAuthorizedUrls(employeeCode);
            return authorizedUrls.contains(url);
        } catch (Exception e) {
            System.err.println("检查URL权限失败: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<String> getAuthorizedUrls(String employeeCode) {
        try {
            // 先尝试从Redis缓存获取
            String cacheKey = "user_urls:" + employeeCode;
            Map<String, Object> sessionInfo = redisSessionManager.getUserSession(employeeCode);
            if (sessionInfo != null && sessionInfo.containsKey("authorizedUrls")) {
                @SuppressWarnings("unchecked")
                List<String> cachedUrls = (List<String>) sessionInfo.get("authorizedUrls");
                if (cachedUrls != null && !cachedUrls.isEmpty()) {
                    return cachedUrls;
                }
            }
            
            // 缓存不存在则从数据库查询
            List<GpmGnfl> functions = getAuthorizedFunctions(employeeCode);
            List<String> urls = functions.stream()
                    .map(GpmGnfl::getUrl)
                    .filter(url -> StringUtils.hasText(url))
                    .collect(Collectors.toList());
            
            // 将结果缓存到Redis
            if (sessionInfo != null) {
                sessionInfo.put("authorizedUrls", urls);
                redisSessionManager.saveUserSessionData(employeeCode, sessionInfo);
            }
            
            return urls;
        } catch (Exception e) {
            System.err.println("获取授权URL列表失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<GpmGnfl> getAuthorizedFunctions(String employeeCode) {
        try {
            // 1. 根据员工代码获取员工信息
            Employee employee = employeeService.getByEmployeeCode(employeeCode);
            if (employee == null) {
                System.err.println("未找到员工信息: " + employeeCode);
                return new ArrayList<>();
            }
            
            // 2. 根据员工部门代码获取角色
            String roleCode = getRoleCodeByDeptCode(employee.getBmdm());
            if (!StringUtils.hasText(roleCode)) {
                System.err.println("未找到部门对应的角色: " + employee.getBmdm());
                return new ArrayList<>();
            }
            
            // 3. 根据角色代码获取功能列表
            return gnflService.getFunctionsByRoleCode(roleCode);
        } catch (Exception e) {
            System.err.println("获取授权功能列表失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public String getRoleCodeByDeptCode(String bmdm) {
        try {
            GpmRole role = cimRoleService.getRoleByBmdm(bmdm);
            return role != null ? role.getRoleCode() : null;
        } catch (Exception e) {
            System.err.println("根据部门代码获取角色代码失败: " + e.getMessage());
            return null;
        }
    }

    @Override
    public Map<String, Object> getUserPermissionSummary(String employeeCode) {
        Map<String, Object> summary = new HashMap<>();
        try {
            // 获取员工信息
            Employee employee = employeeService.getByEmployeeCode(employeeCode);
            if (employee != null) {
                summary.put("employeeCode", employee.getEmployeeCode());
                summary.put("employeeName", employee.getEmployeeName());
                summary.put("deptCode", employee.getBmdm());
                
                // 获取角色信息
                String roleCode = getRoleCodeByDeptCode(employee.getBmdm());
                if (StringUtils.hasText(roleCode)) {
                    GpmRole role = cimRoleService.getRoleByCode(roleCode);
                    if (role != null) {
                        summary.put("roleCode", role.getRoleCode());
                        summary.put("roleName", role.getRoleName());
                    }
                }
                
                // 获取权限信息
                List<GpmGnfl> functions = getAuthorizedFunctions(employeeCode);
                summary.put("functionCount", functions.size());
                summary.put("functions", functions);
                
                List<String> urls = functions.stream()
                        .map(GpmGnfl::getUrl)
                        .filter(url -> StringUtils.hasText(url))
                        .collect(Collectors.toList());
                summary.put("urlCount", urls.size());
                summary.put("urls", urls);
            }
        } catch (Exception e) {
            System.err.println("获取用户权限摘要失败: " + e.getMessage());
            summary.put("error", e.getMessage());
        }
        return summary;
    }

    @Override
    public void refreshUserPermissions(String employeeCode) {
        try {
            // 清除缓存
            Map<String, Object> sessionInfo = redisSessionManager.getUserSession(employeeCode);
            if (sessionInfo != null) {
                sessionInfo.remove("authorizedUrls");
                sessionInfo.remove("authorizedFunctions");
                redisSessionManager.saveUserSessionData(employeeCode, sessionInfo);
            }
            
            // 重新加载权限
            getAuthorizedUrls(employeeCode);
            System.out.println("用户权限缓存已刷新: " + employeeCode);
        } catch (Exception e) {
            System.err.println("刷新用户权限缓存失败: " + e.getMessage());
        }
    }
} 