package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 采购结果Service接口
 */
public interface CgdjService {

    /**
     * 保存采购结果
     * @param cgjgSaveDTO 采购结果保存DTO
     * @return 保存的采购结果详情
     */
    CgjgSaveResponseVO saveCgjg(CgjgSaveDTO cgjgSaveDTO);

    /**
     * 更新采购结果（使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @param cgjgSaveDTO 采购结果保存DTO
     * @return 更新后的采购结果详情
     */
    CgjgSaveResponseVO updateCgjgByJgdjbh(String jgdjbh, CgjgSaveDTO cgjgSaveDTO);

    /**
     * 根据结果登记编号删除采购结果
     * @param jgdjbh 结果登记编号
     * @return 是否删除成功
     */
    boolean deleteByJgdjbh(String jgdjbh);

    /**
     * 根据结果登记编号查询采购结果详情
     * @param jgdjbh 结果登记编号
     * @return 采购结果详情VO
     */
    CgjgSaveResponseVO getCgjgDetailByJgdjbh(String jgdjbh);

    /**
     * 分页查询采购结果列表
     * @param queryDTO 查询参数
     * @return 采购结果列表
     */
    List<CgjgListVO> getCgjgPageList(CgjgPageQueryDTO queryDTO);

    /**
     * 提交采购结果（使用结果登记编号）
     * @param billnoType 单据类型
     * @param jgdjbh 结果登记编号
     * @param money 金额
     * @param auditor 审核人
     * @return 工作流程节点映射
     */
    Map<Integer, List<PubObjFlowTemp>> commitProxyByJgdjbh(BillnoType billnoType, String jgdjbh, Double money, String auditor);

    /**
     * 审核采购结果
     * @param billType 单据类型
     * @param jgdjbh 结果登记编号
     * @param opinion 审核意见
     * @param auditor 审核人
     * @param money 金额
     */
    void checkByJgdjbh(BillnoType billType, String jgdjbh, String opinion, String auditor, BigDecimal money);

    /**
     * 更新采购结果状态
     * @param jgdjbh 结果登记编号
     * @param status 状态
     */
    void updateStatusByJgdjbh(String jgdjbh, Integer status);

    /**
     * 收回采购结果
     * @param jgdjbh 结果登记编号
     * @return 收回后的采购结果详情
     */
    CgjgSaveResponseVO callBackByJgdjbh(String jgdjbh);

    /**
     * 退审采购结果
     * @param jgdjbh 结果登记编号
     * @param opinion 退审意见
     * @param auditor 退审人
     * @return 退审后的采购结果详情
     */
    CgjgSaveResponseVO checkCallBackByJgdjbh(String jgdjbh, String opinion, String auditor);

    /**
     * 销审采购结果
     * @param jgdjbh 结果登记编号
     * @return 销审后的采购结果详情
     */
    CgjgSaveResponseVO noAuditByJgdjbh(String jgdjbh);

    /**
     * 查询采购结果审核记录
     * @param billnoType 单据类型
     * @param jgdjbh 结果登记编号
     * @return 审核记录
     */
    CgjgAuditLogVO findCheckLogByJgdjbh(BillnoType billnoType, String jgdjbh);

    /**
     * 检查采购结果是否有权限审核
     * @param jgdjbh 结果登记编号
     * @return 权限检查结果
     */
    Object isCheckedByAuthorityByJgdjbh(String jgdjbh);

    PageResult<CgjgSaveResponseVO> getApprovedCgzbsqList(CgjgPageQueryDTO queryDTO);
}