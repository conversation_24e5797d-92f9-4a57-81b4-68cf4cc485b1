package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.YssjfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.YssjfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmYssjfs;

import java.util.List;

/**
 * 预算审计方式Service接口
 */
public interface YssjfsService extends IService<GpmYssjfs> {

    /**
     * 分页查询预算审计方式列表
     * @param queryDTO 查询参数
     * @return 预算审计方式列表
     */
    List<YssjfsListVO> getYssjfsPageList(YssjfsPageQueryDTO queryDTO);
} 