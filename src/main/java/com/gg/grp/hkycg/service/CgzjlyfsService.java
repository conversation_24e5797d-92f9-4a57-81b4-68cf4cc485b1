package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.model.dto.CgzjlyfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjlyfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzjlyfs;

import java.util.List;

/**
 * 专家来源方式Service接口
 */
public interface CgzjlyfsService extends IService<GpmCgzjlyfs> {

    /**
     * 分页查询专家来源方式列表
     * @param queryDTO 查询参数
     * @return 专家来源方式列表
     */
    List<CgzjlyfsListVO> getCgzjlyfsPageList(CgzjlyfsPageQueryDTO queryDTO);
} 