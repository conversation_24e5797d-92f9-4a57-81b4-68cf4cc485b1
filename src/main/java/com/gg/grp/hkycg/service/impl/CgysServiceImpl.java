package com.gg.grp.hkycg.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.consts.StaticValue;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.StatusName;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.CgysService;
import com.gg.grp.hkycg.service.PubObjFlowService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.ConvertUtils;
import com.gg.grp.hkycg.utils.DataPermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购验收Service实现类
 */
@Slf4j
@Service
public class CgysServiceImpl extends ServiceImpl<GpmCgysmlMapper, GpmCgysml> implements CgysService {

    @Autowired
    private GpmCgysmlMapper cgysmlMapper;

    @Autowired
    private GpmCgysnrMapper cgysnrMapper;

    @Autowired
    private PubObjFlowService pubObjFlowService;

    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    @Autowired
    private PubAuditLogMapper pubAuditLogMapper;

    @Autowired
    private PubObjFlowMapper pubObjFlowMapper;

    @Autowired
    private PubWorkflowMapper pubWorkflowMapper;

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;

    @Autowired
    private DataPermissionUtils dataPermissionUtils;

    @Override
    public CgysSaveResponseVO saveCgys(CgysSaveDTO cgysSaveDTO) {
        try {
            // 设置默认值
            if (StringUtils.isBlank(cgysSaveDTO.getBaseInfo().getGsdm())) {
                cgysSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(cgysSaveDTO.getBaseInfo().getKjnd())) {
                cgysSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
            }
            for (CgysDetailDTO detail : cgysSaveDTO.getCgysDetails()) {
                if (!detail.getSfcgjgyr() && StringUtils.isNotBlank(detail.getJgnrbh())) {
                    throw new GlobalException("采购验收必须由采购结果引入");
                }
            }

            // 生成验收申请编号
            String ysbh = generateYsbh();
            cgysSaveDTO.getBaseInfo().setYsbh(ysbh);

            // 保存主表
            GpmCgysml cgysml = new GpmCgysml();
            BeanUtils.copyProperties(cgysSaveDTO.getBaseInfo(), cgysml);
            cgysml.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            cgysml.setCreateUserDm(LoginInfo.getCurrEmployeeCode());
            cgysml.setCreateUser(LoginInfo.getCurrEmployeeName());
            cgysml.setZt("1"); // 默认保存状态

            cgysmlMapper.insert(cgysml);

            // 保存明细
            if (cgysSaveDTO.getCgysDetails() != null && !cgysSaveDTO.getCgysDetails().isEmpty()) {
                for (int i = 0; i < cgysSaveDTO.getCgysDetails().size(); i++) {
                    CgysDetailDTO detail = cgysSaveDTO.getCgysDetails().get(i);
                    String ysmxxh = generateYsmxxh(i);

                    GpmCgysnr cgysnr = new GpmCgysnr();
                    BeanUtils.copyProperties(detail, cgysnr);
                    // 设置字段
                    cgysnr.setYsmxxh(ysmxxh);
                    cgysnr.setYsbh(ysbh);
                    cgysnr.setSfcgjgyr(ConvertUtils.convertBooleanToString(detail.getSfcgjgyr()));
                    cgysnr.setZfcg(ConvertUtils.convertBooleanToString(detail.getZfcg()));
                    cgysnr.setJkcp(ConvertUtils.convertBooleanToString(detail.getJkcp()));
                    cgysnr.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    cgysnr.setCreateUser(LoginInfo.getCurrEmployeeName());
                    cgysnrMapper.insert(cgysnr);
                }
            }

            log.info("保存采购验收成功，ysbh：{}", ysbh);
            return buildSaveResponseVO(ysbh);

        } catch (Exception e) {
            log.error("保存采购验收失败", e);
            throw new GlobalException("保存采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgysSaveResponseVO updateCgysByYsbh(String ysbh, CgysSaveDTO cgysSaveDTO) {
        try {
            // 检查是否存在
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml existingCgysml = cgysmlMapper.selectOne(queryWrapper);
            if (existingCgysml == null) {
                throw new GlobalException("采购验收不存在，ysbh: " + ysbh);
            }

            // 检查状态是否可编辑
            String currentStatus = existingCgysml.getZt();
            // 只允许编辑保存状态(1)和退回状态(5)的记录
            if (!"1".equals(currentStatus) && !"5".equals(currentStatus)) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法编辑。只有保存或退回状态的采购验收才能编辑");
            }

            // 权限校验：只有录入人才能更新
            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = existingCgysml.getCreateUserDm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 更新操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, ysbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能修改此采购计划");
            }
            log.info("权限校验通过 - 更新操作，当前用户：{}，jhbh：{}", currentUser, ysbh);

            // 更新主表
            GpmCgysml cgysml = new GpmCgysml();
            BeanUtils.copyProperties(cgysSaveDTO.getBaseInfo(), cgysml);
            cgysml.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            cgysml.setUpdateUser(LoginInfo.getCurrEmployeeName());
            // 保持原始的创建人信息
            cgysml.setCreateTime(existingCgysml.getCreateTime());
            cgysml.setCreateUser(existingCgysml.getCreateUser());
            cgysmlMapper.updateById(cgysml);

            // 删除旧明细
            LambdaQueryWrapper<GpmCgysnr> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(GpmCgysnr::getYsbh, ysbh);
            cgysnrMapper.delete(deleteWrapper);

            // 保存新明细
            if (cgysSaveDTO.getCgysDetails() != null && !cgysSaveDTO.getCgysDetails().isEmpty()) {
                for (int i = 0; i < cgysSaveDTO.getCgysDetails().size(); i++) {
                    CgysDetailDTO detail = cgysSaveDTO.getCgysDetails().get(i);
                    String ysmxxh = generateYsmxxh(i);

                    GpmCgysnr cgysnr = new GpmCgysnr();
                    BeanUtils.copyProperties(detail, cgysnr);
                    cgysnr.setYsmxxh(ysmxxh)
                            .setYsbh(ysbh)
                            .setSfcgjgyr(ConvertUtils.convertBooleanToString(detail.getSfcgjgyr()))
                            .setZfcg(ConvertUtils.convertBooleanToString(detail.getZfcg()))
                            .setJkcp(ConvertUtils.convertBooleanToString(detail.getJkcp()))
                            .setCreateTime(existingCgysml.getCreateTime())
                    .setCreateUser(existingCgysml.getCreateUser());
                    cgysnrMapper.insert(cgysnr);
                }
            }

            if (existingCgysml.getZt().equals("5")){
                updateStatusByYsbh(ysbh, 1);
            }

            log.info("更新采购验收成功，ysbh：{}", ysbh);
            return buildSaveResponseVO(ysbh);

        } catch (Exception e) {
            log.error("更新采购验收失败，ysbh: {}", ysbh, e);
            throw new GlobalException("更新采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteByYsbh(String ysbh) {
        try {
            // 查询采购验收主表信息
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml == null) {
                throw new GlobalException("采购验收不存在，无法删除，ysbh: " + ysbh);
            }

            // 检查状态是否可以删除
            String currentStatus = cgysml.getZt();
            if (!"1".equals(currentStatus) && !"5".equals(currentStatus)) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("只有保存状态的采购验收才能删除，当前状态为：" + statusName);
            }

            // 检查创建人权限（只有创建人才能删除）
            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = cgysml.getCreateUserDm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 删除操作，当前用户：{}，录入人：{}，jhid：{}", currentUser, originalInputUser, ysbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能删除此采购计划");
            }

            // 删除主表
            int mlResult = cgysmlMapper.delete(queryWrapper);

            // 删除明细
            LambdaQueryWrapper<GpmCgysnr> nrQueryWrapper = new LambdaQueryWrapper<>();
            nrQueryWrapper.eq(GpmCgysnr::getYsbh, ysbh);
            int nrResult = cgysnrMapper.delete(nrQueryWrapper);

            log.info("删除采购验收成功，ysbh：{}，主表删除数：{}，明细删除数：{}", ysbh, mlResult, nrResult);
            return mlResult > 0;

        } catch (Exception e) {
            log.error("删除采购验收失败，ysbh: {}", ysbh, e);
            throw new GlobalException("删除采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgysSaveResponseVO getCgysDetailByYsbh(String ysbh) {
        try {
            return buildSaveResponseVO(ysbh);
        } catch (Exception e) {
            log.error("查询采购验收详情失败，ysbh: {}", ysbh, e);
            throw new GlobalException("查询采购验收详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<CgysListVO> getCgysPageList(CgysPageQueryDTO queryDTO) {
        try {
            // 设置默认值
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            String gsdm = StringUtils.isNotBlank(queryDTO.getGsdm()) ? queryDTO.getGsdm() : LoginInfo.getCurrCorpCode();
            String kjnd = StringUtils.isNotBlank(queryDTO.getKjnd()) ? queryDTO.getKjnd() : LoginInfo.getCurrAccountantYear();
            Integer offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取数据权限信息
            String currEmployeeCode = LoginInfo.getCurrEmployeeCode();
            String dataPermission = dataPermissionUtils.getCurrentUserDataPermission();
            String currDeptCode = LoginInfo.getCurrDeptCode(); // 使用LoginInfo.getCurrDeptCode()获取部门代码

            log.info("执行采购验收分页查询，参数：gsdm={}, kjnd={}, condition={}, zt={}, offset={}, size={}, 用户={}, 权限={}, 部门={}",
                    gsdm, kjnd, queryDTO.getCondition(), queryDTO.getZt(), offset, queryDTO.getSize(), currEmployeeCode, dataPermission, currDeptCode);

            // 调用Mapper方法
            List<CgysListVO> resultList = cgysmlMapper.getCgysPageList(
                    gsdm, kjnd, queryDTO.getCondition(), queryDTO.getZt(),
                    queryDTO.getStartDate(), queryDTO.getEndDate(),
                    offset, queryDTO.getSize(),
                    currEmployeeCode, dataPermission, currDeptCode
            );

            log.info("分页查询采购验收成功，记录数：{}", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("分页查询采购验收失败", e);
            throw new GlobalException("分页查询采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Map<Integer, List<PubObjFlowTemp>> commitProxyByYsbh(BillnoType billnoType, String ysbh, Double money, String auditor) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            log.info("开始提交采购验收，ysbh：{}，金额：{}，提交人：{}", ysbh, money, auditor);

            // 1. 校验采购验收状态，只有保存状态（1）才能提交
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml == null) {
                throw new GlobalException("采购验收不存在，无法提交，ysbh: " + ysbh);
            }

            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = cgysml.getCreateUserDm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 提交操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, ysbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能提交此采购计划");
            }
            log.info("权限校验通过 - 提交操作，当前用户：{}，jhbh：{}", currentUser, ysbh);

            // 3. 检查状态 - 只有保存状态（1）才能提交
            if (cgysml.getZt() == null || !"1".equals(cgysml.getZt()) && !"5".equals(cgysml.getZt())) {
                String statusName = StatusName.findByStatus(cgysml.getZt());
                throw new GlobalException("只有保存状态的采购验收才能提交，当前状态为：" + statusName);
            }

            // 4. 获取工作流程模板节点
            List<PubObjFlowTemp> templates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, List<PubObjFlowTemp>> nodeMap = new HashMap<>();

            if (templates == null || templates.isEmpty()) {
                // 如果没有配置审核模板，创建默认审核节点
                log.warn("未找到审核流程模板，将创建默认审核节点，单据类型：{}", billnoType.getCode());

                PubObjFlowTemp defaultTemplate = new PubObjFlowTemp();
                defaultTemplate.setShrdm(StaticValue.getZydm());
                defaultTemplate.setShrxm(StaticValue.getZymc());
                defaultTemplate.setJdmc("超级管理员审核");
                defaultTemplate.setJddm(1314);
                defaultTemplate.setGsdm(StaticValue.getGsdm());
                defaultTemplate.setDjlxid(BillnoType.CGSQ.getCode());
//                defaultTemplate.setFlowcode("CGSQ_FLOW_001");
                defaultTemplate.setXh("1");
                List<PubObjFlowTemp> defaultList = new ArrayList<>();
                defaultList.add(defaultTemplate);
                nodeMap.put(1, defaultList);

                log.info("已创建默认审核节点，审核人：{}", defaultTemplate.getShrxm());
            } else {
                // 按节点序号分组
                for (PubObjFlowTemp template : templates) {
                    Integer nodeSeq = template.getXh() != null ? Integer.parseInt(template.getXh()) : 1;
                    if (template.getDynamicAcquisition().equals("1") && StringUtils.isNotBlank(template.getShtj())){
                        JSONObject auditorInfo = cgjhmlMapper.findAuditorInfo(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear(),
                                ysbh, template.getShtj());
                        if (auditorInfo!=null){
                            template.setShrxm(auditorInfo.getString("shrxm"));
                            template.setShrdm(auditorInfo.getString("shrdm"));
                        }
                    }
                    nodeMap.computeIfAbsent(nodeSeq, k -> new ArrayList<>()).add(template);
                }
                log.info("获取审核流程模板成功，共{}个节点，模板数量：{}", nodeMap.size(), templates.size());
            }

            log.info("采购验收提交成功，ysbh：{}", ysbh);
            return nodeMap;
        } catch (Exception e) {
            log.error("提交采购验收失败，ysbh: {}", ysbh, e);
            throw new GlobalException("提交采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void checkByYsbh(BillnoType billType, String ysbh, String opinion, String auditor, BigDecimal money) {
        try {
            log.info("开始审核采购验收，ysbh：{}，审核人：{}，意见：{}", ysbh, auditor, opinion);

            // 1. 查询采购验收详情
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml == null) {
                throw new GlobalException("采购验收不存在，无法审核，ysbh: " + ysbh);
            }

            // 2. 检查状态 - 只有已提交（2）或审核中（3）的采购验收才能审核
            if (cgysml.getZt() == null ||
                    (!"2".equals(cgysml.getZt()) && !"3".equals(cgysml.getZt()))) {
                String statusName = StatusName.findByStatus(cgysml.getZt());
                throw new GlobalException("只有已提交或审核中状态的采购验收才能审核，当前状态为：" + statusName);
            }

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 3. 获取审批流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billType.getModCode(), String.valueOf(billType.getCode()));

            if (workflow == null) {
                log.warn("未找到审核流程配置，使用默认配置");
            }

            // 4. 查询当前审核节点
            PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                    billType.getModCode(),
                    String.valueOf(billType.getCode()),
                    ysbh
            );

            if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("该节点已被其他人审核或您无权限审核");
            }

            // 6. 执行审核
            int nowNote = Integer.parseInt(nowNodeInfo.getAuditFlag());
            int nextNote = Integer.parseInt(nowNodeInfo.getAuditAftFlag());

            // 判断是否为终审（nextNote == -1 表示最后一个节点）
            boolean isLastNode = (nextNote == -1);

            log.info("审核节点信息 - 当前节点：{}，下一节点：{}，是否终审：{}", nowNote, nextNote, isLastNode);

            // 更新当前节点为已审核
            nowNodeInfo.setIsaudit("1");
            nowNodeInfo.setSpecificCheckPerson(LoginInfo.getCurrEmployeeName());

            // 更新当前节点状态
            LambdaUpdateWrapper<PubObjFlow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PubObjFlow::getModcode, billType.getModCode())
                    .eq(PubObjFlow::getDjlx, String.valueOf(billType.getCode()))
                    .eq(PubObjFlow::getDjh, ysbh)
                    .eq(PubObjFlow::getAuditFlag, String.valueOf(nowNote))
                    .set(PubObjFlow::getIsaudit, "1")
                    .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

            pubObjFlowMapper.update(null, updateWrapper);

            // 7. 记录审核日志
            createAuditLog(ysbh, billType, nowNote, "审核", opinion, auditor, money, nowNodeInfo.getNodeName());

            // 8. 如果是最后一个节点，更新状态为已审核
            if (isLastNode) {
                updateStatusByYsbh(ysbh, 4); // 已审核状态
                log.info("采购验收审核完成，已更新为已审核状态，ysbh：{}", ysbh);
            } else {
                updateStatusByYsbh(ysbh, 3); // 审核中状态
                log.info("采购验收审核通过当前节点，状态更新为审核中，ysbh：{}", ysbh);
            }

            log.info("采购验收审核成功，ysbh：{}，审核人：{}，是否终审：{}", ysbh, auditor, isLastNode);

        } catch (Exception e) {
            log.error("审核采购验收失败，ysbh: {}", ysbh, e);
            throw new GlobalException("审核采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void updateStatusByYsbh(String ysbh, Integer status) {
        try {
            LambdaUpdateWrapper<GpmCgysml> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmCgysml::getYsbh, ysbh);

            GpmCgysml updateEntity = new GpmCgysml();
            // 状态字段需要转换为字符串
            updateEntity.setZt(String.valueOf(status));
            int result = cgysmlMapper.update(updateEntity, updateWrapper);
            log.info("更新采购验收状态成功，ysbh：{}，状态：{}，影响行数：{}", ysbh, status, result);
        } catch (Exception e) {
            log.error("更新采购验收状态失败，ysbh: {}，状态：{}", ysbh, status, e);
            throw new GlobalException("更新状态失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgysSaveResponseVO callBackByYsbh(String ysbh) {
        try {
            log.info("开始收回采购验收，ysbh：{}", ysbh);

            // 1. 查询采购验收
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml == null) {
                throw new GlobalException("采购验收不存在，ysbh: " + ysbh);
            }

            // 2. 检查状态是否可以收回
            String currentStatus = cgysml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购验收状态异常，无法收回");
            }

            // 权限校验：只有录入人才能收回
            String currentUser = LoginInfo.getCurrEmployeeCode();
            String originalInputUser = cgysml.getCreateUserDm();
            if (!currentUser.equals(originalInputUser)) {
                log.warn("权限校验失败 - 收回操作，当前用户：{}，录入人：{}，jhbh：{}", currentUser, originalInputUser, ysbh);
                throw new GlobalException("无权限操作：只有录入人（" + originalInputUser + "）才能收回此采购计划");
            }
            log.info("权限校验通过 - 收回操作，当前用户：{}，jhbh：{}", currentUser, ysbh);

            // 只有已提交(2)的状态才能收回
            if (!"2".equals(currentStatus)) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法收回。只有已提交的采购验收才能收回");
            }

            // 3. 检查是否已经有人开始审核
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(ysbh);
            boolean hasStartedAudit = flowNodes.stream()
                    .anyMatch(node -> "1".equals(node.getIsaudit()));

            if (hasStartedAudit) {
                throw new GlobalException("该采购验收已开始审核，无法收回");
            }

            // 5. 删除工作流程节点
            try {
                // 删除工作流程实例
                if (pubObjFlowService != null) {
                    pubObjFlowService.deleteByCon(BillnoType.CGYS.getModCode(),
                            String.valueOf(BillnoType.CGYS.getCode()), ysbh);
                }
            } catch (Exception e) {
                log.warn("清理工作流程数据时出现异常，但不影响收回操作，单据编号：{}，异常：{}", ysbh, e.getMessage());
            }

            // 6. 更新状态为保存
            updateStatusByYsbh(ysbh, 1);

            log.info("采购验收收回成功，ysbh：{}", ysbh);

            return buildSaveResponseVO(ysbh);

        } catch (Exception e) {
            log.error("收回采购验收失败，ysbh: {}", ysbh, e);
            throw new GlobalException("收回采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgysSaveResponseVO checkCallBackByYsbh(String ysbh, String opinion, String auditor) {
        try {
            log.info("开始退审采购验收，ysbh：{}，退审人：{}", ysbh, auditor);
            // 1. 查询采购验收
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);
            if (cgysml == null) {
                throw new GlobalException("采购验收不存在，单据编号：" + ysbh);
            }
            String status = cgysml.getZt();
            if (StringUtils.isBlank(status)) {
                throw new GlobalException("采购验收状态异常，无法退审");
            }

            // 1. 查询当前审核节点
            PubObjFlow currentNode = pubObjFlowService.selectNowNodeByDjh(
                    BillnoType.CGYS.getModCode(),
                    String.valueOf(BillnoType.CGYS.getCode()),
                    ysbh
            );

            if (currentNode == null) {
                throw new GlobalException("未找到当前审核节点，无法退审");
            }

            // 2. 检查退审权限
            if (!currentNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                throw new GlobalException("您无权限对该采购验收进行退审操作");
            }

            int currentNodeSeq = Integer.parseInt(currentNode.getAuditFlag());
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(LoginInfo.getCurrCorpCode(), LoginInfo.getCurrAccountantYear(),
                    BillnoType.CGYS.getModCode(), String.valueOf(BillnoType.CGYS.getCode()));

            if (workflow == null) {
                throw new GlobalException("未找到审核流程配置");
            }

            if (status.equals("2")) {
                updateStatusByYsbh(ysbh, 5);
                createAuditLog(ysbh, BillnoType.CGYS, currentNodeSeq, "退审", opinion, LoginInfo.getCurrEmployeeCode(), cgysml.getBcysje(),currentNode.getNodeName());
                return buildSaveResponseVO(ysbh);
            }
            // 只有审核中(3)的状态才能退审
            if (!status.equals("3")) {
                throw new GlobalException("当前状态为：" + StatusName.findByStatus(cgysml.getZt()) + "，无法退审。只有审核中的采购计划才能退审");
            }

            // 查询所有工作流程节点，找到上一个节点
            List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(ysbh);
            PubObjFlow previousNode = null;

            for (PubObjFlow node : allNodes) {
                int nodeSeq = Integer.parseInt(node.getAuditFlag());
                if (nodeSeq < currentNodeSeq && (previousNode == null ||
                        Integer.parseInt(previousNode.getAuditFlag()) < nodeSeq)) {
                    previousNode = node;
                }
            }

            // 6. 更新工作流程状态
            if (previousNode != null) {
                LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGYS.getModCode())
                        .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGYS.getCode()))
                        .eq(PubObjFlow::getDjh, ysbh)
                        .eq(PubObjFlow::getAuditFlag, previousNode.getAuditFlag())
                        .set(PubObjFlow::getIsaudit, "0")
                        .set(PubObjFlow::getSpecificCheckPerson, "");

                pubObjFlowMapper.update(null, updatePreviousWrapper);
                if (previousNode == allNodes.get(0)) {
                    updateStatusByYsbh(ysbh, 5);
                } else {
                    // 更新采购计划状态为审核中(3)
                    updateStatusByYsbh(ysbh, 3);
                }
            }

            // 3. 记录退审日志
            createAuditLog(ysbh, BillnoType.CGYS, currentNodeSeq, "退审", opinion, LoginInfo.getCurrEmployeeCode(), cgysml.getBcysje(),currentNode.getNodeName());

            log.info("采购验收退审成功，ysbh：{}，退审人：{}", ysbh, auditor);

            return buildSaveResponseVO(ysbh);

        } catch (Exception e) {
            log.error("退审采购验收失败，ysbh: {}，退审人：{}", ysbh, auditor, e);
            throw new GlobalException("退审采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgysSaveResponseVO noAuditByYsbh(String ysbh) {
        try {
            log.info("开始销审采购验收，ysbh：{}", ysbh);

            // 1. 查询采购验收
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml == null) {
                throw new GlobalException("采购验收不存在，ysbh: " + ysbh);
            }

            // 2. 检查状态是否可以销审
            String currentStatus = cgysml.getZt();
            if (currentStatus == null) {
                throw new GlobalException("采购验收状态异常，无法销审");
            }

            int status = Integer.parseInt(currentStatus);
            // 只有审核中(3)、已审核(4)的状态才能销审
            if (status != 3 && status != 4) {
                String statusName = StatusName.findByStatus(currentStatus);
                throw new GlobalException("当前状态为：" + statusName + "，无法销审。只有已提交、审核中或已审核的采购验收才能销审");
            }
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            PubObjFlow lastAuditNode = pubObjFlowService.selectLastAuditNodeByDjh(
                    BillnoType.CGYS.getModCode(),
                    String.valueOf(BillnoType.CGYS.getCode()),
                    ysbh
            );

            // 3. 销审逻辑
            if (status == 3 || status == 4) {
                // 如果当前状态是审核中(3)或已审核(4)，需要退回到上一个节点

                // 查询所有工作流程节点
                List<PubObjFlow> allNodes = pubObjFlowService.selectByBillno(ysbh);
                if (allNodes.isEmpty()) {
                    // 如果没有工作流程节点，直接设置为已提交状态
                    updateStatusByYsbh(ysbh, 2);
                    log.info("采购验收没有工作流程节点，销审为已提交状态，ysbh：{}", ysbh);
                } else {
                    // 找到最后一个已审核的节点
                    PubObjFlow lastAuditedNode = null;
                    int maxAuditedNodeSeq = -1;

                    for (PubObjFlow node : allNodes) {
                        if ("1".equals(node.getIsaudit())) { // 已审核的节点
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq > maxAuditedNodeSeq) {
                                maxAuditedNodeSeq = nodeSeq;
                                lastAuditedNode = node;
                            }
                        }
                    }

                    if (lastAuditedNode != null) {
                        if (!lastAuditedNode.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                            throw new GlobalException("您无权限对该采购验收进行销审操作");
                        }

                        LambdaUpdateWrapper<PubObjFlow> updatePreviousWrapper = new LambdaUpdateWrapper<>();
                        updatePreviousWrapper.eq(PubObjFlow::getModcode, BillnoType.CGYS.getModCode())
                                .eq(PubObjFlow::getDjlx, String.valueOf(BillnoType.CGYS.getCode()))
                                .eq(PubObjFlow::getDjh, ysbh)
                                .eq(PubObjFlow::getAuditFlag, lastAuditedNode.getAuditFlag())
                                .set(PubObjFlow::getIsaudit, "0")
                                .set(PubObjFlow::getSpecificCheckPerson, LoginInfo.getCurrEmployeeName());

                        pubObjFlowMapper.update(null, updatePreviousWrapper);

                        // 有已审核的节点，找到它的上一个节点
                        PubObjFlow previousNode = null;
                        int previousNodeSeq = -1;

                        for (PubObjFlow node : allNodes) {
                            int nodeSeq = Integer.parseInt(node.getAuditFlag());
                            if (nodeSeq < maxAuditedNodeSeq && nodeSeq > previousNodeSeq) {
                                previousNodeSeq = nodeSeq;
                                previousNode = node;
                            }
                        }

                        if (previousNode != null) {
                            // 更新采购验收状态为审核中(3)
                            updateStatusByYsbh(ysbh, 3);
                            log.info("采购验收销审成功，退回到上一个节点，ysbh：{}，上一节点序号：{}",
                                    ysbh, previousNode.getAuditFlag());
                        } else {
                            // 更新采购验收状态为已提交(2)
                            updateStatusByYsbh(ysbh, 2);
                            log.info("采购验收销审成功，第一个审核节点已重置为未审核状态，ysbh：{}，第一节点序号：{}",
                                    ysbh, maxAuditedNodeSeq);
                        }
                    } else {
                        updateStatusByYsbh(ysbh, 2);
                        log.info("采购验收没有已审核节点，销审为已提交状态，ysbh：{}", ysbh);
                    }
                }
            }

            // 4. 记录销审日志
            createAuditLog(ysbh, BillnoType.CGYS, Integer.parseInt(lastAuditNode.getAuditFlag()), "销审", "", LoginInfo.getCurrEmployeeName(), cgysml.getBcysje(),lastAuditNode.getNodeName());

            log.info("采购验收销审成功，ysbh：{}", ysbh);

            return buildSaveResponseVO(ysbh);

        } catch (Exception e) {
            log.error("销审采购验收失败，ysbh: {}", ysbh, e);
            throw new GlobalException("销审采购验收失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CgysAuditLogVO findCheckLogByYsbh(BillnoType billnoType, String ysbh) {
        try {
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 查询工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billnoType.getModCode(), String.valueOf(billnoType.getCode()));

            String flowcode = "CGYS_FLOW_001"; // 默认流程代码
            if (workflow != null) {
                flowcode = workflow.getFlowcode();
            }

            // 查询审核日志
            List<PubAuditLog> logList = pubAuditLogMapper.selectByBillId(gsdm, kjnd, ysbh, flowcode);

            // 转换为VO
            CgysAuditLogVO auditLogVO = new CgysAuditLogVO();
            auditLogVO.setYsbh(ysbh);

            // 查询采购验收基本信息
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml != null) {
                auditLogVO.setYsmc(cgysml.getYsmc());
            }

            // 转换日志列表
            List<CgysAuditLogVO.AuditLogItem> logItems = logList.stream().map(log -> {
                CgysAuditLogVO.AuditLogItem item = new CgysAuditLogVO.AuditLogItem();
                item.setGsdm(log.getGsdm());
                item.setKjnd(log.getKjnd());
                item.setLogid(log.getLogid());
                item.setBillid(log.getBillid());
                item.setBillname(log.getBillname());
                item.setFlowcode(log.getFlowcode());
                item.setFlowname(log.getFlowname());
                item.setModname(log.getModname());
                item.setBizname(log.getBizname());
                item.setNodeseq(log.getNodeseq());
                item.setNodename(log.getNodename());
                item.setAuditorid(log.getAuditorid());
                item.setAuditor(log.getAuditor());
                item.setCertigierid(log.getCertigierid());
                item.setCertigier(log.getCertigier());
                item.setAdatetime(log.getAdatetime());
                item.setAmt(log.getAmt());
                item.setRemark(log.getRemark());
                item.setAtype(log.getAtype());
                item.setLogseq(log.getLogseq());
                item.setServDateTime(log.getServDateTime());
                item.setComputerName(log.getComputerName());
                return item;
            }).collect(Collectors.toList());

            // 创建日志标题列表
            List<CgysAuditLogVO.LogTitleItem> logTitleList = new ArrayList<>();

            // 添加提交节点
            CgysAuditLogVO.LogTitleItem titleNode = new CgysAuditLogVO.LogTitleItem();
            titleNode.setNodename("提交审核");
            titleNode.setGsdm(gsdm);
            titleNode.setNodeseq(-9); // 提交节点编号
            titleNode.setKjnd(kjnd);
            titleNode.setIsaudit(logList.isEmpty() ? "0" : "1");
            titleNode.setAuditStatus(logList.isEmpty() ? "未提交" : "已提交");

            // 如果有提交日志，设置提交时间
            logList.stream()
                    .filter(log -> log.getNodeseq() != null && log.getNodeseq() == -9)
                    .findFirst()
                    .ifPresent(log -> titleNode.setAuditTime(log.getServDateTime()));

            logTitleList.add(titleNode);

            // 查询工作流程节点，添加审核节点标题（使用ysbh）
            List<PubObjFlow> flowNodes = pubObjFlowService.selectByBillno(ysbh);

            // 查询工作流程模板节点（用于获取节点名称）
            List<PubObjFlowTemp> flowTemplates = pubObjFlowTempService.selectListByDjlxid(billnoType.getCode());
            Map<Integer, String> nodeNameMap = new HashMap<>();
            for (PubObjFlowTemp template : flowTemplates) {
                nodeNameMap.put(Integer.valueOf(template.getXh()), template.getJdmc());
            }

            for (PubObjFlow node : flowNodes) {
                CgysAuditLogVO.LogTitleItem flowTitleNode = new CgysAuditLogVO.LogTitleItem();

                // 通过节点序号获取正确的节点名称
                Integer nodeSeq = null;
                try {
                    nodeSeq = Integer.valueOf(node.getAuditFlag());
                    // 从工作流程模板中获取节点名称，如果没有则使用审核人名称作为备选
                    String nodeName = nodeNameMap.get(nodeSeq);
                    if (nodeName != null && !nodeName.trim().isEmpty()) {
                        flowTitleNode.setNodename(nodeName);
                    } else {
                        // 如果模板中没有节点名称，使用审核人名称作为备选
                        flowTitleNode.setNodename(node.getShrmc() != null ? node.getShrmc() : "审核节点");
                    }
                } catch (NumberFormatException e) {
                    flowTitleNode.setNodename("审核节点");
                    nodeSeq = 1;
                }

                flowTitleNode.setGsdm(node.getGsdm());
                flowTitleNode.setNodeseq(nodeSeq);
                flowTitleNode.setKjnd(node.getKjnd());
                flowTitleNode.setIsaudit("1".equals(node.getIsaudit()) ? "1" : "0");
                flowTitleNode.setAuditStatus("1".equals(node.getIsaudit()) ? "已审核" : "待审核");

                // 查找对应的审核日志设置审核时间
                logList.stream()
                        .filter(log -> log.getNodeseq() != null && log.getNodeseq().equals(flowTitleNode.getNodeseq()))
                        .findFirst()
                        .ifPresent(log -> flowTitleNode.setAuditTime(log.getServDateTime()));

                logTitleList.add(flowTitleNode);
            }

            auditLogVO.setLogList(logItems);
            auditLogVO.setLogTitleList(logTitleList);

            return auditLogVO;

        } catch (Exception e) {
            log.error("查询采购验收审核日志失败，ysbh: {}", ysbh, e);
            throw new GlobalException("查询审核日志失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Object isCheckedByAuthorityByYsbh(String ysbh) {
        try {
            JSONObject resultJson = new JSONObject();
            String currentUser = LoginInfo.getCurrEmployeeName();

            // 查询采购验收
            LambdaQueryWrapper<GpmCgysml> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysml::getYsbh, ysbh);
            GpmCgysml cgysml = cgysmlMapper.selectOne(queryWrapper);

            if (cgysml == null) {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", true);
                resultJson.put("result", "此流程单据已经被删除，当前审核人" + currentUser + "无法查看");
                return resultJson;
            }

            String status = cgysml.getZt();
            if ("2".equals(status) || "3".equals(status)) {
                // 查询当前审核节点
                PubObjFlow nowNodeInfo = pubObjFlowService.selectNowNodeByDjh(
                        BillnoType.CGYS.getModCode(),
                        String.valueOf(BillnoType.CGYS.getCode()),
                        ysbh
                );

                if (nowNodeInfo == null || !nowNodeInfo.getShr1().contains(LoginInfo.getCurrEmployeeCode())) {
                    resultJson.put("isCheck", true);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "此流程当前节点已被审批或当前审核人" + currentUser + "无权审批!");
                } else {
                    resultJson.put("isCheck", false);
                    resultJson.put("isDelete", false);
                    resultJson.put("result", "");
                }
            } else {
                resultJson.put("isCheck", true);
                resultJson.put("isDelete", false);
                resultJson.put("result", "当前状态无需审核");
            }

            return resultJson;

        } catch (Exception e) {
            log.error("检查采购验收审核权限异常，ysbh：{}", ysbh, e);

            JSONObject errorResult = new JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 构建保存响应VO
     */
    private CgysSaveResponseVO buildSaveResponseVO(String ysbh) {
        // 查询主表
        LambdaQueryWrapper<GpmCgysml> mlQueryWrapper = new LambdaQueryWrapper<>();
        mlQueryWrapper.eq(GpmCgysml::getYsbh, ysbh);
        GpmCgysml cgysml = cgysmlMapper.selectOne(mlQueryWrapper);

        if (cgysml == null) {
            throw new GlobalException("采购验收不存在，ysbh: " + ysbh);
        }

        // 查询明细
        LambdaQueryWrapper<GpmCgysnr> nrQueryWrapper = new LambdaQueryWrapper<>();
        nrQueryWrapper.eq(GpmCgysnr::getYsbh, ysbh);
        List<GpmCgysnr> cgysDetails = cgysnrMapper.selectList(nrQueryWrapper);

        // 构建响应VO
        CgysSaveResponseVO responseVO = new CgysSaveResponseVO();

        // 基础信息
        CgysBaseInfoVO baseInfoVO = new CgysBaseInfoVO();
        BeanUtils.copyProperties(cgysml, baseInfoVO);
        baseInfoVO.setZtmc(StatusName.findByStatus(cgysml.getZt()));
        responseVO.setBaseInfo(baseInfoVO);

        // 明细信息
        List<CgysDetailVO> detailVOs = cgysDetails.stream().map(detail -> {
            CgysDetailVO detailVO = new CgysDetailVO();
            BeanUtils.copyProperties(detail, detailVO);
            detailVO.setSfcgjgyr(ConvertUtils.convertStringToBoolean(detail.getSfcgjgyr()));
            detailVO.setZfcg(Boolean.parseBoolean(detail.getZfcg()));
            detailVO.setJkcp(Boolean.parseBoolean(detail.getJkcp()));
            return detailVO;
        }).collect(Collectors.toList());
        responseVO.setCgysDetails(detailVOs);

        return responseVO;
    }

    /**
     * 生成验收申请编号
     */
    private String generateYsbh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        // 查询当天最大序号
        String prefix = "CGYS" + dateStr;
        String maxYsbh = cgysmlMapper.selectMaxYsbhByPrefix(prefix);
        
        int sequenceNumber = 1;
        if (maxYsbh != null && maxYsbh.length() >= prefix.length() + 4) {
            // 提取序列号部分
            String sequence = maxYsbh.substring(prefix.length());
            try {
                sequenceNumber = Integer.parseInt(sequence) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析序列号失败，使用默认值1: {}", sequence, e);
            }
        }
        
        // 格式化为4位数字，例如：0001, 0002, ..., 9999
        return prefix + String.format("%04d", sequenceNumber);
    }

    /**
     * 生成明细编号，格式：CGYSMX年月日001, CGYSMX年月日002, ...
     * @param detailIndex 明细索引，从0开始
     * @return 明细编号
     */
    private String generateYsmxxh(int detailIndex) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String mxPrefix = "CGYSMX" + dateStr;
        
        // 序号从1开始，加上传入的索引值
        int sequenceNumber = 1 + detailIndex;
        
        // 格式化为3位数字，例如：001, 002, ..., 999
        return mxPrefix + String.format("%03d", sequenceNumber);
    }

    /**
     * 创建审核日志
     */
    private void createAuditLog(String ysbh, BillnoType billType, int nodeSeq, String auditType, String opinion, String auditor, BigDecimal money,String nodename) {
        try {

            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();

            // 获取工作流程配置
            PubWorkflow workflow = pubWorkflowMapper.selectByModAndBiz(gsdm, kjnd,
                    billType.getModCode(), String.valueOf(billType.getCode()));

            String flowCode = workflow != null ? workflow.getFlowcode() : "CGYS_FLOW_001";
            String flowName = workflow != null ? workflow.getFlowname() : "采购验收审核流程";

            PubAuditLog auditLog = new PubAuditLog();
            auditLog.setGsdm(gsdm);
            auditLog.setKjnd(kjnd);

            // 获取新的日志ID
            Integer newLogID = pubAuditLogMapper.selectMaxLogID();
            if (newLogID == null) {
                newLogID = 1;
            }
            auditLog.setLogid(newLogID.longValue());

            // 设置单据信息
            auditLog.setBillid(ysbh);
            if (workflow != null) {
                auditLog.setBillname(workflow.getBizname());
            }
            auditLog.setFlowcode(flowCode);
            auditLog.setFlowname(flowName);
            auditLog.setModname(billType.getModCode());
            if (workflow != null) {
                auditLog.setBizname(workflow.getBizname());
            }

            // 设置审核节点信息
            auditLog.setNodeseq(nodeSeq);
            auditLog.setNodename(nodename);

            // 设置审核人信息
            auditLog.setAuditorid(LoginInfo.getCurrEmployeeCode());
            auditLog.setAuditor(auditor != null ? auditor : LoginInfo.getCurrEmployeeName());
            auditLog.setCertigierid(0);
            auditLog.setCertigier("");

            // 设置审核时间
            SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setAdatetime(df1.format(new Date()));

            // 设置金额和备注
            auditLog.setAmt(money != null ? money : BigDecimal.ZERO);
            auditLog.setRemark(opinion != null ? opinion : "");
            auditLog.setAtype(auditType);

            // 设置服务器时间
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            auditLog.setServDateTime(df2.format(new Date()));

            // 设置计算机信息
            try {
                String computerMsg = java.net.InetAddress.getLocalHost().getHostName()
                        + "/" + java.net.InetAddress.getLocalHost().getHostAddress();
                auditLog.setComputerName(computerMsg);
            } catch (Exception e) {
                auditLog.setComputerName("Unknown");
            }

            // 保存审核日志
            pubAuditLogMapper.insert(auditLog);

            log.info("创建采购验收审核日志成功，ysbh：{}，审核类型：{}，审核人：{}", ysbh, auditType, auditor);

        } catch (Exception e) {
            log.error("创建采购验收审核日志失败，ysbh：{}", ysbh, e);
            // 不抛出异常，避免影响主流程
        }
    }
}