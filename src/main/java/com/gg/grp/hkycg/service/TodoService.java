package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.model.dto.TodoQueryDTO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.model.vo.TodoItemVO;

/**
 * 待办事项Service接口
 */
public interface TodoService {
    
    /**
     * 查询我的申请列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TodoItemVO> getMyApplications(TodoQueryDTO queryDTO);
    
    /**
     * 查询我已审核的列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<TodoItemVO> getMyAudited(TodoQueryDTO queryDTO);
} 