package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.model.dto.CgjhPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgjhSaveDTO;
import com.gg.grp.hkycg.model.vo.CgjhAuditLogVO;
import com.gg.grp.hkycg.model.vo.CgjhListVO;
import com.gg.grp.hkycg.model.vo.CgjhSaveResponseVO;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.util.List;
import java.util.Map;

/**
 * 采购计划Service接口
 */
public interface CgjhService{

    /**
     * 保存采购计划
     * @param cgjhSaveDTO 采购计划保存DTO
     * @return 保存的采购计划详情
     */
    CgjhSaveResponseVO saveCgjh(CgjhSaveDTO cgjhSaveDTO);

    /**
     * 更新采购计划
     * @param djbh 单据编号
     * @param cgjhSaveDTO 采购计划保存DTO
     * @return 更新后的采购计划详情
     */
    CgjhSaveResponseVO updateCgjh(String djbh, CgjhSaveDTO cgjhSaveDTO);

    /**
     * 删除采购计划
     * @param jhid 计划ID
     * @return 是否删除成功
     */
    boolean deleteByIdCgjh(String jhid);

    /**
     * 根据单据编号删除采购计划
     * @param jhbh 单据编号
     * @return 是否删除成功
     */
    boolean deleteByJhbh(String jhbh);

    /**
     * 根据计划ID查询采购计划详情
     * @param jhid 计划ID
     * @return 采购计划详情VO
     */
    CgjhSaveResponseVO getCgjhDetailByJhid(String jhid);

    /**
     * 根据单据编号查询采购计划详情
     * @param jhbh 单据编号
     * @return 采购计划详情VO
     */
    CgjhSaveResponseVO getCgjhDetailByJhbh(String jhbh);

    /**
     * 分页查询采购计划列表
     * @param queryDTO 查询参数
     * @return 采购计划列表
     */
    List<CgjhListVO> getCgjhPageList(CgjhPageQueryDTO queryDTO);

    List<CgjhListVO> getApprovedCgjhList(CgjhPageQueryDTO queryDTO);

    /**
     * 提交采购计划
     *
     * @param djbh 单据编号
     * @return 工作流程节点映射
     */
    Map<Integer, List<PubObjFlowTemp>> commitProxy(BillnoType billnoType, String djbh, Double money);

    /**
     * 根据单据编号查询采购计划（用于提交后返回）
     * @param djbh 单据编号
     * @return 采购计划详情VO
     */
    CgjhSaveResponseVO findByLid(String djbh);

    /**
     * 查询审核日志
     * @param billnoType 单据类型
     * @param djbh 单据编号
     * @return 审核日志VO
     */
    CgjhAuditLogVO findCheckLog(BillnoType billnoType, String djbh);

    /**
     * 检查采购计划是否有权限审核
     * @return 权限检查结果
     */
    Object isCheckedByAuthority(String jhbh);

    /**
     * 审核采购计划
     * @param billType 单据类型
     * @param djbh 单据编号
     * @param opinion 审核意见
     * @param auditor 审核人
     * @param money 金额
     */
    void check(BillnoType billType, String djbh, String opinion, String auditor, java.math.BigDecimal money);

    /**
     * 更新采购计划状态
     * @param djbh 单据编号
     * @param status 状态
     */
    void updateStatus(String djbh, Integer status);

    /**
     * 收回采购计划
     * @param djbh 单据编号
     * @return 收回后的采购计划详情
     */
    CgjhSaveResponseVO callBack(String djbh);

    /**
     * 退审采购计划
     * @param djbh 单据编号
     * @param opinion 退审意见
     * @param auditor 退审人
     * @return 退审后的采购计划详情
     */
    CgjhSaveResponseVO checkCallBack(String djbh, String opinion, String auditor);

    /**
     * 销审采购计划
     * @param djbh 单据编号
     * @return 销审后的采购计划详情
     */
    CgjhSaveResponseVO noAudit(String djbh);

    void saveCgjhDetails(String jhbh, CgjhSaveDTO cgjhSaveDTO);

    /**
     * 分页查询采购计划列表
     * @param queryDTO 查询参数
     * @return 采购计划列表
     */
//    List<CgjhListVO> getApprovedCgjhList(CgjhPageQueryDTO queryDTO);

}