package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.model.dto.CgsqSaveDTO;
import com.gg.grp.hkycg.model.vo.CgsqSaveResponseVO;
import com.gg.grp.hkycg.model.dto.CgsqPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgsqListVO;
import com.gg.grp.hkycg.model.dto.CgsqCheckParams;
import com.gg.grp.hkycg.model.vo.CgsqAuditLogVO;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;

import java.util.List;
import java.util.Map;

/**
 * 采购申请Service接口
 */
public interface CgsqService {

    /**
     * 保存采购申请
     * @param cgsqSaveDTO 采购申请保存DTO
     * @return 保存的采购申请详情
     */
    CgsqSaveResponseVO saveCgsq(CgsqSaveDTO cgsqSaveDTO);

    /**
     * 更新采购申请（兼容方法，使用单据编号）
     * @param djbh 单据编号
     * @param cgsqSaveDTO 采购申请保存DTO
     * @return 更新后的采购申请详情
     */
    CgsqSaveResponseVO updateCgsq(String djbh, CgsqSaveDTO cgsqSaveDTO);

    /**
     * 根据申请编号删除采购申请
     * @param sqbh 申请编号
     * @return 是否删除成功
     */
    boolean deleteBySqbh(String sqbh);

    /**
     * 根据单据编号删除采购申请（兼容方法）
     * @param djbh 单据编号
     * @return 是否删除成功
     */
    boolean deleteByDjbh(String djbh);

    /**
     * 根据申请编号查询采购申请详情
     * @param sqbh 申请编号
     * @return 采购申请详情VO
     */
    CgsqSaveResponseVO getCgsqDetailBySqbh(String sqbh);

    /**
     * 根据单据编号查询采购申请详情（兼容方法）
     * @param djbh 单据编号
     * @return 采购申请详情VO
     */
    CgsqSaveResponseVO getCgsqDetailByDjbh(String djbh);

    /**
     * 分页查询采购申请列表
     * @param queryDTO 查询参数
     * @return 采购申请列表
     */
    List<CgsqListVO> getCgsqPageList(CgsqPageQueryDTO queryDTO);

    /**
     * 提交采购申请（使用申请编号）
     * @param billnoType 单据类型
     * @param sqbh 申请编号
     * @param money 金额
     * @param auditor 审核人
     * @return 工作流程节点映射
     */
    Map<Integer, List<PubObjFlowTemp>> commitProxyBySqbh(BillnoType billnoType, String sqbh, Double money, String auditor);

    /**
     * 提交采购申请（兼容方法，使用单据编号）
     * @param billnoType 单据类型
     * @param djbh 单据编号
     * @param money 金额
     * @param auditor 审核人
     * @return 工作流程节点映射
     */
    Map<Integer, List<PubObjFlowTemp>> commitProxy(BillnoType billnoType, String djbh, Double money, String auditor);

    /**
     * 根据申请编号查询采购申请（用于提交后返回）
     * @param sqbh 申请编号
     * @return 采购申请详情VO
     */
    CgsqSaveResponseVO findBySqbh(String sqbh);

    /**
     * 根据单据编号查询采购申请（兼容方法）
     * @param djbh 单据编号
     * @return 采购申请详情VO
     */
    CgsqSaveResponseVO findByLid(String djbh);

    /**
     * 查询审核日志（使用申请编号）
     * @param billnoType 单据类型
     * @param sqbh 申请编号
     * @return 审核日志VO
     */
    CgsqAuditLogVO findCheckLogBySqbh(BillnoType billnoType, String sqbh);

    /**
     * 检查采购申请是否有权限审核
     * @param checkParams 检查参数
     * @return 权限检查结果
     */
    Object isCheckedByAuthority(CgsqCheckParams checkParams);

    /**
     * 检查采购申请是否有权限审核（基于SQBH）
     * @param sqbh 申请编号
     * @return 权限检查结果
     */
    Object isCheckedByAuthorityBySqbh(String sqbh);

    /**
     * 审核采购申请（使用申请编号）
     * @param billType 单据类型
     * @param sqbh 申请编号
     * @param opinion 审核意见
     * @param auditor 审核人
     * @param money 金额
     */
    void checkBySqbh(BillnoType billType, String sqbh, String opinion, String auditor, java.math.BigDecimal money);

    /**
     * 审核采购申请（兼容方法）
     * @param billType 单据类型
     * @param djbh 单据编号
     * @param opinion 审核意见
     * @param auditor 审核人
     * @param money 金额
     */
    void check(BillnoType billType, String djbh, String opinion, String auditor, java.math.BigDecimal money);

    /**
     * 更新采购申请状态（使用申请编号）
     * @param sqbh 申请编号
     * @param status 状态
     */
    void updateStatusBySqbh(String sqbh, Integer status);

    /**
     * 更新采购申请状态（兼容方法）
     * @param djbh 单据编号
     * @param status 状态
     */
    void updateStatus(String djbh, Integer status);

    /**
     * 收回采购申请（使用申请编号）
     * @param sqbh 申请编号
     * @return 收回后的采购申请详情
     */
    CgsqSaveResponseVO callBackBySqbh(String sqbh);

    /**
     * 收回采购申请（兼容方法）
     * @param djbh 单据编号
     * @return 收回后的采购申请详情
     */
    CgsqSaveResponseVO callBack(String djbh);

    /**
     * 退审采购申请（使用申请编号）
     * @param sqbh 申请编号
     * @param opinion 退审意见
     * @param auditor 退审人
     * @return 退审后的采购申请详情
     */
    CgsqSaveResponseVO checkCallBackBySqbh(String sqbh, String opinion, String auditor);

    /**
     * 退审采购申请（兼容方法）
     * @param djbh 单据编号
     * @param opinion 退审意见
     * @param auditor 退审人
     * @return 退审后的采购申请详情
     */
    CgsqSaveResponseVO checkCallBack(String djbh, String opinion, String auditor);

    /**
     * 销审采购申请（使用申请编号）
     * @param sqbh 申请编号
     * @return 销审后的采购申请详情
     */
    CgsqSaveResponseVO noAuditBySqbh(String sqbh);

    /**
     * 销审采购申请（兼容方法）
     * @param djbh 单据编号
     * @return 销审后的采购申请详情
     */
    CgsqSaveResponseVO noAudit(String djbh);

    /**
     * 分页查询已审核状态的采购申请（返回详细信息）
     * @param queryDTO 查询参数
     * @return 已审核采购申请列表
     */
    java.util.List<CgsqListVO> getApprovedCgsqList(CgsqPageQueryDTO queryDTO);
} 