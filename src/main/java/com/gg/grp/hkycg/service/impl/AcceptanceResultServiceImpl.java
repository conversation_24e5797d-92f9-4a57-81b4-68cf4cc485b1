package com.gg.grp.hkycg.service.impl;

import com.gg.grp.hkycg.mapper.AcceptanceResultMapper;
import com.gg.grp.hkycg.model.dto.AcceptanceQueryDTO;
import com.gg.grp.hkycg.model.vo.ProcurementLinkSummaryVO;
import com.gg.grp.hkycg.service.AcceptanceResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 验收结果服务实现类
 */
@Service
public class AcceptanceResultServiceImpl implements AcceptanceResultService {

    @Autowired
    private AcceptanceResultMapper acceptanceResultMapper;

    @Override
    public List<ProcurementLinkSummaryVO> getAcceptanceResultWithCondition(AcceptanceQueryDTO queryDTO) {
        return acceptanceResultMapper.getFullProcurementLinkWithCondition(
                queryDTO.getYsbh(),
                queryDTO.getCondition(),
                queryDTO.getYslxdm(),
                queryDTO.getYsbmdm(),
                queryDTO.getXmmc()
        );
    }
} 