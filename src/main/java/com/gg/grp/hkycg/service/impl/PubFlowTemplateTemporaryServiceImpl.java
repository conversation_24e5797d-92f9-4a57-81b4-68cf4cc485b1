package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.PubFlowTemplateTemporaryMapper;
import com.gg.grp.hkycg.model.dto.FlowTemplateDTO;
import com.gg.grp.hkycg.model.vo.FlowTemplateAuditorVO;
import com.gg.grp.hkycg.model.vo.FlowTemplateNodeVO;
import com.gg.grp.hkycg.model.pojo.PubFlowTemplateTemporary;
import com.gg.grp.hkycg.service.PubFlowTemplateTemporaryService;
import com.gg.grp.hkycg.utils.BeanConvertUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PubFlowTemplateTemporaryServiceImpl extends ServiceImpl<PubFlowTemplateTemporaryMapper, PubFlowTemplateTemporary> implements PubFlowTemplateTemporaryService {
    @Override
    public List<FlowTemplateDTO> queryCacheNodes(Integer billTypeId) {
        List<PubFlowTemplateTemporary> temporaryList = queryByBillTypeId(billTypeId);
        return BeanConvertUtils.convertListTo(temporaryList, FlowTemplateDTO::new);
    }

    private List<PubFlowTemplateTemporary> queryByBillTypeId(Integer billTypeId) {
        LambdaQueryWrapper<PubFlowTemplateTemporary> pFTTWapper = new LambdaQueryWrapper<>();
        pFTTWapper.eq(PubFlowTemplateTemporary::getGsdm, LoginInfo.getCurrCorpCode());
        pFTTWapper.eq(PubFlowTemplateTemporary::getZydm, LoginInfo.getCurrEmployeeCode());
        pFTTWapper.eq(PubFlowTemplateTemporary::getDjlxid, billTypeId);
        return baseMapper.selectList(pFTTWapper);
    }

    @Override
    public void save(List<FlowTemplateDTO> flowTemplateDtoList, Integer billTypeId) {
        delete(billTypeId);
        List<PubFlowTemplateTemporary> temporaryList = BeanConvertUtils.convertListTo(flowTemplateDtoList,
                PubFlowTemplateTemporary::new, (templateDto, templateTemporary) -> {
                    templateTemporary.setDjlxid(templateDto.getDjlxid());
                    templateTemporary.setGsdm(LoginInfo.getCurrCorpCode());
                    templateTemporary.setZydm(LoginInfo.getCurrEmployeeCode());
                });
        saveBatch(temporaryList);
    }

    @Override
    public void delete(Integer billTypeId) {
        LambdaQueryWrapper<PubFlowTemplateTemporary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PubFlowTemplateTemporary::getGsdm, LoginInfo.getCurrCorpCode());
        wrapper.eq(PubFlowTemplateTemporary::getZydm, LoginInfo.getCurrEmployeeCode());
        wrapper.eq(PubFlowTemplateTemporary::getDjlxid, billTypeId);
        baseMapper.delete(wrapper);
    }

    @Override
    public void legal(Integer billTypeId) {

    }

    @Override
    public void checkLegal(Integer billTypeId) {

    }

    @Override
    public String rejectNecessity(Integer billTypeId) {
        return "";
    }

    @Override
    public void checkBeforeSave(List<FlowTemplateNodeVO> nodeList) {
        for (FlowTemplateNodeVO node : nodeList) {
            List<FlowTemplateAuditorVO> auditorList = node.getNodeAuditorList();
            if (auditorList.isEmpty()) {
                throw new GlobalException("节点内请至少添加一个审核人");
            }
//            for (FlowTemplateAuditorVO flowTemplateAuditorVO : auditorList){
//                if (flowTemplateAuditorVO.getDynamicAcquisition()) {
//                    // 动态获取审核人时的验证
//                    if (StringUtils.isBlank(flowTemplateAuditorVO.getShtj()) || StringUtils.isBlank(node.getJdshtj())) {
//                        throw new GlobalException("动态获取审核人时条件表达式不能为空");
//                    }
//                    if (StringUtils.isNotBlank(flowTemplateAuditorVO.getShrdm()) || StringUtils.isNotBlank(flowTemplateAuditorVO.getShrxm())) {
//                        throw new GlobalException("动态获取审核人时请勿填写审核人");
//                    }
//                } else {
//                    // 非动态获取审核人时的验证
//                    if (StringUtils.isNotBlank(flowTemplateAuditorVO.getShtj()) || StringUtils.isNotBlank(node.getJdshtj())) {
//                        throw new GlobalException("非动态获取审核人时条件表达式必须为空");
//                    }
//                    if (StringUtils.isBlank(flowTemplateAuditorVO.getShrdm()) || StringUtils.isBlank(flowTemplateAuditorVO.getShrxm())) {
//                        throw new GlobalException("审核人没有填写");
//                    }
//                }
//            }
        }
    }

    @Override
    public void move2Log(Integer billTypeId, List<FlowTemplateDTO> flowTemplateDTOList) {

    }
}
