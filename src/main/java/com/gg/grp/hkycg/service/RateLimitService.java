package com.gg.grp.hkycg.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;

/**
 * 限流服务
 * 基于Jedis实现滑动窗口限流和IP黑白名单管理
 */
@Service
public class RateLimitService {

    @Autowired
    private JedisPool jedisPool;

    // Redis Lua脚本，实现原子性的滑动窗口限流
    private static final String RATE_LIMIT_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local window = tonumber(ARGV[1])\n" +
        "local maxRequests = tonumber(ARGV[2])\n" +
        "local now = tonumber(ARGV[3])\n" +
        "\n" +
        "-- 清理过期的请求记录\n" +
        "redis.call('ZREMRANGEBYSCORE', key, 0, now - window * 1000)\n" +
        "\n" +
        "-- 获取当前窗口内的请求数\n" +
        "local currentRequests = redis.call('ZCARD', key)\n" +
        "\n" +
        "if currentRequests < maxRequests then\n" +
        "    -- 添加当前请求到有序集合\n" +
        "    redis.call('ZADD', key, now, now)\n" +
        "    -- 设置过期时间\n" +
        "    redis.call('EXPIRE', key, window)\n" +
        "    return {1, maxRequests - currentRequests - 1}\n" +
        "else\n" +
        "    return {0, 0}\n" +
        "end";

    /**
     * 检查是否允许请求通过
     * @param key 限流key
     * @param window 时间窗口（秒）
     * @param maxRequests 最大请求数
     * @return 限流结果
     */
    public RateLimitResult checkRateLimit(String key, int window, int maxRequests) {
        long now = System.currentTimeMillis();
        String rateLimitKey = "rate_limit:" + key;
        
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            
            // 使用Jedis执行Lua脚本
            List<Object> result = (List<Object>) jedis.eval(
                RATE_LIMIT_SCRIPT,
                Collections.singletonList(rateLimitKey),
                java.util.Arrays.asList(
                    String.valueOf(window),
                    String.valueOf(maxRequests),
                    String.valueOf(now)
                )
            );
            
            boolean allowed = ((Long) result.get(0)) == 1;
            long remaining = (Long) result.get(1);
            
            return new RateLimitResult(allowed, remaining, maxRequests);
        } catch (Exception e) {
            // Redis异常时允许请求通过，避免影响业务
            System.err.println("Redis限流检查异常: " + e.getMessage());
            return new RateLimitResult(true, maxRequests - 1, maxRequests);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 检查IP是否在黑名单中
     * @param ip IP地址
     * @return 是否在黑名单中
     */
    public boolean isInBlacklist(String ip) {
        String blacklistKey = "blacklist:ip:" + ip;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            boolean exists = jedis.exists(blacklistKey);
            if (exists) {
                // 获取剩余过期时间进行调试
                Long ttl = jedis.ttl(blacklistKey);
                System.out.println("IP " + ip + " 在黑名单中，剩余时间: " + ttl + " 秒");
            }
            return exists;
        } catch (Exception e) {
            System.err.println("检查黑名单异常: " + e.getMessage());
            return false;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 检查IP是否在白名单中
     * @param ip IP地址
     * @return 是否在白名单中
     */
    public boolean isInWhitelist(String ip) {
        String whitelistKey = "whitelist:ip:" + ip;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            boolean exists = jedis.exists(whitelistKey);
            System.out.println("IP " + ip + " 白名单检查结果: " + exists);
            return exists;
        } catch (Exception e) {
            System.err.println("检查白名单异常: " + e.getMessage());
            return false;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 将IP添加到黑名单
     * @param ip IP地址
     * @param durationMinutes 封禁时长（分钟）
     * @param reason 封禁原因
     */
    public void addToBlacklist(String ip, int durationMinutes, String reason) {
        String blacklistKey = "blacklist:ip:" + ip;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            // 设置值并同时设置过期时间（秒）
            jedis.setex(blacklistKey, durationMinutes * 60, reason);
            
            // 验证设置是否成功
            Long ttl = jedis.ttl(blacklistKey);
            System.out.println("IP " + ip + " 已被加入黑名单，原因: " + reason + 
                             "，时长: " + durationMinutes + "分钟，实际TTL: " + ttl + "秒");
        } catch (Exception e) {
            System.err.println("添加黑名单异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 将IP从黑名单中移除
     * @param ip IP地址
     */
    public void removeFromBlacklist(String ip) {
        String blacklistKey = "blacklist:ip:" + ip;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            jedis.del(blacklistKey);
            System.out.println("IP " + ip + " 已从黑名单中移除");
        } catch (Exception e) {
            System.err.println("移除黑名单异常: " + e.getMessage());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 将IP添加到白名单
     * @param ip IP地址
     * @param description 描述
     */
    public void addToWhitelist(String ip, String description) {
        String whitelistKey = "whitelist:ip:" + ip;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            jedis.set(whitelistKey, description);
            System.out.println("IP " + ip + " 已被加入白名单: " + description);
        } catch (Exception e) {
            System.err.println("添加白名单异常: " + e.getMessage());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 记录失败次数
     * @param key 失败记录key
     * @param maxFailures 最大失败次数
     * @param windowMinutes 时间窗口（分钟）
     * @return 当前失败次数
     */
    public int recordFailure(String key, int maxFailures, int windowMinutes) {
        String failureKey = "failure:" + key;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            Long count = jedis.incr(failureKey);
            if (count == 1) {
                // 第一次失败，设置过期时间
                jedis.expire(failureKey, windowMinutes * 60);
            }
            return count.intValue();
        } catch (Exception e) {
            System.err.println("记录失败次数异常: " + e.getMessage());
            return 0;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 清除失败记录
     * @param key 失败记录key
     */
    public void clearFailureRecord(String key) {
        String failureKey = "failure:" + key;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            jedis.del(failureKey);
        } catch (Exception e) {
            System.err.println("清除失败记录异常: " + e.getMessage());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 获取黑名单剩余时间（秒）
     * @param ip IP地址
     * @return 剩余时间（秒），-1表示不在黑名单中，-2表示永久
     */
    public long getBlacklistTTL(String ip) {
        String blacklistKey = "blacklist:ip:" + ip;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            if (!jedis.exists(blacklistKey)) {
                return -1; // 不在黑名单中
            }
            return jedis.ttl(blacklistKey);
        } catch (Exception e) {
            System.err.println("获取黑名单TTL异常: " + e.getMessage());
            return -1;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 清理所有黑名单
     * @return 清理的数量
     */
    public int clearAllBlacklist() {
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            Set<String> keys = jedis.keys("blacklist:ip:*");
            if (keys != null && !keys.isEmpty()) {
                String[] keyArray = keys.toArray(new String[0]);
                jedis.del(keyArray);
                System.out.println("已清理 " + keys.size() + " 个黑名单IP");
                return keys.size();
            }
            return 0;
        } catch (Exception e) {
            System.err.println("清理所有黑名单异常: " + e.getMessage());
            return 0;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 获取所有黑名单IP及其剩余时间
     * @return 黑名单列表
     */
    public Map<String, Long> getAllBlacklistWithTTL() {
        Map<String, Long> blacklist = new HashMap<>();
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            Set<String> keys = jedis.keys("blacklist:ip:*");
            if (keys != null) {
                for (String key : keys) {
                    String ip = key.replace("blacklist:ip:", "");
                    Long ttl = jedis.ttl(key);
                    blacklist.put(ip, ttl);
                }
            }
        } catch (Exception e) {
            System.err.println("获取所有黑名单异常: " + e.getMessage());
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
        return blacklist;
    }

    /**
     * 测试Redis连接
     * @return 连接是否正常
     */
    public boolean testConnection() {
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            String pong = jedis.ping();
            return "PONG".equals(pong);
        } catch (Exception e) {
            System.err.println("Redis连接测试失败: " + e.getMessage());
            return false;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 获取连接池状态
     * @return 连接池状态信息
     */
    public Map<String, Object> getPoolStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("numActive", jedisPool.getNumActive());
        status.put("numIdle", jedisPool.getNumIdle());
        status.put("numWaiters", jedisPool.getNumWaiters());
        status.put("maxTotal", jedisPool.getMaxTotal());
        status.put("maxIdle", jedisPool.getMaxIdle());
        status.put("isClosed", jedisPool.isClosed());
        return status;
    }

    /**
     * 获取失败次数
     * @param key 失败记录key
     * @return 当前失败次数
     */
    public int getFailureCount(String key) {
        String failureKey = "failure:" + key;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            String count = jedis.get(failureKey);
            return count != null ? Integer.parseInt(count) : 0;
        } catch (Exception e) {
            System.err.println("获取失败次数异常: " + e.getMessage());
            return 0;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 获取失败记录剩余时间（秒）
     * @param key 失败记录key
     * @return 剩余时间（秒），-1表示不存在
     */
    public long getFailureTTL(String key) {
        String failureKey = "failure:" + key;
        Jedis jedis = null;
        
        try {
            jedis = jedisPool.getResource();
            if (!jedis.exists(failureKey)) {
                return -1; // 不存在
            }
            return jedis.ttl(failureKey);
        } catch (Exception e) {
            System.err.println("获取失败记录TTL异常: " + e.getMessage());
            return -1;
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    /**
     * 限流结果
     */
    public static class RateLimitResult {
        private boolean allowed;
        private long remaining;
        private long limit;

        public RateLimitResult(boolean allowed, long remaining, long limit) {
            this.allowed = allowed;
            this.remaining = remaining;
            this.limit = limit;
        }

        public boolean isAllowed() {
            return allowed;
        }

        public long getRemaining() {
            return remaining;
        }

        public long getLimit() {
            return limit;
        }
    }
} 