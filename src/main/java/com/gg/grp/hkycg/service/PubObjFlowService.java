package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.model.pojo.PubObjFlow;

import java.util.List;

/**
 * 工作流程实例服务接口
 */
public interface PubObjFlowService {

    /**
     * 插入工作流程实例
     * @param pubObjFlow 工作流程实例
     * @return 插入结果
     */
    int insert(PubObjFlow pubObjFlow);

    /**
     * 根据条件删除工作流程实例
     * @param modCode 模块代码
     * @param djlx 单据类型
     * @param djh 单据号
     */
    void deleteByCon(String modCode, String djlx, String djh);

    /**
     * 根据单据号查询工作流程实例
     * @param djh 单据号
     * @return 工作流程实例列表
     */
    List<PubObjFlow> selectByBillno(String djh);

    /**
     * 根据条件查询当前审核节点
     * @param modCode 模块代码
     * @param djlx 单据类型
     * @param djh 单据号
     * @return 当前审核节点
     */
    PubObjFlow selectNowNodeByDjh(String modCode, String djlx, String djh);

    PubObjFlow selectLastAuditNodeByDjh(String modCode, String djlx, String djh);
}