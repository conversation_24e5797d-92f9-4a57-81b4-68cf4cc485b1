package com.gg.grp.hkycg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.pojo.GpmGnfl;

import java.util.List;

/**
 * 功能服务接口
 */
public interface GnflService extends IService<GpmGnfl> {
    
    /**
     * 根据URL获取功能信息
     * @param url 前端URL
     * @return 功能信息
     */
    GpmGnfl getFunctionByUrl(String url);
    
    /**
     * 根据功能代码获取功能信息
     * @param code 功能代码
     * @return 功能信息
     */
    GpmGnfl getFunctionByCode(String code);
    
    /**
     * 获取所有功能列表
     * @return 功能列表
     */
    List<GpmGnfl> getAllFunctions();
    
    /**
     * 根据角色代码获取授权的功能列表
     * @param roleCode 角色代码
     * @return 功能列表
     */
    List<GpmGnfl> getFunctionsByRoleCode(String roleCode);

    Result getUserGNFL();
}