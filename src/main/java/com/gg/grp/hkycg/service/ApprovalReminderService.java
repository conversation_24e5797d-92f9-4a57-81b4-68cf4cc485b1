package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.ApprovalReminder;

import java.util.List;

/**
 * 审批催办Service接口
 */
public interface ApprovalReminderService {
    
    /**
     * 查询需要催办的单据
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<ApprovalReminderVO> queryRemindableApprovals(ApprovalReminderQueryDTO queryDTO);
    
    /**
     * 发送催办通知（批量），自动判断单据类型
     * @param reminderSendDTO 单据号列表
     * @return 催办结果
     */
    boolean sendReminder(List<ReminderSendItemVO> reminderSendDTO);
    
    /**
     * 通过单据号查询催办记录
     * @param billNo 单据号
     * @param billType 单据类型
     * @return 催办记录列表
     */
    PageResult<ApprovalReminder> getReminderHistory(String billNo, String billType, Integer current, Integer size);
    
    /**
     * 将催办标记为已处理
     * @param grid 催办记录GRID
     * @return 处理结果
     */
    boolean markAsProcessed(Long grid);
    
    /**
     * 当单据被审核、销审、退审时，自动更新催办状态为3（已处理）
     * @param billNo 单据号
     * @param billType 单据类型
     * @param nodeName 审核节点名称
     * @return 更新结果
     */
    boolean updateReminderStatusAfterAudit(String billNo, String billType, String nodeName);
    
    /**
     * 查询当前登录人被催办的单据
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<MyReminderVO> queryMyReminders(MyReminderQueryDTO queryDTO);

    /**
     * 查询离截止日期指定天数内的所有单据
     * @param days 天数
     * @return 单据列表
     */
    List<BusinessReminderVO> queryBillsNearDeadline(Integer days);

    /**
     * 查询已审核状态的所有单据
     * @param queryDTO 查询条件
     * @return 已审核单据分页结果
     */
    PageResult<Object> queryApprovedBills(ApprovedBillsQueryDTO queryDTO);

    /**
     * 查询所有已审核状态的单据（不分类型）
     * @param queryDTO 查询条件
     * @return 所有已审核单据分页结果
     */
    PageResult<AllApprovedBillVO> queryAllApprovedBills(AllApprovedBillsQueryDTO queryDTO);
}