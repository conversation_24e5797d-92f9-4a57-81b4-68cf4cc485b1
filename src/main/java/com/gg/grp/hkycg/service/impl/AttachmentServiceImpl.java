package com.gg.grp.hkycg.service.impl;

import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.mapper.AttachmentMapper;
import com.gg.grp.hkycg.mapper.AttachmentRelMapper;
import com.gg.grp.hkycg.model.dto.AttachmentUploadDTO;
import com.gg.grp.hkycg.model.vo.AttachmentVO;
import com.gg.grp.hkycg.model.pojo.GpmAttachment;
import com.gg.grp.hkycg.model.pojo.GpmAttachmentRel;
import com.gg.grp.hkycg.service.AttachmentService;
import com.gg.grp.hkycg.utils.ConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.URLEncoder;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
import java.util.Enumeration;

/**
 * 附件服务实现类
 */
@Service
@Slf4j
public class AttachmentServiceImpl implements AttachmentService {
    
    @Autowired
    private AttachmentMapper attachmentMapper;
    
    @Autowired
    private AttachmentRelMapper attachmentRelMapper;
    
    @Value("${attachment.upload.path:./upload/attachment}")
    private String uploadPath;

    @Value("${server.port}")
    private int serverPort;
    
    @Value("${attachment.download.url:/download/}")
    private String downloadUrlPrefix;
    
    @Value("${attachment.preview.url:/preview/}")
    private String previewUrlPrefix;

    @Value("${attachment.base-url:}")
    private String configuredBaseUrl;

    private String finalBaseUrl;


    
    /**
     * 初始化路径
     */
    @PostConstruct
    public void init() {
        // 确保上传路径是绝对路径
        if (!uploadPath.startsWith("/")) {
            // 如果是相对路径，则转换为相对于项目根目录的绝对路径
            String userDir = System.getProperty("user.dir");
            uploadPath = userDir + "/" + (uploadPath.startsWith("./") ? uploadPath.substring(2) : uploadPath);
        }
        log.info("附件上传路径初始化为: {}", uploadPath);
        
        // 确保基础目录存在
        File baseDir = new File(uploadPath);
        if (!baseDir.exists()) {
            boolean created = baseDir.mkdirs();
            if (!created) {
                log.error("无法创建附件基础目录: {}", uploadPath);
            } else {
                log.info("已创建附件基础目录: {}", uploadPath);
            }
        }

        if (StringUtils.hasText(configuredBaseUrl)) {
            finalBaseUrl = configuredBaseUrl;
        } else {
            String serverIP = findHostIp();
            if (serverIP == null) {
                try {
                    serverIP = InetAddress.getLocalHost().getHostAddress();
                } catch (UnknownHostException e) {
                    log.warn("无法确定主机IP地址，将回退到localhost", e);
                    serverIP = "localhost";
                }
            }
            finalBaseUrl = "http://" + serverIP + ":" + serverPort + "/grp/api/attachment";
        }
        log.info("附件 base URL: {}", finalBaseUrl);
        
        // 构建和记录完整的下载和预览URL前缀
        String fullDownloadUrlPrefix = finalBaseUrl + downloadUrlPrefix;
        String fullPreviewUrlPrefix = finalBaseUrl + previewUrlPrefix;
        log.info("附件下载URL前缀: {}", fullDownloadUrlPrefix);
        log.info("附件预览URL前缀: {}", fullPreviewUrlPrefix);
    }

    private String findHostIp() {
        String candidateAddress = null;
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface ni = networkInterfaces.nextElement();
                // 忽略非活动、虚拟和环回接口
                if (!ni.isUp() || ni.isLoopback() || ni.isVirtual()) {
                    continue;
                }
                Enumeration<InetAddress> inetAddresses = ni.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress address = inetAddresses.nextElement();
                    // 只关心IPv4地址
                    if (address instanceof java.net.Inet4Address) {
                        // 优先选择站点本地地址（如 192.168.x.x）
                        if (address.isSiteLocalAddress()) {
                            return address.getHostAddress();
                        }
                        // 保存第一个非环回地址作为备选
                        if (!address.isLoopbackAddress() && candidateAddress == null) {
                            candidateAddress = address.getHostAddress();
                        }
                    }
                }
            }
        } catch (SocketException e) {
            log.error("获取主机IP地址时发生SocketException", e);
        }
        return candidateAddress;
    }

    @Override
    public AttachmentVO uploadAttachment(MultipartFile file, AttachmentUploadDTO dto) {
        try {
            // 参数校验
            if (file == null || file.isEmpty()) {
                throw new GlobalException("上传文件不能为空");
            }
            if (dto == null || !StringUtils.hasText(dto.getDjlx()) || !StringUtils.hasText(dto.getDjh())) {
                throw new GlobalException("单据信息不完整");
            }
            
            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String userId = LoginInfo.getCurrEmployeeCode();
            String userName = LoginInfo.getCurrEmployeeName();
            
            if (!StringUtils.hasText(gsdm) || !StringUtils.hasText(kjnd)) {
                throw new GlobalException("用户未登录或登录信息无效");
            }
            
            // 生成存储路径 - 使用已经初始化好的绝对路径
            String relativePath = "/" + gsdm + "/" + kjnd;
            String absolutePath = uploadPath + relativePath;
            log.info("附件存储路径: {}", absolutePath);
            
            // 创建存储目录
            File dir = new File(absolutePath);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    throw new GlobalException("创建文件存储目录失败: " + absolutePath);
                }
                log.info("已创建目录: {}", absolutePath);
            }
            
            // 生成存储文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String storageFilename = UUID.randomUUID().toString().replace("-", "") + extension;
            
            // 保存文件
            String filePath = absolutePath + "/" + storageFilename;
            log.info("保存文件: {}", filePath);
            File destFile = new File(filePath);
            try {
                file.transferTo(destFile);
                if (!destFile.exists()) {
                    throw new GlobalException("文件保存失败，请检查目录权限");
                }
            } catch (IOException e) {
                log.error("保存文件异常: {}", e.getMessage(), e);
                throw new GlobalException("保存文件失败: " + e.getMessage());
            }
            
            // 计算文件大小（KB）
            BigDecimal fileSize = new BigDecimal(file.getSize()).divide(new BigDecimal(1024), 2, RoundingMode.HALF_UP);
            
            // 保存附件信息
            GpmAttachment attachment = new GpmAttachment();
            attachment.setFjid(UUID.randomUUID().toString().replace("-", ""));
            attachment.setGsdm(gsdm);
            attachment.setKjnd(kjnd);
            attachment.setFjmc(originalFilename);
            attachment.setFjccmc(storageFilename);
            attachment.setFjlx(file.getContentType());
            attachment.setFjdx(fileSize);
            attachment.setFjlj(relativePath + "/" + storageFilename);
            attachment.setLrrId(userId);
            attachment.setLrr(userName);
            attachment.setLrRq(new Date());
            
            // 保存附件关联信息
            GpmAttachmentRel rel = new GpmAttachmentRel();
            rel.setRelid(UUID.randomUUID().toString().replace("-", ""));
            rel.setFjid(attachment.getFjid());
            rel.setGsdm(gsdm);
            rel.setKjnd(kjnd);
            rel.setDjlx(dto.getDjlx());
            rel.setDjh(dto.getDjh());
            rel.setSfmx(ConvertUtils.convertBooleanToString(dto.getSfmx()));
            
            // 保存到数据库
            attachmentMapper.insert(attachment);
            attachmentRelMapper.insert(rel);
            
            // 构建返回对象
            AttachmentVO vo = new AttachmentVO();
            vo.setFjid(attachment.getFjid());
            vo.setFjmc(attachment.getFjmc());
            vo.setFjlx(attachment.getFjlx());
            vo.setFjdx(attachment.getFjdx());
            vo.setLrr(attachment.getLrr());
            vo.setLrRq(attachment.getLrRq());
            vo.setDjlx(rel.getDjlx());
            vo.setDjh(rel.getDjh());
            vo.setSfmx(rel.getSfmx());
            vo.setDownloadUrl(finalBaseUrl + downloadUrlPrefix + attachment.getFjid());
            vo.setPreviewUrl(finalBaseUrl + previewUrlPrefix + attachment.getFjid());
            
            return vo;
        } catch (Exception e) {
            log.error("上传附件异常", e);
            throw new GlobalException("上传附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AttachmentVO> batchUploadAttachments(List<MultipartFile> files, AttachmentUploadDTO dto) {
        List<AttachmentVO> result = new ArrayList<>();
        
        if (files == null || files.isEmpty()) {
            return result;
        }
        
        for (MultipartFile file : files) {
            if (file != null && !file.isEmpty()) {
                try {
                    AttachmentVO vo = uploadAttachment(file, dto);
                    result.add(vo);
                } catch (Exception e) {
                    log.error("批量上传附件异常，文件名：{}", file.getOriginalFilename(), e);
                    // 继续处理下一个文件
                }
            }
        }
        
        return result;
    }
    
    @Override
    public List<AttachmentVO> queryAttachmentsByBill(String djlx, String djh, String sfmx) {
        try {
            // 参数校验
            if (!StringUtils.hasText(djlx) || !StringUtils.hasText(djh)) {
                throw new GlobalException("单据信息不完整");
            }
            
            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            if (!StringUtils.hasText(gsdm) || !StringUtils.hasText(kjnd)) {
                throw new GlobalException("用户未登录或登录信息无效");
            }
            
            // 查询附件列表
            List<AttachmentVO> attachments = attachmentMapper.queryAttachmentsByBill(gsdm, kjnd, djlx, djh, sfmx);
            
            // 设置下载和预览URL
            for (AttachmentVO vo : attachments) {
                vo.setDownloadUrl(finalBaseUrl + downloadUrlPrefix + vo.getFjid());
                vo.setPreviewUrl(finalBaseUrl + previewUrlPrefix + vo.getFjid());
            }
            
            return attachments;
        } catch (Exception e) {
            log.error("查询单据关联的附件列表异常", e);
            throw new GlobalException("查询单据关联的附件列表失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> queryAttachmentsWithDirectoryAndDetail(String djlx, String djh) {
        try {
            // 参数校验
            if (!StringUtils.hasText(djlx) || !StringUtils.hasText(djh)) {
                throw new GlobalException("单据信息不完整");
            }
            
            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            
            if (!StringUtils.hasText(gsdm) || !StringUtils.hasText(kjnd)) {
                throw new GlobalException("用户未登录或登录信息无效");
            }
            
            Map<String, Object> result = new HashMap<>();
            
            // 1. 查询单据的目录和明细
            List<Map<String, Object>> directoryAndDetail = attachmentMapper.queryBillDirectoryAndDetail(gsdm, kjnd, djlx, djh);
            result.put("directoryAndDetail", directoryAndDetail);
            
            // 2. 查询所有相关附件
            List<AttachmentVO> attachments = attachmentMapper.queryAllAttachmentsByBillNo(gsdm, kjnd, djlx, djh);
            
            // 设置下载和预览URL
            for (AttachmentVO vo : attachments) {
                vo.setDownloadUrl(finalBaseUrl + downloadUrlPrefix + vo.getFjid());
                vo.setPreviewUrl(finalBaseUrl + previewUrlPrefix + vo.getFjid());
            }
            
            result.put("attachments", attachments);
            
            return result;
        } catch (Exception e) {
            log.error("查询单据目录、明细和附件异常", e);
            throw new GlobalException("查询单据目录、明细和附件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttachment(String fjid) {
        try {
            // 参数校验
            if (!StringUtils.hasText(fjid)) {
                throw new GlobalException("附件ID不能为空");
            }
            
            // 查询附件信息
            GpmAttachment attachment = attachmentMapper.getAttachmentById(fjid);
            if (attachment == null) {
                log.warn("要删除的附件不存在，附件ID：{}", fjid);
                return false;
            }
            
            // 删除物理文件
            String filePath = uploadPath + attachment.getFjlj();
            File file = new File(filePath);
            if (file.exists() && file.isFile()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    log.warn("删除物理文件失败，路径：{}", filePath);
                }
            }
            
            // 删除数据库记录
            attachmentRelMapper.deleteAttachmentRelByFjid(fjid);
            attachmentMapper.deleteById(fjid);
            
            return true;
        } catch (Exception e) {
            log.error("删除附件异常", e);
            throw new GlobalException("删除附件失败：" + e.getMessage());
        }
    }
    
    @Override
    public void downloadAttachment(String fjid, HttpServletResponse response) {
        try {
            // 参数校验
            if (!StringUtils.hasText(fjid)) {
                throw new GlobalException("附件ID不能为空");
            }
            
            // 查询附件信息
            GpmAttachment attachment = attachmentMapper.getAttachmentById(fjid);
            if (attachment == null) {
                throw new GlobalException("附件不存在");
            }
            
            // 获取文件路径
            String filePath = uploadPath + attachment.getFjlj();
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                throw new GlobalException("附件文件不存在");
            }
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachment.getFjmc(), "UTF-8"));
            response.setHeader("Content-Length", String.valueOf(file.length()));
            
            // 输出文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            }
        } catch (Exception e) {
            log.error("下载附件异常", e);
            throw new GlobalException("下载附件失败：" + e.getMessage());
        }
    }
    
    @Override
    public void previewAttachment(String fjid, HttpServletResponse response) {
        try {
            // 参数校验
            if (!StringUtils.hasText(fjid)) {
                throw new GlobalException("附件ID不能为空");
            }
            
            // 查询附件信息
            GpmAttachment attachment = attachmentMapper.getAttachmentById(fjid);
            if (attachment == null) {
                throw new GlobalException("附件不存在");
            }
            
            // 获取文件路径
            String filePath = uploadPath + attachment.getFjlj();
            Path path = Paths.get(filePath);
            if (!Files.exists(path) || !Files.isRegularFile(path)) {
                throw new GlobalException("附件文件不存在");
            }
            
            // 设置响应头
            String contentType = attachment.getFjlx();
            if (!StringUtils.hasText(contentType)) {
                contentType = "application/octet-stream";
            }
            response.setContentType(contentType);
            response.setHeader("Content-Length", String.valueOf(Files.size(path)));
            
            // 输出文件
            try (FileInputStream fis = new FileInputStream(filePath);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, len);
                }
                os.flush();
            }
        } catch (Exception e) {
            log.error("预览附件异常", e);
            throw new GlobalException("预览附件失败：" + e.getMessage());
        }
    }
} 