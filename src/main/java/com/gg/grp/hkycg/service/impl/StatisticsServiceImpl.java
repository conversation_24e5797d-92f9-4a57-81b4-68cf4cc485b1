package com.gg.grp.hkycg.service.impl;

import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.StatisticsMapper;
import com.gg.grp.hkycg.model.dto.BillCountStatisticsDTO;
import com.gg.grp.hkycg.model.vo.BillCountStatisticsVO;
import com.gg.grp.hkycg.service.StatisticsService;
import com.gg.grp.hkycg.utils.DataPermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 统计服务实现
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private DataPermissionUtils dataPermissionUtils;

    // 创建固定大小的线程池用于并行查询
    private final ExecutorService executor = Executors.newFixedThreadPool(5);

    @Override
    @Cacheable(value = "billStatistics", key = "T(com.gg.grp.hkycg.common.LoginInfo).getCurrCorpCode() + '_' + T(com.gg.grp.hkycg.common.LoginInfo).getCurrAccountantYear() + '_' + #dto.startDate + '_' + #dto.endDate + '_' + T(com.gg.grp.hkycg.common.LoginInfo).getCurrEmployeeCode()" , unless = "#result == null")
    public BillCountStatisticsVO getBillCompletionStatistics(BillCountStatisticsDTO dto) {
        // 设置默认查询参数
        if (dto == null) {
            dto = new BillCountStatisticsDTO();
        }
        
        // 使用当前登录用户的公司代码和会计年度作为默认值
        String gsdm = StringUtils.isNotBlank(dto.getGsdm()) ? dto.getGsdm() : LoginInfo.getCurrCorpCode();
        String kjnd = StringUtils.isNotBlank(dto.getKjnd()) ? dto.getKjnd() : LoginInfo.getCurrAccountantYear();
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        String currEmployeeCode = LoginInfo.getCurrEmployeeCode();

        // 获取当前用户的数据权限和部门代码
        String dataPermission = dataPermissionUtils.getCurrentUserDataPermission();
        String currDeptCode = dataPermissionUtils.getCurrentUserDeptCode();

        // 使用CompletableFuture并行查询各个单据类型的统计数据
        CompletableFuture<Map<String, Object>> cgjhFuture = CompletableFuture.supplyAsync(() -> {
            log.debug("开始查询采购计划统计数据");
            Map<String, Object> result = statisticsMapper.countCgjhStats(gsdm, kjnd, startDate, endDate, currEmployeeCode, dataPermission, currDeptCode);
            log.debug("采购计划统计数据查询完成");
            return result != null ? result : createDefaultStats();
        }, executor);

        CompletableFuture<Map<String, Object>> cgsqFuture = CompletableFuture.supplyAsync(() -> {
            log.debug("开始查询采购申请统计数据");
            Map<String, Object> result = statisticsMapper.countCgsqStats(gsdm, kjnd, startDate, endDate, currEmployeeCode, dataPermission, currDeptCode);
            log.debug("采购申请统计数据查询完成");
            return result != null ? result : createDefaultStats();
        }, executor);

        CompletableFuture<Map<String, Object>> cgzbFuture = CompletableFuture.supplyAsync(() -> {
            log.debug("开始查询采购招标统计数据");
            Map<String, Object> result = statisticsMapper.countCgzbStats(gsdm, kjnd, startDate, endDate, currEmployeeCode, dataPermission, currDeptCode);
            log.debug("采购招标统计数据查询完成");
            return result != null ? result : createDefaultStats();
        }, executor);

        CompletableFuture<Map<String, Object>> cgjgFuture = CompletableFuture.supplyAsync(() -> {
            log.debug("开始查询采购结果统计数据");
            Map<String, Object> result = statisticsMapper.countCgjgStats(gsdm, kjnd, startDate, endDate, currEmployeeCode, dataPermission, currDeptCode);
            log.debug("采购结果统计数据查询完成");
            return result != null ? result : createDefaultStats();
        }, executor);

        CompletableFuture<Map<String, Object>> cgysFuture = CompletableFuture.supplyAsync(() -> {
            log.debug("开始查询采购验收统计数据");
            Map<String, Object> result = statisticsMapper.countCgysStats(gsdm, kjnd, startDate, endDate, currEmployeeCode, dataPermission, currDeptCode);
            log.debug("采购验收统计数据查询完成");
            return result != null ? result : createDefaultStats();
        }, executor);
        
        // 合并所有CompletableFuture的结果
        CompletableFuture<BillCountStatisticsVO> resultFuture = CompletableFuture.allOf(
                cgjhFuture, cgsqFuture, cgzbFuture, cgjgFuture, cgysFuture
        ).thenApply(v -> {
            try {
                // 获取各个查询结果
                Map<String, Object> cgjhStats = cgjhFuture.get();
                Map<String, Object> cgsqStats = cgsqFuture.get();
                Map<String, Object> cgzbStats = cgzbFuture.get();
                Map<String, Object> cgjgStats = cgjgFuture.get();
                Map<String, Object> cgysStats = cgysFuture.get();
                
                // 构建响应数据
                BillCountStatisticsVO result = new BillCountStatisticsVO();
                
                // 设置类别（行数据）
                List<String> categories = Arrays.asList("采购计划", "采购申请", "采购招标", "采购登记", "采购验收");
                result.setCategories(categories);
                
                // 设置已办结数据（列数据）
                List<Integer> completedCounts = new ArrayList<>();
                completedCounts.add(getIntValue(cgjhStats.get("completed")));
                completedCounts.add(getIntValue(cgsqStats.get("completed")));
                completedCounts.add(getIntValue(cgzbStats.get("completed")));
                completedCounts.add(getIntValue(cgjgStats.get("completed")));
                completedCounts.add(getIntValue(cgysStats.get("completed")));
                result.setCompletedCounts(completedCounts);
                
                // 设置未办结数据（列数据）
                List<Integer> incompleteCounts = new ArrayList<>();
                incompleteCounts.add(getIntValue(cgjhStats.get("incomplete")));
                incompleteCounts.add(getIntValue(cgsqStats.get("incomplete")));
                incompleteCounts.add(getIntValue(cgzbStats.get("incomplete")));
                incompleteCounts.add(getIntValue(cgjgStats.get("incomplete")));
                incompleteCounts.add(getIntValue(cgysStats.get("incomplete")));
                result.setIncompleteCounts(incompleteCounts);
                
                return result;
            } catch (Exception e) {
                log.error("合并统计数据异常", e);
                throw new RuntimeException("统计查询异常", e);
            }
        });
        
        try {
            BillCountStatisticsVO result = resultFuture.get();
            log.info("统计单据办结情况成功，公司代码：{}，会计年度：{}", gsdm, kjnd);
            return result;
        } catch (Exception e) {
            log.error("获取统计结果异常", e);
            throw new RuntimeException("获取统计结果异常", e);
        }
    }
    
    /**
     * 创建默认的统计结果（0, 0）
     */
    private Map<String, Object> createDefaultStats() {
        Map<String, Object> result = new HashMap<>(2);
        result.put("completed", 0);
        result.put("incomplete", 0);
        return result;
    }
    
    /**
     * 安全获取整数值，避免空指针
     */
    private Integer getIntValue(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }
} 