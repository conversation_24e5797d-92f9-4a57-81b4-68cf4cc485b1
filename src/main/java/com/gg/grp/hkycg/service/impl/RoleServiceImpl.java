package com.gg.grp.hkycg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.RoleDto;
import com.gg.grp.hkycg.model.vo.AllPersonAndDeptListVO;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.RoleService;
import com.gg.grp.hkycg.service.RoleUserService;
import com.gg.grp.hkycg.service.SqbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, GpmRole> implements RoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private DeptMapper deptMapper;

    @Autowired
    private RoleUserService roleUserService;

    @Autowired
    private RoleUserMapper roleUserMapper;

    @Autowired
    private RolezbMapper rolezbMapper;

    @Autowired
    private SqbService sqbService;

    @Autowired
    private RoleGnflMapper roleGnflMapper;

    @Override
    public Result upRoleList() {
        LambdaQueryWrapper<GpmRole> roleWrapper = new LambdaQueryWrapper<>();
        List<GpmRole> roles = roleMapper.selectList(roleWrapper);
        LambdaQueryWrapper<Dept> deptWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<Dept> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(Dept::getSyzt, "2")
                .eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                .eq(Dept::getKjnd, LoginInfo.getCurrEmployeeName())
                .notIn(Dept::getBmmc, "停用");
        List<Dept> bmList = deptMapper.selectList(lambdaQueryWrapper);

        //过滤的部门
        List<Map<String,Object>> assemblingRoleBm= new ArrayList<>();

        roles.forEach(m->{
            bmList.forEach(m1->{
                if(m.getRoleCode().length()==2&&"01".equals(m1.getBmdm())&&"01".equals(m.getRoleCode())){
                    Map<String,Object> bm = new HashMap<>();
                    bm.put("bmdm",m1.getBmdm());
                    bm.put("bmmc",m.getRoleName());
                    assemblingRoleBm.add(bm);
                }else if(m.getRoleCode().length()>2&&m.getRoleCode().equals(m1.getBmdm())){
                    Map<String,Object> bm = new HashMap<>();
                    bm.put("bmdm",m1.getBmdm());
                    bm.put("bmmc",m1.getBmmc());
                    assemblingRoleBm.add(bm);
                }
            });
        });

        assemblingRoleBm.forEach(dept -> {
            List<AllPersonAndDeptListVO> allPersonAndDeptList = roleUserMapper.getAllPersonAndDeptList(LoginInfo.getCurrCorpCode(),
                    LoginInfo.getCurrAccountantYear(), dept.get("bmdm").toString(),
                    !"01".equals(dept.get("bmdm").toString()) ? dept.get("bmdm").toString() : null,
                    1, 100
            );

            LambdaQueryWrapper<GpmRolezb> rolezbWrapper = new LambdaQueryWrapper<>();
            rolezbWrapper.eq(GpmRolezb::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmRolezb::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(GpmRolezb::getRolecode, dept.get("bmdm").toString());
            List<GpmRolezb> rolezbs = rolezbMapper.selectList(rolezbWrapper);

            List<GpmRoleUser> roleUserList = new ArrayList<>();

            List<GpmSqb> sqbList = new ArrayList<>();

            for (AllPersonAndDeptListVO allPersonAndDeptListVO :allPersonAndDeptList){
                GpmRoleUser roleUser = new GpmRoleUser();
                roleUser.setGsdm(LoginInfo.getCurrCorpCode());
                roleUser.setKjnd(LoginInfo.getCurrAccountantYear());
                roleUser.setRolecode(dept.get("bmdm").toString());
                roleUser.setZydm(allPersonAndDeptListVO.getZydm());
                roleUserList.add(roleUser);

                for (GpmRolezb rolezb:rolezbs){
                    GpmSqb sqb = new GpmSqb();
                    sqb.setGsdm(LoginInfo.getCurrCorpCode());
                    sqb.setKjnd(LoginInfo.getCurrAccountantYear());
                    sqb.setSqren("1");
                    sqb.setBmdm(allPersonAndDeptListVO.getBmdm());
                    sqb.setBz("公共指标");
                    //暂定GPM_SQB中保存角色代码
                    sqb.setRolecode(dept.get("bmdm").toString());
                    //职员代码
                    sqb.setBsqren(allPersonAndDeptListVO.getZydm());
                    sqb.setZbid(String.valueOf(rolezb.getZbid()));
                    sqb.setSyqx(rolezb.getSyqx());
                    sqbList.add(sqb);
                }
            }

            //批量插入角色人员
            if (!roleUserList.isEmpty()){
                roleUserService.saveBatch(roleUserList);
            }
            //批量插入授权表
            if (!sqbList.isEmpty()){
                sqbService.saveBatch(sqbList);
            }
        });
        return Result.success("添加成功");
    }

    /**
     * 保存角色信息
     * @return
     */
    @Override
    public Result saveRole(RoleDto roleDto) {
        if (StringUtils.isNotEmpty(roleDto.getId())){
            LambdaUpdateWrapper<GpmRole> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmRole::getId,roleDto.getId())
                    .set(GpmRole::getRoleName,roleDto.getRoleName())
                    .set(GpmRole::getRoleCode,roleDto.getRoleCode());
            //修改保存操作
            boolean update = this.update(updateWrapper);
            return update ? Result.success("修改成功") : Result.error("修改失败");

        }else {
            //保存操作
            LambdaQueryWrapper<GpmRole> roleWrapper = new LambdaQueryWrapper<>();
            roleWrapper.eq(GpmRole::getRoleCode,roleDto.getRoleCode());
            this.remove(roleWrapper);

            GpmRole role = new GpmRole();
            role.setRoleCode(roleDto.getRoleCode());
            role.setRoleName(roleDto.getRoleName());
            role.setIsDefault("0");
            role.setId(UUID.randomUUID().toString().toUpperCase());

            //部门代码
            LambdaQueryWrapper<Dept> deptWrapper = new LambdaQueryWrapper<>();
            deptWrapper.eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(Dept::getBmmc, roleDto.getRoleName().trim());
            Dept dept = deptMapper.selectOne(deptWrapper);
            if (dept != null) {
                role.setBmdm(dept.getBmdm());
            }else {
                role.setBmdm("");
            }

            boolean save = this.save(role);
            return save ? Result.success("保存成功") : Result.error("保存失败");
        }
    }

    /**
     * 删除角色信息
     * @return
     */
    @Override
    public Result deleteRole(RoleDto roleDto) {
        //GPM_ROLE
        LambdaQueryWrapper<GpmRole> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.eq(GpmRole::getRoleCode, roleDto.getRoleCode());
        this.remove(roleWrapper);

        //GPM_ROLEGNFL
        LambdaQueryWrapper<GpmRolegnfl> rolegnflWrapper = new LambdaQueryWrapper<>();
        rolegnflWrapper.eq(GpmRolegnfl::getRolecode, roleDto.getRoleCode());
        roleGnflMapper.delete(rolegnflWrapper);

        //GPM_ROLEUSER
        LambdaQueryWrapper<GpmRoleUser> roleUserWrapper = new LambdaQueryWrapper<>();
        roleUserWrapper.eq(GpmRoleUser::getRolecode, roleDto.getRoleCode())
        .eq(GpmRoleUser::getGsdm, LoginInfo.getCurrCorpCode())
                .eq(GpmRoleUser::getKjnd, LoginInfo.getCurrAccountantYear());
        roleUserMapper.delete(roleUserWrapper);

        //GPM_ROLEZB
        LambdaQueryWrapper<GpmRolezb> rolezbWrapper = new LambdaQueryWrapper<>();
        rolezbWrapper.eq(GpmRolezb::getRolecode, roleDto.getRoleCode())
                .eq(GpmRolezb::getGsdm, LoginInfo.getCurrCorpCode())
                .eq(GpmRolezb::getKjnd, LoginInfo.getCurrAccountantYear());
        rolezbMapper.delete(rolezbWrapper);

        //GPM_SQB 暂定
        LambdaQueryWrapper<GpmSqb> sqbWrapper = new LambdaQueryWrapper<>();
        sqbWrapper.eq(GpmSqb::getRolecode, roleDto.getRoleCode())
                .eq(GpmSqb::getGsdm, LoginInfo.getCurrCorpCode())
                .eq(GpmSqb::getKjnd, LoginInfo.getCurrAccountantYear());
        sqbService.remove(sqbWrapper);

        return Result.success("删除成功");
    }

    @Override
    public Result updateRole(RoleDto roleDto) {
        if (StringUtils.isNotEmpty(roleDto.getId())){
            LambdaUpdateWrapper<GpmRole> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GpmRole::getId,roleDto.getId())
                    .set(GpmRole::getRoleName,roleDto.getRoleName())
                    .set(GpmRole::getRoleCode,roleDto.getRoleCode());
            //修改保存操作
            boolean update = this.update(updateWrapper);
            return update ? Result.success("修改成功") : Result.error("修改失败");

        }else {
            //保存操作
            LambdaQueryWrapper<GpmRole> roleWrapper = new LambdaQueryWrapper<>();
            roleWrapper.eq(GpmRole::getRoleCode,roleDto.getRoleCode());
            this.remove(roleWrapper);

            GpmRole role = new GpmRole();
            role.setRoleCode(roleDto.getRoleCode());
            role.setRoleName(roleDto.getRoleName());
            role.setIsDefault("0");
            role.setId(UUID.randomUUID().toString().toUpperCase());

            //部门代码
            LambdaQueryWrapper<Dept> deptWrapper = new LambdaQueryWrapper<>();
            deptWrapper.eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(Dept::getBmmc, roleDto.getRoleName().trim());
            Dept dept = deptMapper.selectOne(deptWrapper);
            if (dept != null) {
                role.setBmdm(dept.getBmdm());
            }else {
                role.setBmdm("");
            }

            boolean save = this.save(role);
            return save ? Result.success("保存成功") : Result.error("保存失败");
        }
    }
}
