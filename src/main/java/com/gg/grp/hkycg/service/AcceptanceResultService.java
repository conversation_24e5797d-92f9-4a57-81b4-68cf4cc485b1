package com.gg.grp.hkycg.service;

import com.gg.grp.hkycg.model.dto.AcceptanceQueryDTO;
import com.gg.grp.hkycg.model.vo.ProcurementLinkSummaryVO;

import java.util.List;

public interface AcceptanceResultService {
    
    /**
     * 带条件查询采购全流程数据
     * @param queryDTO 查询条件
     * @return 采购全流程数据
     */
    List<ProcurementLinkSummaryVO> getAcceptanceResultWithCondition(AcceptanceQueryDTO queryDTO);
} 