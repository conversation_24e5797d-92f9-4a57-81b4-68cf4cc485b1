package com.gg.grp.hkycg.service.impl;

import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.mapper.TodoMapper;
import com.gg.grp.hkycg.model.dto.TodoQueryDTO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.model.vo.TodoItemVO;
import com.gg.grp.hkycg.service.TodoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 待办事项Service实现类
 */
@Slf4j
@Service
public class TodoServiceImpl implements TodoService {

    @Autowired
    private TodoMapper todoMapper;

    @Override
    public PageResult<TodoItemVO> getMyApplications(TodoQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 计算偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String employeeCode = LoginInfo.getCurrEmployeeCode();

            // 查询我的申请列表
            List<TodoItemVO> myApplications = todoMapper.getMyApplications(
                    gsdm, kjnd, employeeCode,
                    queryDTO.getKeyword(), queryDTO.getBillType(),
                    queryDTO.getStatus(), queryDTO.getStartDate(),
                    queryDTO.getEndDate(), offset, queryDTO.getSize()
            );

            // 获取总记录数
            long totalCount = 0;
            if (!myApplications.isEmpty()) {
                totalCount = myApplications.get(0).getTotalCount();
            }

            // 创建分页结果
            return new PageResult<>(myApplications, totalCount, queryDTO.getCurrent(), queryDTO.getSize());
        } catch (Exception e) {
            log.error("查询我的申请列表异常", e);
            throw new RuntimeException("查询我的申请列表失败", e);
        }
    }

    @Override
    public PageResult<TodoItemVO> getMyAudited(TodoQueryDTO queryDTO) {
        try {
            // 参数校验和默认值设置
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 计算偏移量
            int offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 获取当前登录用户信息
            String gsdm = LoginInfo.getCurrCorpCode();
            String kjnd = LoginInfo.getCurrAccountantYear();
            String employeeCode = LoginInfo.getCurrEmployeeCode();

            // 查询我已审核的列表
            List<TodoItemVO> myAudited = todoMapper.getMyAudited(
                    gsdm, kjnd, employeeCode,
                    queryDTO.getKeyword(), queryDTO.getBillType(),
                    queryDTO.getStatus(), queryDTO.getStartDate(),
                    queryDTO.getEndDate(), offset, queryDTO.getSize()
            );

            // 获取总记录数
            long totalCount = 0;
            if (!myAudited.isEmpty()) {
                totalCount = myAudited.get(0).getTotalCount();
            }

            // 创建分页结果
            return new PageResult<>(myAudited, totalCount, queryDTO.getCurrent(), queryDTO.getSize());
        } catch (Exception e) {
            log.error("查询我已审核的列表异常", e);
            throw new RuntimeException("查询我已审核的列表失败", e);
        }
    }
} 