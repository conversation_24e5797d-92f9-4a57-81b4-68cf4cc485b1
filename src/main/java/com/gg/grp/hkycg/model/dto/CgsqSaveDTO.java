package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 采购申请保存DTO
 */
@Data
public class CgsqSaveDTO {

    /**
     * 基础信息
     */
    @Valid
    @NotNull(message = "基础信息不能为空")
    private CgsqBaseInfoDTO baseInfo;

    /**
     * 采购申请计划明细列表 - 每个明细包含自己的预算指标（可为空）
     */
    @Valid
    @JsonProperty("CgsqDetails")
    private List<CgsqJhmxWithBudgetDTO> CgsqDetails;
} 