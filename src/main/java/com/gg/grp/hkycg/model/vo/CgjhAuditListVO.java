package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购计划审核列表VO
 */
@Data
public class CgjhAuditListVO {
    /**
     * 单据编号
     */
    private String djbh;

    /**
     * 计划编号
     */
    private BigDecimal jhbh;

    /**
     * 计划名称
     */
    private String jhmc;

    /**
     * 计划金额
     */
    private BigDecimal jhje;

    /**
     * 申请人
     */
    private String lrr;

    /**
     * 申请人代码
     */
    private String lrrId;

    /**
     * 录入日期
     */
    private String lrRq;

    /**
     * 拟采购日期
     */
    private String cgrq;

    /**
     * 单据状态
     */
    private BigDecimal zt;

    /**
     * 单据状态名称
     */
    private String ztmc;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

    /**
     * 当前审核节点名称
     */
    private String currentNodeName;

    /**
     * 当前审核人
     */
    private String currentAuditor;

    /**
     * 是否可审核（当前用户是否为审核人）
     */
    private Boolean canAudit;

    /**
     * 审核节点序号
     */
    private Integer nodeSeq;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 计划管理部门
     */
    private String bmmc;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 备注
     */
    private String bz;

    /**
     * 归口管理部门
     */
    private String gkglbm;

    /**
     * 单据类型
     */
    private String djlx;

    /**
     * 总记录数（分页用）
     */
    @JsonIgnore
    private Long totalCount;
} 