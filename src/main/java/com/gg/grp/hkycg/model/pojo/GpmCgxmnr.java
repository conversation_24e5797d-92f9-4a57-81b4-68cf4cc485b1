package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购项目内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGXMNR")
public class GpmCgxmnr {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("XMBH")
    private BigDecimal xmbh; // 项目编号

    @TableField("XMXH")
    private BigDecimal xmxh; // 项目序号

    @TableField("JHXH")
    private BigDecimal jhxh; // 计划序号

    @TableField("CGMLDM")
    private String cgmldm; // 采购品目代码

    @TableField("CGMLMC")
    private String cgmlmc; // 采购品目名称

    @TableField("WPMC")
    private String wpmc; // 物品名称

    @TableField("GGXH")
    private String ggxh; // 规格型号

    @TableField("WPSL")
    private BigDecimal wpsl; // 物品数量

    @TableField("JLDW")
    private String jldw; // 计量单位

    @TableField("WPDJ")
    private BigDecimal wpdj; // 物品单价

    @TableField("JE")
    private BigDecimal je; // 金额
} 