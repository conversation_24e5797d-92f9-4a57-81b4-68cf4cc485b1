package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购项目目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGXMML")
public class GpmCgxmml {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("XMBH")
    private BigDecimal xmbh; // 项目编号

    @TableField("XMID")
    private String xmid; // 项目ID

    @TableField("XMDM")
    private String xmdm; // 项目代码

    @TableField("XMMC")
    private String xmmc; // 项目名称

    @TableField("XMRQ")
    private String xmrq; // 项目日期

    @TableField("JHID")
    private String jhid; // 计划ID

    @TableField("YSDWDM")
    private String ysdwdm; // 验收单位代码

    @TableField("YSDWMC")
    private String ysdwmc; // 验收单位名称

    @TableField("BMDM")
    private String bmdm; // 部门代码

    @TableField("BMMC")
    private String bmmc; // 部门名称

    @TableField("ZZFSDM")
    private String zzfsdm; // 组织方式代码

    @TableField("ZZFSMC")
    private String zzfsmc; // 组织方式名称

    @TableField("CGFSDM")
    private String cgfsdm; // 采购方式代码

    @TableField("CGFSMC")
    private String cgfsmc; // 采购方式名称

    @TableField("LXR")
    private String lxr; // 联系人

    @TableField("LXDH")
    private String lxdh; // 联系电话

    @TableField("ZJLYDM")
    private String zjlydm; // 专家来源代码

    @TableField("ZJLY")
    private String zjly; // 专家来源名称

    @TableField("HJJE")
    private BigDecimal hjje; // 合计金额

    @TableField("SCFJ")
    private String scfj; // 附件

    @TableField("BZ")
    private String bz; // 备注

    @TableField("ZT")
    private BigDecimal zt; // 状态

    @TableField("STAMP")
    private BigDecimal stamp; // 时间戳

    @TableField("OAZT")
    private String oazt; // OA状态

    @TableField("LRR_ID")
    private BigDecimal lrrId; // 录入人ID

    @TableField("LRR")
    private String lrr; // 录入人

    @TableField("LR_RQ")
    private String lrRq; // 录入日期

    @TableField("SSR_ID")
    private BigDecimal ssrId; // 审核人ID

    @TableField("SSR")
    private String ssr; // 审核人

    @TableField("SS_RQ")
    private String ssRq; // 审核日期

    @TableField("PFR_ID")
    private BigDecimal pfrId; // 批复人ID

    @TableField("PFR")
    private String pfr; // 批复人

    @TableField("PF_RQ")
    private String pfRq; // 批复日期

    @TableField("YSR_ID")
    private BigDecimal ysrId; // 预算人ID

    @TableField("YSR")
    private String ysr; // 预算人

    @TableField("YS_RQ")
    private String ysRq; // 预算日期

    @TableField("ASHR_ID")
    private Integer ashrId; // 审核人ID(OA)

    @TableField("ASHR")
    private String ashr; // 审核人(OA)

    @TableField("ASH_RQ")
    private String ashRq; // 审核日期(OA)

    @TableField("ASHJD")
    private Integer ashjd; // 审核节点

    @TableField("ASHZT")
    private String ashzt; // 审核状态

    @TableField("AXSHJD")
    private Integer axshjd; // 下一步审核节点

    @TableField("ASFTH")
    private String asfth; // 是否退回

    @TableField("ATHXX")
    private String athxx; // 退回信息

    @TableField("FLOWCODE")
    private String flowcode; // 流程代码

    @TableField("IsHaveHT")
    private String isHaveHT; // 是否有合同

    @TableField("PFBH")
    private String pfbh; // 批复编号

    @TableField("XMQD_RQ")
    private String xmqdRq; // 项目启动日期

    @TableField("CGLYDM")
    private String cglydm; // 采购来源代码

    @TableField("CGLYMC")
    private String cglymc; // 采购来源名称

    @TableField("CGZJLYFSDM")
    private String cgzjlyfsdm; // 专家来源方式代码

    @TableField("CGZJLYFSMC")
    private String cgzjlyfsmc; // 专家来源方式名称

    @TableField("ZJJGDM")
    private String zjjgdm; // 中介机构代码

    @TableField("ZJJGMC")
    private String zjjgmc; // 中介机构名称

    @TableField("isclose")
    private String isclose; // 是否关闭

    @TableField("closeuser")
    private String closeuser; // 关闭人

    @TableField("closedate")
    private Date closedate; // 关闭日期
} 