package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 指标使用记录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GBI_ZBSYREC")
public class GbiZbsyrec {

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 模块
     */
    @TableField("MODULE")
    private String module;

    /**
     * 单据类型
     */
    @TableField("DJLX")
    private String djlx;

    /**
     * 单据ID
     */
    @TableField("DJID")
    private String djid;

    /**
     * 单据分录号
     */
    @TableField("DJFLH")
    private Integer djflh;

    /**
     * 单据分录明细
     */
    @TableField("DJFLMX")
    private Integer djflmx;

    /**
     * 单据业务日期
     */
    @TableField("DJYWRQ")
    private String djywrq;

    /**
     * 单据状态
     */
    @TableField("DJZT")
    private String djzt;

    /**
     * 计划ID
     */
    @TableField("JHID")
    private Integer jhid;

    /**
     * 单据金额
     */
    @TableField("DJJE")
    private BigDecimal djje;

    /**
     * 单据摘要
     */
    @TableField("DJZY")
    private String djzy;

    /**
     * 收支方向
     */
    @TableField("SZFX")
    private String szfx;

    /**
     * 指标类别
     */
    @TableField("ZBLB")
    private String zblb;

    /**
     * 操作类型
     */
    @TableField("CZLX")
    private String czlx;

    /**
     * 借贷标志
     */
    @TableField("JDBZ")
    private String jdbz;

    /**
     * 冲账标志
     */
    @TableField("ZZBZ")
    private String zzbz;

    /**
     * 数量
     */
    @TableField("SL")
    private BigDecimal sl;

    /**
     * 业务单据ID
     */
    @TableField("YWDJID")
    private String ywdjid;

    /**
     * 明细ID - 关联到采购计划明细
     */
    @TableField("MXXH")
    private String mxxh;
}