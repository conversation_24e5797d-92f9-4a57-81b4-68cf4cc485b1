package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("GPM_ROLE")
public class GpmRole {
    /**
     * 角色代码
     */
    @TableId("ROLECODE")
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField("ROLENAME")
    private String roleName;

    /**
     * 默认为0
     */
    @TableField("isDefault")
    private String isDefault;

    /**
     * 唯一识别码
     */
    @TableField("id")
    private String id;

    /**
     * 部门代码 预算生成指标的时候 一键给对应的部门授权指标
     */
    @TableField("bmdm")
    private String bmdm;
}
