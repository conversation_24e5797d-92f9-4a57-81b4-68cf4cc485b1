package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 采购结果保存DTO
 */
@Data
public class CgjgSaveDTO {

    /**
     * 基础信息
     */
    @Valid
    @NotNull(message = "基础信息不能为空")
    private CgjgBaseInfoDTO baseInfo;

    /**
     * 采购结果明细列表
     */
    @Valid
    @JsonProperty("CgjgDetails")
    private List<CgjgDetailDTO> CgjgDetails;
}
