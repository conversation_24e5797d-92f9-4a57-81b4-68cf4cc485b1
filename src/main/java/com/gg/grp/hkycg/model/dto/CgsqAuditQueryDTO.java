package com.gg.grp.hkycg.model.dto;

import lombok.Data;

/**
 * 采购申请审核查询DTO
 */
@Data
public class CgsqAuditQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 申请名称（模糊查询）
     */
    private String applicationName;
    
    /**
     * 申请人（模糊查询）
     */
    private String applicant;
    
    /**
     * 单据状态（可选：2-已提交，3-审核中）
     */
    private Integer billStatus;
    
    /**
     * 开始日期（格式：yyyy-MM-dd）
     */
    private String startDate;
    
    /**
     * 结束日期（格式：yyyy-MM-dd）
     */
    private String endDate;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
} 