package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础资料要素关联设置表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_JCZLYSGLSZ")
public class GpmJczlysglsz {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("GLLX")
    private String gllx; // 关联类型

    @TableField("SCDM")
    private String scdm; // 基础资料代码

    @TableField("DCDM")
    private String dcdm; // 关联资料代码
} 