package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购计划预算指标表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGJH_YSZB")
public class GpmCgjhYszb {

    /**
     * 单据编号
     */
    @TableField("DJBH")
    private String djbh;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 序号
     */
    @TableField("XH")
    private Integer xh;

    /**
     * 指标代码
     */
    @TableField("ZBDM")
    private String zbdm;

    /**
     * 金额
     */
    @TableField("JE")
    private BigDecimal je;

    /**
     * 指标ID
     */
    @TableField("ZBID")
    private Integer zbid;

    /**
     * 计划指标ID
     */
    @TableField("JHZBID")
    private Integer jhzbid;

    /**
     * 摘要
     */
    @TableField("ZY")
    private String zy;

    /**
     * 科目代码
     */
    @TableField("KMDM")
    private String kmdm;

    /**
     * 科目名称
     */
    @TableField("KMMC")
    private String kmmc;

    /**
     * 指标功能科目代码
     */
    @TableField("ZBGNKMDM")
    private String zbgnkmdm;

    /**
     * 指标功能科目名称
     */
    @TableField("ZBGNKMMC")
    private String zbgnkmmc;

    /**
     * 指标经济科目代码
     */
    @TableField("ZBJJKMDM")
    private String zbjjkmdm;

    /**
     * 指标经济科目名称
     */
    @TableField("ZBJJKMMC")
    private String zbjjkmmc;

    /**
     * 控制指标ID
     */
    @TableField("KZZBID")
    private Integer kzzbid;
} 