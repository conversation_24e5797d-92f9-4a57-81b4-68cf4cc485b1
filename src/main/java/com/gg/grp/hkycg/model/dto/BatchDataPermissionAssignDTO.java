package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量数据权限分配DTO
 */
@Data
public class BatchDataPermissionAssignDTO {
    
    /**
     * 职员代码列表
     */
    @NotEmpty(message = "职员代码列表不能为空")
    private List<String> zydmList;
    
    /**
     * 数据权限代码
     */
    @NotBlank(message = "数据权限代码不能为空")
    private String datacode;
} 