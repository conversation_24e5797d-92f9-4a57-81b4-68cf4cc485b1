package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 已审核采购招标响应VO
 */
@Data
public class ApprovedBiddingVO {
    
    /**
     * 拟招标日期
     */
    @JsonProperty("nzbrq")
    private String plannedBiddingDate;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 招标编号
     */
    @JsonProperty("zbbh")
    private String biddingNo;
    
    /**
     * 招标申请名称
     */
    @JsonProperty("zbsqmc")
    private String biddingApplicationName;
    
    /**
     * 制单日期
     */
    @JsonProperty("zdrq")
    private String createDate;
    
    /**
     * 单据状态
     */
    @JsonProperty("djzt")
    private String billStatus;
    
    /**
     * 招标申请部门
     */
    @JsonProperty("zbsqbm")
    private String biddingApplicationDept;
    
    /**
     * 招标负责人
     */
    @JsonProperty("zbfzr")
    private String biddingManager;
    
    /**
     * 经办人
     */
    @JsonProperty("jbr")
    private String handler;
    
    /**
     * 招标理由
     */
    @JsonProperty("zbly")
    private String biddingReason;
    
    /**
     * 采购申请金额
     */
    @JsonProperty("cgsqje")
    private BigDecimal purchaseApplicationAmount;
}
