package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 采购组织方式列表VO
 */
@Data
public class CgzzfsListVO {
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 组织方式代码
     */
    private String zzfsdm;
    
    /**
     * 组织方式名称
     */
    private String zzfsmc;
    
    /**
     * 使用状态
     */
    private String syzt;
    
    /**
     * 使用状态名称
     */
    private String syztmc;
    
    /**
     * 助记码
     */
    private String zjm;
    
    /**
     * 备注
     */
    private String bz;
    
    /**
     * 排序号
     */
    private Integer pxh;
    
    /**
     * 是否显示
     */
    private String sfxs;
    
    /**
     * 是否显示名称
     */
    private String sfxsmc;

    /**
     * 总记录数（分页用）
     */
    @JsonIgnore
    private Long totalCount;
} 