package com.gg.grp.hkycg.model.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CheckFlowTemplateBillVO {
    /**
     * 状态名
     */
    private String status;
    /**
     * 可供选择的状态名
     */
    private List<String> availableStatusList;
    public void putAvailableStatus(String status){
        if (null == availableStatusList){
            this.availableStatusList = new ArrayList<>();
        }
        this.availableStatusList.add(status);
    }
    public void putAvailableStatus(String status, int index){
        if (null == availableStatusList){
            this.availableStatusList = new ArrayList<>();
        }
        this.availableStatusList.add(Math.min(index,availableStatusList.size()), status);
    }
    /**
     * 单据类型id
     */
    private Integer billTypeId;
    /**
     * 单据类型名称
     */
    private String billTypeName;
    /**
     * 节点列表
     */
    private List<FlowTemplateNodeVO> nodeList;
}
