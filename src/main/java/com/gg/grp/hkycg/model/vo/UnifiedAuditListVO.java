package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 统一审核列表VO
 * 用于同时展示采购计划和采购申请的审核信息
 */
@Data
public class UnifiedAuditListVO {
    
    /**
     * 单据类型 (e.g., "采购计划", "采购申请", "招标申请", "采购结果")
     */
    @JsonProperty("djlxmc")
    private String billType;
    
    /**
     * 单据编号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 单据名称/标题
     */
    @JsonProperty("djmc")
    private String billName;
    
    /**
     * 申请人/经办人
     */
    @JsonProperty("sqr")
    private String applicant;
    
    /**
     * 申请部门
     */
    @JsonProperty("bmmc")
    private String applyDept;
    
    /**
     * 单据金额
     */
    @JsonProperty("djje")
    private BigDecimal amount;
    
    /**
     * 提交/申请时间
     */
    @JsonProperty("zdrq")
    private String submitTime;
    
    /**
     * 状态代码
     */
    @JsonProperty("zt")
    private String statusCode;
    
    /**
     * 状态名称
     */
    @JsonProperty("ztmc")
    private String statusName;

    /**
     * 单据内容
     */
    @JsonProperty("djnr")
    private String billContent;
    
    /**
     * 总记录数（分页用）
     */
    @JsonIgnore
    private Integer totalCount;
} 