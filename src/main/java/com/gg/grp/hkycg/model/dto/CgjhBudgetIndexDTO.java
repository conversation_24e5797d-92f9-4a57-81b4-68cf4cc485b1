package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购计划预算指标DTO
 */
@Data
public class CgjhBudgetIndexDTO {

    /**
     * 主键ID
     */
    private String zbid;

    /**
     * 序号/索引
     */
    private Integer index;

    /**
     * 项目
     */
    private String project;

    /**
     * 会计科目
     */
    private String accountTitle;

    /**
     * 经济科目
     */
    private String economicSubject;

    /**
     * 可用余额
     */
    private BigDecimal residual;

    /**
     * 金额
     */
    private String amount;

    /**
     * 费用备注
     */
    private String expenseRemark;

    /**
     * 支出部门
     */
    private String outlayDept;

    /**
     * 执行方式
     */
    private String executeWay;

    /**
     * 扩展字段7代码
     */
    private String fZ7DM;


    /**
     * 扩展字段7名称
     */
    private String fZ7MC;

    /**
     * 扩展字段8代码
     */
    private String fZ8DM;

    /**
     * 扩展字段8名称
     */
    private String fZ8MC;

    /**
     * 扩展字段9代码
     */
    private String fZ9DM;

    /**
     * 扩展字段9名称
     */
    private String fZ9MC;

    /**
     * 员工
     */
    private String employee;

    /**
     * 指标来源
     */
    private String indexSource;

    /**
     * 功能科目
     */
    private String funcSubject;

    /**
     * 明细序号 - 关联到采购计划明细
     */
    private String mxxh;

    private String zbdm;

} 