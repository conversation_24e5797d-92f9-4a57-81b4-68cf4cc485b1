package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 招标申请基础信息DTO
 */
@Data
public class CgzbsqBaseInfoDTO {

    /**
     * 招标申请编号（主键）
     */
    private String zbsqbh;

    /**
     * 招标申请名称
     */
    private String zbsqmc;

    /**
     * 采购申请金额
     */
    private BigDecimal cgsqje;

    /**
     * 招标申请部门代码
     */
    private String zbsqbmdm;

    /**
     * 招标申请部门名称
     */
    private String zbsqbmmc;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 项目已采购金额
     */
    private BigDecimal xmycgje;

    /**
     * 中介机构代码
     */
    private String zjjgdm;

    /**
     * 中介机构名称
     */
    private String zjjgmc;

    /**
     * 采购专家来源方式代码
     */
    private String cgzjlyfsdm;

    /**
     * 采购专家来源方式名称
     */
    private String cgzjlyfsmc;

    /**
     * 政府采购方式代码
     */
    private String zfcgfsdm;

    /**
     * 政府采购方式名称
     */
    private String zfcgfsmc;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 招标负责人
     */
    private String zbfzr;

    /**
     * 拟招标日期（格式：yyyy-MM-dd）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String nzbrq;

    /**
     * 项目金额
     */
    private BigDecimal xmje;

    /**
     * 采购理由
     */
    private String cgly;

    /**
     * 备注
     */
    private String bz;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;
} 