package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购中介机构表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGZJJG")
public class GpmCgzjjg {

    @TableField("CGZJJGID")
    private String cgzjjgid;

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("ZJJGDM")
    private String zjjgdm; // 中介机构代码

    @TableField("ZJJGMC")
    private String zjjgmc; // 中介机构名称

    @TableField("SYZT")
    private String syzt; // 使用状态

    @TableField("ZJM")
    private String zjm; // 助记码

    @TableField("BZ")
    private String bz; // 备注

    @TableField("PXH")
    private Integer pxh; // 排序号

    @TableField("JC")
    private String jc; // 级次

    @TableField("SFHW")
    private String sfhw; // 是否海外

    @TableField("SFFW")
    private String sffw; // 是否服务

    @TableField("SFGC")
    private String sfgc; // 是否工程

    @TableField("LXR")
    private String lxr; // 联系人

    @TableField("LXDH")
    private String lxdh; // 联系电话
} 