package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 已审核采购结果响应VO
 */
@Data
public class ApprovedResultVO {
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 结果登记编号
     */
    @JsonProperty("jgdjbh")
    private String resultRegistrationNo;
    
    /**
     * 结果登记名称
     */
    @JsonProperty("jgdjmc")
    private String resultRegistrationName;
    
    /**
     * 制单日期
     */
    @JsonProperty("zdrq")
    private String createDate;
    
    /**
     * 单据状态
     */
    @JsonProperty("djzt")
    private String billStatus;
    
    /**
     * 采购部门
     */
    @JsonProperty("cgbm")
    private String purchaseDept;
    
    /**
     * 招标负责人
     */
    @JsonProperty("zbfzr")
    private String biddingManager;
    
    /**
     * 经办人
     */
    @JsonProperty("jbr")
    private String handler;
    
    /**
     * 采购理由
     */
    @JsonProperty("cgly")
    private String purchaseReason;
    
    /**
     * 结果登记金额
     */
    @JsonProperty("jgdjje")
    private BigDecimal resultRegistrationAmount;
}
