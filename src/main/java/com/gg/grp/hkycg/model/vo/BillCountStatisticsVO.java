package com.gg.grp.hkycg.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 单据数量统计VO
 */
@Data
@Accessors(chain = true)
public class BillCountStatisticsVO {
    
    /**
     * 柱状图类别（行数据）
     */
    private List<String> categories;
    
    /**
     * 已办结数据
     */
    private List<Integer> completedCounts;
    
    /**
     * 未办结数据
     */
    private List<Integer> incompleteCounts;
} 