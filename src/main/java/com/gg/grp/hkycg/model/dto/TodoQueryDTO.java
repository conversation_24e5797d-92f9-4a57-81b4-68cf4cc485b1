package com.gg.grp.hkycg.model.dto;

import lombok.Data;

/**
 * 待办事项查询条件DTO
 */
@Data
public class TodoQueryDTO {
    /**
     * 当前页码，默认为1
     */
    private Integer current = 1;
    
    /**
     * 每页大小，默认为10
     */
    private Integer size = 10;
    
    /**
     * 查询关键字（可模糊匹配单据编号、单据名称）
     */
    private String keyword;
    
    /**
     * 单据类型
     * 可选值：
     * - CGJH：采购计划
     * - CGSQ：采购申请
     * - CGZB：采购招标
     * - CGJG：采购结果
     * - CGYS：采购验收
     */
    private String billType;
    
    /**
     * 单据状态
     * 可选值：
     * - 1：保存
     * - 2：已提交
     * - 3：审核中
     * - 4：已审核
     * - 5：已退回
     */
    private String status;
    
    /**
     * 开始日期（格式：yyyy-MM-dd）
     */
    private String startDate;
    
    /**
     * 结束日期（格式：yyyy-MM-dd）
     */
    private String endDate;
} 