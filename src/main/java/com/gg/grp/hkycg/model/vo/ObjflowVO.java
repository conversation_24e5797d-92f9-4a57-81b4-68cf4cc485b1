package com.gg.grp.hkycg.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ObjflowVO {
    /**
     * 单据号
     */
    @JSONField(name = "djh")
    private String DJH;
    /**
     * 审核人工号
     */
    @JSONField(name = "shr")
    private String SHR;
    /**
     * 审核节点代码
     */
    @JSONField(name = "currNodeCode")
    private Integer auditFlag;
    /**
     * 下一审核节点的节点代码
     */
    @JSONField(name = "nextNodeCode")
    private Integer auditAftFlag;
    /**
     * 审核人姓名
     */
    @JSONField(name = "approverName")
    private String shrmc;
    /**
     * 审核节点名称
     */
    private String currNodeName;
}
