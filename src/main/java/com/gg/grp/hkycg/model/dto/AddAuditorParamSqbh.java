package com.gg.grp.hkycg.model.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 加审参数（使用申请编号SQBH）
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AddAuditorParamSqbh {
    /**
     * 申请编号（主键）
     */
    private String sqbh;

    /**
     * 审核人列表
     */
    private List<String> auditors;
} 