package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作流程模板实体类
 * 对应数据表：PUB_OBJ_FLOW_TEMP
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PUB_OBJ_FLOW_TEMP")
public class PubObjFlowTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

//    /**
//     * 会计年度
//     */
//    @TableField("KJND")
//    private String kjnd;

    /**
     * 流程代码
     */
//    @TableField("FLOWCODE")
//    private String flowcode;

    /**
     * 单据类型ID
     */
    @TableField("DJLXID")
    private Integer djlxid;

    /**
     * 节点代码
     */
    @TableField("JDDM")
    private Integer jddm;

    /**
     * 节点名称
     */
    @TableField("JDMC")
    private String jdmc;

    /**
     * 审核人代码
     */
    @TableField("SHRDM")
    private String shrdm;

    /**
     * 审核人姓名
     */
    @TableField("SHRXM")
    private String shrxm;

    /**
     * 审核条件
     */
    @TableField("SHTJ")
    private String shtj;

    /**
     * 节点审核条件
     */
    @TableField("JDSHTJ")
    private String jdshtj;

    /**
     * 序号
     */
    @TableField("XH")
    private String xh;

    /**
     * 是否动态获取审核人
     */
    @TableField("dynamicAcquisition")
    private String dynamicAcquisition;

}
