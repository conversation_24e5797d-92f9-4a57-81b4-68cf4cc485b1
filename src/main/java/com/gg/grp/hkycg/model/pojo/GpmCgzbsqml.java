package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 招标申请目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGZBSQML")
public class GpmCgzbsqml {

    /**
     * 招标申请编号
     */
    @TableId("ZBSQBH")
    private String zbsqbh;

    /**
     * 招标申请名称
     */
    @TableField("ZBSQMC")
    private String zbsqmc;

    /**
     * 采购申请金额
     */
    @TableField("CGSQJE")
    private BigDecimal cgsqje;

    /**
     * 招标申请部门代码
     */
    @TableField("ZBSQBMDM")
    private String zbsqbmdm;

    /**
     * 招标申请部门名称
     */
    @TableField("ZBSQBMMC")
    private String zbsqbmmc;

    /**
     * 项目名称
     */
    @TableField("XMMC")
    private String xmmc;

    /**
     * 项目已采购金额
     */
    @TableField("XMYCGJE")
    private BigDecimal xmycgje;

    /**
     * 中介机构代码
     */
    @TableField("ZJJGDM")
    private String zjjgdm;

    /**
     * 中介机构名称
     */
    @TableField("ZJJGMC")
    private String zjjgmc;

    /**
     * 采购专家来源方式代码
     */
    @TableField("CGZJLYFSDM")
    private String cgzjlyfsdm;

    /**
     * 采购专家来源方式名称
     */
    @TableField("CGZJLYFSMC")
    private String cgzjlyfsmc;

    /**
     * 政府采购方式代码
     */
    @TableField("ZFCGFSDM")
    private String zfcgfsdm;

    /**
     * 政府采购方式名称
     */
    @TableField("ZFCGFSMC")
    private String zfcgfsmc;

    /**
     * 经办人
     */
    @TableField("JBR")
    private String jbr;

    /**
     * 招标负责人
     */
    @TableField("ZBFZR")
    private String zbfzr;

    /**
     * 拟招标日期
     */
    @TableField("NZBRQ")
    private String nzbrq;

    /**
     * 项目金额
     */
    @TableField("XMJE")
    private BigDecimal xmje;

    /**
     * 采购理由
     */
    @TableField("CGLY")
    private String cgly;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 状态
     */
    @TableField("ZT")
    private String zt;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 创建时间
     */
    @TableField("CJSJ")
    private String cjsj;

    /**
     * 修改时间
     */
    @TableField("XGSJ")
    private String xgsj;

    /**
     * 创建人代码
     */
    @TableField("CJRDM")
    private String cjrdm;

    /**
     * 创建人名称
     */
    @TableField("CJRMC")
    private String cjrmc;
} 