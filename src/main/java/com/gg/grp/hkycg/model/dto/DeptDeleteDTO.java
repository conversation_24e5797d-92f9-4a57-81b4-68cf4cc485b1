package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 部门删除DTO
 */
@Data
public class DeptDeleteDTO {

    /**
     * 部门代码
     */
    @NotBlank(message = "部门代码不能为空")
    private String bmdm;
}

/**
 * 部门批量删除DTO
 */
@Data
class DeptBatchDeleteDTO {

    /**
     * 部门代码列表
     */
    @NotEmpty(message = "删除的部门代码列表不能为空")
    private List<String> bmdmList;
}
