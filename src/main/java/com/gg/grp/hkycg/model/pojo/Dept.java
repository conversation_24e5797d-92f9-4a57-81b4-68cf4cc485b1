package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 部门实体类
 */
@Data
@TableName("PUBBMXX")
public class Dept {

    @TableField("BMID")
    private String bmid;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 部门代码
     */
    @TableField("BMDM")
    private String bmdm;
    
    /**
     * 部门名称
     */
    @TableField("BMMC")
    private String bmmc;

    /**
     *
     */
    @TableField("BMlb")
    private String bmlb;

    /**
     * 负责人代码
     */
    @TableField("fzrdm")
    private String fzrdm;

    @TableField("tel")
    private String tel;

    @TableField("xjbms")
    private String xjbms;

    @TableField("zgrs")
    private String zgrs;

    /**
     * 使用状态
     */
    @TableField("syzt")
    private String syzt;

    @TableField("Jlr_ID")
    private String jlrId;

    @TableField("Jl_RQ")
    private String jlRq;

    @TableField("Xgr_ID")
    private String zgrId;

    @TableField("Xg_RQ")
    private String xgRq;

    @TableField("Sjly")
    private String sjly;

    @TableField("SFMX")
    private String sfmx;

    @TableField("zjm")
    private String zjm;

    @TableField("fzrdm2")
    private String fzrdm2;

    @TableField("fzrdm3")
    private String fzrdm3;

    @TableField("sftck")
    private String sftck;

} 