package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购结果审核列表VO
 */
@Data
public class CgjgAuditListVO {
    
    /**
     * 结果登记编号（主键）
     */
    private String jgdjbh;
    
    /**
     * 结果登记名称
     */
    private String jgdjmc;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 申请状态
     */
    private String zt;
    
    /**
     * 申请状态名称
     */
    private String ztmc;
    
    /**
     * 创建时间
     */
    private String cjsj;

    /**
     * 采购部门代码
     */
    private String cgbmdm;
    
    /**
     * 采购部门名称
     */
    private String cgbmmc;
    
    /**
     * 项目金额
     */
    private BigDecimal xmje;

    /**
     * 中标总金额
     */
    private BigDecimal zbzje;

    /**
     * 节约金额
     */
    private BigDecimal jyje;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 申请人
     */
    private String sqr;

    /**
     * 申请时间
     */
    private String sqsj;

    /**
     * 当前审核人
     */
    private String dqshr;

    /**
     * 当前审核节点
     */
    private String dqshjd;

    /**
     * 审核状态
     */
    private String shzt;

    /**
     * 审核状态名称
     */
    private String shztmc;

    /**
     * 采购方式代码
     */
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    private String cgfsmc;

    @JsonIgnore
    private Integer totalCount;
}
