package com.gg.grp.hkycg.model.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 采购计划审核查询参数
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CgjhAuditQueryDTO {
    /**
     * 当前页码
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 开始日期（yyyyMMdd）
     */
    private String startDate;

    /**
     * 结束日期（yyyyMMdd）
     */
    private String endDate;

    /**
     * 单据状态（2-已提交，3-审核中）
     */
    private Integer billStatus;

    /**
     * 单据名称
     */
    private String billName;

    /**
     * 申请人（模糊查询）
     */
    private String applicant;
} 