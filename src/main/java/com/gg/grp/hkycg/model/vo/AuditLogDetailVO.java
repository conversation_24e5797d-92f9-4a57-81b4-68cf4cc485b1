package com.gg.grp.hkycg.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * 审核记录明细VO
 */
@Data
public class AuditLogDetailVO {

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核意见
     */
    private String opinion;

    /**
     * 节点序号
     */
    private Integer nodeSeq;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 审核类型
     */
    private String auditType;
}