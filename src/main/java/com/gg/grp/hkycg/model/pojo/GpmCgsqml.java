package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购申请目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGSQML")
public class GpmCgsqml {

    /**
     * 申请编号（主键）
     */
    @TableId("SQBH")
    private String sqbh;

    /**
     * 计划编号（外键）
     */
    @TableField("JHBH")
    private String jhbh;

    /**
     * 申请名称
     */
    @TableField("SQMC")
    private String sqmc;

    /**
     * 采购类型代码
     */
    @TableField("CGLXDM")
    private String cglxdm;

    /**
     * 采购类型名称
     */
    @TableField("CGLXMC")
    private String cglxmc;

    /**
     * 经办人
     */
    @TableField("JBR")
    private String jbr;

    /**
     * 申请部门代码
     */
    @TableField("SQBMDM")
    private String sqbmdm;

    /**
     * 申请部门名称
     */
    @TableField("SQBMMC")
    private String sqbmmc;

    /**
     * 计划部门代码
     */
    @TableField("BMDM")
    private String bmdm;

    /**
     * 计划部门名称
     */
    @TableField("BMMC")
    private String bmmc;

    /**
     * 项目名称
     */
    @TableField("XMMC")
    private String xmmc;

    /**
     * 采购方式代码
     */
    @TableField("CGFSDM")
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    @TableField("CGFSMC")
    private String cgfsmc;

    /**
     * 采购组织方式代码
     */
    @TableField("ZZFSDM")
    private String zzfsdm;

    /**
     * 采购组织方式名称
     */
    @TableField("ZZFSMC")
    private String zzfsmc;

    /**
     * 专家来源方式代码
     */
    @TableField("CGZJLYFSDM")
    private String cgzjlyfsdm;

    /**
     * 专家来源方式名称
     */
    @TableField("CGZJLYFSMC")
    private String cgzjlyfsmc;

    /**
     * 代理中介机构代码
     */
    @TableField("ZJJGDM")
    private String zjjgdm;

    /**
     * 代理中介机构名称
     */
    @TableField("ZJJGMC")
    private String zjjgmc;

    /**
     * 政府采购方式代码
     */
    @TableField("ZFCGFSDM")
    private String zfcgfsdm;

    /**
     * 政府采购方式名称
     */
    @TableField("ZFCGFSMC")
    private String zfcgfsmc;

    /**
     * 预算审计方式代码
     */
    @TableField("YSSJFSDM")
    private String yssjfsdm;

    /**
     * 预算审计方式名称
     */
    @TableField("YSSJFSMC")
    private String yssjfsmc;

    /**
     * 拟采购日期
     */
    @TableField("NCGRQ")
    private String ncgrq;

    /**
     * 政府采购编号
     */
    @TableField("ZFCGBH")
    private String zfcgbh;

    /**
     * 申请金额
     */
    @TableField("SQJE")
    private BigDecimal sqje;

    /**
     * 临时采购计划（Y/N）
     */
    @TableField("LSCGJH")
    private String lscgjh;

    /**
     * 三重一大事项立项审批（Y/N）
     */
    @TableField("SCYDSXLXSP")
    private String scydsxlxsp;

    /**
     * 上级部门立项审批（Y/N）
     */
    @TableField("SJBMLXSP")
    private String sjbmlxsp;

    /**
     * 是否政府采购（Y/N）
     */
    @TableField("SZFCG")
    private String szfcg;

    /**
     * 纪委部门立项审批（Y/N）
     */
    @TableField("JWBMLXSP")
    private String jwbmlxsp;

    /**
     * 申请依据及理由
     */
    @TableField("SQYJJLY")
    private String sqyjjly;

    /**
     * 采购理由代码
     */
    @TableField("CGLYDM")
    private String cglydm;

    /**
     * 采购理由名称
     */
    @TableField("CGLYMC")
    private String cglymc;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 创建日期
     */
    @TableField("CJRQ")
    private String cjrq;

    /**
     * 创建人名称
     */
    @TableField("CJRMC")
    private String cjrmc;

    /**
     * 创建人代码
     */
    @TableField("CJRDM")
    private String cjrdm;

    /**
     * 状态
     */
    @TableField("ZT")
    private String zt;

    /**
     * 是否采购计划引入
     */
    @TableField("SFCGJHYR")
    private String sfcgjhyr;

} 