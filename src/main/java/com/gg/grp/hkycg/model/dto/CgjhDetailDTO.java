package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购计划明细DTO
 */
@Data
public class CgjhDetailDTO {
    /**
     * 明细编号（明细序号）
     */
    private String jhmxxh;

    /**
     * 采购品目代码
     */
    private String cgmldm;

    /**
     * 采购品目名称
     */
    private String cgmlmc;

    /**
     * 物品名称
     */
    private String wpmc;

    /**
     * 规格型号
     */
    private String ggxh;

    /**
     * 计量单位
     */
    private String jldw;

    /**
     * 数量上限
     */
    private BigDecimal slsx;

    /**
     * 预算上限
     */
    private BigDecimal yssx;

    /**
     * 计划单价
     */
    private BigDecimal jhdj;

    /**
     * 计划数量
     */
    private BigDecimal jhsl;

    /**
     * 计划金额
     */
    private BigDecimal jhje;

    /**
     * 预算指标
     */
    private String yszb;

    /**
     * 归口管理部门
     */
    private String gkglbmdm;

    /**
     * 归口管理部门
     */
    private String gkglbmmc;

    /**
     * 是否固定资产
     */
    private Boolean sfGdzc;

    /**
     * 是否进口产品
     */
    private Boolean sfJkcp;

    /**
     * 是否涉密
     */
    private Boolean sfSm;

    /**
     * 是否协议供货
     */
    private Boolean sfXygh;

    /**
     * 是否政府采购
     */
    private Boolean sfZfcg;

    /**
     * 备注
     */
    private String bz;

    /**
     * 采购方式代码
     */
    private String jhnrcgfsdm;

    /**
     * 采购方式名称
     */
    private String jhnrcgfsmc;
}
