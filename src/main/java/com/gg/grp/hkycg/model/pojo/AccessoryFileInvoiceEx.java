package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AccessoryFileInvoiceEx extends Model<AccessoryFileInvoiceEx> {
    /**
     * 对应accessory_file表的id
     */
    private String lid;
    /**
     * 发票号
     */
    @TableField("invoice_no")
    private String invoiceNo;
    /**
     * 对方单位
     */
    private String dfdw;
    /**
     * 发票金额
     */
    @TableField("invoice_money")
    private BigDecimal invoiceMoney;

    /**
     * 销售方开户行及账号
     */
    @TableField("vat_invoice_seller_bank_account")
    private String vatInvoiceSellerBankAccount;

    @Override
    public Serializable pkVal() {
        return this.lid;
    }
}
