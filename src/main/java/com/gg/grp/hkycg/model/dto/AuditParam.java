package com.gg.grp.hkycg.model.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 审核参数
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AuditParam {
    /**
     * 单据编号
     */
    protected String jhbh;

    /**
     * 审核人
     */
    protected String auditor;

    /**
     * 审核意见
     */
    protected String opinion;

    /**
     * 是否终审
     */
    protected Boolean auditFlag;

    /**
     * 金额
     */
    protected BigDecimal money;
} 