package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 招标申请保存DTO
 */
@Data
public class CgzbsqSaveDTO {

    /**
     * 基础信息
     */
    @Valid
    @NotNull(message = "基础信息不能为空")
    private CgzbsqBaseInfoDTO baseInfo;

    /**
     * 招标申请明细列表
     */
    @Valid
    @JsonProperty("CgzbsqDetails")
    private List<CgzbsqDetailDTO> CgzbsqDetails;
} 