package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购验收基础信息DTO
 */
@Data
public class CgysBaseInfoDTO {

    /**
     * 验收申请编号（主键）
     */
    private String ysbh;

    /**
     * 验收申请名称
     */
    private String ysmc;

    /**
     * 验收申请部门代码
     */
    private String ysbmdm;

    /**
     * 验收申请部门名称
     */
    private String ysbmmc;

    /**
     * 采购申请
     */
    private String cgsq;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 采购计划部门代码
     */
    private String cgjhbmdm;

    /**
     * 采购计划部门名称
     */
    private String cgjhbmmc;

    /**
     * 采购方式代码
     */
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    private String cgfsmc;

    /**
     * 采购申请金额
     */
    private BigDecimal cgsqje;

    /**
     * 待验收金额
     */
    private BigDecimal dysjje;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 验收类型代码
     */
    private String yslxdm;

    /**
     * 验收类型名称
     */
    private String yslxmc;

    /**
     * 验收组织部门代码
     */
    private String yszzbmdm;

    /**
     * 验收组织部门名称
     */
    private String yszzbmmc;

    /**
     * 验收参与部门代码
     */
    private String yscybmdm;

    /**
     * 验收参与部门名称
     */
    private String yscybmmc;

    /**
     * 验收负责人
     */
    private String ysfzr;

    /**
     * 参与人员
     */
    private String cyry;

    /**
     * 验收地点
     */
    private String ysdd;

    /**
     * 拟验收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date nysrq;

    /**
     * 本次验收金额
     */
    private BigDecimal bcysje;

    /**
     * 备注
     */
    private String bz;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

    /**
     * 状态
     */
    private String zt;
}