package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 审核日志实体类
 * 对应数据表：PUBAUDITLOG
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PUBAUDITLOG")
public class PubAuditLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 日志ID
     */
    @TableField("LOGID")
    private Long logid;

    /**
     * 单据ID
     */
    @TableField("BILLID")
    private String billid;

    /**
     * 单据名称
     */
    @TableField("BILLNAME")
    private String billname;

    /**
     * 流程代码
     */
    @TableField("FLOWCODE")
    private String flowcode;

    /**
     * 流程名称
     */
    @TableField("FLOWNAME")
    private String flowname;

    /**
     * 模块名称
     */
    @TableField("MODNAME")
    private String modname;

    /**
     * 业务名称
     */
    @TableField("BIZNAME")
    private String bizname;

    /**
     * 节点序号
     */
    @TableField("NODESEQ")
    private Integer nodeseq;

    /**
     * 节点名称
     */
    @TableField("NODENAME")
    private String nodename;

    /**
     * 审核人ID
     */
    @TableField("AUDITORID")
    private String auditorid;

    /**
     * 审核人
     */
    @TableField("AUDITOR")
    private String auditor;

    /**
     * 证明人ID
     */
    @TableField("CERTIGIERID")
    private Integer certigierid;

    /**
     * 证明人
     */
    @TableField("CERTIGIER")
    private String certigier;

    /**
     * 审核时间
     */
    @TableField("ADATETIME")
    private String adatetime;

    /**
     * 金额
     */
    @TableField("AMT")
    private BigDecimal amt;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 审核类型
     */
    @TableField("ATYPE")
    private String atype;

    /**
     * 日志序号（自增主键）
     */
    @TableId(value = "LOGSEQ", type = IdType.AUTO)
    private Integer logseq;

    /**
     * 服务器时间
     */
    @TableField("ServDateTime")
    private String servDateTime;

    /**
     * 计算机名称
     */
    @TableField("ComputerName")
    private String computerName;
} 