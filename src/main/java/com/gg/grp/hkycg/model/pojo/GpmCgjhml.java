package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 采购计划目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGJHML")
public class GpmCgjhml {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("CZKJND")
    private String czkjnd; // 财政年度

    @TableId("JHBH")
    private String jhbh; // 计划编号

    @TableField("CGRQ")
    private String cgrq; // 采购日期

    @TableField("YSDWDM")
    private String ysdwdm; // 验收单位代码

    @TableField("YSDWMC")
    private String ysdwmc; // 验收单位名称

    @TableField("BMDM")
    private String bmdm; // 部门代码

    @TableField("BMMC")
    private String bmmc; // 部门名称

    @TableField(value = "NCGRQ",jdbcType = JdbcType.DATE)
    private Date nCgrq;//拟采购日期

    @TableField("BNJHJE")
    private BigDecimal bnJhje;//本年计划金额

    @TableField("YZXJE")
    private BigDecimal yzxJe;//已执行金额

    @TableField("SQYJJLY")
    private String sqyjjly;//申请依据及理由

    @TableField("QTBCJSM")
    private String qtbcjsm;//其他补充及说明

    @TableField("LXR")
    private String lxr; // 联系人

    @TableField("LXDH")
    private String lxdh; // 联系电话

    @TableField("ZT")
    private BigDecimal zt; // 状态

    @TableField("STAMP")
    private BigDecimal stamp; // 时间戳

    @TableField("OAZT")
    private String oazt; // OA状态

    @TableField("JHJE")
    private BigDecimal jhje; // 计划金额

    @TableField("LRR_ID")
    private BigDecimal lrrId; // 录入人ID

    @TableField("LRR")
    private String lrr; // 录入人

    @TableField("LR_RQ")
    private String lrRq; // 录入日期

    @TableField("SSR_ID")
    private BigDecimal ssrId; // 审核人ID

    @TableField("SSR")
    private String ssr; // 审核人

    @TableField("SS_RQ")
    private String ssRq; // 审核日期

    @TableField("PFR_ID")
    private BigDecimal pfrId; // 批复人ID

    @TableField("PFR")
    private String pfr; // 批复人

    @TableField("PF_RQ")
    private String pfRq; // 批复日期

    @TableField("ASHR_ID")
    private Integer ashrId; // 审核人ID(OA)

    @TableField("ASHR")
    private String ashr; // 审核人(OA)

    @TableField("ASH_RQ")
    private String ashRq; // 审核日期(OA)

    @TableField("ASHJD")
    private Integer ashjd; // 审核节点

    @TableField("ASHZT")
    private String ashzt; // 审核状态

    @TableField("AXSHJD")
    private Integer axshjd; // 下一步审核节点

    @TableField("ASFTH")
    private String asfth; // 是否退回

    @TableField("ATHXX")
    private String athxx; // 退回信息

    @TableField("FLOWCODE")
    private String flowcode; // 流程代码

    @TableField("ZXZT")
    private String zxzt; // 执行状态

    @TableField("JHJDXID")
    private String jhjdxid; // 计划节点ID

    @TableField("JHJDXBH")
    private String jhjdxbh; // 计划节点编号

    @TableField("JHJDXMC")
    private String jhjdxmc; // 计划节点名称

    @TableField("CGBH")
    private String cgbh; // 采购编号

    @TableField("SFZJHT")
    private String sfzjht; // 是否追加合同

    @TableField("JHMC")
    private String jhmc; // 计划名称

    @TableField("SFTHZB")
    private String sfthzb; // 是否退回重报

    @TableField("JHDKFlag")
    private String jhdkFlag; // 计划打款标志

    @TableField("FJNUM")
    private Integer fjnum; // 附件数量

    @TableField("isclose")
    private String isclose; // 是否关闭

    @TableField("closeuser")
    private String closeuser; // 关闭人

    @TableField("closedate")
    private Date closedate; // 关闭日期

    /**
     * 采购类型代码
     */
    @TableField("CGLXDM")
    private String cglxdm;

    /**
     * 采购类型名称
     */
    @TableField("CGLXMC")
    private String cglxmc;

    @TableField("JBR")
    private String jbr;//经办人

    /**
     * 项目
     */
    @TableField("XMMC")
    private String xmmc;

    @TableField("CGFSDM")
    private String cgfsdm;
    @TableField("CGFSMC")
    private String cgfsmc;
//    专家来源代码
    @TableField("ZJLYDM")
    private String zjlydm;
//    专家来源名称
    @TableField("ZJLY")
    private String zjly;
    @TableField("HJJE")
    private BigDecimal hjje;
    @TableField("SCFJ")
    private BigDecimal scfj;
    @TableField("BZ")
    private BigDecimal bz;
    @TableField("YSR_ID")
    private BigDecimal ysrid;

    @TableField("YSR")
    private String ysr;

    @TableField("YS_RQ")
    private String ysrrq;

    @TableField("IsHaveHT")
    private BigDecimal isHaveHT;

    @TableField("PFBH")
    private BigDecimal pfbh;

    @TableField("XMQD_RQ")
    private BigDecimal xmqdrq;

    @TableField("CGLYDM")
    private BigDecimal cglydm;
    @TableField("CGLYMC")
    private BigDecimal cglymc;
    @TableField("CGZJLYFSDM")
    private BigDecimal cgzjlyfsdm;
    @TableField("CGZJLYFSMC")
    private BigDecimal cgzjlyfsmc;

    @TableField("ZJJGDM")
    private BigDecimal zjjgdm;
    @TableField("ZJJGMC")
    private BigDecimal zjjgmc;

} 