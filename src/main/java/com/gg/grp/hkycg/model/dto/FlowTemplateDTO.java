package com.gg.grp.hkycg.model.dto;

import lombok.Data;

@Data
public class FlowTemplateDTO {
    /**
     * 单据类型id
     */
    private Integer djlxid;
    /**
     * 节点代码
     */
    private Integer jddm;
    /**
     * 节点名称
     */
    private String jdmc;
    /**
     * 节点审核条件
     */
    private String jdshtj;
    /**
     * 节点审核条件通过
     */
    private Boolean jdshtjFit;
    /**
     * 是否动态查找审核人
     */
    private Boolean dynamicAcquisition;
    /**
     * 审核人代码
     */
    private String shrdm;
    /**
     * 审核人姓名
     */
    private String shrxm;
    /**
     * 审核条件
     */
    private String shtj;
    /**
     * 节点审核条件通过
     */
    private Boolean shtjFit;
}
