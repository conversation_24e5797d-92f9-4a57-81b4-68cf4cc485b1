package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gg.grp.hkycg.model.dto.CgjhBudgetIndexDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购申请保存响应VO
 */
@Data
public class CgsqSaveResponseVO {

    /**
     * 基础信息
     */
    private CgsqBaseInfoResponseVO baseInfo;

    /**
     * 采购申请计划明细列表（包含预算指标）
     */
    @JsonProperty("CgsqDetails")
    private List<CgsqJhmxWithBudgetResponseVO> CgsqDetails;

    /**
     * 基础信息响应VO
     */
    @Data
    public static class CgsqBaseInfoResponseVO {
        /**
         * 申请编号（主键）
         */
        private String sqbh;

        /**
         * 单据编号（外键）
         */
        private String jhbh;

        /**
         * 申请名称
         */
        private String sqmc;

        /**
         * 采购类型代码
         */
        private String cglxdm;

        /**
         * 采购类型名称
         */
        private String cglxmc;

        /**
         * 经办人
         */
        private String jbr;

        /**
         * 申请部门代码
         */
        private String sqbmdm;

        /**
         * 申请部门名称
         */
        private String sqbmmc;

        /**
         * 计划部门代码
         */
        private String bmdm;

        /**
         * 计划部门名称
         */
        private String bmmc;

        /**
         * 项目名称
         */
        private String xmmc;

        /**
         * 采购方式代码
         */
        private String cgfsdm;

        /**
         * 采购方式名称
         */
        private String cgfsmc;

        /**
         * 采购组织方式代码
         */
        private String zzfsdm;

        /**
         * 采购组织方式名称
         */
        private String zzfsmc;

        /**
         * 专家来源方式代码
         */
        private String cgzjlyfsdm;

        /**
         * 专家来源方式名称
         */
        private String cgzjlyfsmc;

        /**
         * 代理中介机构代码
         */
        private String zjjgdm;

        /**
         * 代理中介机构名称
         */
        private String zjjgmc;

        /**
         * 政府采购方式代码
         */
        private String zfcgfsdm;

        /**
         * 政府采购方式名称
         */
        private String zfcgfsmc;

        /**
         * 预算审计方式代码
         */
        private String yssjfsdm;

        /**
         * 预算审计方式名称
         */
        private String yssjfsmc;

        /**
         * 拟采购日期
         */
        private String ncgrq;

        /**
         * 政府采购编号
         */
        private String zfcgbh;

        /**
         * 申请金额
         */
        private BigDecimal sqje;

        /**
         * 临时采购计划
         */
        private boolean lscgjh;

        /**
         * 三重一大事项立项审批
         */
        private boolean scydsxlxsp;

        /**
         * 上级部门立项审批
         */
        private boolean sjbmlxsp;

        /**
         * 是否政府采购
         */
        private boolean szfcg;

        /**
         * 纪委部门立项审批
         */
        private boolean jwbmlxsp;

        /**
         * 申请依据及理由
         */
        private String sqyjjly;

        /**
         * 采购理由代码
         */
        private String cglydm;

        /**
         * 采购理由名称
         */
        private String cglymc;

        /**
         * 是否采购计划引入标志位
         */
        private Boolean sfcgjhyr;

        /**
         * 公司代码
         */
        private String gsdm;

        /**
         * 会计年度
         */
        private String kjnd;

        /**
         * 状态
         */
        private String zt;

        /**
         * 状态名称
         */
        private String ztmc;

        /**
         * 创建日期
         */
        private String cjrq;

        /**
         * 创建人代码
         */
        private String cjrdm;

        /**
         * 创建人名称
         */
        private String cjrmc;
    }

    /**
     * 采购申请计划明细与预算响应VO
     */
    @Data
    public static class CgsqJhmxWithBudgetResponseVO {
        /**
         * 计划明细信息
         */
        private CgsqJhmxResponseVO jhmx;

        /**
         * 预算指标信息
         */
        private CgjhBudgetIndexDTO budgetIndex;
    }

    /**
     * 采购申请计划明细响应VO
     */
    @Data
    public static class CgsqJhmxResponseVO {
        /**
         * 明细编号
         */
        private String sqmxxh;

        /**
         * 物品(服务)名称
         */
        private String wpmc;

        /**
         * 归口管理部门代码
         */
        private String gkglbmdm;

        /**
         * 归口管理部门名称
         */
        private String gkglbmmc;

        /**
         * 采购品目代码
         */
        private String cgmldm;

        /**
         * 采购品目名称
         */
        private String cgmlmc;

        /**
         * 计划名称
         */
        private String jhmc;

        /**
         * 本次采购数量
         */
        private Integer bccgsl;

        /**
         * 本次采购金额
         */
        private BigDecimal bccgje;

        /**
         * 单价
         */
        private Integer dj;

        /**
         * 规格型号
         */
        private String ggxh;

        /**
         * 计量单位
         */
        private String jldw;

        /**
         * 计划金额
         */
        private BigDecimal jhje;

        /**
         * 已用金额
         */
        private BigDecimal yyje;

        /**
         * 可用金额
         */
        private BigDecimal kyje;

        /**
         * 计划审定数量
         */
        private Integer jhsdsl;

        /**
         * 已用数量
         */
        private Integer yysl;

        /**
         * 可申请数量
         */
        private Integer ksqsl;

        /**
         * 项目名称
         */
        private String xmmc;

        /**
         * 政府采购
         */
        private Boolean zfcg;

        /**
         * 进口产品
         */
        private Boolean jkcp;

        /**
         * 备注
         */
        private String bz;
    }
} 