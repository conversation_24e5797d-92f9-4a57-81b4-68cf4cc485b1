package com.gg.grp.hkycg.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购结果审核日志VO
 */
@Data
public class CgjgAuditLogVO {
    
    /**
     * 结果登记编号
     */
    private String jgdjbh;
    
    /**
     * 单据类型
     */
    private String billType;
    
    /**
     * 审核日志列表
     */
    private List<AuditLogItem> logList;
    
    /**
     * 审核节点标题列表
     */
    private List<LogTitleItem> logTitleList;
    
    /**
     * 审核日志项
     */
    @Data
    public static class AuditLogItem {
        /**
         * 公司代码
         */
        private String gsdm;
        
        /**
         * 会计年度
         */
        private String kjnd;
        
        /**
         * 日志ID
         */
        private Long logid;
        
        /**
         * 单据ID
         */
        private String billid;
        
        /**
         * 单据名称
         */
        private String billname;
        
        /**
         * 流程代码
         */
        private String flowcode;
        
        /**
         * 流程名称
         */
        private String flowname;
        
        /**
         * 模块名称
         */
        private String modname;
        
        /**
         * 业务名称
         */
        private String bizname;
        
        /**
         * 节点序号
         */
        private Integer nodeseq;
        
        /**
         * 节点名称
         */
        private String nodename;
        
        /**
         * 审核人ID
         */
        private String auditorid;
        
        /**
         * 审核人姓名
         */
        private String auditor;
        
        /**
         * 证明人ID
         */
        private Integer certigierid;
        
        /**
         * 证明人姓名
         */
        private String certigier;
        
        /**
         * 审核时间
         */
        private String adatetime;
        
        /**
         * 金额
         */
        private BigDecimal amt;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 审核类型
         */
        private String atype;
        
        /**
         * 日志序号
         */
        private Integer logseq;
        
        /**
         * 服务器时间
         */
        private String servDateTime;
        
        /**
         * 计算机名称
         */
        private String computerName;
    }

    /**
     * 审核节点标题信息
     */
    @Data
    public static class LogTitleItem {
        /**
         * 公司代码
         */
        private String gsdm;
        
        /**
         * 会计年度
         */
        private String kjnd;
        
        /**
         * 节点序号
         */
        private Integer nodeseq;
        
        /**
         * 节点名称
         */
        private String nodename;
        
        /**
         * 是否已审核（0-未审核，1-已审核）
         */
        private String isaudit;
        
        /**
         * 审核状态描述
         */
        private String auditStatus;
        
        /**
         * 审核时间
         */
        private String auditTime;
    }
}
