package com.gg.grp.hkycg.model.dto;

import lombok.Data;

/**
 * 采购结果分页查询DTO
 */
@Data
public class CgjgPageQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 查询条件（结果登记名称、编号等模糊搜索）
     */
    private String condition;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 申请状态
     */
    private String zt;
    
    /**
     * 开始日期 (格式: yyyy-MM-dd)
     */
    private String startDate;
    
    /**
     * 结束日期 (格式: yyyy-MM-dd)
     */
    private String endDate;
    
    /**
     * 采购部门代码
     */
    private String cgbmdm;
    
    /**
     * 采购方式代码
     */
    private String cgfsdm;
    
        /**
     * 是否采购申请引入
     */
    private Boolean sfcgsqyr;

    /**
     * 是否采购招标引入
     */
    private Boolean sfcgzbyr;
}
