package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购验收列表VO
 */
@Data
public class CgysListVO {

    /**
     * 总记录数
     */
    @JsonIgnore
    private Long totalCount;

    /**
     * 验收申请编号（主键）
     */
    private String ysbh;

    /**
     * 验收申请名称
     */
    private String ysmc;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

    /**
     * 状态
     */
    private String zt;

    /**
     * 状态名称
     */
    private String ztmc;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 验收申请部门代码
     */
    private String ysbmdm;

    /**
     * 验收申请部门名称
     */
    private String ysbmmc;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 验收负责人
     */
    private String ysfzr;

    /**
     * 采购方式代码
     */
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    private String cgfsmc;

    /**
     * 验收组织部门代码
     */
    private String yszzbmdm;

    /**
     * 验收组织部门名称
     */
    private String yszzbmmc;

    /**
     * 采购品目代码
     */
    private String cgmldm;

    /**
     * 采购品目名称
     */
    private String cgmlmc;

    /**
     * 归口管理部门代码
     */
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    private String gkglbmmc;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 政府采购
     */
    private Boolean zfcg;

    /**
     * 进口产品
     */
    private Boolean jkcp;

    /**
     * 是否采购结果引入
     */
    private Boolean sfcgjgyr;

    /**
     * 采购结果编号
     */
    private String jgdjbh;

    /**
     * 采购结果名称
     */
    private String jgdjmc;

    /**
     * 采购申请金额
     */
    private BigDecimal cgsqje;

    /**
     * 待验收金额
     */
    private BigDecimal dysjje;

    /**
     * 本次验收金额
     */
    private BigDecimal bcysje;

    /**
     * 备注
     */
    private String bz;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 拟验收时间
     */
    private String nysrq;

}