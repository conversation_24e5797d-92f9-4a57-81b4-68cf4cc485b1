package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 调整单内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GBI_TZDNR")
public class GbiTzdnr {

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 调整单ID
     */
    @TableField("TZDID")
    private Integer tzdid;

    /**
     * 调整单分录号
     */
    @TableField("TZDFLH")
    private Integer tzdflh;

    /**
     * 指标ID
     */
    @TableField("ZBID")
    private Integer zbid;

    /**
     * 摘要
     */
    @TableField("ZY")
    private String zy;

    /**
     * 金额
     */
    @TableField("JE")
    private BigDecimal je;

    /**
     * 收支属性
     */
    @TableField("SZSX")
    private Integer szsx;

    /**
     * 指标来源
     */
    @TableField("ZBLY")
    private String zbly;
} 