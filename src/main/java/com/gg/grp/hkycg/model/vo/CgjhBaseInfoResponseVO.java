package com.gg.grp.hkycg.model.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购计划基础信息响应VO
 */
@Data
public class CgjhBaseInfoResponseVO {

    // =============== 基础信息 ===============

    /**
     * 计划编号
     */
    private String jhbh;
    
    /**
     * 计划名称
     */
    private String jhmc;

    /**
     * 采购类型代码
     */
    private String cglxdm;

    /**
     * 采购类型名称
     */
    private String cglxmc;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 计划部门代码
     */
    private String bmdm;

    /**
     * 计划部门名称
     */
    private String bmmc;

    /**
     * 项目
     */
    private String xmmc;

    /**
     * 拟采购日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date nCgrq;

    /**
     * 计划金额
     */
    private BigDecimal jhje;

    /**
     * 本年计划金额
     */
    private BigDecimal bnJhje;

    /**
     * 已执行金额
     */
    private BigDecimal yzxJe;

    /**
     * 申请依据及理由
     */
    private String sqyjjly;

    /**
     * 其他补充及说明
     */
    private String qtbcjsm;

    // =============== 系统字段 ===============

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 年度
     */
    private String kjnd;

    /**
     * 验收单位代码
     */
    private String ysdwdm;

    /**
     * 验收单位名称
     */
    private String ysdwmc;

    /**
     * 联系人
     */
    private String lxr;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 状态
     */
    private BigDecimal zt;

    /**
     * 状态名称
     */
    private String ztmc;

    /**
     * OA状态
     */
    private String oazt;

    /**
     * 录入人ID
     */
    private BigDecimal lrrId;

    /**
     * 录入人
     */
    private String lrr;

    /**
     * 录入日期
     */
    private String lrRq;

    /**
     * 采购日期
     */
    private String cgrq;

    /**
     * 时间戳
     */
    private BigDecimal stamp;
} 