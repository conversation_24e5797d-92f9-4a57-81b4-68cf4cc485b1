package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 附件信息VO
 */
@Data
public class AttachmentVO {
    
    /**
     * 附件ID
     */
    @JsonProperty("fjid")
    private String fjid;
    
    /**
     * 附件名称(原始文件名)
     */
    @JsonProperty("fjmc")
    private String fjmc;
    
    /**
     * 附件类型(MIME类型)
     */
    @JsonProperty("fjlx")
    private String fjlx;
    
    /**
     * 附件大小(KB)
     */
    @JsonProperty("fjdx")
    private BigDecimal fjdx;
    
    /**
     * 下载地址
     */
    @JsonProperty("downloadUrl")
    private String downloadUrl;
    
    /**
     * 预览地址
     */
    @JsonProperty("previewUrl")
    private String previewUrl;
    
    /**
     * 录入人
     */
    @JsonProperty("lrr")
    private String lrr;
    
    /**
     * 录入日期
     */
    @JsonProperty("lrRq")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lrRq;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String djlx;
    
    /**
     * 单据号
     */
    @JsonProperty("djh")
    private String djh;
    
    /**
     * 是否明细 "0"-否，"1"-是
     */
    @JsonProperty("sfmx")
    private String sfmx;
} 