package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "accessory_file")
public class AccessoryFile {
    /**
     * 物理主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

    /**
     * 逻辑主键
     */
    private String lid;

    /**
     * 附件类型
     */
    private Integer type;

    /**
     * 实际文件名称(上传时)
     */
    @TableField(value = "practical_file_name")
    private String practicalFileName;

    /**
     * 储存文件名称(唯一化名称)
     */
    @TableField(value = "re_file_name")
    private String reFileName;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    private Double fileSize;

    /**
     * 关联单据类型
     */
    @TableField(value = "bill_type")
    private Integer billType;

    /**
     * 关联单据
     */
    private String bid;

    /**
     * 上传人
     */
    @TableField(value = "upload_account")
    private String uploadAccount;

    /**
     * 删除
     */
    private Boolean del;

    /**
     * 删除人
     */
    @TableField(value = "del_account")
    private String delAccount;
}
