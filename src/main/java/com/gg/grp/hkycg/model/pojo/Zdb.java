package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 字典表实体类
 */
@Data
@TableName("GPM_ZDB")
public class Zdb {

    /**
     * 字段代码
     */
    @TableField(value = "ZDDM")
    private String zddm;

    /**
     * 字段名称
     */
    @TableField(value = "ZDMC")
    private String zdmc;

    /**
     * 字段类型
     */
    @TableField(value = "ZDLX")
    private String zdlx;
}
