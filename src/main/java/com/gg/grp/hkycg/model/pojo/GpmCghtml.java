package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购合同目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGHTML")
public class GpmCghtml {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("HTBH")
    private BigDecimal htbh; // 合同编号

    @TableField("HTDM")
    private String htdm; // 合同代码

    @TableField("HTMC")
    private String htmc; // 合同名称

    @TableField("HTID")
    private String htid; // 合同ID

    @TableField("HTRQ")
    private String htrq; // 合同日期

    @TableField("YHTDM")
    private String yhtdm; // 原合同代码

    @TableField("YHTMC")
    private String yhtmc; // 原合同名称

    @TableField("XMID")
    private String xmid; // 项目ID

    @TableField("XMDM")
    private String xmdm; // 项目代码

    @TableField("CGFSDM")
    private String cgfsdm; // 采购方式代码

    @TableField("CGFSMC")
    private String cgfsmc; // 采购方式名称

    @TableField("YSDWDM")
    private String ysdwdm; // 验收单位代码

    @TableField("YSDWMC")
    private String ysdwmc; // 验收单位名称

    @TableField("BMDM")
    private String bmdm; // 部门代码

    @TableField("BMMC")
    private String bmmc; // 部门名称

    @TableField("ZJJGDM")
    private String zjjgdm; // 中介机构代码

    @TableField("ZJJGMC")
    private String zjjgmc; // 中介机构名称

    @TableField("GYS")
    private String gys; // 供应商

    @TableField("BD")
    private String bd; // 付款单位

    @TableField("SKRDM")
    private String skrdm; // 收款人代码

    @TableField("SKRMC")
    private String skrmc; // 收款人名称

    @TableField("BZ")
    private String bz; // 备注

    @TableField("JE")
    private BigDecimal je; // 金额

    @TableField("ZBJE")
    private BigDecimal zbje; // 招标金额

    @TableField("WJJE")
    private BigDecimal wjje; // 未决金额

    @TableField("LRR_ID")
    private BigDecimal lrrId; // 录入人ID

    @TableField("LRR")
    private String lrr; // 录入人

    @TableField("LR_RQ")
    private String lrRq; // 录入日期

    @TableField("SHR_ID")
    private BigDecimal shrId; // 审核人ID

    @TableField("SHR")
    private String shr; // 审核人

    @TableField("SH_RQ")
    private String shRq; // 审核日期

    @TableField("HTBAR_ID")
    private BigDecimal htbarId; // 备案人ID

    @TableField("HTBAR")
    private String htbar; // 备案人

    @TableField("HTBA_RQ")
    private String htbaRq; // 备案日期

    @TableField("PFr_id")
    private BigDecimal pfrId; // 批复人ID

    @TableField("PFR")
    private String pfr; // 批复人

    @TableField("PF_rq")
    private String pfRq; // 批复日期

    @TableField("HTYSR_ID")
    private BigDecimal htysrId; // 预算人ID

    @TableField("HTYSR")
    private String htysr; // 预算人

    @TableField("HTYS_RQ")
    private String htysRq; // 预算日期

    @TableField("HTSJR_ID")
    private BigDecimal htsjrId; // 收件人ID

    @TableField("HTSJR")
    private String htsjr; // 收件人

    @TableField("HTSJ_RQ")
    private String htsjRq; // 收件日期

    @TableField("SCR_ID")
    private BigDecimal scrId; // 上传人ID

    @TableField("SCR")
    private String scr; // 上传人

    @TableField("SC_RQ")
    private String scRq; // 上传日期

    @TableField("ZT")
    private String zt; // 状态

    @TableField("SCFJ")
    private String scfj; // 附件

    @TableField("YSYJ")
    private String ysyj; // 验收意见

    @TableField("GYS_DM")
    private String gysDm; // 供应商代码

    @TableField("HTDM_HT")
    private String htdmHt; // 合同代码

    @TableField("JHMC")
    private String jhmc; // 计划名称

    @TableField("ZBDWLXR")
    private String zbdwlxr; // 中标单位联系人

    @TableField("ZBDWTel")
    private String zbdwTel; // 中标单位电话
} 