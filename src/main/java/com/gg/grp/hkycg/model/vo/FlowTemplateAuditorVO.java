package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FlowTemplateAuditorVO implements TableVO {
    /**
     * 是否动态获取审核人
     */
    @JsonProperty("isDynamicAcquisition")
    private Boolean dynamicAcquisition;
    /**
     * 审核人姓名
     */
    private String shrxm;
    /**
     * 审核人代码
     */
    private String shrdm;
    /**
     * 审核条件
     */
    private String shtj;

    /**
     * 和另一个审核人的工号和姓名是否相等
     * @param otherAuditor 另一个审核人
     * @return 和另一个审核人的工号和姓名是否相等
     */
    public Boolean equalsName(FlowTemplateAuditorVO otherAuditor){
        return this.shrdm.equals(otherAuditor.shrdm)
                && this.shrxm.equals(otherAuditor.shrxm)
                && this.shtj.equals(otherAuditor.shtj);
    }
}
