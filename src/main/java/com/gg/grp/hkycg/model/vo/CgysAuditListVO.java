package com.gg.grp.hkycg.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购验收审核列表VO
 */
@Data
public class CgysAuditListVO {

    /**
     * 总记录数
     */
    private Long totalCount;

    /**
     * 验收申请编号
     */
    private String ysbh;

    /**
     * 验收申请名称
     */
    private String ysmc;

    /**
     * 验收申请部门名称
     */
    private String ysbmmc;

    /**
     * 采购申请
     */
    private String cgsq;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 采购方式名称
     */
    private String cgfsmc;

    /**
     * 本次验收金额
     */
    private BigDecimal bcysje;

    /**
     * 状态
     */
    private String zt;

    /**
     * 状态名称
     */
    private String ztmc;

    /**
     * 拟验收日期
     */
    private Date nysrq;

    /**
     * 提交时间
     */
    private Date tjsj;

    /**
     * 审核节点
     */
    private String shjd;

    /**
     * 当前审核人
     */
    private String dqshr;
}