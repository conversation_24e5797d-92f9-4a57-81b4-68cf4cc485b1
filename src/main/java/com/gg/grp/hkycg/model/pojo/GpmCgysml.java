package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购验收申请主表实体类
 * 对应数据表：GPM_CGYSML
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("GPM_CGYSML")
public class GpmCgysml implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 验收申请编号
     */
    @TableId("YSBH")
    private String ysbh;

    /**
     * 验收申请名称
     */
    @TableField("YSMC")
    private String ysmc;

    /**
     * 验收申请部门代码
     */
    @TableField("YSBMDM")
    private String ysbmdm;

    /**
     * 验收申请部门名称
     */
    @TableField("YSBMMC")
    private String ysbmmc;

    /**
     * 采购申请
     */
    @TableField("CGSQ")
    private String cgsq;

    /**
     * 经办人
     */
    @TableField("JBR")
    private String jbr;

    /**
     * 采购计划部门代码
     */
    @TableField("CGJHBMDM")
    private String cgjhbmdm;

    /**
     * 采购计划部门名称
     */
    @TableField("CGJHBMMC")
    private String cgjhbmmc;

    /**
     * 采购方式代码
     */
    @TableField("CGFSDM")
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    @TableField("CGFSMC")
    private String cgfsmc;

    /**
     * 采购申请金额
     */
    @TableField("CGSQJE")
    private BigDecimal cgsqje;

    /**
     * 待验收金额
     */
    @TableField("DYSJJE")
    private BigDecimal dysjje;

    /**
     * 项目名称
     */
    @TableField("XMMC")
    private String xmmc;

    /**
     * 验收类型代码
     */
    @TableField("YSLXDM")
    private String yslxdm;

    /**
     * 验收类型名称
     */
    @TableField("YSLXMC")
    private String yslxmc;

    /**
     * 验收组织部门代码
     */
    @TableField("YSZZBMDM")
    private String yszzbmdm;

    /**
     * 验收组织部门名称
     */
    @TableField("YSZZBMMC")
    private String yszzbmmc;

    /**
     * 验收参与部门代码
     */
    @TableField("YSCYBMDM")
    private String yscybmdm;

    /**
     * 验收参与部门名称
     */
    @TableField("YSCYBMMC")
    private String yscybmmc;

    /**
     * 验收负责人
     */
    @TableField("YSFZR")
    private String ysfzr;

    /**
     * 参与人员
     */
    @TableField("CYRY")
    private String cyry;

    /**
     * 验收地点
     */
    @TableField("YSDD")
    private String ysdd;

    /**
     * 拟验收日期
     */
    @TableField("NYSRQ")
    private Date nysrq;

    /**
     * 本次验收金额
     */
    @TableField("BCYSJE")
    private BigDecimal bcysje;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private String updateTime;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 更新人
     */
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 状态
     */
    @TableField("ZT")
    private String zt;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 创建人代码
     */
    @TableField("CREATE_USERDM")
    private String createUserDm;

    /**
     * 更新人代码
     */
    @TableField("UPDATE_USERDM")
    private String updateUserDm;
} 