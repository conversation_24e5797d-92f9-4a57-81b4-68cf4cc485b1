package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购品目表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("HBG_ZFCGML")
public class HbgZfcgml {

    @TableField("CGMLID")
    private String cgmlid;

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("CGMLDM")
    private String cgmldm; // 采购品目代码

    @TableField("CGMLMC")
    private String cgmlmc; // 采购品目名称

    @TableField("ZJM")
    private String zjm; // 助记码

    @TableField("SYZT")
    private String syzt; // 使用状态

    @TableField("SFMX")
    private String sfmx; // 是否明细

    @TableField("SJDM")
    private String sjdm; // 上级代码

    @TableField("DMJC")
    private Short dmjc; // 级次

    @TableField("SFJZCG")
    private Integer sfjzcg; // 是否集中采购
} 