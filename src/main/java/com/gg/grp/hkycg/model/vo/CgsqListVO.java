package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购申请列表VO
 */
@Data
public class CgsqListVO {
    
    /**
     * 申请编号（主键）
     */
    private String sqbh;
    
    /**
     * 申请名称
     */
    private String sqmc;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 申请状态
     */
    private String zt;
    
    /**
     * 申请状态名称
     */
    private String ztmc;
    
    /**
     * 采购类型代码
     */
    private String cglxdm;
    
    /**
     * 采购类型名称
     */
    private String cglxmc;
    
    /**
     * 创建日期
     */
    private String cjrq;

    /**
     * 申请部门代码
     */
    private String sqbmdm;
    
    /**
     * 申请部门名称
     */
    private String sqbmmc;
    
    /**
     * 申请金额
     */
    private BigDecimal sqje;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 创建人
     */
    private String cjrmc;

    @JsonIgnore
    private Integer totalCount;

    private String ncgrq;

} 