package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("GPM_ROLEUSER")
public class GpmRoleUser {
    @TableField("GSDM")
    private String gsdm;

    @TableField("KJND")
    private String kjnd;

    /**
     * 角色代码
     */
    @TableField("ROLECODE")
    private String rolecode;

    /**
     * 职员代码
     */
    @TableField("ZYDM")
    private String zydm;

    /**
     * 授权人
     */
    @TableField("SQREN")
    private String sqren;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     *
     */
    @TableField("JZRQ")
    private String jzrq;
}
