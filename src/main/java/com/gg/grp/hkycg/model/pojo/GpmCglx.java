package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 采购类型实体类
 * 对应表：GPM_CGLX
 */
@Data
@TableName("GPM_CGLX")
public class GpmCglx {

    @TableField("CGLXID")
    private String cglxid;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 采购类型代码
     */
    @TableField("CGLXDM")
    private String cglxdm;

    /**
     * 采购类型名称
     */
    @TableField("CGLXMC")
    private String cglxmc;

    /**
     * 使用状态
     */
    @TableField("SYZT")
    private String syzt;

    /**
     * 助记码
     */
    @TableField("ZJM")
    private String zjm;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 排序号
     */
    @TableField("PXH")
    private Integer pxh;

    /**
     * 级次
     */
    @TableField("JC")
    private String jc;
} 