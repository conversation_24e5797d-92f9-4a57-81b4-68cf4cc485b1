package com.gg.grp.hkycg.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购结果保存响应VO
 */
@Data
public class CgjgSaveResponseVO {

    /**
     * 基础信息
     */
    private CgjgBaseInfoResponseVO baseInfo;

    /**
     * 采购结果明细
     */
    private List<CgjgDetailResponseVO> cgjgDetails;

    /**
     * 基础信息响应VO
     */
    @Data
    public static class CgjgBaseInfoResponseVO {
        /**
         * 结果登记编号（主键）
         */
        private String jgdjbh;

        /**
         * 结果登记名称
         */
        private String jgdjmc;

        /**
         * 采购部门代码
         */
        private String cgbmdm;

        /**
         * 采购部门名称
         */
        private String cgbmmc;

        /**
         * 采购方式代码
         */
        private String cgfsdm;

        /**
         * 采购方式名称
         */
        private String cgfsmc;

        /**
         * 中介机构代码
         */
        private String zjjgdm;

        /**
         * 中介机构名称
         */
        private String zjjgmc;

        /**
         * 经办人
         */
        private String jbr;

        /**
         * 招标负责人
         */
        private String zbfzr;

        /**
         * 采购申请金额
         */
        private BigDecimal cgsqje;

        /**
         * 预算金额
         */
        private BigDecimal yyje;

        /**
         * 可用金额
         */
        private BigDecimal kyje;

        /**
         * 结果登记金额
         */
        private BigDecimal jgdjje;

        /**
         * 采购理由
         */
        private String cgly;

        /**
         * 备注
         */
        private String bz;

        /**
         * 创建时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String createTime;

        /**
         * 修改时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String updateTime;

        /**
         * 创建用户
         */
        private String createUser;

        /**
         * 更新用户
         */
        private String updateUser;

        /**
         * 状态
         */
        private String zt;

        /**
         * 状态名称
         */
        private String ztmc;

        /**
         * 公司代码
         */
        private String gsdm;

        /**
         * 会计年度
         */
        private String kjnd;
    }

    /**
     * 采购结果明细响应VO
     */
    @Data
    public static class CgjgDetailResponseVO {
        /**
         * 结果内容编号（主键）
         */
        private String jgnrbh;

        /**
         * 结果登记编号（外键）
         */
        private String jgdjbh;

        /**
         * 是否采购申请引入
         */
        private Boolean sfcgsqyr;

        /**
         * 是否采购招标引入
         */
        private Boolean sfcgzbyr;

        /**
         * 采购申请编号
         */
        private String cgsqbh;

        /**
         * 采购申请名称
         */
        private String cgsqmc;

        /**
         * 采购招标编号
         */
        private String cgzbbh;

        /**
         * 采购招标名称
         */
        private String cgzbmc;

        /**
         * 中标供应商
         */
        private String zbgys;

        /**
         * 中标金额
         */
        private BigDecimal zbje;

        /**
         * 中标时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String zbsj;

        /**
         * 中标价
         */
        private BigDecimal zbj;

        /**
         * 违约金
         */
        private BigDecimal wyj;

        /**
         * 采购目录代码
         */
        private String cgmldm;

        /**
         * 采购目录名称
         */
        private String cgmlmc;

        /**
         * 物品名称
         */
        private String wpmc;

        /**
         * 归口管理部门代码
         */
        private String gkglbmdm;

        /**
         * 归口管理部门名称
         */
        private String gkglbmmc;

        /**
         * 本次采购数量
         */
        private BigDecimal bccgsl;

        /**
         * 本次采购金额
         */
        private BigDecimal bccgje;

        /**
         * 单价
         */
        private BigDecimal dj;

        /**
         * 规格型号
         */
        private String ggxh;

        /**
         * 计量单位
         */
        private String jldw;

        /**
         * 预算金额
         */
        private BigDecimal yyje;

        /**
         * 可用金额
         */
        private BigDecimal kyje;

        /**
         * 预算数量
         */
        private BigDecimal yysl;

        /**
         * 可申请数量
         */
        private BigDecimal ksqsl;

        /**
         * 项目名称
         */
        private String xmmc;

        /**
         * 政府采购
         */
        private Boolean zfcg;

        /**
         * 进口产品
         */
        private Boolean jkcp;

        /**
         * 备注
         */
        private String bz;

        /**
         * 采购招标申请编号
         */
        private String zbsqbh;

        /**
         * 采购招标申请名称
         */
        private String zbsqmc;

        /**
         * 采购申请明细编号
         */
        private String sqmxxh;

        /**
         * 采购招标明细编号
         */
        private String zbmxxh;

    }
}
