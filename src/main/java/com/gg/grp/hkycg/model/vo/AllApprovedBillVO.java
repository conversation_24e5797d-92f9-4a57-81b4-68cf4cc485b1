package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 所有已审核单据响应VO
 */
@Data
public class AllApprovedBillVO {
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 单据编号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 单据名称
     */
    @JsonProperty("djmc")
    private String billName;
    
    /**
     * 单据日期
     */
    @JsonProperty("djrq")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date billDate;
}
