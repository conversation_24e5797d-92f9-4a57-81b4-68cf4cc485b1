package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 待办事项列表项VO
 */
@Data
public class TodoItemVO {
    /**
     * 单据编号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 单据名称
     */
    @JsonProperty("djmc")
    private String billName;
    
    /**
     * 单据类型名称
     */
    @JsonProperty("djlxmc")
    private String billTypeName;
    
    /**
     * 单据状态代码
     */
    @JsonProperty("zt")
    private String statusCode;
    
    /**
     * 单据状态名称
     */
    @JsonProperty("ztmc")
    private String statusName;

    @JsonProperty("djnr")
    private String billContent;
    
    /**
     * 总记录数（用于分页）
     */
    @JsonIgnore
    private Long totalCount;
} 