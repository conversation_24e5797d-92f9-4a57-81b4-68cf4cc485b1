package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("PUB_FLOW_TEMPLATE_TEMPORARY")
public class PubFlowTemplateTemporary {
    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;
    /**
     * 职员代码
     */
    @TableField("ZYDM")
    private String zydm;
    /**
     * 单据类型id
     */
    @TableField("DJLXID")
    private Integer djlxid;
    /**
     * 节点代码
     */
    @TableField("JDDM")
    private Integer jddm;
    /**
     * 节点名称
     */
    @TableField("JDMC")
    private String jdmc;
    /**
     * 节点审核条件
     */
    @TableField("JDSHTJ")
    private String jdshtj;
    /**
     * 是否动态查找审核人
     */
    @TableField("ShiFou_DongTaiChaZhao")
    private Boolean dynamicAcquisition;
    /**
     * 审核人代码
     */
    @TableField("SHRDM")
    private String shrdm;
    /**
     * 审核人姓名
     */
    @TableField("SHRXM")
    private String shrxm;
    /**
     * 审核条件
     */
    @TableField("SHTJ")
    private String shtj;
    /**
     * 审核条件是否合法
     */
    @TableField("SFHF")
    private Boolean legally;
}
