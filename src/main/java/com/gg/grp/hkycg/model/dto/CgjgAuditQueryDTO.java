package com.gg.grp.hkycg.model.dto;

import lombok.Data;

/**
 * 采购结果审核查询DTO
 */
@Data
public class CgjgAuditQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 查询条件（结果登记名称、编号等模糊搜索）
     */
    private String condition;
    
    /**
     * 申请人
     */
    private String applicant;
    
    /**
     * 单据状态
     */
    private Integer billStatus;
    
    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
}
