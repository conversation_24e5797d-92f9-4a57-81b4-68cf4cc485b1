package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购项目招投标目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGXMZTBML")
public class GpmCgxmztbml {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("ZTBBH")
    private BigDecimal ztbbh; // 招投标编号

    @TableField("XMID")
    private String xmid; // 项目ID

    @TableField("XMDM")
    private String xmdm; // 项目代码

    @TableField("XMMC")
    private String xmmc; // 项目名称

    @TableField("YSDWDM")
    private String ysdwdm; // 验收单位代码

    @TableField("YSDWMC")
    private String ysdwmc; // 验收单位名称

    @TableField("BMDM")
    private String bmdm; // 部门代码

    @TableField("BMMC")
    private String bmmc; // 部门名称

    @TableField("CGFSDM")
    private String cgfsdm; // 采购方式代码

    @TableField("CGFSMC")
    private String cgfsmc; // 采购方式名称

    @TableField("XMJE")
    private BigDecimal xmje; // 项目金额

    @TableField("HTDM")
    private String htdm; // 合同代码

    @TableField("PFBH")
    private String pfbh; // 批复编号

    @TableField("LR_RQ")
    private String lrRq; // 录入日期

    @TableField("LRR")
    private String lrr; // 录入人

    @TableField("LRR_ID")
    private BigDecimal lrrId; // 录入人ID

    @TableField("SSR_ID")
    private BigDecimal ssrId; // 审核人ID

    @TableField("SSR")
    private String ssr; // 审核人

    @TableField("SS_RQ")
    private String ssRq; // 审核日期

    @TableField("PFR_ID")
    private BigDecimal pfrId; // 批复人ID

    @TableField("PFR")
    private String pfr; // 批复人

    @TableField("PF_RQ")
    private String pfRq; // 批复日期

    @TableField("ASHR_ID")
    private Integer ashrId; // 审核人ID(OA)

    @TableField("ASHR")
    private String ashr; // 审核人(OA)

    @TableField("ASH_RQ")
    private String ashRq; // 审核日期(OA)

    @TableField("ASHJD")
    private Integer ashjd; // 审核节点

    @TableField("ASHZT")
    private String ashzt; // 审核状态

    @TableField("AXSHJD")
    private Integer axshjd; // 下一步审核节点

    @TableField("ASFTH")
    private String asfth; // 是否退回

    @TableField("ATHXX")
    private String athxx; // 退回信息

    @TableField("stamp")
    private BigDecimal stamp; // 时间戳

    @TableField("OAZT")
    private String oazt; // OA状态

    @TableField("FLOWCODE")
    private String flowcode; // 流程代码

    @TableField("SCFJ_ZBWJ")
    private String scfjZbwj; // 招标文件附件

    @TableField("SCFJ_ZBGGS")
    private String scfjZbggs; // 招标公告附件

    @TableField("Create_RQ")
    private String createRq; // 创建日期

    @TableField("ISZF")
    private String iszf; // 是否作废
} 