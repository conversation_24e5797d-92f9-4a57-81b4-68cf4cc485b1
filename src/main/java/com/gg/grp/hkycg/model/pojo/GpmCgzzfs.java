package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购组织方式表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGZZFS")
public class GpmCgzzfs {

    @TableField(value = "CGZZFSID")
    private String cgzzfsid;

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("ZZFSDM")
    private String zzfsdm; // 组织方式代码

    @TableField("ZZFSMC")
    private String zzfsmc; // 组织方式名称

    @TableField("SYZT")
    private String syzt; // 使用状态

    @TableField("ZJM")
    private String zjm; // 助记码

    @TableField("BZ")
    private String bz; // 备注

    @TableField("PXH")
    private Integer pxh; // 排序号

    @TableField("sfxs")
    private String sfxs; // 是否显示
} 