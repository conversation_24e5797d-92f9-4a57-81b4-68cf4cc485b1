package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 招标申请内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGZBSQNR")
public class GpmCgzbsqnr {

    /**
     * 招标申请编号
     */
    @TableField("ZBSQBH")
    private String zbsqbh;

    /**
     * 是否采购申请引入
     */
    @TableField("SFCGSQYR")
    private String sfcgsqyr;

    /**
     * 明细序号
     */
    @TableField("ZBMXXH")
    private String zbmxxh;

    /**
     * 物品名称
     */
    @TableField("WPMC")
    private String wpmc;

    /**
     * 归口管理部门代码
     */
    @TableField("GKGLBMDM")
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    @TableField("GKGLBMMC")
    private String gkglbmmc;

    /**
     * 采购目录代码
     */
    @TableField("CGMLDM")
    private String cgmldm;

    /**
     * 采购目录名称
     */
    @TableField("CGMLMC")
    private String cgmlmc;

    /**
     * 本次采购数量
     */
    @TableField("BCCGSL")
    private Integer bccgsl;

    /**
     * 本次采购金额
     */
    @TableField("BCCGJE")
    private BigDecimal bccgje;

    /**
     * 单价
     */
    @TableField("DJ")
    private Integer dj;

    /**
     * 规格型号
     */
    @TableField("GGXH")
    private String ggxh;

    /**
     * 计量单位
     */
    @TableField("JLDW")
    private String jldw;

    /**
     * 计划金额
     */
    @TableField("JHJE")
    private BigDecimal jhje;

    /**
     * 已用金额
     */
    @TableField("YYJE")
    private BigDecimal yyje;

    /**
     * 可用金额
     */
    @TableField("KYJE")
    private BigDecimal kyje;

    /**
     * 计划审定数量
     */
    @TableField("JHSDSL")
    private Integer jhsdsl;

    /**
     * 已用数量
     */
    @TableField("YYSL")
    private Integer yysl;

    /**
     * 可申请数量
     */
    @TableField("KSQSL")
    private Integer ksqsl;

    /**
     * 项目名称
     */
    @TableField("XMMC")
    private String xmmc;

    /**
     * 政府采购
     */
    @TableField("ZFCG")
    private String zfcg;

    /**
     * 进口产品
     */
    @TableField("JKCP")
    private String jkcp;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 创建时间
     */
    @TableField("CJSJ")
    private String cjsj;

    /**
     * 更新时间
     */
    @TableField("GXSJ")
    private String gxsj;

    /**
     * 创建人代码
     */
    @TableField("CJRDM")
    private String cjrdm;

    /**
     * 创建人名称
     */
    @TableField("CJRMC")
    private String cjrmc;

    /**
     * 更新人代码
     */
    @TableField("GXRDM")
    private String gxrdm;

    /**
     * 更新人名称
     */
    @TableField("GXRMC")
    private String gxrmc;

    /**
     * 申请编号
     */
    @TableField("SQBH")
    private String sqbh;

    /**
     * 申请编号
     */
    @TableField("SQMC")
    private String sqmc;

    /**
     * 明细序号
     */
    @TableField("SQMXXH")
    private String sqmxxh;

} 