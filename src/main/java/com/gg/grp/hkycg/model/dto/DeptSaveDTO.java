package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 部门保存DTO
 */
@Data
public class DeptSaveDTO {

    /**
     * 部门代码
     */
    @NotBlank(message = "部门代码不能为空")
    private String bmdm;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    private String bmmc;

    /**
     * 部门类别
     */
    private String bmlb;

    /**
     * 负责人代码
     */
    private String fzrdm;

    /**
     * 电话
     */
    private String tel;

    /**
     * 下级部门数
     */
    private String xjbms;

    /**
     * 职工人数
     */
    private String zgrs;

    /**
     * 使用状态
     */
    private String syzt;

    /**
     * 是否明细
     */
    private String sfmx;

    /**
     * 助记码
     */
    private String zjm;

    /**
     * 负责人代码2
     */
    private String fzrdm2;

    /**
     * 负责人代码3
     */
    private String fzrdm3;

    /**
     * 是否统计库
     */
    private String sftck;
}
