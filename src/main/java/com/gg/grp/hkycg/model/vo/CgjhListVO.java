package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购计划列表VO
 */
@Data
public class CgjhListVO {
    
    /**
     * 单据编号
     */
    private String jhbh;
    
    /**
     * 计划名称
     */
    private String jhmc;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 计划状态
     */
    private String zt;
    
    /**
     * 计划状态名称
     */
    private String ztmc;
    
    /**
     * 采购类型代码
     */
    private String cglxdm;
    
    /**
     * 采购类型名称
     */
    private String cglxmc;
    
    /**
     * 采购品目代码
     */
    private String cgmldm;
    
    /**
     * 采购品目名称
     */
    private String cgmlmc;
    
    /**
     * 录入日期
     */
    private String lrRq;

    /**
     * 部门代码
     */
    private String bmdm;
    
    /**
     * 部门名称
     */
    private String bmmc;
    
    /**
     * 计划金额
     */
    private BigDecimal jhje;

    @JsonIgnore
    private Integer totalCount;

    private BigDecimal ysje;

    private String ncgrq;

} 