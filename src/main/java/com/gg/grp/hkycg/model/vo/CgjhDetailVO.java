package com.gg.grp.hkycg.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购计划详情VO
 */
@Data
public class CgjhDetailVO {

    // =============== 基础信息 ===============

    private String jhid;
    
    /**
     * 单据编号（计划编号）
     */
    private String djbh;
    
    /**
     * 计划名称
     */
    private String jhmc;

    private BigDecimal zt; // 状态

    /**
     * 采购类型代码
     */
    private String cglxdm;

    /**
     * 采购类型名称
     */
    private String cglxmc;
    
    /**
     * 经办人
     */
    private String jbr;
    
    /**
     * 计划部门
     */
    private String bmmc;

    /**
     * 项目
     */
    @TableField("XMID")
    private String xmid;
    @TableField("XMDM")
    private String xmdm;
    @TableField("XMMC")
    private String xmmc;
    @TableField("XMRQ")
    private String xmrq;
    
    /**
     * 拟采购日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date nCgrq;
    
    /**
     * 计划金额
     */
    private BigDecimal jhje;
    
    /**
     * 本年计划金额
     */
    private BigDecimal bnJhje;
    
    /**
     * 已执行金额
     */
    private BigDecimal yzxJe;
    
    /**
     * 申请依据及理由
     */
    private String sqyjjly;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 年度
     */
    private String kjnd;

    // =============== 指标信息 ===============

    /**
     * 指标ID
     */
    private String zbId;

    /**
     * 指标代码
     */
    private String zbDm;

    // =============== 采购计划明细 ===============
    
    /**
     * 采购计划明细
     */
    private CgjhDetailItemVO detail;

    /**
     * 采购计划明细项VO
     */
    @Data
    public static class CgjhDetailItemVO {
        
        /**
         * 明细编号（明细序号）
         */
        private String mxxh;
        
        /**
         * 采购品目代码
         */
        private String cgmldm;
        
        /**
         * 采购品目名称
         */
        private String cgmlmc;
        
        /**
         * 物品(服务)名称
         */
        private String wpmc;
        
        /**
         * 规格型号
         */
        private String ggxh;
        
        /**
         * 计量单位
         */
        private String jldw;
        
        /**
         * 数量上限
         */
        private BigDecimal slsx;
        
        /**
         * 预算上限
         */
        private BigDecimal yssx;
        
        /**
         * 计划单价
         */
        private BigDecimal jhdj;
        
        /**
         * 计划数量
         */
        private BigDecimal jhsl;
        
        /**
         * 计划金额
         */
        private BigDecimal jhje;
        
        /**
         * 预算指标
         */
        private String yszb;
        
        /**
         * 归口管理部门
         */
        private String gkglbm;
        
        /**
         * 是否固定资产
         */
        private Boolean sfGdzc;
        
        /**
         * 是否进口产品
         */
        private Boolean sfJkcp;
        
        /**
         * 是否涉密
         */
        private Boolean sfSm;
        
        /**
         * 是否协议供货
         */
        private Boolean sfXygh;
        
        /**
         * 是否政府采购
         */
        private Boolean sfZfcg;
        
        /**
         * 备注
         */
        private String bz;
    }
} 