package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 我的被催办单据VO
 */
@Data
public class MyReminderVO {
    
    /**
     * 单据号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 单据类型编码
     */
    @JsonProperty("djlx")
    private String billTypeCode;
    
    /**
     * 单据类型名称
     */
    @JsonProperty("djlxmc")
    private String billTypeName;
    
    /**
     * 单据名称
     */
    @JsonProperty("djmc")
    private String billName;
    
    /**
     * 单据状态编码
     */
    @JsonProperty("zt")
    private String statusCode;
    
    /**
     * 单据状态名称
     */
    @JsonProperty("ztmc")
    private String statusName;
    
    /**
     * 催办人
     */
    @JsonProperty("cbr")
    private String reminderUser;
    
    /**
     * 催办时间
     */
    @JsonProperty("cbsj")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reminderTime;
    
    /**
     * 催办内容
     */
    @JsonProperty("cbnr")
    private String reminderContent;
    
    /**
     * 催办状态
     * 0-未处理
     * 1-已处理
     */
    @JsonProperty("cbzt")
    private String reminderResult;
    
    /**
     * 审核节点名称
     */
    private String nodeName;
    
    /**
     * 总记录数（用于分页）
     */
    @JsonIgnore
    private Long totalCount;
} 