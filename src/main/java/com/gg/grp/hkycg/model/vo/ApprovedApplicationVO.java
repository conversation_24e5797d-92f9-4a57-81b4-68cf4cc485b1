package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 已审核采购申请响应VO
 */
@Data
public class ApprovedApplicationVO {
    
    /**
     * 采购日期
     */
    @JsonProperty("cgrq")
    private String purchaseDate;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 申请编号
     */
    @JsonProperty("sqbh")
    private String applicationNo;
    
    /**
     * 申请名称
     */
    @JsonProperty("sqmc")
    private String applicationName;
    
    /**
     * 制单日期
     */
    @JsonProperty("zdrq")
    private String createDate;
    
    /**
     * 单据状态
     */
    @JsonProperty("djzt")
    private String billStatus;
    
    /**
     * 申请部门
     */
    @JsonProperty("sqbm")
    private String applicationDept;
    
    /**
     * 经办人
     */
    @JsonProperty("jbr")
    private String handler;
    
    /**
     * 采购理由
     */
    @JsonProperty("cgly")
    private String purchaseReason;
    
    /**
     * 申请金额
     */
    @JsonProperty("sqje")
    private BigDecimal applicationAmount;
}
