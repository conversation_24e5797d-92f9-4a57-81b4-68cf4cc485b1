package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("GPM_ROLEZB")
public class GpmRolezb {
    @TableField("GSDM")
    private String gsdm;

    @TableField("KJND")
    private String kjnd;

    /**
     * 角色代码
     */
    @TableField("ROLECODE")
    private String rolecode;

    /**
     * 指标ID
     */
    @TableField("ZBID")
    private Integer zbid;

    /**
     * 授权人
     */
    @TableField("SQREN")
    private String sqren;

    @TableField("JZRQ")
    private String jzrq;

    /**
     * 使用权限
     */
    @TableField("SYQX")
    private String syqx;
}
