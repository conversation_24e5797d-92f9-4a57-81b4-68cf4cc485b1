package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 已审核单据查询条件DTO
 */
@Data
public class ApprovedBillsQueryDTO {

    /**
     * 当前页码
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 单据类型
     * 可选值：
     * - CGJH：采购计划
     * - CGSQ：采购申请
     * - CGZB：采购招标
     * - CGJG：采购结果
     * - CGYS：采购验收
     */
    @JsonProperty("djlx")
    private String billType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
}
