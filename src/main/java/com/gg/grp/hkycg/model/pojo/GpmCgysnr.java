package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购验收内容明细实体类
 * 对应数据表：GPM_CGYSNR
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("GPM_CGYSNR")
public class GpmCgysnr implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 明细编号
     */
    @TableField("YSMXXH")
    private String ysmxxh;

    /**
     * 结果内容编号（主键）
     */
    @TableField("JGNRBH")
    private String jgnrbh;

    /**
     * 标段编号
     */
    @TableField("BDBH")
    private String bdbh;

    /**
     * 标段名称
     */
    @TableField("BDMC")
    private String bdmc;

    /**
     * 标段单价
     */
    @TableField("BDDJ")
    private BigDecimal bddj;

    /**
     * 标段数量
     */
    @TableField("BDSL")
    private BigDecimal bdsl;

    /**
     * 标段总额
     */
    @TableField("BDZE")
    private BigDecimal bdze;

    /**
     * 本次验收数量
     */
    @TableField("BCYSSSL")
    private BigDecimal bcysssl;

    /**
     * 本次验收总额
     */
    @TableField("BCYSSZE")
    private BigDecimal bcyssze;

    /**
     * 已验收数量
     */
    @TableField("YYSSSL")
    private BigDecimal yysssl;

    /**
     * 已验收总额
     */
    @TableField("YYSSZE")
    private BigDecimal yyssze;

    /**
     * 供应商
     */
    @TableField("GYS")
    private String gys;

    /**
     * 采购品种
     */
    @TableField("CGPZ")
    private String cgpz;

    /**
     * 物品(服务)名称
     */
    @TableField("WPMC")
    private String wpmc;

    /**
     * 规格型号
     */
    @TableField("GXTH")
    private String gxth;

    /**
     * 计量单位
     */
    @TableField("JLDW")
    private String jldw;

    /**
     * 品牌明细
     */
    @TableField("PMMC")
    private String pmmc;

    /**
     * 厂商
     */
    @TableField("CS")
    private String cs;

    /**
     * 产地
     */
    @TableField("CD")
    private String cd;

    /**
     * 品牌
     */
    @TableField("PP")
    private String pp;

    /**
     * 采购品目代码
     */
    @TableField("CGMLDM")
    private String cgmldm;

    /**
     * 采购品目名称
     */
    @TableField("CGMLMC")
    private String cgmlmc;

    /**
     * 归口管理部门代码
     */
    @TableField("GKGLBMDM")
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    @TableField("GKGLBMMC")
    private String gkglbmmc;

    /**
     * 项目名称
     */
    @TableField("XMMC")
    private String xmmc;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 政府采购
     */
    @TableField("ZFCG")
    private String zfcg;

    /**
     * 进口产品
     */
    @TableField("JKCP")
    private String jkcp;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private String createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private String updateTime;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 更新人
     */
    @TableField("UPDATE_USER")
    private String updateUser;
    
    /**
     * 是否采购结果引入
     */
    @TableField("SFCGJGYR")
    private String sfcgjgyr;
    
    /**
     * 验收申请编号
     */
    @TableField("YSBH")
    private String ysbh;
    
    /**
     * 采购结果编号
     */
    @TableField("JGDJBH")
    private String jgdjbh;
    
    /**
     * 采购结果名称
     */
    @TableField("JGDJMC")
    private String jgdjmc;
} 