package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 附件关联信息实体类
 */
@Data
@TableName("GPM_ATTACHMENT_REL")
public class GpmAttachmentRel {
    
    /**
     * 关联ID，使用UUID
     */
    @TableId("RELID")
    private String relid;
    
    /**
     * 附件ID，关联GPM_ATTACHMENT表
     */
    @TableField("FJID")
    private String fjid;
    
    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;
    
    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;
    
    /**
     * 单据类型(CGJH/CGSQ/CGZB/CGDJ/CGYS)
     */
    @TableField("DJLX")
    private String djlx;
    
    /**
     * 单据号
     */
    @TableField("DJH")
    private String djh;
    
    /**
     * 是否明细
     */
    @TableField("SFMX")
    private String sfmx;
} 