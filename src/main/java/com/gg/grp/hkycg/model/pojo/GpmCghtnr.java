package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购合同内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGHTNR")
public class GpmCghtnr {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("HTBH")
    private BigDecimal htbh; // 合同编号

    @TableField("HTXH")
    private BigDecimal htxh; // 合同序号

    @TableField("XMXH")
    private BigDecimal xmxh; // 项目序号

    @TableField("CGMLDM")
    private String cgmldm; // 采购品目代码

    @TableField("CGMLMC")
    private String cgmlmc; // 采购品目名称

    @TableField("WPMC")
    private String wpmc; // 物品名称

    @TableField("GGXH")
    private String ggxh; // 规格型号

    @TableField("WPLB")
    private String wplb; // 物品类别

    @TableField("WPCD")
    private String wpcd; // 产地

    @TableField("WPSJ")
    private String wpsj; // 商家

    @TableField("WPPP")
    private String wppp; // 品牌

    @TableField("PPMX")
    private String ppmx; // 品牌明细

    @TableField("JLDW")
    private String jldw; // 计量单位

    @TableField("WPSL")
    private BigDecimal wpsl; // 物品数量

    @TableField("HSDW")
    private String hsdw; // 换算单位

    @TableField("WPDJ")
    private BigDecimal wpdj; // 物品单价

    @TableField("JE")
    private BigDecimal je; // 金额

    @TableField("HTDM_HT")
    private String htdmHt; // 合同代码
} 