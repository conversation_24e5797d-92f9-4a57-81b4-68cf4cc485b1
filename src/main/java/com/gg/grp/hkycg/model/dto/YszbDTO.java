package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class YszbDTO {
    /**
     * 指标ID
     */
    @NotNull(message = "指标ID不能为空")
    private Integer zbid;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

    /**
     * 指标代码
     */
    private String zbdm;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal je;

    /**
     * 摘要
     */
    private String zy;

    /**
     * 计划指标ID
     */
    private Integer jhzbid;

    /**
     * 科目代码
     */
    private String kmdm;

    /**
     * 科目名称
     */
    private String kmmc;

    /**
     * 指标功能科目代码
     */
    private String zbgnkmdm;

    /**
     * 指标功能科目名称
     */
    private String zbgnkmmc;

    /**
     * 指标经济科目代码
     */
    private String zbjjkmdm;

    /**
     * 指标经济科目名称
     */
    private String zbjjkmmc;

    /**
     * 控制指标ID
     */
    private Integer kzzbid;

}
