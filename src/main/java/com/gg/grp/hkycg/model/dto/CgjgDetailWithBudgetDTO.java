package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 采购结果明细与预算指标组合DTO（一对一关系）
 */
@Data
public class CgjgDetailWithBudgetDTO {

    /**
     * 明细信息
     */
    @Valid
    @NotNull(message = "明细信息不能为空")
    private CgjgDetailDTO detail;

    /**
     * 该明细对应的预算指标（一对一关系）
     */
    @Valid
    @NotNull(message = "预算指标不能为空")
    private CgjhBudgetIndexDTO budgetIndex;
}
