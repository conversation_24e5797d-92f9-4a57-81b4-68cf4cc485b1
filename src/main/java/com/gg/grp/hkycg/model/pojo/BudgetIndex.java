package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预算指标实体类
 * 对应数据表：OER_YSZB 和 GBI_ZBSYREC
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("budget_index")  // 虚拟表名，实际操作OER_YSZB和GBI_ZBSYREC
public class BudgetIndex implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 指标来源
     */
    @TableField("index_source")
    private String indexSource;

    /**
     * 经济科目
     */
    @TableField("economic_subject")
    private String economicSubject;

    /**
     * 会计科目
     */
    @TableField("account_title")
    private String accountTitle;

    /**
     * 功能科目
     */
    @TableField("func_subject")
    private String funcSubject;

    /**
     * 索引
     */
    @TableField("index")
    private Integer index;

    /**
     * 项目
     */
    @TableField("project")
    private String project;

    /**
     * fZ9DM
     */
    @TableField("fZ9DM")
    private String fZ9DM;

    /**
     * 费用备注
     */
    @TableField("expense_remark")
    private String expenseRemark;

    /**
     * 余额
     */
    @TableField("residual")
    private BigDecimal residual;

    /**
     * fZ8DM
     */
    @TableField("fZ8DM")
    private String fZ8DM;

    /**
     * fZ7DM
     */
    @TableField("fZ7DM")
    private String fZ7DM;

    /**
     * 支出部门
     */
    @TableField("outlay_dept")
    private String outlayDept;

    /**
     * 员工
     */
    @TableField("employee")
    private String employee;

    /**
     * cyskzfs
     */
    @TableField("cyskzfs")
    private Integer cyskzfs;

    /**
     * 执行方式
     */
    @TableField("execute_way")
    private String executeWay;

    /**
     * fZ9MC
     */
    @TableField("fZ9MC")
    private String fZ9MC;

    /**
     * fZ8MC
     */
    @TableField("fZ8MC")
    private String fZ8MC;

    /**
     * fZ7MC
     */
    @TableField("fZ7MC")
    private String fZ7MC;

    /**
     * 业务ID (用于系统内部关联)
     */
    @TableField("lid")
    private String lid;

    /**
     * 标识 (用于系统内部关联)
     */
    @TableField("bid")
    private String bid;

    /**
     * 删除标志（0-未删除，1-已删除）
     */
    @TableField("del")
    private Boolean del;

    /**
     * 员工代码 (用于系统内部逻辑)
     */
    @TableField("employee_code")
    private String employeeCode;

    /**
     * 公司代码 (用于系统内部逻辑)
     */
    @TableField("gsdm")
    private String gsdm;

    /**
     * 会计年度 (用于系统内部逻辑)
     */
    @TableField("kjnd")
    private String kjnd;

    /**
     * 员工姓名 (用于系统内部逻辑)
     */
    @TableField("employee_name")
    private String employeeName;
} 