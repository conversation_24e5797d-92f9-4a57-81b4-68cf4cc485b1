package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购项目验收目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGXMYSML")
public class GpmCgxmysml {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("XMYSBH")
    private Integer xmysbh; // 验收编号

    @TableField("XMYSMC")
    private String xmysmc; // 验收名称

    @TableField("XMYSID")
    private String xmysid; // 验收ID

    @TableField("XMID")
    private String xmid; // 项目ID

    @TableField("XMDM")
    private String xmdm; // 项目代码

    @TableField("XMMC")
    private String xmmc; // 项目名称

    @TableField("CGFSDM")
    private String cgfsdm; // 采购方式代码

    @TableField("CGFSMC")
    private String cgfsmc; // 采购方式名称

    @TableField("YSDWDM")
    private String ysdwdm; // 验收单位代码

    @TableField("YSDWMC")
    private String ysdwmc; // 验收单位名称

    @TableField("BMDM")
    private String bmdm; // 部门代码

    @TableField("BMMC")
    private String bmmc; // 部门名称

    @TableField("BZ")
    private String bz; // 备注

    @TableField("JE")
    private BigDecimal je; // 金额

    @TableField("SCFJ")
    private String scfj; // 附件

    @TableField("ZT")
    private String zt; // 状态

    @TableField("STAMP")
    private Integer stamp; // 时间戳

    @TableField("OAZT")
    private String oazt; // OA状态

    @TableField("LRR_ID")
    private Integer lrrId; // 录入人ID

    @TableField("LRR")
    private String lrr; // 录入人

    @TableField("LR_RQ")
    private String lrRq; // 录入日期

    @TableField("SSR_ID")
    private Integer ssrId; // 审核人ID

    @TableField("SSR")
    private String ssr; // 审核人

    @TableField("SS_RQ")
    private String ssRq; // 审核日期

    @TableField("PFR_ID")
    private Integer pfrId; // 批复人ID

    @TableField("PFR")
    private String pfr; // 批复人

    @TableField("PF_RQ")
    private String pfRq; // 批复日期

    @TableField("XMYSR_ID")
    private Integer xmysrId; // 验收人ID

    @TableField("XMYSR")
    private String xmysr; // 验收人

    @TableField("XMYS_RQ")
    private String xmysRq; // 验收日期

    @TableField("SHR_ID")
    private Integer shrId; // 审核人ID(OA)

    @TableField("SHR")
    private String shr; // 审核人(OA)

    @TableField("SH_RQ")
    private String shRq; // 审核日期(OA)

    @TableField("ASHJD")
    private Integer ashjd; // 审核节点

    @TableField("AXSHJD")
    private Integer axshjd; // 下一步审核节点

    @TableField("ASHZT")
    private String ashzt; // 审核状态

    @TableField("ASFTH")
    private String asfth; // 是否退回

    @TableField("ATHXX")
    private String athxx; // 退回信息

    @TableField("FLOWCODE")
    private String flowcode; // 流程代码
} 