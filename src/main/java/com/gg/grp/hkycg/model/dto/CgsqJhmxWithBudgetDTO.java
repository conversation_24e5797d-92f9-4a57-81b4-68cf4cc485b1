package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 采购申请计划明细与预算指标组合DTO（一对一关系）
 */
@Data
public class CgsqJhmxWithBudgetDTO {

    /**
     * 计划明细信息
     */
    @Valid
    @NotNull(message = "计划明细信息不能为空")
    private CgsqJhmxDTO jhmx;

    /**
     * 该明细对应的预算指标（一对一关系）
     */
    @Valid
    @NotNull(message = "预算指标不能为空")
    private CgjhBudgetIndexDTO budgetIndex;
} 