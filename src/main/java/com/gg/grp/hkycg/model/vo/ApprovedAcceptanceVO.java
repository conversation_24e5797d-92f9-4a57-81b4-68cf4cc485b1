package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 已审核采购验收响应VO
 */
@Data
public class ApprovedAcceptanceVO {
    
    /**
     * 拟验收日期
     */
    @JsonProperty("nysrq")
    private String plannedAcceptanceDate;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 验收申请编号
     */
    @JsonProperty("yssqbh")
    private String acceptanceApplicationNo;
    
    /**
     * 验收申请名称
     */
    @JsonProperty("yssqmc")
    private String acceptanceApplicationName;
    
    /**
     * 制单日期
     */
    @JsonProperty("zdrq")
    private String createDate;
    
    /**
     * 单据状态
     */
    @JsonProperty("djzt")
    private String billStatus;
    
    /**
     * 验收申请部门
     */
    @JsonProperty("yssqbm")
    private String acceptanceApplicationDept;
    
    /**
     * 验收负责人
     */
    @JsonProperty("ysfzr")
    private String acceptanceManager;
    
    /**
     * 参与人员
     */
    @JsonProperty("cyry")
    private String participants;
    
    /**
     * 备注
     */
    @JsonProperty("bz")
    private String remarks;
    
    /**
     * 本次验收金额
     */
    @JsonProperty("bcysje")
    private BigDecimal currentAcceptanceAmount;
}
