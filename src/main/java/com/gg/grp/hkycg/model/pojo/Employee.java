package com.gg.grp.hkycg.model.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 职员信息Model
 *
 * <AUTHOR>
 *
 */
@TableName("PUBZYXX")
@Data
public class Employee{

    /**
     * 职员代码
     */
    @TableField(value = "ZYDM")
    private String employeeCode;
    /**
     * 职员姓名
     */
    @TableField(value = "ZYXM")
    private String employeeName;
    /**
     *
     */
    @TableField(value = "gsdm")
    private String gsdm;
    /**
     *
     */
    @TableField(value = "kjnd")
    private String kjnd;
    /**
     *
     */
    @TableField(value = "zjm")
    private String zjm;
    /**
     *
     */
    @TableField(value = "xb")
    private String xb;
    /**
     *
     */
    @TableField(value = "bmdm")
    private String bmdm;
    /**
     *
     */
    @TableField(value = "zylb")
    private String zylb;
    /**
     *
     */
    @TableField(value = "sfzh")
    private String sfzh;
    /**
     *
     */
    @TableField(value = "zw")
    private String zw;
    /**
     *
     */
    @TableField(value = "tel")
    private String tel;
    /**
     *
     */
    @TableField(value = "email")
    private String email;
    /**
     *
     */
    @TableField(value = "jtzz")
    private String jtzz;
    /**
     *
     */
    @TableField(value = "csny")
    private String csny;
    /**
     *
     */
    @TableField(value = "whcd")
    private String whcd;
    /**
     *
     */
    @TableField(value = "dzrq")
    private String dzrq;
    /**
     *
     */
    @TableField(value = "lzrq")
    private String lzrq;
    /**
     *
     */
    @TableField(value = "syzt")
    private String syzt;
    /**
     *
     */
    @TableField(value = "GLzt")
    private String GLzt;
    /**
     *
     */
    @TableField(value = "zyzp")
    private String zyzp;
    /**
     *
     */
    @TableField(value = "scgzzn")
    private int scgzzn;
    /**
     *
     */
    @TableField(value = "JLR_ID")
    private int JLR_ID;
    /**
     *
     */
    @TableField(value = "JL_RQ")
    private String JL_RQ;
    /**
     *
     */
    @TableField(value = "XGR_ID")
    private int XGR_ID;
    /**
     *
     */
    @TableField(value = "XG_RQ")
    private String XG_RQ;
    /**
     *
     */
    @TableField(value = "Sjly")
    private String Sjly;
    /**
     *
     */
    @TableField(value = "bz")
    private String bz;
    /**
     *
     */
    @JSONField(serialize = false, deserialize = false)
    @TableField(value = "password")
    private String password;
    /**
     *
     */
    @TableField(value = "DFYH")
    private String DFYH;
    /**
     *
     */
    @TableField(value = "GRZH")
    private String GRZH;
    /**
     *
     */
    @TableField(value = "GWKYH")
    private String GWKYH;
    /**
     *
     */
    @TableField(value = "GWKZH")
    private String GWKZH;
    /**
     *
     */
    @TableField(value = "WBDM")
    private String WBDM;
    /**
     *
     */
    @TableField(value = "zyzp1")
    private String zyzp1;
    /**
     *
     */
    @TableField(value = "appsw")
    private String appsw;
    /**
     *是否有填写权限
     */
    @TableField(value = "zj")
    private String zj;
}
