package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购结果基础信息DTO
 */
@Data
public class CgjgBaseInfoDTO {

    /**
     * 结果登记编号（主键）
     */
    private String jgdjbh;

    /**
     * 结果登记名称
     */
    private String jgdjmc;

    /**
     * 采购部门代码
     */
    private String cgbmdm;

    /**
     * 采购部门名称
     */
    private String cgbmmc;

    /**
     * 采购方式代码
     */
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    private String cgfsmc;

    /**
     * 中介机构代码
     */
    private String zjjgdm;

    /**
     * 中介机构名称
     */
    private String zjjgmc;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 招标负责人
     */
    private String zbfzr;

    /**
     * 采购申请金额
     */
    private BigDecimal cgsqje;

    /**
     * 预算金额
     */
    private BigDecimal yyje;

    /**
     * 可用金额
     */
    private BigDecimal kyje;

    /**
     * 结果登记金额
     */
    private BigDecimal jgdjje;

    /**
     * 采购理由
     */
    private String cgly;

    /**
     * 备注
     */
    private String bz;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

}
