package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 招标申请审核列表VO
 */
@Data
public class CgzbsqAuditListVO {
    
    /**
     * 招标申请编号
     */
    private String zbsqbh;
    
    /**
     * 招标申请名称
     */
    private String zbsqmc;
    
    /**
     * 项目金额
     */
    private BigDecimal xmje;
    
    /**
     * 经办人
     */
    private String jbr;
    
    /**
     * 招标负责人
     */
    private String zbfzr;
    
    /**
     * 创建时间
     */
    private String cjsj;
    
    /**
     * 拟招标日期（格式：yyyy-MM-dd）
     */
    private String nzbrq;
    
    /**
     * 状态
     */
    private String zt;
    
    /**
     * 状态名称
     */
    private String ztmc;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 当前审核节点名称
     */
    private String currentNodeName;
    
    /**
     * 当前审核人
     */
    private String currentAuditor;
    
    /**
     * 是否可以审核
     */
    private Boolean canAudit;
    
    /**
     * 审核级别
     */
    private Integer shjb;
    
    /**
     * 招标申请部门代码
     */
    private String zbsqbmdn;
    
    /**
     * 招标申请部门名称
     */
    private String zbsqbmmc;
    
    /**
     * 备注
     */
    private String bz;
    
    /**
     * 单据类型
     */
    private String djlx;
    
    /**
     * 提交日期
     */
    private String tjrq;
    
    /**
     * 审核人
     */
    private String shr;

    @JsonIgnore
    private Integer totalCount;
} 