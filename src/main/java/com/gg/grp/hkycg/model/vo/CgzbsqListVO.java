package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 招标申请列表VO
 */
@Data
public class CgzbsqListVO {
    
    /**
     * 招标申请编号（主键）
     */
    private String zbsqbh;
    
    /**
     * 招标申请名称
     */
    private String zbsqmc;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 申请状态
     */
    private String zt;
    
    /**
     * 申请状态名称
     */
    private String ztmc;
    
    /**
     * 创建时间
     */
    private String cjsj;

    /**
     * 招标申请部门代码
     */
    private String zbsqbmdn;
    
    /**
     * 招标申请部门名称
     */
    private String zbsqbmmc;
    
    /**
     * 项目金额
     */
    private BigDecimal xmje;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 招标负责人
     */
    private String zbfzr;

    /**
     * 拟招标日期（格式：yyyy-MM-dd）
     */
    private String nzbrq;

    /**
     * 政府采购方式代码
     */
    private String zfcgfsdm;

    /**
     * 政府采购方式名称
     */
    private String zfcgfsmc;

    @JsonIgnore
    private Integer totalCount;
} 