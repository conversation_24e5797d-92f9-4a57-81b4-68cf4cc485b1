package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 招标申请保存响应VO
 */
@Data
public class CgzbsqSaveResponseVO {

    /**
     * 基础信息
     */
    private CgzbsqBaseInfoResponseVO baseInfo;

    /**
     * 招标申请明细列表
     */
    @JsonProperty("CgzbsqDetails")
    private List<CgzbsqDetailResponseVO> CgzbsqDetails;

    /**
     * 基础信息响应VO
     */
    @Data
    public static class CgzbsqBaseInfoResponseVO {
        /**
         * 招标申请编号（主键）
         */
        private String zbsqbh;

        /**
         * 招标申请名称
         */
        private String zbsqmc;

        /**
         * 采购申请金额
         */
        private BigDecimal cgsqje;

        /**
         * 招标申请部门代码
         */
        private String zbsqbmdm;

        /**
         * 招标申请部门名称
         */
        private String zbsqbmmc;

        /**
         * 项目名称
         */
        private String xmmc;

        /**
         * 项目已采购金额
         */
        private BigDecimal xmycgje;

        /**
         * 中介机构代码
         */
        private String zjjgdm;

        /**
         * 中介机构名称
         */
        private String zjjgmc;

        /**
         * 采购专家来源方式代码
         */
        private String cgzjlyfsdm;

        /**
         * 采购专家来源方式名称
         */
        private String cgzjlyfsmc;

        /**
         * 政府采购方式代码
         */
        private String zfcgfsdm;

        /**
         * 政府采购方式名称
         */
        private String zfcgfsmc;

        /**
         * 经办人
         */
        private String jbr;

        /**
         * 招标负责人
         */
        private String zbfzr;

        /**
         * 拟招标日期（格式：yyyy-MM-dd）
         */
        private String nzbrq;

        /**
         * 项目金额
         */
        private BigDecimal xmje;

        /**
         * 采购理由
         */
        private String cgly;

        /**
         * 备注
         */
        private String bz;

        /**
         * 创建时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String cjsj;

        /**
         * 修改时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String xgsj;

        /**
         * 状态
         */
        private String zt;

        /**
         * 状态名称
         */
        private String ztmc;

        /**
         * 公司代码
         */
        private String gsdm;

        /**
         * 会计年度
         */
        private String kjnd;

        /**
         * 创建人代码
         */
        private String cjrdm;

        /**
         * 创建人名称
         */
        private String cjrmc;
    }

    /**
     * 招标申请明细响应VO
     */
    @Data
    public static class CgzbsqDetailResponseVO {
        /**
         * 招标申请编号
         */
        private String zbsqbh;

        /**
         * 是否采购申请引入
         */
        private Boolean sfcgsqyr;

        /**
         * 明细序号
         */
        private String zbmxxh;

        /**
         * 物品名称
         */
        private String wpmc;

        /**
         * 归口管理部门代码
         */
        private String gkglbmdm;

        /**
         * 归口管理部门名称
         */
        private String gkglbmmc;

        /**
         * 采购目录代码
         */
        private String cgmldm;

        /**
         * 采购目录名称
         */
        private String cgmlmc;

        /**
         * 本次采购数量
         */
        private Integer bccgsl;

        /**
         * 本次采购金额
         */
        private BigDecimal bccgje;

        /**
         * 单价
         */
        private Integer dj;

        /**
         * 规格型号
         */
        private String ggxh;

        /**
         * 计量单位
         */
        private String jldw;

        /**
         * 计划金额
         */
        private BigDecimal jhje;

        /**
         * 已用金额
         */
        private BigDecimal yyje;

        /**
         * 可用金额
         */
        private BigDecimal kyje;

        /**
         * 计划审定数量
         */
        private Integer jhsdsl;

        /**
         * 已用数量
         */
        private Integer yysl;

        /**
         * 可申请数量
         */
        private Integer ksqsl;

        /**
         * 项目名称
         */
        private String xmmc;

        /**
         * 政府采购
         */
        private Boolean zfcg;

        /**
         * 进口产品
         */
        private Boolean jkcp;

        /**
         * 备注
         */
        private String bz;

        /**
         * 申请编号
         */
        private String sqbh;

        /**
         * 申请名称
         */
        private String sqmc;

        /**
         * 申请明细序号
         */
        private String sqmxxh;

    }
} 