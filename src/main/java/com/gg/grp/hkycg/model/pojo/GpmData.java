package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * GPM_DATA表对应的实体类
 */
@Data
@TableName("GPM_DATA")
public class GpmData {
    
    /**
     * 数据编码
     */
    @TableField("DATACODE")
    private String datacode;
    
    /**
     * 数据名称
     */
    @TableField("DATANAME")
    private String dataname;

    /**
     * 是否默认
     */
    @TableField("isDefault")
    private String isDefault;

    /**
     * id
     */
    @TableField("DATAID")
    private String dataid;

    /**
     *
     */
    @TableField("isMarkBill")
    private String isMarkBill;

} 