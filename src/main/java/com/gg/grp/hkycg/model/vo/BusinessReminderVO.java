package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 业务提醒VO
 */
@Data
public class BusinessReminderVO {
    
    /**
     * 单据编号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 单据名称
     */
    @JsonProperty("djmc")
    private String billName;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 单据类型名称
     */
    @JsonProperty("djlxmc")
    private String billTypeName;
    
    /**
     * 单据状态码
     */
    @JsonProperty("zt")
    private String statusCode;
    
    /**
     * 单据状态名称
     */
    @JsonProperty("ztmc")
    private String statusName;
} 