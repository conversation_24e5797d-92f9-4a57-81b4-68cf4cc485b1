package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 采购中介机构列表VO
 */
@Data
public class CgzjjgListVO {
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 中介机构代码
     */
    private String zjjgdm;
    
    /**
     * 中介机构名称
     */
    private String zjjgmc;
    
    /**
     * 使用状态
     */
    private String syzt;
    
    /**
     * 使用状态名称
     */
    private String syztmc;
    
    /**
     * 助记码
     */
    private String zjm;
    
    /**
     * 备注
     */
    private String bz;
    
    /**
     * 排序号
     */
    private Integer pxh;
    
    /**
     * 级次
     */
    private String jc;
    
    /**
     * 是否海外
     */
    private String sfhw;
    
    /**
     * 是否海外名称
     */
    private String sfhwmc;
    
    /**
     * 是否服务
     */
    private String sffw;
    
    /**
     * 是否服务名称
     */
    private String sffwmc;
    
    /**
     * 是否工程
     */
    private String sfgc;
    
    /**
     * 是否工程名称
     */
    private String sfgcmc;
    
    /**
     * 联系人
     */
    private String lxr;
    
    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 总记录数（分页用）
     */
    @JsonIgnore
    private Long totalCount;
} 