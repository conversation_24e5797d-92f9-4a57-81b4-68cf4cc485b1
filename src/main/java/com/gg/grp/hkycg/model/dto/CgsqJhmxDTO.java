package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购申请计划明细DTO
 */
@Data
public class CgsqJhmxDTO {

    /**
     * 明细编号
     */
    private String sqmxxh;

    /**
     * 申请编号（外键）
     */
    private String sqbh;

    /**
     * 物品(服务)名称
     */
    private String wpmc;

    /**
     * 归口管理部门代码
     */
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    private String gkglbmmc;

    /**
     * 采购品目代码
     */
    private String cgmldm;

    /**
     * 采购品目名称
     */
    private String cgmlmc;

    /**
     * 计划名称
     */
    private String jhmc;

    /**
     * 本次采购数量
     */
    private Integer bccgsl;

    /**
     * 本次采购金额
     */
    private BigDecimal bccgje;

    /**
     * 单价
     */
    private Integer dj;

    /**
     * 规格型号
     */
    private String ggxh;

    /**
     * 计量单位
     */
    private String jldw;

    /**
     * 计划金额
     */
    private BigDecimal jhje;

    /**
     * 已用金额
     */
    private BigDecimal yyje;

    /**
     * 可用金额
     */
    private BigDecimal kyje;

    /**
     * 计划审定数量
     */
    private Integer jhsdsl;

    /**
     * 已用数量
     */
    private Integer yysl;

    /**
     * 可申请数量
     */
    private Integer ksqsl;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 政府采购
     */
    private Boolean zfcg;

    /**
     * 进口产品
     */
    private Boolean jkcp;

    /**
     * 备注
     */
    private String bz;
} 