package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购结果列表VO
 */
@Data
public class CgjgListVO {
    
    /**
     * 结果登记编号（主键）
     */
    private String jgdjbh;
    
    /**
     * 结果登记名称
     */
    private String jgdjmc;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 状态
     */
    private String zt;
    
    /**
     * 状态名称
     */
    private String ztmc;
    
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 采购部门代码
     */
    private String cgbmdm;
    
    /**
     * 采购部门名称
     */
    private String cgbmmc;
    
    /**
     * 经办人
     */
    private String jbr;

    /**
     * 招标负责人
     */
    private String zbfzr;

    /**
     * 采购方式代码
     */
    private String cgfsdm;

    /**
     * 采购方式名称
     */
    private String cgfsmc;

    /**
     * 中介机构代码
     */
    private String zjjgdm;

    /**
     * 中介机构名称
     */
    private String zjjgmc;

    /**
     * 是否采购申请引入
     */
    private Boolean sfcgsqyr;

    /**
     * 是否采购招标引入
     */
    private Boolean sfcgzbyr;

    /**
     * 采购申请编号
     */
    private String cgsqbh;

    /**
     * 采购申请名称
     */
    private String cgsqmc;

    /**
     * 采购招标编号
     */
    private String cgzbbh;

    /**
     * 采购招标名称
     */
    private String cgzbmc;

    /**
     * 采购申请金额
     */
    private BigDecimal cgsqje;

    /**
     * 预算金额
     */
    private BigDecimal yyje;

    /**
     * 可用金额
     */
    private BigDecimal kyje;

    /**
     * 结果登记金额
     */
    private BigDecimal jgdjje;

    /**
     * 采购理由
     */
    private String cgly;

    /**
     * 备注
     */
    private String bz;

    @JsonIgnore
    private Integer totalCount;
}
