package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 专家来源方式表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGZJLYFS")
public class GpmCgzjlyfs {

    @TableField(value = "CGZJLYFSID")
    private String cgzjlyfsid;

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableId("CGZJLYFSDM")
    private String cgzjlyfsdm; // 采购专家来源方式代码

    @TableField("CGZJLYFSMC")
    private String cgzjlyfsmc; // 采购专家来源方式名称

    @TableField("SYZT")
    private String syzt; // 使用状态

    @TableField("ZJM")
    private String zjm; // 助记码

    @TableField("BZ")
    private String bz; // 备注

    @TableField("PXH")
    private Integer pxh; // 排序号

    @TableField("JC")
    private String jc; // 级次
} 