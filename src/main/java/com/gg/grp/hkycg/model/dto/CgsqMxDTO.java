package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CgsqMxDTO {

    /**
     * 明细编号(自动生成)
     */
    private String mxxh;

    /**
     * 申请编号（外键）
     */
    private String sqbh;

    /**
     * 采购品目代码
     */
    private String cgmldm;

    /**
     * 采购品目名称
     */
    private String cgmlmc;

    /**
     * 物品(服务)名称
     */
    private String wpmc;

    /**
     * 规格型号
     */
    private String ggxh;

    /**
     * 计量单位
     */
    private String jldw;

    /**
     * 申请数量
     */
    private Integer sqsl;

    /**
     * 申请单价
     */
    private BigDecimal sqdj;

    /**
     * 申请金额
     */
    private BigDecimal sqje;

    /**
     * 预算指标
     */
    private String yszb;

    /**
     * 备注
     */
    private String bz;
}
