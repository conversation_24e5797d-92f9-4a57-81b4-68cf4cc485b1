package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购价格内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGDJNR")
public class GpmCgdjnr {

    @TableId("JGNRBH")
    private String jgnrbh; // 结果内容编号

    @TableField("JGDJBH")
    private String jgdjbh; // 结果登记编号

    @TableField("SFCGSQYR")
    private String sfcgsqyr; // 是否采购申请引入

    @TableField("SFCGZBYR")
    private String sfcgzbyr; // 是否采购指标引入

    @TableField("CGSQBH")
    private String cgsqbh; // 采购申请编号

    @TableField("CGSQMC")
    private String cgsqmc; // 采购申请名称

    @TableField("CGZBBH")
    private String cgzbbh; // 采购指标编号

    @TableField("CGZBMC")
    private String cgzbmc; // 采购指标名称

    @TableField("ZBGYS")
    private String zbgys; // 中标供应商

    @TableField("ZBJE")
    private BigDecimal zbje; // 中标金额

    @TableField("ZBSJ")
    private Date zbsj; // 中标时间

    @TableField("ZBJ")
    private BigDecimal zbj; // 中标价

    @TableField("WYJ")
    private BigDecimal wyj; // 违约金

    @TableField("CGMLDM")
    private String cgmldm; // 采购目录代码

    @TableField("CGMLMC")
    private String cgmlmc; // 采购目录名称

    @TableField("WPMC")
    private String wpmc; // 物品名称

    @TableField("GKGLBMDM")
    private String gkglbmdm; // 归口管理部门代码

    @TableField("GKGLBMMC")
    private String gkglbmmc; // 归口管理部门名称

    @TableField("BCCGSL")
    private BigDecimal bccgsl; // 本次采购数量

    @TableField("BCCGJE")
    private BigDecimal bccgje; // 本次采购金额

    @TableField("DJ")
    private BigDecimal dj; // 单价

    @TableField("GGXH")
    private String ggxh; // 规格型号

    @TableField("JLDW")
    private String jldw; // 计量单位

    @TableField("YYJE")
    private BigDecimal yyje; // 预算金额

    @TableField("KYJE")
    private BigDecimal kyje; // 可用金额

    @TableField("YYSL")
    private BigDecimal yysl; // 预算数量

    @TableField("KSQSL")
    private BigDecimal ksqsl; // 可申请数量

    @TableField("XMMC")
    private String xmmc; // 项目名称

    @TableField("ZFCG")
    private String zfcg; // 政府采购

    @TableField("JKCP")
    private String jkcp; // 进口产品

    @TableField("BZ")
    private String bz; // 备注

    @TableField("SQMXXH")
    private String sqmxxh; // 申请明细序号

    @TableField("ZBMXXH")
    private String zbmxxh; // 采购招标明细序号

    /**
     * 采购招标申请编号
     */
    @TableField("ZBSQBH")
    private String zbsqbh;

    /**
     * 采购招标申请名称
     */
    @TableField("ZBSQMC")
    private String zbsqmc;

} 