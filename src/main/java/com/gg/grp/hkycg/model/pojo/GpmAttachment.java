package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 附件信息实体类
 */
@Data
@TableName("GPM_ATTACHMENT")
public class GpmAttachment {
    
    /**
     * 附件ID，使用UUID
     */
    @TableId("FJID")
    private String fjid;
    
    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;
    
    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;
    
    /**
     * 附件名称(原始文件名)
     */
    @TableField("FJMC")
    private String fjmc;
    
    /**
     * 附件存储文件名
     */
    @TableField("FJCCMC")
    private String fjccmc;
    
    /**
     * 附件类型(MIME类型)
     */
    @TableField("FJLX")
    private String fjlx;
    
    /**
     * 附件大小(KB)
     */
    @TableField("FJDX")
    private BigDecimal fjdx;
    
    /**
     * 附件存储路径
     */
    @TableField("FJLJ")
    private String fjlj;
    
    /**
     * 录入人ID
     */
    @TableField("LRR_ID")
    private String lrrId;
    
    /**
     * 录入人
     */
    @TableField("LRR")
    private String lrr;
    
    /**
     * 录入日期
     */
    @TableField("LR_RQ")
    private Date lrRq;
    
    /**
     * 修改人ID
     */
    @TableField("XGR_ID")
    private String xgrId;
    
    /**
     * 修改人
     */
    @TableField("XGR")
    private String xgr;
    
    /**
     * 修改日期
     */
    @TableField("XG_RQ")
    private Date xgRq;
} 