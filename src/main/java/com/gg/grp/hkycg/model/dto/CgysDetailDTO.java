package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购验收明细DTO
 */
@Data
public class CgysDetailDTO {

    /**
     * 明细编号（主键）
     */
    private String ysmxxh;

    /**
     * 结果内容编号
     */
    private String jgnrbh;

    /**
     * 验收申请编号（外键）
     */
    private String ysbh;

    /**
     * 标段编号
     */
    private String bdbh;

    /**
     * 标段名称
     */
    private String bdmc;

    /**
     * 标段单价
     */
    private BigDecimal bddj;

    /**
     * 标段数量
     */
    private BigDecimal bdsl;

    /**
     * 标段总额
     */
    private BigDecimal bdze;

    /**
     * 本次验收数量
     */
    private BigDecimal bcysssl;

    /**
     * 本次验收总额
     */
    private BigDecimal bcyssze;

    /**
     * 已验收数量
     */
    private BigDecimal yysssl;

    /**
     * 已验收总额
     */
    private BigDecimal yyssze;

    /**
     * 供应商
     */
    private String gys;

    /**
     * 物品(服务)名称
     */
    private String wpmc;

    /**
     * 规格型号
     */
    private String gxth;

    /**
     * 计量单位
     */
    private String jldw;

    /**
     * 品牌明细
     */
    private String pmmc;

    /**
     * 厂商
     */
    private String cs;

    /**
     * 产地
     */
    private String cd;

    /**
     * 品牌
     */
    private String pp;

    /**
     * 采购品目代码
     */
    private String cgmldm;

    /**
     * 采购品目名称
     */
    private String cgmlmc;

    /**
     * 归口管理部门代码
     */
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    private String gkglbmmc;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 备注
     */
    private String bz;

    /**
     * 政府采购
     */
    private Boolean zfcg;

    /**
     * 进口产品
     */
    private Boolean jkcp;

    /**
     * 是否采购结果引入
     */
    private Boolean sfcgjgyr;

    /**
     * 采购结果编号
     */
    private String jgdjbh;

    /**
     * 采购结果名称
     */
    private String jgdjmc;
}