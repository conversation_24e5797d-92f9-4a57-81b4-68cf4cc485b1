package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 授权表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_SQB")
public class GpmSqb {

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 申请人
     */
    @TableField("SQREN")
    private String sqren;

    /**
     * 被申请人
     */
    @TableField("BSQREN")
    private String bsqren;

    /**
     * 部门代码
     */
    @TableField("BMDM")
    private String bmdm;

    /**
     * 项目代码
     */
    @TableField("XMDM")
    private String xmdm;

    /**
     * 办公厅议申请
     */
    @TableField("BGLYSQ")
    private String bglysq;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 指标ID
     */
    @TableField("ZBID")
    private String zbid;

    /**
     * 截止日期
     */
    @TableField("JZRQ")
    private String jzrq;

    /**
     * 使用权限
     */
    @TableField("SYQX")
    private String syqx;

    /**
     * 申请额度
     */
    @TableField("SQED")
    private BigDecimal sqed;

    /**
     * 其他限制项目代码
     */
    @TableField("QTXZXMDM")
    private String qtxzxmdm;

    /**
     * 角色代码
     */
    @TableField("ROLECODE")
    private String rolecode;

    /**
     * 申请日期
     */
    @TableField("SQRQ")
    private String sqrq;

    /**
     * 申请备注
     */
    @TableField("SQBZ")
    private String sqbz;
} 