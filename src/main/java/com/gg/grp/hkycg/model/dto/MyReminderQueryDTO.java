package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 我的被催办单据查询条件DTO
 */
@Data
public class MyReminderQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 单据号
     */
    private String billNo;
    
    /**
     * 单据类型
     */
    private String billType;
    
    /**
     * 催办状态
     * 0-未处理
     * 1-已处理
     */
    private String reminderResult;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
} 