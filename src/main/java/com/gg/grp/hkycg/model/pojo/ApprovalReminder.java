package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 审批催办记录实体类
 * 对应数据表：GPM_REMINDER
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("GPM_REMINDER")
public class ApprovalReminder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "GRID", type = IdType.AUTO)
    private String grid;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 单据号
     */
    @TableField("DJBH")
    private String djbh;

    /**
     * 单据类型编码
     */
    @TableField("DJLX")
    private String djlx;

    /**
     * 催办时间
     */
    @TableField("CBSJ")
    private String reminderTime;

    /**
     * 催办人编码
     */
    @TableField("CBRDM")
    private String reminderUserDm;

    /**
     * 催办人姓名
     */
    @TableField("CBR")
    private String reminderUser;

    /**
     * 催办接收人编码
     */
    @TableField("BCBRDM")
    private String receiverDm;

    /**
     * 催办接收人姓名
     */
    @TableField("BCBR")
    private String receiver;

    /**
     * 催办内容
     */
    @TableField("CBNR")
    private String reminderContent;

    /**
     * 催办结果
     * 0-未处理
     * 1-已处理
     */
    @TableField("CBZT")
    private String reminderResult;

    /**
     * 审批节点名称
     */
    @TableField("SHJD")
    private String nodeName;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private String createTime;

    /**
     * 创建时间
     */
    @TableField("UPDATE_TIME")
    private String updateTime;

    /**
     * 重发次数
     */
    @TableField("CFCS")
    private Integer cfcs;

} 