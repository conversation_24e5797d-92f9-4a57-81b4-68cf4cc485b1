package com.gg.grp.hkycg.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 采购计划保存DTO
 */
@Data
public class CgjhSaveDTO {

    /**
     * 基础信息
     */
    @Valid
    @NotNull(message = "基础信息不能为空")
    private CgjhBaseInfoDTO baseInfo;

    /**
     * 多明细结构 - 每个明细包含自己的预算指标
     */
    @Valid
    @NotNull(message = "采购计划明细不能为空")
    private List<CgjhDetailWithBudgetDTO> detailsWithBudget;
} 