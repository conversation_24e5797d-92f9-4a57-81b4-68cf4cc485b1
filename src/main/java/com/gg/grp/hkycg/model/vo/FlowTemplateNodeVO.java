package com.gg.grp.hkycg.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlowTemplateNodeVO {
    /**
     * 节点代码
     */
    private Integer jddm;
    /**
     * 节点名称
     */
    private String jdmc;
    /**
     * 节点审核条件
     */
    private String jdshtj;

    /**
     * 序号
     */
    private String xh;

    /**
     * 节点内的审核人
     */
    private List<FlowTemplateAuditorVO> nodeAuditorList;

    public FlowTemplateNodeVO(Integer jddm) {
        this.jddm = jddm;
    }
}
