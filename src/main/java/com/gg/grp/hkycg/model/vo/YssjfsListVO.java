package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 预算审计方式列表VO
 */
@Data
public class YssjfsListVO {
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 预算审计方式代码
     */
    private String yssjfsdm;
    
    /**
     * 预算审计方式名称
     */
    private String yssjfsmc;
    
    /**
     * 使用状态
     */
    private String syzt;
    
    /**
     * 使用状态名称
     */
    private String syztmc;
    
    /**
     * 助记码
     */
    private String zjm;
    
    /**
     * 备注
     */
    private String bz;
    
    /**
     * 排序号
     */
    private Integer pxh;
    
    /**
     * 级次
     */
    private String jc;

    /**
     * 总记录数（分页用）
     */
    @JsonIgnore
    private Long totalCount;
} 