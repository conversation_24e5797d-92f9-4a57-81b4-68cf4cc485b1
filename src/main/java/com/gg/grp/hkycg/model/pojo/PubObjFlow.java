package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作流程实体类
 * 对应数据表：PUB_OBJ_FLOW
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PUB_OBJ_FLOW")
public class PubObjFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程ID
     */
    @TableField("POF_ID")
    private String pofId;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 模块代码
     */
    @TableField("MODCODE")
    private String modcode;

    /**
     * 单据类型
     */
    @TableField("DJLX")
    private String djlx;

    /**
     * 单据号
     */
    @TableField("DJH")
    private String djh;

    /**
     * 流程代码
     */
    @TableField("FLOWCODE")
    private String flowcode;

    /**
     * 审核人1
     */
    @TableField("SHR")
    private String shr1;

    /**
     * 审核人名称
     */
    @TableField("SHRMC")
    private String shrmc;

    /**
     * 审核标志
     */
    @TableField("AUDIT_FLAG")
    private String auditFlag;

    /**
     * 审核后标志
     */
    @TableField("AUDIT_AFT_FLAG")
    private String auditAftFlag;

    /**
     * 是否审核
     */
    @TableField("ISAUDIT")
    private String isaudit;

    /**
     * 具体审核人
     */
    @TableField("specificCheckPerson")
    private String specificCheckPerson;

    @TableField("node_name")
    private String nodeName;

}
