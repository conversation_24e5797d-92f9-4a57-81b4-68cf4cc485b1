package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 调整单主表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GBI_TZDML")
public class GbiTzdml {

    /**
     * 调整单ID
     */
    @TableId("TZDID")
    private Integer tzdid;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 调整单号
     */
    @TableField("TZDH")
    private String tzdh;

    /**
     * 调整单状态
     */
    @TableField("TZDZT")
    private Integer tzdzt;

    /**
     * 调整日期
     */
    @TableField("TZRQ")
    private String tzrq;

    /**
     * 指标类别
     */
    @TableField("ZBLB")
    private String zblb;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 附件张数
     */
    @TableField("FJZS")
    private Integer fjzs;

    /**
     * 申请人ID
     */
    @TableField("SQRID")
    private String sqrid;

    /**
     * 申请人
     */
    @TableField("SQR")
    private String sqr;

    /**
     * 制单人ID
     */
    @TableField("ZDRID")
    private Integer zdrid;

    /**
     * 制单人
     */
    @TableField("ZDR")
    private String zdr;

    /**
     * 制单日期
     */
    @TableField("ZDRQ")
    private String zdrq;

    /**
     * 审核人ID
     */
    @TableField("SHRID")
    private Integer shrid;

    /**
     * 审核人
     */
    @TableField("SHR")
    private String shr;

    /**
     * 审核日期
     */
    @TableField("SHRQ")
    private String shrq;

    /**
     * 作废人ID
     */
    @TableField("ZFRID")
    private Integer zfrid;

    /**
     * 作废人
     */
    @TableField("ZFR")
    private String zfr;

    /**
     * 作废日期
     */
    @TableField("ZFRQ")
    private String zfrq;

    /**
     * 凭证号
     */
    @TableField("IDPZH")
    private String idpzh;

    /**
     * 凭证业务日期
     */
    @TableField("PZYWRQ")
    private String pzywrq;

    /**
     * 凭证人ID
     */
    @TableField("PZRID")
    private Integer pzrid;

    /**
     * 凭证人
     */
    @TableField("PZR")
    private String pzr;

    /**
     * 凭证日期
     */
    @TableField("PZRQ")
    private String pzrq;

    /**
     * 调整单号
     */
    @TableField("IDTZDH")
    private String idtzdh;

    /**
     * 单据类型
     */
    @TableField("DJLX")
    private String djlx;

    /**
     * 数据来源
     */
    @TableField("SJLY")
    private String sjly;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 项目类型
     */
    @TableField("xmlx")
    private String xmlx;
} 