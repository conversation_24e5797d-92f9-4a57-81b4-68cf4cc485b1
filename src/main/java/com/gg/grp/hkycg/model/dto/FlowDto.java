package com.gg.grp.hkycg.model.dto;

import com.gg.grp.hkycg.common.enums.BillnoType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class FlowDto {
    /**
     * 单据号
     */
    private String djh;
    /**
     * 单据类型
     */
    private BillnoType billType;
    /**
     * 当前操作的审核节点
     */
    private Integer currShjd;
    /**
     * 当前操作的审核节点名称
     */
    private String currShjdName;
    /**
     * 审核意见
     */
    private String checkOpinion;
    /**
     * 下一审核节点
     */
    private Integer nextShjd;
    /**
     * 单据金额
     */
    private BigDecimal je;

    /**
     * 用于办件库发送mq消息判断，是否是连续审核
     * true-连续  false-不连续
     */
    private Boolean checkFlag;

    /**
     * 连续审核时，配合办件库修改上一个发送的mq消息流程的状态
     */
    private Integer auditFlag;
}
