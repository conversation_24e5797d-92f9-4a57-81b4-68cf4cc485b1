package com.gg.grp.hkycg.model.dto;

import lombok.Data;

/**
 * 统一审核查询DTO
 * 用于查询采购计划、采购申请和采购招标申请的审核列表
 */
@Data
public class UnifiedAuditQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页数量
     */
    private Integer size = 10;
    
    /**
     * 查询条件（可用于单据名称、申请人等的模糊查询）
     */
    private String condition;
    
    /**
     * 单据类型
     * (e.g., "采购计划", "采购申请", "招标申请", "采购结果")
     */
    private String billType;
    
    /**
     * 开始日期 (格式: yyyy-MM-dd)
     */
    private String startDate;
    
    /**
     * 结束日期 (格式: yyyy-MM-dd)
     */
    private String endDate;
    
    /**
     * 单据类型（CGJH-采购计划，CGSQ-采购申请，CGZBSQ-采购招标申请，ALL-全部）
     */
    private String djlx = "ALL";
    
    /**
     * 单据名称（模糊查询）
     */
    private String djmc;
    
    /**
     * 申请人（模糊查询）
     */
    private String sqr;
    
    /**
     * 单据状态（2-已提交，3-审核中）
     */
    private Integer zt;
    
    /**
     * 公司代码（可选，默认使用当前登录用户公司）
     */
    private String gsdm;
    
    /**
     * 会计年度（可选，默认使用当前登录用户年度）
     */
    private String kjnd;
} 