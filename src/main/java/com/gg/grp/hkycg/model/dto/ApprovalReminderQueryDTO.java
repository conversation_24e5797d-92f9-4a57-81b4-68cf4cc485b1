package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 审批催办查询条件DTO
 */
@Data
public class ApprovalReminderQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 单据号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 单据类型
     * 可选值：
     * - CGJH：采购计划
     * - CGSQ：采购申请
     * - CGZB：采购招标
     * - CGJG：采购结果
     * - CGYS：采购验收
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 单据状态
     * 可选值：
     * - 2：已提交
     * - 3：审核中
     */
    @JsonProperty("zt")
    private String status;
    
    /**
     * 审核节点名称
     */
    private String nodeName;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
    
    /**
     * 滞留开始时间
     * 只显示在此时间之后的滞留单据
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stagnationStartTime;
    
    /**
     * 是否显示已催办的单据
     * true: 只显示已催办的单据
     * false: 只显示未催办的单据
     * 默认为false
     */
    @JsonProperty("sfcb")
    private Boolean showReminded = false;
} 