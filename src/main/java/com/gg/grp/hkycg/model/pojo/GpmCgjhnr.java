package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 采购计划内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGJHNR")
public class GpmCgjhnr {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("JHBH")
    private String jhbh; // 计划编号

    @TableField("JHMXXH")
    private String jhmxxh; // 明细序号

    @TableField("CGMLDM")
    private String cgmldm; // 采购品目代码

    @TableField("CGMLMC")
    private String cgmlmc; // 采购品目名称

    @TableField("WPMC")
    private String wpmc; // 物品名称

    @TableField("GGXH")
    private String ggxh; // 规格型号

    @TableField("JLDW")
    private String jldw; // 计量单位

    @TableField("WPSL")
    private BigDecimal wpsl; // 物品数量

    @TableField("WPDJ")
    private BigDecimal wpdj; // 物品单价

    @TableField("JE")
    private BigDecimal je; // 金额

    @TableField("BZ")
    private String bz; // 备注

    @TableField("DKFlag")
    private String dkFlag; // 打款标志

    @TableField("DKJHID")
    private String dkjhid; // 打款计划ID

    @TableField("DKJHXH")
    private BigDecimal dkjhxh; // 打款计划序号

    @TableField("DKYSDWDM")
    private String dkysdwdm; // 打款预算单位代码

    @TableField("DKYSDWMC")
    private String dkysdwmc; // 打款预算单位名称

    @TableField("JHNRCGFSDM")
    private String jhnrcgfsdm; // 采购方式代码

    @TableField("JHNRCGFSMC")
    private String jhnrcgfsmc; // 采购方式名称

    @TableField("SFJK")
    private String sfjk; // 是否借款

    /**
     * 数量上限
     */
    @TableField("SLSX")
    private BigDecimal slsx;

    /**
     * 预算上限
     */
    @TableField("YSSX")
    private BigDecimal yssx;

    /**
     * 计划单价
     */
    @NotNull(message = "计划单价不能为空")
    @TableField("JHDJ")
    private BigDecimal jhdj;

    /**
     * 计划数量
     */
    @NotNull(message = "计划数量不能为空")
    @TableField("JHSL")
    private BigDecimal jhsl;

    /**
     * 计划金额
     */
    @NotNull(message = "计划金额不能为空")
    @TableField("JHJE")
    private BigDecimal jhje;

    /**
     * 预算指标
     */
    @TableField("YSZB")
    private String yszb;

    /**
     * 归口管理部门
     */
    @TableField("GKGLBMDM")
    private String gkglbmdm;

    /**
     * 归口管理部门
     */
    @TableField("GKGLBMMC")
    private String gkglbmmc;

    /**
     * 是否固定资产
     */
    @TableField("SFGDZC")
    private Boolean sfGdzc;

    /**
     * 是否进口产品
     */
    @TableField("SFJKCP")
    private Boolean sfJkcp;

    /**
     * 是否涉密
     */
    @TableField("SFSM")
    private Boolean sfSm;

    /**
     * 是否协议供货
     */
    @TableField("SFXYGH")
    private Boolean sfXygh;

    /**
     * 是否政府采购
     */
    @TableField("SFZFCG")
    private Boolean sfZfcg;

    /**
     * 创建时间
     */
    @TableField("CJSJ")
    private String cjsj;

    /**
     * 更新时间
     */
    @TableField("GXSJ")
    private String gxsj;

} 