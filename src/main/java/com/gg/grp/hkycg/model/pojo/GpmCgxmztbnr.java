package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购项目招投标内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGXMZTBNR")
public class GpmCgxmztbnr {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("XMID")
    private String xmid; // 项目ID

    @TableField("BH")
    private BigDecimal bh; // 编号

    @TableField("ZBFZR")
    private String zbfzr; // 招标负责人

    @TableField("GGNR")
    private String ggnr; // 公告内容

    @TableField("PBDW")
    private String pbdw; // 评标单位

    @TableField("PBSJ")
    private String pbsj; // 评标时间

    @TableField("PBDD")
    private String pbdd; // 评标地点

    @TableField("PBFZR")
    private String pbfzr; // 评标负责人

    @TableField("PW")
    private String pw; // 评委

    @TableField("ZBDW")
    private String zbdw; // 招标单位

    @TableField("ZBDWDM")
    private String zbdwdm; // 招标单位代码

    @TableField("ZBSJ")
    private String zbsj; // 招标时间

    @TableField("addr")
    private String addr; // 地址

    @TableField("ZBJE")
    private BigDecimal zbje; // 招标金额

    @TableField("ZBGG")
    private String zbgg; // 招标规格

    @TableField("LXFS")
    private String lxfs; // 联系方式

    @TableField("TBR")
    private String tbr; // 投标人
} 