package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购申请计划明细表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGSQNR")
public class GpmCgsqnr {

    /**
     * 申请编号（外键）
     */
    @TableField("SQBH")
    private String sqbh;

    /**
     * 申请明细编号
     */
    @TableId("SQMXXH")
    private String sqmxxh;

    /**
     * 物品(服务)名称
     */
    @TableField("WPMC")
    private String wpmc;

    /**
     * 归口管理部门代码
     */
    @TableField("GKGLBMDM")
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    @TableField("GKGLBMMC")
    private String gkglbmmc;

    /**
     * 采购品目代码
     */
    @TableField("CGMLDM")
    private String cgmldm;

    /**
     * 采购品目名称
     */
    @TableField("CGMLMC")
    private String cgmlmc;

    /**
     * 计划名称
     */
    @TableField("JHMC")
    private String jhmc;

    /**
     * 本次采购数量
     */
    @TableField("BCCGSL")
    private Integer bccgsl;

    /**
     * 本次采购金额
     */
    @TableField("BCCGJE")
    private BigDecimal bccgje;

    /**
     * 单价
     */
    @TableField("DJ")
    private Integer dj;

    /**
     * 规格型号
     */
    @TableField("GGXH")
    private String ggxh;

    /**
     * 计量单位
     */
    @TableField("JLDW")
    private String jldw;

    /**
     * 计划金额
     */
    @TableField("JHJE")
    private BigDecimal jhje;

    /**
     * 已用金额
     */
    @TableField("YYJE")
    private BigDecimal yyje;

    /**
     * 可用金额
     */
    @TableField("KYJE")
    private BigDecimal kyje;

    /**
     * 计划审定数量
     */
    @TableField("JHSDSL")
    private Integer jhsdsl;

    /**
     * 已用数量
     */
    @TableField("YYSL")
    private Integer yysl;

    /**
     * 可申请数量
     */
    @TableField("KSQSL")
    private Integer ksqsl;

    /**
     * 项目名称
     */
    @TableField("XMMC")
    private String xmmc;

    /**
     * 政府采购
     */
    @TableField("ZFCG")
    private String zfcg;

    /**
     * 进口产品
     */
    @TableField("JKCP")
    private String jkcp;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 创建时间
     */
    @TableField("CJSJ")
    private String cjsj;

    /**
     * 更新时间
     */
    @TableField("GXSJ")
    private String gxsj;

    /**
     * 创建人代码
     */
    @TableField("CJRDM")
    private String cjrdm;

    /**
     * 创建人名称
     */
    @TableField("CJRMC")
    private String cjrmc;

    /**
     * 更新人代码
     */
    @TableField("GXRDM")
    private String gxrdm;

    /**
     * 更新人名称
     */
    @TableField("GXRMC")
    private String gxrmc;
} 