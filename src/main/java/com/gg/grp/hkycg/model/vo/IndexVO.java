package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算指标查询结果VO
 */
@Data
public class IndexVO {
    
    /**
     * 指标ID
     */
    private Integer zbid;
    
    /**
     * 公司代码
     */
    private String gsdm;
    
    /**
     * 会计年度
     */
    private String kjnd;
    
    /**
     * 指标代码
     */
    private String zbdm;
    
    /**
     * 指标类型
     */
    private String zblx;
    
    /**
     * 真实指标代码
     */
    private String realIndexCode;
    
    /**
     * 部门代码
     */
    private String bmdm;
    
    /**
     * 部门名称
     */
    private String bmmc;
    
    /**
     * 职员代码
     */
    private String zydm;
    
    /**
     * 职员姓名
     */
    private String zymc;
    
    /**
     * 项目代码
     */
    private String xmdm;
    
    /**
     * 项目名称
     */
    private String xmmc;
    
    /**
     * 项目类别代码
     */
    private String projectClassifyCode;
    
    /**
     * 项目类别名称
     */
    private String projectClassifyName;
    
    /**
     * 功能科目代码
     */
    private String funcSubjectCode;
    
    /**
     * 功能科目名称
     */
    private String funcSubjectName;
    
    /**
     * 经济科目代码
     */
    private String economicSubjectCode;
    
    /**
     * 经济科目名称
     */
    private String economicSubjectName;
    
    /**
     * 指标来源代码
     */
    private String indexSourceCode;
    
    /**
     * 指标来源名称
     */
    private String indexSourceName;
    
    /**
     * 余额（可用金额）
     */
    private BigDecimal residual;
    
    /**
     * 指标金额
     */
    private BigDecimal amt;
    
    /**
     * 预算金额
     */
    private BigDecimal YSJE;
    
    /**
     * 年初余额
     */
    private BigDecimal NCYSY;
    
    /**
     * 登记金额
     */
    private BigDecimal DJJE;
    
    /**
     * 状态
     */
    private String state;
    
    /**
     * 模块
     */
    private String module;
    
    /**
     * 录入人代码
     */
    private Integer inputCode;
    
    /**
     * 录入人姓名
     */
    private String inputName;
    
    /**
     * 录入日期
     */
    private String inputDate;
    
    /**
     * 录入时间
     */
    private String inputTime;
    
    /**
     * 审核人代码
     */
    private Integer approverCode;
    
    /**
     * 审核人姓名
     */
    private String approverName;
    
    /**
     * 审核日期
     */
    private String approverDate;
    
    /**
     * 审核时间
     */
    private String approverTime;
    
    /**
     * 批复人ID
     */
    private Integer PFRID;
    
    /**
     * 批复人
     */
    private String PFR;
    
    /**
     * 批复日期
     */
    private String PFRQ;
    
    /**
     * 批复时间
     */
    private String PFSJ;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 备注2
     */
    private String remarks;
    
    /**
     * 预算方案代码
     */
    private String YSFADM;
    
    /**
     * 预算方案名称
     */
    private String YSFAMC;
    
    /**
     * 支出类型代码
     */
    private String expenditureTypeCode;
    
    /**
     * 支出类型名称
     */
    private String expenditureTypeName;
    
    /**
     * 文号代码
     */
    private String WHDM;
    
    /**
     * 文号名称
     */
    private String WHMC;
    
    /**
     * 资金来源代码
     */
    private String ZJLYDM;
    
    /**
     * 资金来源名称
     */
    private String ZJLYMC;
    
    /**
     * 资金性质代码
     */
    private String ZJXZDM;
    
    /**
     * 资金性质名称
     */
    private String ZJXZMC;
    
    /**
     * 结算方式代码
     */
    private String JSFSDM;
    
    /**
     * 结算方式名称
     */
    private String JSFSMC;
    
    /**
     * 支付方式代码
     */
    private String ZFFSDM;
    
    /**
     * 支付方式名称
     */
    private String ZFFSMC;
    
    /**
     * 预算单位代码
     */
    private String YSDWDM;
    
    /**
     * 预算单位名称
     */
    private String YSDWMC;
    
    /**
     * 扩展字段6代码
     */
    private String FZ6DM;
    
    /**
     * 扩展字段6名称
     */
    private String FZ6MC;
    
    /**
     * 扩展字段7代码
     */
    private String FZ7DM;
    
    /**
     * 扩展字段7名称
     */
    private String FZ7MC;
    
    /**
     * 扩展字段8代码
     */
    private String FZ8DM;
    
    /**
     * 扩展字段8名称
     */
    private String FZ8MC;
    
    /**
     * 扩展字段9代码
     */
    private String FZ9DM;
    
    /**
     * 扩展字段9名称
     */
    private String FZ9MC;
    
    /**
     * 扩展字段A代码
     */
    private String FZADM;
    
    /**
     * 扩展字段A名称
     */
    private String FZAMC;
    
    /**
     * 预计比例
     */
    private BigDecimal YJBFB;
    
    /**
     * 承诺书控制方式
     */
    private String CYSKZFS;
    
    /**
     * 是否结转
     */
    private String SFJZ;
    
    /**
     * 指标编号
     */
    private String IDZBBH;
    
    /**
     * 消费状态
     */
    private String XFZT;
    
    /**
     * 真实来源
     */
    private Integer realSource;
    
    /**
     * 备用公开字段
     */
    private String BYGKZ;
    
    /**
     * 行号
     */
    private Integer rownumber;
    
    /**
     * 总记录数（用于分页）
     */
    @JsonIgnore
    private Long totalCount;
} 