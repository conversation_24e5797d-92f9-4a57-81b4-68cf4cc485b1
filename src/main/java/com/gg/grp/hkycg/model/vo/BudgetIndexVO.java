package com.gg.grp.hkycg.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算指标VO
 */
@Data
public class BudgetIndexVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 指标ID
     */
    private Integer indexID;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 指标来源
     */
    private String indexSource;

    /**
     * 经济科目
     */
    private String economicSubject;

    /**
     * 会计科目
     */
    private String accountTitle;

    /**
     * 功能科目
     */
    private String funcSubject;

    /**
     * 索引
     */
    private Integer index;

    /**
     * 项目
     */
    private String project;

    /**
     * fZ9DM
     */
    private String fZ9DM;

    /**
     * 费用备注
     */
    private String expenseRemark;

    /**
     * 余额
     */
    private BigDecimal residual;

    /**
     * fZ8DM
     */
    private String fZ8DM;

    /**
     * fZ7DM
     */
    private String fZ7DM;

    /**
     * 支出部门
     */
    private String outlayDept;

    /**
     * 员工
     */
    private String employee;

    /**
     * cyskzfs
     */
    private Integer cyskzfs;

    /**
     * 执行方式
     */
    private String executeWay;

    /**
     * fZ9MC
     */
    private String fZ9MC;

    /**
     * fZ8MC
     */
    private String fZ8MC;

    /**
     * fZ7MC
     */
    private String fZ7MC;

    /**
     * 业务ID (保留用于系统内部关联)
     */
    private String lid;

    /**
     * 标识 (保留用于系统内部关联)
     */
    private String bid;

    /**
     * 删除标志 (保留用于系统内部逻辑)
     */
    private Boolean del;

    /**
     * 员工代码 (保留用于系统内部逻辑)
     */
    private String employeeCode;

    /**
     * 公司代码 (保留用于系统内部逻辑)
     */
    private String gsdm;

    /**
     * 会计年度 (保留用于系统内部逻辑)
     */
    private String kjnd;

    /**
     * 员工姓名 (保留用于系统内部逻辑)
     */
    private String employeeName;
} 