package com.gg.grp.hkycg.model.pojo;

import com.gg.grp.hkycg.model.dto.FlowTemplateAuditorDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
public class Fit<PERSON><PERSON>ckNode implements Comparable<FitCheckNode> {
    public FitCheckNode(Integer jddm) {
        this.jddm = jddm;
    }


    /**
     * 节点代码
     */
    private Integer jddm;
    /**
     * 节点名称
     */
    private String jdmc;
    /**
     * 审核人
     * key 职员代码
     * value 职员姓名
     */
    private List<FlowTemplateAuditorDTO> shr;

    @Override
    public int compareTo(@NotNull FitCheckNode otherNode) {
        return this.jddm.compareTo(otherNode.jddm);
    }
}
