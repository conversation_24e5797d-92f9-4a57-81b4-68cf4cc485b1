package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 预算指标表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("OER_YSZB")
public class OerYszb {

    @TableField("MLID")
    private Integer mlid; // 目录ID

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 会计年度

    @TableField("BNXID")
    private Integer bnxid; // 本年序ID

    @TableField("DataID")
    private Integer dataId; // 数据ID

    @TableField("XH")
    private Integer xh; // 序号

    @TableField("ZBDM")
    private String zbdm; // 指标代码

    @TableField("JE")
    private BigDecimal je; // 金额

    @TableField("ZBID")
    private Integer zbid; // 指标ID

    @TableField("XM")
    private String xm;  //项目名称

    @TableField("ZY")
    private String zy; // 摘要

    @TableField("KMDM")
    private String kmdm; // 科目代码

    @TableField("KMMC")
    private String kmmc; // 科目名称

    @TableField("DJLXID")
    private Integer djlxid; // 单据类型ID

    @TableField("zbgnkmdm")
    private String zbgnkmdm; // 指标功能科目代码

    @TableField("zbgnkmmc")
    private String zbgnkmmc; // 指标功能科目名称

    @TableField("zbjjkmdm")
    private String zbjjkmdm; // 指标经济科目代码

    @TableField("zbjjkmmc")
    private String zbjjkmmc; // 指标经济科目名称

    @TableField("kzzbid")
    private Integer kzzbid; // 控制指标ID

    @TableField("ZBFZ6DM")
    private String zbfz6dm; // 指标辅助6代码

    @TableField("ZBFZ6MC")
    private String zbfz6mc; // 指标辅助6名称

    @TableField("ZBFZ7DM")
    private String zbfz7dm; // 指标辅助7代码

    @TableField("ZBFZ7MC")
    private String zbfz7mc; // 指标辅助7名称

    @TableField("ZBFZ8DM")
    private String zbfz8dm; // 指标辅助8代码

    @TableField("ZBFZ8MC")
    private String zbfz8mc; // 指标辅助8名称

    @TableField("ZBFZ9DM")
    private String zbfz9dm; // 指标辅助9代码

    @TableField("ZBFZ9MC")
    private String zbfz9mc; // 指标辅助9名称

    @TableField("ZBFZADM")
    private String zbfzadm; // 指标辅助A代码

    @TableField("ZBFZAMC")
    private String zbfzamc; // 指标辅助A名称

    @TableField("YSKJKMDM")
    private String yskjkmdm; // 预算会计科目代码

    @TableField("YSKJKMMC")
    private String yskjkmmc; // 预算会计科目名称

    @TableField("CZYSZBID")
    private Integer czyszbid; // 冲转预算指标ID

    @TableField("ZBFZ11DM")
    private String zbfz11dm; // 指标辅助11代码

    @TableField("ZBFZ11MC")
    private String zbfz11mc; // 指标辅助11名称

    @TableField("ZBFZ12DM")
    private String zbfz12dm; // 指标辅助12代码

    @TableField("ZBFZ12MC")
    private String zbfz12mc; // 指标辅助12名称

    @TableField("ZBFZ13DM")
    private String zbfz13dm; // 指标辅助13代码

    @TableField("ZBFZ13MC")
    private String zbfz13mc; // 指标辅助13名称

    @TableField("ZBFZ14DM")
    private String zbfz14dm; // 指标辅助14代码

    @TableField("ZBFZ14MC")
    private String zbfz14mc; // 指标辅助14名称

    @TableField("ZBFZ15DM")
    private String zbfz15dm; // 指标辅助15代码

    @TableField("ZBFZ15MC")
    private String zbfz15mc; // 指标辅助15名称

    @TableField("ZBFZ16DM")
    private String zbfz16dm; // 指标辅助16代码

    @TableField("ZBFZ16MC")
    private String zbfz16mc; // 指标辅助16名称

    @TableField("ZBFZ17DM")
    private String zbfz17dm; // 指标辅助17代码

    @TableField("ZBFZ17MC")
    private String zbfz17mc; // 指标辅助17名称

    @TableField("ZBFZ18DM")
    private String zbfz18dm; // 指标辅助18代码

    @TableField("ZBFZ18MC")
    private String zbfz18mc; // 指标辅助18名称

    @TableField("ZBFZ19DM")
    private String zbfz19dm; // 指标辅助19代码

    @TableField("ZBFZ19MC")
    private String zbfz19mc; // 指标辅助19名称

    @TableField("ZBFZ20DM")
    private String zbfz20dm; // 指标辅助20代码

    @TableField("ZBFZ20MC")
    private String zbfz20mc; // 指标辅助20名称

    @TableField("ZBFZ21DM")
    private String zbfz21dm; // 指标辅助21代码

    @TableField("ZBFZ21MC")
    private String zbfz21mc; // 指标辅助21名称

    @TableField("ZBFZ22DM")
    private String zbfz22dm; // 指标辅助22代码

    @TableField("ZBFZ22MC")
    private String zbfz22mc; // 指标辅助22名称

    @TableField("ZBFZ23DM")
    private String zbfz23dm; // 指标辅助23代码

    @TableField("ZBFZ23MC")
    private String zbfz23mc; // 指标辅助23名称

    @TableField("ZBFZ24DM")
    private String zbfz24dm; // 指标辅助24代码

    @TableField("ZBFZ24MC")
    private String zbfz24mc; // 指标辅助24名称

    @TableField("ZBFZ25DM")
    private String zbfz25dm; // 指标辅助25代码

    @TableField("ZBFZ25MC")
    private String zbfz25mc; // 指标辅助25名称

    @TableField("ZBFZ26DM")
    private String zbfz26dm; // 指标辅助26代码

    @TableField("ZBFZ26MC")
    private String zbfz26mc; // 指标辅助26名称

    @TableField("ZBFZ27DM")
    private String zbfz27dm; // 指标辅助27代码

    @TableField("ZBFZ27MC")
    private String zbfz27mc; // 指标辅助27名称

    @TableField("ZBFZ28DM")
    private String zbfz28dm; // 指标辅助28代码

    @TableField("ZBFZ28MC")
    private String zbfz28mc; // 指标辅助28名称

    @TableField("ZBFZ29DM")
    private String zbfz29dm; // 指标辅助29代码

    @TableField("ZBFZ29MC")
    private String zbfz29mc; // 指标辅助29名称

    @TableField("ZBFZ30DM")
    private String zbfz30dm; // 指标辅助30代码

    @TableField("ZBFZ30MC")
    private String zbfz30mc; // 指标辅助30名称

    @TableField("DJBH")
    private String djbh; // 单据编号

    /**
     * 明细序号 - 关联到采购计划明细
     */
    @TableField("MXXH")
    private String mxxh; // 明细序号

}
