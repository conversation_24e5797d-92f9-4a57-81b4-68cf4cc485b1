package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购价格目录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGDJML")
public class GpmCgdjml {

    @TableId("JGDJBH")
    private String jgdjbh; // 结果登记编号

    @TableField("JGDJMC")
    private String jgdjmc; // 结果登记名称

    @TableField("CGBMDM")
    private String cgbmdm; // 采购部门代码

    @TableField("CGBMMC")
    private String cgbmmc; // 采购部门名称

    @TableField("CGFSDM")
    private String cgfsdm; // 采购方式代码

    @TableField("CGFSMC")
    private String cgfsmc; // 采购方式名称

    @TableField("ZJJGDM")
    private String zjjgdm; // 中介机构代码

    @TableField("ZJJGMC")
    private String zjjgmc; // 中介机构名称

    @TableField("JBR")
    private String jbr; // 经办人

    @TableField("ZBFZR")
    private String zbfzr; // 指标负责人

    @TableField("CGSQJE")
    private BigDecimal cgsqje; // 采购申请金额

    @TableField("YYJE")
    private BigDecimal yyje; // 预算金额

    @TableField("KYJE")
    private BigDecimal kyje; // 可用金额

    @TableField("JGDJJE")
    private BigDecimal jgdjje; // 结果登记金额

    @TableField("CGLY")
    private String cgly; // 采购理由

    @TableField("BZ")
    private String bz; // 备注

    @TableField("CREATE_TIME")
    private String createTime; // 创建时间

    @TableField("UPDATE_TIME")
    private Date updateTime; // 更新时间

    @TableField("CREATE_USER")
    private String createUser; // 创建用户

    @TableField("UPDATE_USER")
    private String updateUser; // 更新用户

    @TableField("CREATE_USERDM")
    private String createUserDm; // 创建用户代码

    @TableField("UPDATE_USERDM")
    private String updateUserDm; // 更新用户代码

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 会计年度

    @TableField("ZT")
    private String zt; // 状态
} 