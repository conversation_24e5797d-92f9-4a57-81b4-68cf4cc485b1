package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作流程配置实体类
 * 对应数据表：PUBWORKFLOW
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PUBWORKFLOW")
public class PubWorkflow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 流程代码
     */
    @TableField("FLOWCODE")
    private String flowcode;

    /**
     * 流程名称
     */
    @TableField("FLOWNAME")
    private String flowname;

    /**
     * 模块名称
     */
    @TableField("MODNAME")
    private String modname;

    /**
     * 业务代码
     */
    @TableField("BIZCODE")
    private String bizcode;

    /**
     * 业务名称
     */
    @TableField("BIZNAME")
    private String bizname;

    /**
     * 是否启动
     */
    @TableField("ISSTART")
    private String isstart;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;
} 