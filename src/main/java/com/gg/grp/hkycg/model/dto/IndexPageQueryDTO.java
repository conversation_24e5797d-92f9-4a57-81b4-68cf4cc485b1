package com.gg.grp.hkycg.model.dto;

import lombok.Data;

/**
 * 预算指标分页查询参数DTO
 */
@Data
public class IndexPageQueryDTO {
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 10;
    
    /**
     * 查询条件（模糊搜索，可匹配指标ID、部门名称、项目名称、摘要、指标代码、指标名称）
     */
    private String condition;
    
    // 为了兼容参考代码中的参数名
    public Integer getPageNum() {
        return this.size;
    }
    
    public void setPageNum(Integer pageNum) {
        this.size = pageNum;
    }
    
    public Integer getPages() {
        return this.current;
    }
    
    public void setPages(Integer pages) {
        this.current = pages;
    }
} 