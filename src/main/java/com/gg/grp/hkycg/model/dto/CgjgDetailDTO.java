package com.gg.grp.hkycg.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购结果明细DTO
 */
@Data
public class CgjgDetailDTO {

    /**
     * 结果内容编号（主键）
     */
    private String jgnrbh;

    /**
     * 结果登记编号（外键）
     */
    private String jgdjbh;

    /**
     * 是否采购申请引入
     */
    private Boolean sfcgsqyr;

    /**
     * 是否采购招标引入
     */
    private Boolean sfcgzbyr;

    /**
     * 采购申请编号
     */
    private String cgsqbh;

    /**
     * 采购申请明细序号
     */
    private String sqmxxh;

    /**
     * 采购申请名称
     */
    private String cgsqmc;

    /**
     * 采购招标编号
     */
    private String cgzbbh;

    /**
     * 采购招标申请编号
     */
    private String zbsqbh;

    /**
     * 采购招标申请名称
     */
    private String zbsqmc;

    /**
     * 采购招标明细序号
     */
    private String zbmxxh;

    /**
     * 采购招标名称
     */
    private String cgzbmc;

    /**
     * 中标供应商
     */
    private String zbgys;

    /**
     * 中标金额
     */
    private BigDecimal zbje;

    /**
     * 中标时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String zbsj;

    /**
     * 中标价
     */
    private BigDecimal zbj;

    /**
     * 违约金
     */
    private BigDecimal wyj;

    /**
     * 采购目录代码
     */
    private String cgmldm;

    /**
     * 采购目录名称
     */
    private String cgmlmc;

    /**
     * 物品名称
     */
    private String wpmc;

    /**
     * 归口管理部门代码
     */
    private String gkglbmdm;

    /**
     * 归口管理部门名称
     */
    private String gkglbmmc;

    /**
     * 本次采购数量
     */
    private BigDecimal bccgsl;

    /**
     * 本次采购金额
     */
    private BigDecimal bccgje;

    /**
     * 单价
     */
    private BigDecimal dj;

    /**
     * 规格型号
     */
    private String ggxh;

    /**
     * 计量单位
     */
    private String jldw;

    /**
     * 预算金额
     */
    private BigDecimal yyje;

    /**
     * 可用金额
     */
    private BigDecimal kyje;

    /**
     * 预算数量
     */
    private BigDecimal yysl;

    /**
     * 可申请数量
     */
    private BigDecimal ksqsl;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 政府采购
     */
    private Boolean zfcg;

    /**
     * 进口产品
     */
    private Boolean jkcp;

    /**
     * 备注
     */
    private String bz;
}
