package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 采购项目验收内容表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_CGXMYSNR")
public class GpmCgxmysnr {

    @TableField("GSDM")
    private String gsdm; // 公司代码

    @TableField("KJND")
    private String kjnd; // 年度

    @TableField("XMYSBH")
    private Integer xmysbh; // 验收编号

    @TableField("XMYSXH")
    private Integer xmysxh; // 验收序号

    @TableField("XMXH")
    private String xmxh; // 项目序号

    @TableField("CGMLDM")
    private String cgmldm; // 采购品目代码

    @TableField("CGMLMC")
    private String cgmlmc; // 采购品目名称

    @TableField("WPMC")
    private String wpmc; // 物品名称

    @TableField("GGXH")
    private String ggxh; // 规格型号

    @TableField("WPLB")
    private String wplb; // 物品类别

    @TableField("WPCD")
    private String wpcd; // 产地

    @TableField("WPSJ")
    private String wpsj; // 商家

    @TableField("WPPP")
    private String wppp; // 品牌

    @TableField("PPMX")
    private String ppmx; // 品牌明细

    @TableField("JLDW")
    private String jldw; // 计量单位

    @TableField("HSDW")
    private String hsdw; // 换算单位

    @TableField("WPSL")
    private Integer wpsl; // 物品数量

    @TableField("WPDJ")
    private BigDecimal wpdj; // 物品单价

    @TableField("JE")
    private BigDecimal je; // 金额

    @TableField("ZCKP")
    private String zckp; // 资产卡片

    @TableField("WZRKD")
    private String wzrkd; // 物资入库单

    @TableField("SLv")
    private String slv; // 数量

    @TableField("WSDJ")
    private BigDecimal wsdj; // 无税单价

    @TableField("ISDY")
    private Integer isdy; // 是否打印

    @TableField("CGCustNo")
    private String cgCustNo; // 客户编号

    @TableField("CGCustName")
    private String cgCustName; // 客户名称
} 