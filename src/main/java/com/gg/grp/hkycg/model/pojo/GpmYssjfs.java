package com.gg.grp.hkycg.model.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预算审计方式表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GPM_YSSJFS")
public class GpmYssjfs {

    @TableField("YSSJFSID")
    private String yssjfsid;

    /**
     * 公司代码
     */
    @TableField("GSDM")
    private String gsdm;

    /**
     * 会计年度
     */
    @TableField("KJND")
    private String kjnd;

    /**
     * 预算审计方式代码
     */
    @TableId("YSSJFSDM")
    private String yssjfsdm;

    /**
     * 预算审计方式名称
     */
    @TableField("YSSJFSMC")
    private String yssjfsmc;

    /**
     * 使用状态
     */
    @TableField("SYZT")
    private String syzt;

    /**
     * 助记码
     */
    @TableField("ZJM")
    private String zjm;

    /**
     * 备注
     */
    @TableField("BZ")
    private String bz;

    /**
     * 排序号
     */
    @TableField("PXH")
    private Integer pxh;

    /**
     * 级次
     */
    @TableField("JC")
    private String jc;
} 