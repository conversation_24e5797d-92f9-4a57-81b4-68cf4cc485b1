package com.gg.grp.hkycg.model.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 指标项目表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GBI_ZBXMB")
public class GbiZbxmb {

    /**
     * 指标ID
     */
    @TableField(value = "ZBID")
    private Integer indexID;
    /**
     * 公司代码
     */
    @TableField(value = "GSDM")
    private String corpCode;
    /**
     * 会计年度
     */
    @TableField(value = "KJND")
    private String accountantYear;
    /**
     * 指标代码
     */
    @TableField(value = "ZBDM")
    private String indexCode;
    /**
     * 指标类别
     */
    @TableField(value = "ZBLB")
    private String indexType;
    /**
     * 实际指标ID
     */
    @TableField(value = "SJZBID")
    private String realIndexCode;
    /**
     * 摘要
     */
    @TableField(value = "ZY")
    private String note;
    /**
     * 预算方案代码
     */
    @TableField(value = "YSFADM")
    private String YSFADM;
    /**
     * 预算方案名称
     */
    @TableField(value = "YSFAMC")
    private String YSFAMC;
    /**
     * 经费类型代码
     */
    @TableField(value = "JFLXDM")
    private String expenditureTypeCode;
    /**
     * 经费类型名称
     */
    @TableField(value = "JFLXMC")
    private String expenditureTypeName;

    @TableField(value = "WHDM")
    private String WHDM;

    @TableField(value = "WHMC")
    private String WHMC;
    /**
     * 指标来源代码
     */
    @TableField(value = "ZBLYDM")
    private String indexSourceCode;
    /**
     * 指标来源名称
     */
    @TableField(value = "ZBLYMC")
    private String indexSourceName;

    @TableField(value = "ZJLYDM")
    private String ZJLYDM;

    @TableField(value = "ZJLYMC")
    private String ZJLYMC;

    @TableField(value = "ZJXZDM")
    private String ZJXZDM;

    @TableField(value = "ZJXZMC")
    private String ZJXZMC;

    @TableField(value = "JSFSDM")
    private String JSFSDM;

    @TableField(value = "JSFSMC")
    private String JSFSMC;

    @TableField(value = "ZFFSDM")
    private String ZFFSDM;

    @TableField(value = "ZFFSMC")
    private String ZFFSMC;

    @TableField(value = "YSDWDM")
    private String YSDWDM;

    @TableField(value = "YSDWMC")
    private String YSDWMC;
    /**
     * 部门编码
     */
    @TableField(value = "BMDM")
    private String deptCode;
    /**
     * 部门名称
     */
    @TableField(value = "BMMC")
    private String deptName;
    /**
     * 职员代码
     */
    @TableField(value = "ZYDM")
    private String employeeCode;
    /**
     * 职员姓名 个人
     */
    @TableField(value = "ZYMC")
    private String employeeName;
    /**
     * 项目分类代码
     */
    @TableField(value = "XMFLDM")
    private String projectClassifyCode;
    /**
     * 项目分类名称
     */
    @TableField(value = "XMFLMC")
    private String projectClassifyName;
    /**
     * 项目代码
     */
    @TableField(value = "XMDM")
    private String projectCode;
    /**
     * 项目名称
     */
    @TableField(value = "XMMC")
    private String projectName;
    /**
     * 功能科目代码
     */
    @TableField(value = "GNKMDM")
    private String funcSubjectCode;
    /**
     * 功能科目名称
     */
    @TableField(value = "GNKMMC")
    private String funcSubjectName;
    /**
     * 经济科目代码
     */
    @TableField(value = "JJKMDM")
    private String economicSubjectCode;
    /**
     * 经济科目名称
     */
    @TableField(value = "JJKMMC")
    private String economicSubjectName;

    /**
     * 资金来源代码
     */
    @TableField(value = "FZ6DM")
    private String FZ6DM;

    /**
     * 资金来源名称
     */
    @TableField(value = "FZ6MC")
    private String FZ6MC;

    /**
     * 政府采购代码
     */
    @TableField(value = "FZ7DM")
    private String FZ7DM;

    /**
     * 政府采购名称
     */
    @TableField(value = "FZ7MC")
    private String FZ7MC;

    /**
     * 公开招标代码
     */
    @TableField(value = "FZ8DM")
    private String FZ8DM;

    /**
     * 公开招标名称
     */
    @TableField(value = "FZ8MC")
    private String FZ8MC;

    /**
     * 采购内容代码
     */
    @TableField(value = "FZ9DM")
    private String FZ9DM;

    /**
     * 采购内容名称
     */
    @TableField(value = "FZ9MC")
    private String FZ9MC;

    /**
     * 项目类别代码
     */
    @TableField(value = "FZADM")
    private String FZADM;

    /**
     * 项目类别名称
     */
    @TableField(value = "FZAMC")
    private String FZAMC;

    @TableField(value = "ZTH")
    private String ZTH;
    /**
     * 科目代码
     */
    @TableField(value = "KMDM")
    private String subjectCode;
    /**
     * 科目名称
     */
    @TableField(value = "KMMC")
    private String subjectName;
    /**
     * 状态 目前指标审核在客户端操作 zt=3表示审核完成
     */
    @TableField(value = "ZT")
    private String state;


    @TableField(value = "SFZFCG")
    private String SFZFCG;
    /**
     * 金额
     */
    @TableField(value = "JE")
    private Double amt;

    @TableField(value = "NCYSY")
    private Double NCYSY;

    @TableField(value = "DJJE")
    private Double DJJE;
    /**
     * 余额
     */
    @TableField(value = "YE")
    private Double residual;
    /**
     * 期初金额 表中YE字段 ，控制指标比例时查询用字段
     */
    @TableField(exist = false)
    private Double qcje;
    /**
     * 录入人ID
     */
    @TableField(value = "LRRID")
    private Integer inputCode;
    /**
     * 录入人姓名
     */
    @TableField(value = "LRR")
    private String inputName;
    /**
     * 录入日期
     */
    @TableField(value = "LRRQ")
    private String inputDate;
    /**
     * 录入时间
     */
    @TableField(value = "LRSJ")
    private String inputTime;
    /**
     * 审核人代码
     */
    @TableField(value = "SHRID")
    private Integer approverCode;
    /**
     * 审核人姓名
     */
    @TableField(value = "SHR")
    private String approverName;
    /**
     * 审核日期
     */
    @TableField(value = "SHRQ")
    private String approverDate;
    /**
     * 审核时间
     */
    @TableField(value = "SHSJ")
    private String approverTime;

    @TableField(value = "PFRID")
    private Integer PFRID;

    @TableField(value = "PFR")
    private String PFR;

    @TableField(value = "PFRQ")
    private String PFRQ;

    @TableField(value = "PFSJ")
    private String PFSJ;

    @TableField(value = "CurSHJD")
    private Integer CurSHJD;

    @TableField(value = "NextSHJD")
    private Integer NextSHJD;
    /**
     * 业务编号
     */
    @TableField(value = "FlowCode")
    private String FlowCode;

    @TableField(value = "YJBFB")
    private Double YJBFB;

    /**
     * 控制金额是否允许超额
     */
    @TableField(value = "CYSKZFS")
    private String CYSKZFS;

    @TableField(value = "SFJZ")
    private String SFJZ;
    /**
     * 模块
     */
    @TableField(value = "module")
    private String module;
    /**
     * 备注
     */
    @TableField(value = "BZ")
    private String remarks;

    /**
     * 唯一识别码 项目HBG_JFB的唯一识别ID 借用
     */
    @TableField(value = "IDZBBH")
    private String IDZBBH;

    @TableField(value = "XFZT")
    private Integer XFZT;
    /**
     * 实际来源
     */
    @TableField(value = "SJLY")
    private Integer realSource;

    @TableField(value = "BYGKZ")
    private String BYGKZ;
    @TableField(value = "EDWZC")
    private String edwzc;
    /**
     * 预算批复数
     */
    private BigDecimal ysje;
    /**
     * 数量(分页使用)
     */
    @JSONField(serialize = false, deserialize = false)
    @TableField(exist = false)
    private Long total;
} 