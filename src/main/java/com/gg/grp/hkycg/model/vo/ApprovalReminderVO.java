package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 审批催办查询结果VO
 */
@Data
public class ApprovalReminderVO {
    
    /**
    /**
     * 审核节点
     */
    private String nodeName;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlxmc")
    private String billTypeName;
    
    /**
     * 单据名称
     */
    @JsonProperty("djmc")
    private String billName;
    
    /**
     * 单据号
     */
    @JsonProperty("djbh")
    private String billNo;
    
    /**
     * 制单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 送审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;
    
    /**
     * 单据状态
     */
    @JsonProperty("zt")
    private String statusCode;
    
    /**
     * 单据状态名称
     */
    @JsonProperty("ztmc")
    private String statusName;
    
    /**
     * 制单部门
     */
    @JsonProperty("zdbmmc")
    private String createDept;
    
    /**
     * 制单人
     */
    @JsonProperty("zdr")
    private String creator;
    
    /**
     * 下个审核人
     */
    @JsonProperty("xgshr")
    private String nextAuditor;
    
    /**
     * 单据金额
     */
    @JsonProperty("djje")
    private BigDecimal amount;
    
    /**
     * 最后催办时间
     */
    @JsonProperty("cbsj")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String lastReminderTime;
    
    /**
     * 最后催办结果
     */
    @JsonProperty("cbjgzt")
    private String lastReminderResult;

    /**
     * 最后催办结果
     */
    @JsonProperty("cbjgztmc")
    private String lastReminderResultZTMC;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 重发次数
     */
    @JsonProperty("cfcs")
    private Integer cfcs = 0;
    
    /**
     * 当前页数
     */
    @JsonIgnore
    private Long total;
    
    /**
     * 最后审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastAuditTime;
} 