package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 已审核采购计划响应VO
 */
@Data
public class ApprovedPlanVO {
    
    /**
     * 采购截止日期
     */
    @JsonProperty("cgjzrq")
    private String purchaseDeadline;
    
    /**
     * 单据类型
     */
    @JsonProperty("djlx")
    private String billType;
    
    /**
     * 计划编号
     */
    @JsonProperty("jhbh")
    private String planNo;
    
    /**
     * 计划名称
     */
    @JsonProperty("jhmc")
    private String planName;
    
    /**
     * 制单日期
     */
    @JsonProperty("zdrq")
    private String createDate;
    
    /**
     * 单据状态
     */
    @JsonProperty("djzt")
    private String billStatus;
    
    /**
     * 计划部门
     */
    @JsonProperty("jhbm")
    private String planDept;
    
    /**
     * 经办人
     */
    @JsonProperty("jbr")
    private String handler;
    
    /**
     * 申请依据及理由
     */
    @JsonProperty("sqyjjly")
    private String applicationBasisAndReason;
    
    /**
     * 计划金额
     */
    @JsonProperty("jhje")
    private BigDecimal planAmount;
}
