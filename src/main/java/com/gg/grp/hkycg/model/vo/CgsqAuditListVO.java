package com.gg.grp.hkycg.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购申请审核列表VO
 */
@Data
public class CgsqAuditListVO {
    /**
     * 申请编号（主键）
     */
    private String sqbh;

    /**
     * 单据编号（外键）
     */
    private String djbh;

    /**
     * 申请名称
     */
    private String sqmc;

    /**
     * 申请金额
     */
    private BigDecimal sqje;

    /**
     * 申请人
     */
    private String cjrmc;

    /**
     * 申请人代码
     */
    private String cjrdm;

    /**
     * 创建日期
     */
    private String cjrq;

    /**
     * 拟采购日期
     */
    private String ncgrq;

    /**
     * 单据状态
     */
    private BigDecimal zt;

    /**
     * 单据状态名称
     */
    private String ztmc;

    /**
     * 公司代码
     */
    private String gsdm;

    /**
     * 会计年度
     */
    private String kjnd;

    /**
     * 当前审核节点名称
     */
    private String currentNodeName;

    /**
     * 当前审核人
     */
    private String currentAuditor;

    /**
     * 是否可审核（当前用户是否为审核人）
     */
    private Boolean canAudit;

    /**
     * 审核节点序号
     */
    private Integer nodeSeq;

    /**
     * 经办人
     */
    private String jbr;

    /**
     * 申请部门名称
     */
    private String sqbmmc;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 备注
     */
    private String bz;

    /**
     * 采购类型名称
     */
    private String cglxmc;

    /**
     * 单据类型
     */
    private String djlx;

    /**
     * 总记录数（分页用）
     */
    @JsonIgnore
    private Long totalCount;
} 