package com.gg.grp.hkycg.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 限流注解
 * 支持基于IP的限流和黑白名单功能
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    
    /**
     * 限流key前缀，默认使用方法名
     */
    String key() default "";
    
    /**
     * 时间窗口大小（秒），默认60秒
     */
    int window() default 60;
    
    /**
     * 时间窗口内最大请求次数，默认10次
     */
    int maxRequests() default 10;
    
    /**
     * 是否启用IP黑白名单检查，默认启用
     */
    boolean enableBlacklist() default true;
    
    /**
     * 超过限制后的提示信息
     */
    String message() default "请求过于频繁，请稍后再试";
    
    /**
     * 连续失败多少次后加入黑名单（0表示不启用）
     */
    int maxFailures() default 10;
    
    /**
     * 黑名单封禁时间（分钟），默认30分钟
     */
    int blacklistDuration() default 30;
} 