package com.gg.grp.hkycg.filter;

import com.alibaba.fastjson.JSON;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.security.JwtUtils;
import com.gg.grp.hkycg.common.security.UserDetailsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtils jwtUtils;

    private AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        if (requestURI.startsWith(contextPath)) {
            requestURI = requestURI.substring(contextPath.length());
        }

        // 跳过公开路径的JWT验证
        if (isPublicPath(requestURI)) {
            System.out.println("跳过公开路径的JWT验证: " + requestURI);
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String jwt = parseJwt(request);
            if (jwt != null && jwtUtils.validateJwtToken(jwt)) {
                String username = jwtUtils.getUserNameFromJwtToken(jwt);
                
                // 设置Security上下文
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(username, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);

                // 从JWT中解析用户信息并设置到LoginInfo中
                setLoginInfoFromJwt(jwt);
                
            } else {
                response.setContentType("application/json;charset=UTF-8");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                Result error = Result.error("未授权!");
                response.getWriter().write(JSON.toJSONString(error));
                return;
            }
        } catch (Exception e) {
            logger.error("Cannot set user authentication: {}", e);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 从JWT中解析用户信息并设置到LoginInfo
     */
    private void setLoginInfoFromJwt(String jwt) {
        try {
            // 从JWT中获取所有用户信息
            String userCode = jwtUtils.getUserNameFromJwtToken(jwt);
            String employeeName = jwtUtils.getEmployeeNameFromJwtToken(jwt);
            String corpCode = jwtUtils.getCorpCodeFromJwtToken(jwt);
            String deptCode = jwtUtils.getDeptCodeFromJwtToken(jwt);
            String deptName = jwtUtils.getDeptNameFromJwtToken(jwt);
            String accountantYear = jwtUtils.getAccountantYearFromJwtToken(jwt);
            String businessDate = jwtUtils.getBusinessDateFromJwtToken(jwt);
            
            // 设置登录信息到ThreadLocal

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setEmployeeCode(userCode);
            loginInfo.setEmployeeName(employeeName);
            loginInfo.setCorpCode(corpCode);
            loginInfo.setDeptCode(deptCode);
            loginInfo.setDeptName(deptName);
            loginInfo.setAccountantYear(accountantYear);
            loginInfo.setBusiDate(businessDate);
            loginInfo.setCzyId(userCode); // 使用员工代码作为操作员ID
            
            LoginInfo.setLoginInfo(loginInfo);
            
            System.out.println("JWT认证成功，已设置LoginInfo: " + 
                "用户=" + loginInfo.getEmployeeCode() + 
                ", 姓名=" + loginInfo.getEmployeeName() + 
                ", 部门=" + loginInfo.getDeptCode() + "(" + loginInfo.getDeptName() + ")" +
                ", 业务日期=" + loginInfo.getBusiDate());
                
        } catch (Exception e) {
            System.err.println("从JWT设置LoginInfo失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 判断是否为公开路径，这些路径不需要JWT验证
     */
    private boolean isPublicPath(String requestURI) {
        String[] publicPaths = {
                "/api/login", "/api/loginOut", "/api/attachment/download/**", "/api/attachment/preview/**",
                "/#/**","/","/upload/**", "/api/v3/api-docs/**",
                "/swagger-ui/**", "/swagger-resources/**", "/v2/api-docs", "/webjars/**",
                "/static/**", "/assets/**", "/css/**", "/js/**", "/images/**", "/favicon.ico",
                "/index.html", "/WEB-INF/**", "/doc.html", "/v3/api-docs/**", "/swagger-ui.html"
        };

        for (String pattern : publicPaths) {
            if (antPathMatcher.match(pattern, requestURI)) {
                return true;
            }
        }
        return false;
    }

    private String parseJwt(HttpServletRequest request) {
        String headerAuth = request.getHeader("Authorization");
        if (StringUtils.hasText(headerAuth) && headerAuth.startsWith("Bearer ")) {
            return headerAuth.substring(7);
        }
        return null;
    }
} 