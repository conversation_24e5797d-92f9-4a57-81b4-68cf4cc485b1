package com.gg.grp.hkycg.mapper;

import com.gg.grp.hkycg.model.vo.ProcurementLinkSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/12
 */
@Repository
@Mapper
public interface AcceptanceResultMapper {
    
    /**
     * 带条件查询采购全流程数据
     * @param ysbh 验收编号
     * @param condition 模糊查询条件(验收编号、验收申请名称)
     * @param yslxdm 采购类型代码
     * @param ysbmdm 验收申请部门代码
     * @param xmmc 项目名称
     * @return 采购全流程数据
     */
    List<ProcurementLinkSummaryVO> getFullProcurementLinkWithCondition(
            @Param("ysbh") String ysbh,
            @Param("condition") String condition,
            @Param("yslxdm") String yslxdm,
            @Param("ysbmdm") String ysbmdm,
            @Param("xmmc") String xmmc);
} 