package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.vo.CgzbsqListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzbsqml;
import com.gg.grp.hkycg.model.vo.CgzbsqAuditListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 招标申请目录表Mapper接口
 */
@Mapper
public interface GpmCgzbsqmlMapper extends BaseMapper<GpmCgzbsqml> {

    /**
     * 获取指定前缀的最大招标申请编号
     * @param prefix 前缀，例如"CGZB20250712"
     * @return 最大招标申请编号，例如"CGZB202507120003"
     */
    @Select("SELECT MAX(ZBSQBH) FROM GPM_CGZBSQML WHERE ZBSQBH LIKE #{prefix} + '%'")
    String selectMaxZbsqbhByPrefix(@Param("prefix") String prefix);

    /**
     * 分页查询招标申请列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件
     * @param zt 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 招标申请列表
     */
    List<CgzbsqListVO> getCgzbsqPageList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("condition") String condition,
            @Param("zt") String zt,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("currEmployeeCode") String currEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("currDeptCode") String currDeptCode
    );

    /**
     * 查询当前用户需要审核的招标申请列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param auditorCode 审核人代码
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param applicationName 申请名称（可选）
     * @param applicant 申请人（可选）
     * @param billStatus 单据状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 需要审核的招标申请列表
     */
    List<CgzbsqAuditListVO> getCgzbsqAuditList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("auditorCode") String auditorCode,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("applicationName") String applicationName,
            @Param("applicant") String applicant,
            @Param("billStatus") Integer billStatus,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );
} 