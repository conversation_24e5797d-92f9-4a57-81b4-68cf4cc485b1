package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.PubObjFlow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流程实例Mapper接口
 */
@Mapper
public interface PubObjFlowMapper extends BaseMapper<PubObjFlow> {

    /**
     * 根据条件删除工作流程实例
     * @param modCode 模块代码
     * @param djlx 单据类型
     * @param djh 单据号
     */
    void deleteByCon(@Param("modCode") String modCode, 
                    @Param("djlx") String djlx, 
                    @Param("djh") String djh);

    /**
     * 根据单据号查询工作流程实例
     * @param djh 单据号
     * @return 工作流程实例列表
     */
    List<PubObjFlow> selectByBillno(@Param("djh") String djh);

    /**
     * 根据条件查询当前审核节点
     * @param modCode 模块代码
     * @param djlx 单据类型
     * @param djh 单据号
     * @return 当前审核节点
     */
    PubObjFlow selectNowNodeByDjh(@Param("modCode") String modCode, 
                                 @Param("djlx") String djlx, 
                                 @Param("djh") String djh);

    PubObjFlow selectLastAuditNodeByDjh(@Param("modCode") String modCode,
                                        @Param("djlx") String djlx,
                                        @Param("djh") String djh);
}