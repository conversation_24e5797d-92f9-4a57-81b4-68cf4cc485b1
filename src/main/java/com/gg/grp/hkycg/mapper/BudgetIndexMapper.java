package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.BudgetIndex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预算指标Mapper接口
 */
@Mapper
public interface BudgetIndexMapper extends BaseMapper<BudgetIndex> {

    /**
     * 根据业务ID查询预算指标列表
     * @param lid 业务ID
     * @return 预算指标列表
     */
    List<BudgetIndex> selectByLid(@Param("lid") String lid);

    /**
     * 根据业务ID删除预算指标
     * @param lid 业务ID
     * @return 删除记录数
     */
    int deleteByLid(@Param("lid") String lid);

    /**
     * 批量插入预算指标
     * @param budgetIndexList 预算指标列表
     * @return 插入记录数
     */
    int batchInsert(@Param("list") List<BudgetIndex> budgetIndexList);
} 