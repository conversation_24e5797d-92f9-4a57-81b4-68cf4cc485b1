package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.GpmCgdjml;
import com.gg.grp.hkycg.model.vo.CgjgListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 采购结果目录表Mapper接口
 */
@Mapper
public interface GpmCgdjmlMapper extends BaseMapper<GpmCgdjml> {

    /**
     * 获取指定前缀的最大结果登记编号
     * @param prefix 前缀，例如"CGDJ20250712"
     * @return 最大结果登记编号，例如"CGDJ202507120003"
     */
    @Select("SELECT MAX(JGDJBH) FROM GPM_CGDJML WHERE JGDJBH LIKE #{prefix} + '%'")
    String selectMaxJgdjbhByPrefix(@Param("prefix") String prefix);

    /**
     * 分页查询采购结果列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件
     * @param zt 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 采购结果列表
     */
    List<CgjgListVO> getCgjgPageList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("condition") String condition,
            @Param("zt") String zt,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("currEmployeeCode") String currEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("currDeptCode") String currDeptCode
    );
} 