package com.gg.grp.hkycg.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Map;

/**
 * 动态SQL查询Mapper
 */
@Mapper
public interface DynamicQueryMapper {

    /**
     * 检查条件是否满足
     * @param sql 动态构造的SQL语句
     * @return 满足条件的记录数
     */
    int checkCondition(@Param("sql") String sql);

    /**
     * 获取单据数据
     * @param sql 动态构造的SQL语句
     * @return 单据数据Map
     */
    Map<String, Object> getBillData(@Param("sql") String sql);
} 