package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.CglxPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CglxListVO;
import com.gg.grp.hkycg.model.pojo.GpmCglx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购类型Mapper接口
 */
@Mapper
public interface GpmCglxMapper extends BaseMapper<GpmCglx> {
    
    /**
     * 分页查询采购类型列表
     * @param queryDTO 查询参数
     * @return 采购类型列表
     */
    List<CglxListVO> getCglxPageList(@Param("query") CglxPageQueryDTO queryDTO);
} 