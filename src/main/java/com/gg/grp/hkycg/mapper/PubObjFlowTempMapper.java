package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流程模板Mapper接口
 */
@Mapper
public interface PubObjFlowTempMapper extends BaseMapper<PubObjFlowTemp> {

    /**
     * 根据单据类型ID查询工作流程模板列表
     * @param djlxid 单据类型ID
     * @return 工作流程模板列表
     */
    List<PubObjFlowTemp> selectListByDjlxid(@Param("djlxid") Integer djlxid);

    /**
     * 根据条件查询工作流程模板
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param flowcode 流程代码
     * @param jddm 节点代码
     * @return 工作流程模板列表
     */
    List<PubObjFlowTemp> selectByCondition(@Param("gsdm") String gsdm, 
                                          @Param("kjnd") String kjnd,
                                          @Param("flowcode") String flowcode, 
                                          @Param("jddm") Integer jddm);
} 