package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.GpmCgjhnr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 采购计划内容表Mapper接口
 */
@Mapper
public interface GpmCgjhnrMapper extends BaseMapper<GpmCgjhnr> {
    
    /**
     * 根据前缀查询最大序号
     * 假设明细编号格式为：前缀 + 三位数字序号（如CGJHMX20230101001）
     * 此方法提取数字部分并返回最大值
     *
     * @param prefix 明细编号前缀，如CGJHMX20230101
     * @return 最大序号，如果没有记录则返回null
     */
    @Select("SELECT MAX(CAST(SUBSTRING(JHMXXH, LEN(#{prefix}) + 1, 3) AS INT)) FROM GPM_CGJHNR WHERE JHMXXH LIKE #{prefix} + '%'")
    Integer selectMaxSequenceByPrefix(@Param("prefix") String prefix);
} 