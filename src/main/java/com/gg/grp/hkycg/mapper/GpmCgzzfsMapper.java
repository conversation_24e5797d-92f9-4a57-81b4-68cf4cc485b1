package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.CgzzfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzzfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzzfs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购组织方式Mapper接口
 */
@Mapper
public interface GpmCgzzfsMapper extends BaseMapper<GpmCgzzfs> {
    
    /**
     * 分页查询采购组织方式列表
     * @param queryDTO 查询参数
     * @return 采购组织方式列表
     */
    List<CgzzfsListVO> getCgzzfsPageList(@Param("query") CgzzfsPageQueryDTO queryDTO);
} 