package com.gg.grp.hkycg.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.OerYszb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 预算指标Mapper接口
 */
@Mapper
public interface IndexMapper extends BaseMapper<OerYszb> {
    
    /**
     * 分页查询预算指标 (使用Map参数)
     * @param params 查询参数Map
     * @return JSONObject列表
     */
    List<JSONObject> qryIndex(Map<String, Object> params);
    
    /**
     * 根据ZBID查询预算指标（使用复杂residual逻辑）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param zbid 指标ID
     * @param endDate 截止日期（用于计算residual，格式：yyyyMMdd）
     * @param mlid 目录ID（可选，用于排除某些记录）
     * @return GbiZbxmb对象（包含复杂计算的residual字段）
     */
    com.gg.grp.hkycg.model.pojo.GbiZbxmb getIndexByZbid(@Param("gsdm") String gsdm,
                                                    @Param("kjnd") String kjnd,
                                                    @Param("zbid") String zbid,
                                                    @Param("endDate") String endDate,
                                                    @Param("mlid") String mlid);
    
    /**
     * 根据DJBH查询采购计划预算指标余额（通过djbh关联占用记录）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param zbid 指标ID
     * @param endDate 截止日期（用于计算residual，格式：yyyyMMdd）
     * @param djbh 单据编号（用于排除当前采购计划的占用记录）
     * @return GbiZbxmb对象（包含复杂计算的residual字段，且减去当前采购计划的占用）
     */
    com.gg.grp.hkycg.model.pojo.GbiZbxmb getCgjhBudgetIndexByDjbh(@Param("gsdm") String gsdm,
                                                                @Param("kjnd") String kjnd,
                                                                @Param("zbid") String zbid,
                                                                @Param("endDate") String endDate,
                                                                @Param("djbh") String djbh);
    
    /**
     * 查询采购计划的占用金额
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param djbh 单据编号
     * @param zbid 指标ID
     * @return 占用金额
     */
    java.math.BigDecimal getCgjhOccupiedAmount(@Param("gsdm") String gsdm,
                                              @Param("kjnd") String kjnd,
                                              @Param("djbh") String djbh,
                                              @Param("zbid") String zbid);
    
    /**
     * 批量插入预算指标
     * @param oerYszbList 预算指标列表
     * @return 插入数量
     */
    int batchInsertOerYszb(@Param("list") List<OerYszb> oerYszbList);
    
    /**
     * 获取最大MLID
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @return 最大MLID
     */
    Integer selectMaxMlid(@Param("gsdm") String gsdm, @Param("kjnd") String kjnd);
    
    /**
     * 获取最大ZBID
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @return 最大ZBID
     */
    Integer selectMaxZbid(@Param("gsdm") String gsdm, @Param("kjnd") String kjnd);
    
    /**
     * 获取最大BNXID
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @return 最大BNXID
     */
    Integer selectMaxBnxid(@Param("gsdm") String gsdm, @Param("kjnd") String kjnd);
    
    /**
     * 获取最大DataID
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @return 最大DataID
     */
    Integer selectMaxDataId(@Param("gsdm") String gsdm, @Param("kjnd") String kjnd);
    
    /**
     * 检查预算指标是否存在
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param zbid 指标ID
     * @return 存在返回1，不存在返回0
     */
    Integer checkBudgetIndexExists(@Param("gsdm") String gsdm, @Param("kjnd") String kjnd, @Param("zbid") Integer zbid);
} 