package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.GpmCgsqml;
import org.apache.ibatis.annotations.Mapper;
import com.gg.grp.hkycg.model.vo.CgsqListVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 采购申请目录表Mapper接口
 */
@Mapper
public interface GpmCgsqmlMapper extends BaseMapper<GpmCgsqml> {

    /**
     * 获取指定前缀的最大申请编号
     * @param prefix 前缀，例如"CGSQ20250712"
     * @return 最大申请编号，例如"CGSQ202507120003"
     */
    @Select("SELECT MAX(SQBH) FROM GPM_CGSQML WHERE SQBH LIKE #{prefix} + '%'")
    String selectMaxSqbhByPrefix(@Param("prefix") String prefix);

    /**
     * 分页查询采购申请列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件
     * @param zt 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 采购申请列表
     */
    List<CgsqListVO> getCgsqPageList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("condition") String condition,
            @Param("zt") String zt,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("currEmployeeCode") String currEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("currDeptCode") String currDeptCode
    );

    /**
     * 分页查询已审核采购申请列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件
     * @param zt 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param cgfssfzb 采购方式是否招标
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 采购申请列表
     */
    List<CgsqListVO> getCgsqApprovedPageList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("condition") String condition,
            @Param("zt") String zt,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("cgfssfzb") Boolean cgfssfzb,
            @Param("currEmployeeCode") String currEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("currDeptCode") String currDeptCode
    );

} 