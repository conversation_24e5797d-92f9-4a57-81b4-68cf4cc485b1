package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gg.grp.hkycg.model.vo.DeptListVO;
import com.gg.grp.hkycg.model.pojo.Dept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 部门Mapper接口
 */
@Mapper
public interface DeptMapper extends BaseMapper<Dept> {
    
    /**
     * 分页查询部门信息并关联查询负责人姓名
     * @param page 分页对象
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件
     * @return 部门列表VO分页结果
     */
    IPage<DeptListVO> selectDeptWithResponsiblePerson(Page<DeptListVO> page, 
                                                     @Param("gsdm") String gsdm, 
                                                     @Param("kjnd") String kjnd, 
                                                     @Param("condition") String condition);
} 