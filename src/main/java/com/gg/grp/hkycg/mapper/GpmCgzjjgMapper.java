package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.CgzjjgPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjjgListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzjjg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购中介机构Mapper接口
 */
@Mapper
public interface GpmCgzjjgMapper extends BaseMapper<GpmCgzjjg> {

    /**
     * 分页查询采购中介机构列表
     * @param queryDTO 查询参数
     * @return 采购中介机构列表
     */
    List<CgzjjgListVO> getCgzjjgPageList(@Param("query") CgzjjgPageQueryDTO queryDTO);
} 