package com.gg.grp.hkycg.mapper;

import com.gg.grp.hkycg.model.vo.TodoItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 待办事项Mapper接口
 */
@Repository
@Mapper
public interface TodoMapper {
    
    /**
     * 查询我的申请列表
     * 
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param employeeCode 员工编码
     * @param keyword 关键字（可模糊匹配单据编号、单据名称）
     * @param billType 单据类型
     * @param status 单据状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param size 每页大小
     * @return 待办事项列表
     */
    List<TodoItemVO> getMyApplications(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("employeeCode") String employeeCode,
            @Param("keyword") String keyword,
            @Param("billType") String billType,
            @Param("status") String status,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size
    );
    
    /**
     * 查询我已审核的列表
     * 
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param employeeCode 员工编码
     * @param keyword 关键字（可模糊匹配单据编号、单据名称）
     * @param billType 单据类型
     * @param status 单据状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param size 每页大小
     * @return 待办事项列表
     */
    List<TodoItemVO> getMyAudited(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("employeeCode") String employeeCode,
            @Param("keyword") String keyword,
            @Param("billType") String billType,
            @Param("status") String status,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size
    );
} 