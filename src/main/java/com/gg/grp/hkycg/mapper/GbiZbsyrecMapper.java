package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.GbiZbsyrec;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 指标使用记录表Mapper
 */
@Mapper
public interface GbiZbsyrecMapper extends BaseMapper<GbiZbsyrec> {
    
    /**
     * 批量插入指标使用记录
     * @param gbiZbsyrecList 指标使用记录列表
     * @return 插入数量
     */
    int batchInsertGbiZbsyrec(@Param("list") List<GbiZbsyrec> gbiZbsyrecList);
} 