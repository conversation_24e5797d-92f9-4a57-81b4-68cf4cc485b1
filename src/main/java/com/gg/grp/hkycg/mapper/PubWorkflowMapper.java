package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.PubWorkflow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 工作流程配置Mapper接口
 */
@Mapper
public interface PubWorkflowMapper extends BaseMapper<PubWorkflow> {

    /**
     * 根据模块代码和业务代码查询工作流程
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param modname 模块名称
     * @param bizcode 业务代码
     * @return 工作流程配置
     */
    PubWorkflow selectByModAndBiz(@Param("gsdm") String gsdm, 
                                 @Param("kjnd") String kjnd,
                                 @Param("modname") String modname, 
                                 @Param("bizcode") String bizcode);
} 