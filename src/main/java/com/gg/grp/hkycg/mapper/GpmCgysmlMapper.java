package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.vo.CgysListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgysml;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 采购验收主表Mapper接口
 */
@Mapper
public interface GpmCgysmlMapper extends BaseMapper<GpmCgysml> {

    /**
     * 获取指定前缀的最大验收申请编号
     * @param prefix 前缀，例如"CGYS20250712"
     * @return 最大验收申请编号，例如"CGYS202507120003"
     */
    @Select("SELECT MAX(YSBH) FROM GPM_CGYSML WHERE YSBH LIKE #{prefix} + '%'")
    String selectMaxYsbhByPrefix(@Param("prefix") String prefix);

    /**
     * 分页查询采购验收列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 搜索条件
     * @param zt 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param size 每页大小
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 采购验收列表
     */
    List<CgysListVO> getCgysPageList(@Param("gsdm") String gsdm,
                                     @Param("kjnd") String kjnd,
                                     @Param("condition") String condition,
                                     @Param("zt") String zt,
                                     @Param("startDate") String startDate,
                                     @Param("endDate") String endDate,
                                     @Param("offset") Integer offset,
                                     @Param("size") Integer size,
                                     @Param("currEmployeeCode") String currEmployeeCode,
                                     @Param("dataPermission") String dataPermission,
                                     @Param("currDeptCode") String currDeptCode);
}