package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.YssjfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.YssjfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmYssjfs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预算审计方式Mapper接口
 */
@Mapper
public interface GpmYssjfsMapper extends BaseMapper<GpmYssjfs> {
    
    /**
     * 分页查询预算审计方式列表
     * @param queryDTO 查询参数
     * @return 预算审计方式列表
     */
    List<YssjfsListVO> getYssjfsPageList(@Param("query") YssjfsPageQueryDTO queryDTO);
} 