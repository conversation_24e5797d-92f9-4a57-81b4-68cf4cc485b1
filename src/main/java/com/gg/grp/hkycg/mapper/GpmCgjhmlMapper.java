package com.gg.grp.hkycg.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.vo.CgjhListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgjhml;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购计划目录表Mapper接口
 */
@Mapper
public interface GpmCgjhmlMapper extends BaseMapper<GpmCgjhml> {

    /**
     * 获取最大的计划编号
     * @return 最大计划编号
     */
    @Select("SELECT MAX(JHBH) FROM GPM_CGJHML")
    BigDecimal selectMaxJhbh();

    /**
     * 获取指定前缀的最大计划编号
     * @param prefix 前缀，例如"CGJH20250712"
     * @return 最大计划编号，例如"CGJH202507120003"
     */
    @Select("SELECT MAX(JHBH) FROM GPM_CGJHML WHERE JHBH LIKE #{prefix} + '%'")
    String selectMaxJhbhByPrefix(@Param("prefix") String prefix);

    /**
     * 分页查询采购计划列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件（可选）
     * @param zt 状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 采购计划列表
     */
    List<CgjhListVO> getCgjhPageList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("condition") String condition,
            @Param("zt") String zt,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("currEmployeeCode") String currEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("currDeptCode") String currDeptCode
    );

    /**
     *
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param billNo 单据编号
     * @param con 条件
     * @return 审核人代码,审核人名称
     */
    JSONObject findAuditorInfo(@Param("gsdm") String gsdm, @Param("kjnd") String kjnd,
                               @Param("billNo") String billNo, @Param("con") String con);

    /**
     * 分页查询采购计划列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param condition 查询条件（可选）
     * @param zt 状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param offset 分页偏移量
     * @param size 每页大小
     * @param currEmployeeCode 当前用户职员代码
     * @param dataPermission 数据权限代码
     * @param currDeptCode 当前用户部门代码
     * @return 采购计划列表
     */
    List<CgjhListVO> getApprovedCgjhList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("condition") String condition,
            @Param("zt") String zt,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("currEmployeeCode") String currEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("currDeptCode") String currDeptCode
    );

}