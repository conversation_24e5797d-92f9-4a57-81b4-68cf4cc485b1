package com.gg.grp.hkycg.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.RoleDto;
import com.gg.grp.hkycg.model.vo.ModuleGivenDTO;
import com.gg.grp.hkycg.model.vo.AllPersonAndDeptListVO;
import com.gg.grp.hkycg.model.vo.RoleUserListVO;
import com.gg.grp.hkycg.model.pojo.GpmRoleUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RoleUserMapper extends BaseMapper<GpmRoleUser> {
    /**
     * 查询人员和部门信息
     */
     List<AllPersonAndDeptListVO> getAllPersonAndDeptList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("roleCode") String roleCode,
            @Param("condition") String condition,
            @Param("pages") Integer pages,
            @Param("pageNum") Integer pageNum
    );

    List<RoleUserListVO> queryRoleUserList(ModuleGivenDTO moduleGivenDTO);

    List<JSONObject> getGNFLByRole(RoleDto roleDto);

    List<String> queryUserAuthorizeList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("zydm") String zydm
    );
}
