package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.CgfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgfs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购方式Mapper接口
 */
@Mapper
public interface GpmCgfsMapper extends BaseMapper<GpmCgfs> {
    
    /**
     * 分页查询采购方式列表
     * @param queryDTO 查询参数
     * @return 采购方式列表
     */
    List<CgfsListVO> getCgfsPageList(@Param("query") CgfsPageQueryDTO queryDTO);
} 