package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.ApprovalReminder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Date;

/**
 * 审批催办Mapper接口
 */
@Repository
@Mapper
public interface ApprovalReminderMapper extends BaseMapper<ApprovalReminder> {

    /**
     * 查询需要催办的单据
     *
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param billNo 单据号
     * @param billType 单据类型
     * @param status 单据状态
     * @param nodeName 审核节点名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param stagnationStartTime 滞留开始时间
     * @param offset 偏移量
     * @param size 每页大小
     * @return 需要催办的单据列表
     */
    List<ApprovalReminderVO> queryRemindableApprovals(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("billNo") String billNo,
            @Param("billType") String billType,
            @Param("status") String status,
            @Param("nodeName") String nodeName,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("stagnationStartTime") Date stagnationStartTime,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("showReminded") Boolean showReminded
    );
    
    /**
     * 查询当前登录人被催办的单据
     *
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param receiverDm 接收人代码
     * @param billNo 单据号
     * @param billType 单据类型
     * @param reminderResult 催办状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param size 每页大小
     * @return 被催办的单据列表
     */
    List<MyReminderVO> queryMyReminders(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("receiverDm") String receiverDm,
            @Param("billNo") String billNo,
            @Param("billType") String billType,
            @Param("reminderResult") String reminderResult,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size
    );
    
    /**
     * 查询单据的最后一次催办记录
     *
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param djbh 单据号
     * @param djlx 单据类型
     * @param modcode 模块代码
     * @return 最后一次催办记录
     */
    ApprovalReminder getLastReminderByBill(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("djbh") String djbh,
            @Param("djlx") String djlx,
            @Param("modcode") String modcode,
            @Param("nodeName") String nodeName
    );

    /**
     * 查询离截止日期指定天数内的所有单据
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param employeeCode 员工编码
     * @param days 天数
     * @return 单据列表
     */
    List<BusinessReminderVO> queryBillsNearDeadline(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("employeeCode") String employeeCode,
            @Param("days") Integer days);

    /**
     * 查询已审核状态的采购计划
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param offset 偏移量
     * @param size 每页大小
     * @return 已审核采购计划列表
     */
    List<ApprovedPlanVO> queryApprovedPlans(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    /**
     * 查询已审核状态的采购申请
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param offset 偏移量
     * @param size 每页大小
     * @return 已审核采购申请列表
     */
    List<ApprovedApplicationVO> queryApprovedApplications(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    /**
     * 查询已审核状态的采购招标
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param offset 偏移量
     * @param size 每页大小
     * @return 已审核采购招标列表
     */
    List<ApprovedBiddingVO> queryApprovedBiddings(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    /**
     * 查询已审核状态的采购结果
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param offset 偏移量
     * @param size 每页大小
     * @return 已审核采购结果列表
     */
    List<ApprovedResultVO> queryApprovedResults(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    /**
     * 查询已审核状态的采购验收
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param offset 偏移量
     * @param size 每页大小
     * @return 已审核采购验收列表
     */
    List<ApprovedAcceptanceVO> queryApprovedAcceptances(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    /**
     * 查询已审核状态的采购计划总数
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总数
     */
    long countApprovedPlans(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    /**
     * 查询已审核状态的采购申请总数
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总数
     */
    long countApprovedApplications(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    /**
     * 查询已审核状态的采购招标总数
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总数
     */
    long countApprovedBiddings(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    /**
     * 查询已审核状态的采购结果总数
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总数
     */
    long countApprovedResults(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    /**
     * 查询已审核状态的采购验收总数
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 总数
     */
    long countApprovedAcceptances(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    /**
     * 查询所有已审核状态的单据
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param offset 偏移量
     * @param size 每页大小
     * @return 所有已审核单据列表
     */
    List<AllApprovedBillVO> queryAllApprovedBills(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    /**
     * 查询所有已审核状态的单据总数
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @return 总数
     */
    long countAllApprovedBills(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd);

}