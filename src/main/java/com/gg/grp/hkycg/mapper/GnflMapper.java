package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.vo.UserGnflUrlVO;
import com.gg.grp.hkycg.model.pojo.GpmGnfl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 功能Mapper接口
 */
@Mapper
public interface GnflMapper extends BaseMapper<GpmGnfl> {
    
    /**
     * 根据角色代码获取授权的功能列表
     */
    @Select("SELECT g.* FROM GPM_GNFL g " +
            "INNER JOIN GPM_ROLEGNFL rg ON g.CODE = rg.GNFLCODE " +
            "WHERE rg.ROLECODE = #{roleCode}")
    List<GpmGnfl> getFunctionsByRoleCode(@Param("roleCode") String roleCode);

    List<UserGnflUrlVO> getUserGnflUrl(
            @Param("gsdm") String currCorpCode,
            @Param("kjnd") String currAccountantYear,
            @Param("zydm") String currEmployeeCode);
}