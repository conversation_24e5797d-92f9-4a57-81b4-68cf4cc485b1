package com.gg.grp.hkycg.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.GbiZbxmb;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 指标项目表Mapper
 */
@Mapper
public interface GbiZbxmbMapper extends BaseMapper<GbiZbxmb> {
    /**
     * 指标授权模块 --查询未被授权的指标
     * @param temp
     * @return
     */
    List<JSONObject> getAllIndexList(Map<String, Object> temp);
}