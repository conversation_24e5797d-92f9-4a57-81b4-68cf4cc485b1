package com.gg.grp.hkycg.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 统计相关Mapper接口
 */
@Mapper
public interface StatisticsMapper {
    
    /**
     * 采购计划统计（合并已办结和未办结）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return Map包含已办结(completed)和未办结(incomplete)数量
     */
    Map<String, Object> countCgjhStats(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 采购申请统计（合并已办结和未办结）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return Map包含已办结(completed)和未办结(incomplete)数量
     */
    Map<String, Object> countCgsqStats(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 采购招标统计（合并已办结和未办结）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return Map包含已办结(completed)和未办结(incomplete)数量
     */
    Map<String, Object> countCgzbStats(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 采购结果统计（合并已办结和未办结）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return Map包含已办结(completed)和未办结(incomplete)数量
     */
    Map<String, Object> countCgjgStats(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 采购验收统计（合并已办结和未办结）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return Map包含已办结(completed)和未办结(incomplete)数量
     */
    Map<String, Object> countCgysStats(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 查询采购计划数量
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param completed 是否已办结 true=已办结 false=未办结
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return 单据数量
     */
    Integer countCgjh(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("completed") boolean completed,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 查询采购申请数量
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param completed 是否已办结 true=已办结 false=未办结
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return 单据数量
     */
    Integer countCgsq(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("completed") boolean completed,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 查询采购招标数量
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param completed 是否已办结 true=已办结 false=未办结
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return 单据数量
     */
    Integer countCgzb(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("completed") boolean completed,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 查询采购结果数量
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param completed 是否已办结 true=已办结 false=未办结
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return 单据数量
     */
    Integer countCgjg(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("completed") boolean completed,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );

    /**
     * 查询采购验收数量
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param completed 是否已办结 true=已办结 false=未办结
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param CurrEmployeeCode 当前用户代码
     * @param dataPermission 数据权限代码
     * @param CurrDeptCode 当前用户部门代码
     * @return 单据数量
     */
    Integer countCgys(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("completed") boolean completed,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("CurrEmployeeCode") String CurrEmployeeCode,
            @Param("dataPermission") String dataPermission,
            @Param("CurrDeptCode") String CurrDeptCode
    );
} 