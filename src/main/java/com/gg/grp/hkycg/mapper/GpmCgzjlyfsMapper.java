package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.dto.CgzjlyfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjlyfsListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgzjlyfs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专家来源方式Mapper接口
 */
@Mapper
public interface GpmCgzjlyfsMapper extends BaseMapper<GpmCgzjlyfs> {

    /**
     * 分页查询专家来源方式列表
     * @param queryDTO 查询参数
     * @return 专家来源方式列表
     */
    List<CgzjlyfsListVO> getCgzjlyfsPageList(@Param("query") CgzjlyfsPageQueryDTO queryDTO);
} 