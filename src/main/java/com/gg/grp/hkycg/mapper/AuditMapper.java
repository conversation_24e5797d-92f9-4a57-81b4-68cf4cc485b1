package com.gg.grp.hkycg.mapper;

import com.gg.grp.hkycg.model.vo.UnifiedAuditListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 统一审核Mapper
 */
@Repository
@Mapper
public interface AuditMapper {

    /**
     * 查询统一待办审核列表
     *
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param auditorCode 审核人代码
     * @param condition 查询条件
     * @param billType 单据类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param offset 偏移量
     * @param size 数量
     * @return 统一审核列表
     */
    List<UnifiedAuditListVO> getUnifiedAuditList(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("auditorCode") String auditorCode,
            @Param("condition") String condition,
            @Param("billType") String billType,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("zt") String zt
    );
}
