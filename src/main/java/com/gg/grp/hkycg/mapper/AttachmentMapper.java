package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.vo.AttachmentVO;
import com.gg.grp.hkycg.model.pojo.GpmAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 附件Mapper接口
 */
@Mapper
public interface AttachmentMapper extends BaseMapper<GpmAttachment> {
    
    /**
     * 查询单据关联的附件列表
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param djlx 单据类型
     * @param djh 单据号
     * @param sfmx 是否明细
     * @return 附件列表
     */
    List<AttachmentVO> queryAttachmentsByBill(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("djlx") String djlx,
            @Param("djh") String djh,
            @Param("sfmx") String sfmx
    );
    
    /**
     * 查询单据的目录和明细
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param djlx 单据类型
     * @param djh 单据号
     * @return 目录和明细列表
     */
    List<Map<String, Object>> queryBillDirectoryAndDetail(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("djlx") String djlx,
            @Param("djh") String djh
    );
    
    /**
     * 查询与单据编号相关的所有附件
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param djlx 单据类型
     * @param djh 单据号
     * @return 附件列表
     */
    List<AttachmentVO> queryAllAttachmentsByBillNo(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("djlx") String djlx,
            @Param("djh") String djh
    );
    
    /**
     * 根据附件ID查询附件信息
     * @param fjid 附件ID
     * @return 附件信息
     */
    GpmAttachment getAttachmentById(@Param("fjid") String fjid);
} 