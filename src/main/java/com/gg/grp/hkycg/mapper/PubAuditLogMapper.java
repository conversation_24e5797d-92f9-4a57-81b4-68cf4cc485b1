package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.PubAuditLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 审核日志Mapper接口
 */
@Mapper
public interface PubAuditLogMapper extends BaseMapper<PubAuditLog> {

    /**
     * 获取最大日志ID
     * @return 最大日志ID
     */
    Integer selectMaxLogID();

    /**
     * 根据单据号查询审核日志
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param billid 单据ID
     * @param flowcode 流程代码
     * @return 审核日志列表
     */
    java.util.List<PubAuditLog> selectByBillId(@Param("gsdm") String gsdm, 
                                              @Param("kjnd") String kjnd,
                                              @Param("billid") String billid, 
                                              @Param("flowcode") String flowcode);

    /**
     * 删除提交相关的审核日志
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param billid 单据ID
     * @param flowcode 流程代码
     */
    void deleteSubmitLog(@Param("gsdm") String gsdm, 
                        @Param("kjnd") String kjnd,
                        @Param("billid") String billid, 
                        @Param("flowcode") String flowcode);

    /**
     * 删除所有审核日志（销审用）
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param billid 单据ID
     * @param flowcode 流程代码
     */
    void deleteAllAuditLogs(@Param("gsdm") String gsdm, 
                           @Param("kjnd") String kjnd,
                           @Param("billid") String billid, 
                           @Param("flowcode") String flowcode);
} 