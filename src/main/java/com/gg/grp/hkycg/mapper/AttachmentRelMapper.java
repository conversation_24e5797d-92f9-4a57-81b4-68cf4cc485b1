package com.gg.grp.hkycg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gg.grp.hkycg.model.pojo.GpmAttachmentRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 附件关联Mapper接口
 */
@Mapper
public interface AttachmentRelMapper extends BaseMapper<GpmAttachmentRel> {
    
    /**
     * 删除单据关联的附件
     * @param gsdm 公司代码
     * @param kjnd 会计年度
     * @param djlx 单据类型
     * @param djh 单据号
     * @param sfmx 是否明细
     * @return 删除数量
     */
    int deleteAttachmentRelByBill(
            @Param("gsdm") String gsdm,
            @Param("kjnd") String kjnd,
            @Param("djlx") String djlx,
            @Param("djh") String djh,
            @Param("sfmx") String sfmx
    );
    
    /**
     * 删除附件关联
     * @param fjid 附件ID
     * @return 删除数量
     */
    int deleteAttachmentRelByFjid(@Param("fjid") String fjid);
} 