package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.BatchDataPermissionAssignDTO;
import com.gg.grp.hkycg.model.dto.DataPermissionAssignDTO;
import com.gg.grp.hkycg.model.pojo.GpmData;
import com.gg.grp.hkycg.service.GpmDataService;
import com.gg.grp.hkycg.service.GpmUserdataService;
import com.gg.grp.hkycg.service.GpmUserdataService.BatchAssignResult;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据权限控制器
 * <AUTHOR>
 */
@Tag(name = "数据权限管理")
@RestController
@RequestMapping("/dataPermission")
public class DataPermissionController {
    
    @Autowired
    private GpmDataService gpmDataService;
    
    @Autowired
    private GpmUserdataService gpmUserdataService;
    
    /**
     * 查询所有数据权限代码和名称
     * @return 数据权限列表
     */
    @GetMapping("/list")
    public Result<List<GpmData>> listAllDataPermission() {
        List<GpmData> dataList = gpmDataService.listAllDataPermission();
        return Result.success(dataList);
    }
    
    /**
     * 查询指定职员的数据权限
     * @param zydm 职员代码
     * @return 数据权限信息
     */
    @GetMapping("/getByZydm/{zydm}")
    public Result<GpmData> getUserDataPermission(@PathVariable String zydm) {
        GpmData data = gpmUserdataService.getUserDataPermission(zydm);
        if (data != null) {
            return Result.success(data);
        } else {
            return Result.error("该职员未分配数据权限,使用默认数据权限");
        }
    }
    
    /**
     * 为职员分配数据权限
     * @param dto 数据权限分配DTO
     * @return 分配结果
     */
    @PostMapping("/assign")
    public Result<Void> assignDataPermission(@RequestBody @Validated DataPermissionAssignDTO dto) {
        boolean result = gpmUserdataService.assignDataPermission(dto.getZydm(), dto.getDatacode());
        if (result) {
            return Result.success("数据权限分配成功");
        } else {
            return Result.error("数据权限分配失败，请检查数据权限代码是否存在");
        }
    }
    
    /**
     * 批量为职员分配数据权限
     * @param dto 批量数据权限分配DTO
     * @return 批量分配结果
     */
    @PostMapping("/batchAssign")
    public Result<Map<String, Integer>> batchAssignDataPermission(@RequestBody @Validated BatchDataPermissionAssignDTO dto) {
        BatchAssignResult result = gpmUserdataService.batchAssignDataPermission(dto.getZydmList(), dto.getDatacode());
        
        Map<String, Integer> resultMap = new HashMap<>(2);
        resultMap.put("successCount", result.getSuccessCount());
        resultMap.put("failCount", result.getFailCount());
        
        String message = String.format("批量分配数据权限完成，成功：%d，失败：%d", result.getSuccessCount(), result.getFailCount());
        if (result.getFailCount() > 0) {
            return Result.error(message);
        } else {
            CacheStatisticsUtils.clearStatisticsCache();
            return Result.success(message, resultMap);
        }
    }
}
