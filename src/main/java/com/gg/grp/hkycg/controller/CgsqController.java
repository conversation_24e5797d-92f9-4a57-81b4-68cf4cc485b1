package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.CgsqAuditLogVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.model.vo.CgsqSaveResponseVO;
import com.gg.grp.hkycg.model.pojo.GpmCgsqnr;
import com.gg.grp.hkycg.service.CgsqService;
import com.gg.grp.hkycg.model.vo.CgsqListVO;
import com.gg.grp.hkycg.service.CgsqnrService;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

/**
 * 采购申请控制器
 * <AUTHOR>
 */
@Tag(name = "采购申请管理")
@Slf4j
@RestController
@RequestMapping("/cgsq")
@Validated
public class CgsqController {

    @Autowired
    private CgsqService cgsqService;

    @Autowired
    private CgsqnrService cgsqnrService;

    /**
     * 保存采购申请
     * @param cgsqSaveDTO 采购申请保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgsqSaveResponseVO> save(@Valid @RequestBody CgsqSaveDTO cgsqSaveDTO) {
        try {
            // 检查基础信息是否为空
            if (cgsqSaveDTO.getBaseInfo() == null) {
                return Result.error("基础信息不能为空");
            }
            if (!cgsqSaveDTO.getBaseInfo().getSqbh().isEmpty()) {
                return Result.error("保存后不能再次保存采购申请，请使用更新接口");
            }

            // 检查是否由采购计划引入
            Boolean sfcgjhyr = cgsqSaveDTO.getBaseInfo().getSfcgjhyr();
            if (!sfcgjhyr){
                return Result.error("采购申请必须由采购计划引入");
            }
            if (StringUtils.isBlank(cgsqSaveDTO.getBaseInfo().getJhbh())) {
                return Result.error("由采购计划引入时必须提供采购计划编号");
            }
            // 检查是否有计划明细
            boolean hasJhmx = cgsqSaveDTO.getCgsqDetails() != null && !cgsqSaveDTO.getCgsqDetails().isEmpty();
            if (!hasJhmx) {
                return Result.error("需要提供计划明细");
            }

            // 调用服务层保存
            CgsqSaveResponseVO result = cgsqService.saveCgsq(cgsqSaveDTO);
            CacheStatisticsUtils.clearStatisticsCache();
            
            return Result.success("保存采购申请成功", result);

        } catch (Exception e) {
            log.error("保存采购申请失败", e);
            throw new GlobalException("保存采购申请失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @param cgsqSaveDTO 采购申请保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/modifyCgsq/{sqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgsqSaveResponseVO> updateCgsq(@PathVariable String sqbh, @RequestBody CgsqSaveDTO cgsqSaveDTO) {
        try {
            // 手动验证必需字段
            if (cgsqSaveDTO == null) {
                return Result.error("请求数据不能为空");
            }

            // 检查明细信息是否为空
            if (cgsqSaveDTO.getCgsqDetails() == null || cgsqSaveDTO.getCgsqDetails().isEmpty()) {
                return Result.error("采购申请明细不能为空");
            }
            
            // 设置默认值
            if (cgsqSaveDTO.getBaseInfo() != null) {
                if (cgsqSaveDTO.getBaseInfo().getGsdm() == null) {
                    cgsqSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
                }
                if (cgsqSaveDTO.getBaseInfo().getKjnd() == null) {
                    cgsqSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
                }
            }

            CgsqSaveResponseVO result = cgsqService.updateCgsq(sqbh, cgsqSaveDTO);

            log.info("更新采购申请成功，sqbh：{}", sqbh);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("更新采购申请失败，sqbh: {}", sqbh, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据申请编号查询采购申请详情（RESTful风格）
     * @param sqbh 申请编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByDjbh/{sqbh}")
    public Result<CgsqSaveResponseVO> getCgsqDetail(@PathVariable String sqbh) {
        try {
            // 查询采购申请详情
            CgsqSaveResponseVO cgsqDetail = cgsqService.getCgsqDetailBySqbh(sqbh);
            
            return Result.success("查询采购申请成功", cgsqDetail);
        } catch (Exception e) {
            log.error("查询采购申请异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("查询采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 删除采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @return 统一响应结果
     */
    @GetMapping("/deleteByDjbh/{sqbh}")
    public Result<String> deleteCgsq(@PathVariable String sqbh) {
        try {
            // 删除采购申请
            boolean result = cgsqService.deleteBySqbh(sqbh);
            CacheStatisticsUtils.clearStatisticsCache();
            return result ? Result.success("删除采购申请成功!") : Result.error("没有找到对应的采购申请或删除失败");
        } catch (Exception e) {
            log.error("删除采购申请异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("删除采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 提交采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @return 统一响应结果
     */
    @GetMapping("/commit/{sqbh}")
    public Result<CgsqSaveResponseVO> commitCgsq(@PathVariable String sqbh) {
        try {
            log.info("开始提交采购申请，申请编号：{}", sqbh);
            
            // 1. 查询采购申请详情获取金额
            CgsqSaveResponseVO cgsqDetail = cgsqService.getCgsqDetailBySqbh(sqbh);
            if (cgsqDetail == null || cgsqDetail.getBaseInfo() == null) {
                return Result.error("采购申请不存在");
            }
            
            BigDecimal money = cgsqDetail.getBaseInfo().getSqje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }

            // 2. 检查单据状态是否为保存状态（1）
            String status = cgsqDetail.getBaseInfo().getZt();
            if (!"1".equals(status)) {
                String statusName = getStatusName(status);
                return Result.error("只有保存状态的采购申请才能提交，当前状态为：" + statusName);
            }
            
            log.info("采购申请状态检查通过，开始提交，申请编号：{}，金额：{}", sqbh, money);
            
            // 3. 调用提交代理方法
            cgsqService.commitProxyBySqbh(BillnoType.CGSQ, sqbh, money.doubleValue(), LoginInfo.getCurrEmployeeName());
            
            log.info("采购申请提交完成，开始查询提交后的详情，申请编号：{}", sqbh);
            
            return Result.success("提交采购申请成功", cgsqService.getCgsqDetailBySqbh(sqbh));
            
        } catch (Exception e) {
            log.error("提交采购申请异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("提交采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 审核采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCgsq/{sqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgsqSaveResponseVO> auditCgsq(@PathVariable String sqbh, @RequestBody AuditParamSqbh param) {
        try {
            // 设置申请编号
            param.setSqbh(sqbh);
            
            // 1. 查询采购申请详情获取金额
            CgsqSaveResponseVO cgsqDetail = cgsqService.getCgsqDetailBySqbh(sqbh);
            if (cgsqDetail == null || cgsqDetail.getBaseInfo() == null) {
                return Result.error("采购申请不存在");
            }
            
            BigDecimal money = cgsqDetail.getBaseInfo().getSqje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 调用审核方法
            cgsqService.checkBySqbh(BillnoType.CGSQ, sqbh, param.getOpinion(), param.getAuditor(), money);
            
            return Result.success("审核采购申请成功", cgsqService.getCgsqDetailBySqbh(sqbh));
            
        } catch (Exception e) {
            log.error("审核采购申请异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("审核采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 收回采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @return 统一响应结果
     */
    @GetMapping("/callback/{sqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgsqSaveResponseVO> callBackCgsq(@PathVariable String sqbh) {
        try {
            log.info("开始收回采购申请，申请编号：{}", sqbh);
            
            // 调用收回方法
            CgsqSaveResponseVO result = cgsqService.callBackBySqbh(sqbh);
            
            log.info("采购申请收回成功，申请编号：{}", sqbh);
            
            return Result.success("收回采购申请成功", result);
            
        } catch (Exception e) {
            log.error("收回采购申请异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("收回采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 退审采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCallBack/{sqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgsqSaveResponseVO> rejectCgsq(@PathVariable String sqbh, @RequestBody AuditParamSqbh param) {
        try {
            log.info("开始退审采购申请，申请编号：{}，退审人：{}", sqbh, param.getAuditor());
            
            // 设置申请编号
            param.setSqbh(sqbh);
            
            // 调用退审方法
            CgsqSaveResponseVO result = cgsqService.checkCallBackBySqbh(sqbh, param.getOpinion(), param.getAuditor());
            
            log.info("采购申请退审成功，申请编号：{}，退审人：{}", sqbh, param.getAuditor());
            
            return Result.success("退审采购申请成功", result);
            
        } catch (Exception e) {
            log.error("退审采购申请异常，申请编号：{}，退审人：{}，异常信息：{}", 
                     sqbh, param.getAuditor(), e.getMessage(), e);
            return Result.error("退审采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 销审采购申请（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @return 统一响应结果
     */
    @GetMapping("/noAudit/{sqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgsqSaveResponseVO> cancelCgsq(@PathVariable String sqbh) {
        try {
            log.info("开始销审采购申请，申请编号：{}", sqbh);
            
            // 调用销审方法
            CgsqSaveResponseVO result = cgsqService.noAuditBySqbh(sqbh);
            
            log.info("采购申请销审成功，申请编号：{}", sqbh);
            
            return Result.success("销审采购申请成功", result);
            
        } catch (Exception e) {
            log.error("销审采购申请异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("销审采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 查询审核记录（RESTful风格，使用申请编号）
     * @param sqbh 申请编号
     * @return 统一响应结果
     */
    @GetMapping("/auditLog/{sqbh}")
    public Result<CgsqAuditLogVO> getAuditLog(@PathVariable String sqbh) {
        try {
            CgsqAuditLogVO result = cgsqService.findCheckLogBySqbh(BillnoType.CGSQ, sqbh);
            return Result.success("查询审核记录成功", result);
        } catch (Exception e) {
            log.error("查询采购申请审核记录异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            return Result.error("查询审核记录失败：" + e.getMessage());
        }
    }

    /**
     * 检查采购申请是否有权限审核（RESTful风格，使用申请编号，无参数）
     * @param sqbh 申请编号
     * @return 权限检查结果
     */
    @GetMapping("/checkAuthority/{sqbh}")
    public Object checkAuthority(@PathVariable String sqbh) {
        try {
            // 直接使用基于SQBH的权限检查方法，不需要参数
            return cgsqService.isCheckedByAuthorityBySqbh(sqbh);
        } catch (Exception e) {
            log.error("检查采购申请审核权限异常，申请编号：{}，异常信息：{}", sqbh, e.getMessage(), e);
            
            com.alibaba.fastjson.JSONObject errorResult = new com.alibaba.fastjson.JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 分页查询所有采购申请（RESTful风格）
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgsqListVO>> getCgsqPageList(CgsqPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgsqPageQueryDTO();
            }

            log.info("分页查询采购申请，查询条件：{}", queryDTO);

            // 执行查询
            List<CgsqListVO> cgsqList = cgsqService.getCgsqPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgsqList.isEmpty()) {
                totalCount = cgsqList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgsqListVO> pageResult = new PageResult<>(cgsqList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购申请成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购申请异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购申请失败：" + e.getMessage());
        }
    }
    /**
     * 获取状态名称
     * @param status 状态值
     * @return 状态名称
     */
    private String getStatusName(String status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case "1":
                return "保存";
            case "2":
                return "已提交";
            case "3":
                return "审核中";
            case "4":
                return "已审核";
            case "5":
                return "退回";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 分页查询已审核状态的采购申请（返回详细信息）
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @PostMapping("/approved")
    public Result<PageResult<CgsqSaveResponseVO>> getApprovedCgsqList(@RequestBody CgsqPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgsqPageQueryDTO();
            }

            log.info("分页查询已审核采购申请，查询条件：{}", queryDTO);

            // 设置查询条件为已审核状态
            queryDTO.setZt("4"); // 固定为已审核状态

            // 先查询基础列表获取总记录数和基础信息
            List<CgsqListVO> basicList = cgsqService.getApprovedCgsqList(queryDTO);
            
            // 获取总记录数
            long totalCount = 0;
            if (!basicList.isEmpty()) {
                totalCount = basicList.get(0).getTotalCount();
            }

            // 转换为详细响应列表
            List<CgsqSaveResponseVO> detailList = new ArrayList<>();
            for (CgsqListVO basicItem : basicList) {
                try {
                    // 使用SQBH或DJBH构建详情
                    String identifier = basicItem.getSqbh() != null ? basicItem.getSqbh() : null;
                    CgsqSaveResponseVO detail = cgsqService.getCgsqDetailBySqbh(identifier);
                    detailList.add(detail);
                } catch (Exception e) {
                    log.warn("构建采购申请详情失败，sqbh: {}, 错误: {}",
                            basicItem.getSqbh(), e.getMessage());
                    // 继续处理其他记录，不中断整个查询
                }
            }

            // 创建分页结果
            PageResult<CgsqSaveResponseVO> pageResult = new PageResult<>(detailList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询已审核采购申请成功，总记录数：{}，返回记录数：{}", totalCount, detailList.size());
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询已审核采购申请异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询已审核采购申请失败：" + e.getMessage());
        }
    }

    /**
     * 根据mxxh查询采购申请详情（RESTful风格）
     * @param mxxh
     * @return 统一响应结果
     */
    @GetMapping("/queryByMxxh/{mxxh}")
    public Result<CgsqSaveResponseVO> getCgsqByMxxh(@PathVariable String mxxh) {
        try {
            LambdaQueryWrapper<GpmCgsqnr> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgsqnr::getSqmxxh, mxxh);
            GpmCgsqnr cgsqnr = cgsqnrService.getOne(queryWrapper);
            if (cgsqnr == null){
                return Result.error("查询采购申请失败：申请编号不存在");
            }
            // 查询采购申请详情
            CgsqSaveResponseVO cgsqDetail = cgsqService.getCgsqDetailBySqbh(cgsqnr.getSqbh());

            return Result.success("查询采购申请成功", cgsqDetail);
        } catch (Exception e) {
            log.error("查询采购申请异常，申请编号：{}，异常信息：{}", mxxh, e.getMessage(), e);
            return Result.error("查询采购申请失败：" + e.getMessage());
        }
    }

}