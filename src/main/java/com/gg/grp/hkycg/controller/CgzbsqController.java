package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.GpmCgzbsqnr;
import com.gg.grp.hkycg.service.CgzbsqService;
import com.gg.grp.hkycg.service.CgzbsqnrService;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 招标申请控制器
 * <AUTHOR>
 */
@Tag(name = "采购招标管理")
@Slf4j
@RestController
@RequestMapping("/cgzbsq")
@Validated
public class CgzbsqController {

    @Autowired
    private CgzbsqService cgzbsqService;

    @Autowired
    private CgzbsqnrService cgzbsqnrService;

    /**
     * 保存招标申请
     * @param cgzbsqSaveDTO 招标申请保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/save")
    public Result<CgzbsqSaveResponseVO> save(@Valid @RequestBody CgzbsqSaveDTO cgzbsqSaveDTO) {
        try {
            // 检查基础信息是否为空
            if (cgzbsqSaveDTO.getBaseInfo() == null) {
                return Result.error("基础信息不能为空");
            }

            // 检查是否有明细
            boolean hasDetails = cgzbsqSaveDTO.getCgzbsqDetails() != null && !cgzbsqSaveDTO.getCgzbsqDetails().isEmpty();
            if (!hasDetails) {
                return Result.error("需要提供招标申请明细");
            }

            // 调用服务层保存
            CgzbsqSaveResponseVO result = cgzbsqService.saveCgzbsq(cgzbsqSaveDTO);
            CacheStatisticsUtils.clearStatisticsCache();
            return Result.success("保存招标申请成功", result);

        } catch (Exception e) {
            log.error("保存招标申请失败", e);
            throw new GlobalException("保存招标申请失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @param cgzbsqSaveDTO 招标申请保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/modifyCgzbsq/{zbsqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgzbsqSaveResponseVO> updateCgzbsq(@PathVariable String zbsqbh, @RequestBody CgzbsqSaveDTO cgzbsqSaveDTO) {
        try {
            // 手动验证必需字段
            if (cgzbsqSaveDTO == null) {
                return Result.error("请求数据不能为空");
            }

            // 检查明细信息是否为空
            if (cgzbsqSaveDTO.getCgzbsqDetails() == null || cgzbsqSaveDTO.getCgzbsqDetails().isEmpty()) {
                return Result.error("招标申请明细不能为空");
            }
            
            // 设置默认值
            if (cgzbsqSaveDTO.getBaseInfo() != null) {
                if (cgzbsqSaveDTO.getBaseInfo().getGsdm() == null) {
                    cgzbsqSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
                }
                if (cgzbsqSaveDTO.getBaseInfo().getKjnd() == null) {
                    cgzbsqSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
                }
            }

            // 调用Service层的updateCgzbsqByZbsqbh方法
            CgzbsqSaveResponseVO result = cgzbsqService.updateCgzbsqByZbsqbh(zbsqbh, cgzbsqSaveDTO);

            log.info("更新招标申请成功，zbsqbh：{}", zbsqbh);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("更新招标申请失败，zbsqbh: {}", zbsqbh, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据招标申请编号查询招标申请详情（RESTful风格）
     * @param zbsqbh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByZbsqbh/{zbsqbh}")
    public Result<CgzbsqSaveResponseVO> getCgzbsqDetail(@PathVariable String zbsqbh) {
        try {
            // 查询招标申请详情
            CgzbsqSaveResponseVO cgzbsqDetail = cgzbsqService.getCgzbsqDetailByZbsqbh(zbsqbh);
            
            return Result.success("查询招标申请成功", cgzbsqDetail);
        } catch (Exception e) {
            log.error("查询招标申请异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("查询招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 删除招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/deleteByZbsqbh/{zbsqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteCgzbsq(@PathVariable String zbsqbh) {
        try {
            // 删除招标申请
            boolean result = cgzbsqService.deleteByZbsqbh(zbsqbh);
            CacheStatisticsUtils.clearStatisticsCache();
            return result ? Result.success("删除招标申请成功!") : Result.error("没有找到对应的招标申请或删除失败");
        } catch (Exception e) {
            log.error("删除招标申请异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("删除招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 提交招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/commit/{zbsqbh}")
    public Result<CgzbsqSaveResponseVO> commitCgzbsq(@PathVariable String zbsqbh) {
        try {
            log.info("开始提交招标申请，招标申请编号：{}", zbsqbh);
            
            // 1. 查询招标申请详情获取金额
            CgzbsqSaveResponseVO cgzbsqDetail = cgzbsqService.getCgzbsqDetailByZbsqbh(zbsqbh);
            if (cgzbsqDetail == null || cgzbsqDetail.getBaseInfo() == null) {
                return Result.error("招标申请不存在");
            }
            
            BigDecimal money = cgzbsqDetail.getBaseInfo().getXmje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 检查单据状态是否为保存状态（1）
            String status = cgzbsqDetail.getBaseInfo().getZt();
            if (status == null || !"1".equals(status)) {
                String statusName = getStatusName(status);
                return Result.error("只有保存状态的招标申请才能提交，当前状态为：" + statusName);
            }
            
            log.info("招标申请状态检查通过，开始提交，招标申请编号：{}，金额：{}", zbsqbh, money);
            
            // 3. 调用提交代理方法
            cgzbsqService.commitProxyByZbsqbh(BillnoType.CGZB, zbsqbh, money.doubleValue(), LoginInfo.getCurrEmployeeName());
            
            log.info("招标申请提交完成，开始查询提交后的详情，招标申请编号：{}", zbsqbh);
            
            return Result.success("提交招标申请成功", cgzbsqService.getCgzbsqDetailByZbsqbh(zbsqbh));
            
        } catch (Exception e) {
            log.error("提交招标申请异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("提交招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 审核招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCgzbsq/{zbsqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgzbsqSaveResponseVO> auditCgzbsq(@PathVariable String zbsqbh, @RequestBody AuditParamZbsqbh param) {
        try {
            // 设置招标申请编号
            param.setZbsqbh(zbsqbh);
            
            // 1. 查询招标申请详情获取金额
            CgzbsqSaveResponseVO cgzbsqDetail = cgzbsqService.getCgzbsqDetailByZbsqbh(zbsqbh);
            if (cgzbsqDetail == null || cgzbsqDetail.getBaseInfo() == null) {
                return Result.error("招标申请不存在");
            }
            
            BigDecimal money = cgzbsqDetail.getBaseInfo().getXmje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 调用审核方法
            cgzbsqService.checkByZbsqbh(BillnoType.CGZB, zbsqbh, param.getOpinion(), param.getAuditor(), money);
            
            return Result.success("审核招标申请成功", cgzbsqService.getCgzbsqDetailByZbsqbh(zbsqbh));
            
        } catch (Exception e) {
            log.error("审核招标申请异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("审核招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 收回招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/callback/{zbsqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgzbsqSaveResponseVO> callBackCgzbsq(@PathVariable String zbsqbh) {
        try {
            log.info("开始收回招标申请，招标申请编号：{}", zbsqbh);
            
            // 调用收回方法
            CgzbsqSaveResponseVO result = cgzbsqService.callBackByZbsqbh(zbsqbh);
            
            log.info("招标申请收回成功，招标申请编号：{}", zbsqbh);
            
            return Result.success("收回招标申请成功", result);
            
        } catch (Exception e) {
            log.error("收回招标申请异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("收回招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 退审招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCallBack/{zbsqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgzbsqSaveResponseVO> rejectCgzbsq(@PathVariable String zbsqbh, @RequestBody AuditParamZbsqbh param) {
        try {
            log.info("开始退审招标申请，招标申请编号：{}，退审人：{}", zbsqbh, param.getAuditor());
            
            // 设置招标申请编号
            param.setZbsqbh(zbsqbh);
            
            // 调用退审方法
            CgzbsqSaveResponseVO result = cgzbsqService.checkCallBackByZbsqbh(zbsqbh, param.getOpinion(), param.getAuditor());
            
            log.info("招标申请退审成功，招标申请编号：{}，退审人：{}", zbsqbh, param.getAuditor());
            
            return Result.success("退审招标申请成功", result);
            
        } catch (Exception e) {
            log.error("退审招标申请异常，招标申请编号：{}，退审人：{}，异常信息：{}", 
                     zbsqbh, param.getAuditor(), e.getMessage(), e);
            return Result.error("退审招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 销审招标申请（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/noAudit/{zbsqbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgzbsqSaveResponseVO> cancelCgzbsq(@PathVariable String zbsqbh) {
        try {
            log.info("开始销审招标申请，招标申请编号：{}", zbsqbh);
            
            // 调用销审方法
            CgzbsqSaveResponseVO result = cgzbsqService.noAuditByZbsqbh(zbsqbh);
            
            log.info("招标申请销审成功，招标申请编号：{}", zbsqbh);
            
            return Result.success("销审招标申请成功", result);
            
        } catch (Exception e) {
            log.error("销审招标申请异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("销审招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 查询审核记录（RESTful风格，使用招标申请编号）
     * @param zbsqbh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/auditLog/{zbsqbh}")
    public Result<CgzbsqAuditLogVO> getAuditLog(@PathVariable String zbsqbh) {
        try {
            CgzbsqAuditLogVO result = cgzbsqService.findCheckLogByZbsqbh(BillnoType.CGZB, zbsqbh);
            return Result.success("查询审核记录成功", result);
        } catch (Exception e) {
            log.error("查询招标申请审核记录异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            return Result.error("查询审核记录失败：" + e.getMessage());
        }
    }

    /**
     * 检查招标申请是否有权限审核（RESTful风格，使用招标申请编号，无参数）
     * @param zbsqbh 招标申请编号
     * @return 权限检查结果
     */
    @GetMapping("/checkAuthority/{zbsqbh}")
    public Object checkAuthority(@PathVariable String zbsqbh) {
        try {
            // 直接使用基于ZBSQBH的权限检查方法，不需要参数
            return cgzbsqService.isCheckedByAuthorityByZbsqbh(zbsqbh);
        } catch (Exception e) {
            log.error("检查招标申请审核权限异常，招标申请编号：{}，异常信息：{}", zbsqbh, e.getMessage(), e);
            
            com.alibaba.fastjson.JSONObject errorResult = new com.alibaba.fastjson.JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 分页查询所有招标申请（RESTful风格）
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgzbsqListVO>> getCgzbsqPageList(CgzbsqPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgzbsqPageQueryDTO();
            }

            log.info("分页查询招标申请，查询条件：{}", queryDTO);

            // 执行查询
            List<CgzbsqListVO> cgzbsqList = cgzbsqService.getCgzbsqPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgzbsqList.isEmpty()) {
                totalCount = cgzbsqList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgzbsqListVO> pageResult = new PageResult<>(cgzbsqList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询招标申请成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询招标申请异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询已审核的招标申请
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @PostMapping("/approved")
    public Result<PageResult<CgzbsqSaveResponseVO>> getApprovedCgzbsqList(@RequestBody CgzbsqPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgzbsqPageQueryDTO();
            }

            log.info("分页查询已审核招标申请，查询条件：{}", queryDTO);

            // 调用服务层方法获取已审核列表
            PageResult<CgzbsqSaveResponseVO> pageResult = cgzbsqService.getApprovedCgzbsqList(queryDTO);

            log.info("分页查询已审核招标申请成功，总记录数：{}，返回记录数：{}", pageResult.getTotal(), pageResult.getRecords().size());
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询已审核招标申请异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage(), e);
            return Result.error("查询已审核招标申请失败：" + e.getMessage());
        }
    }

    /**
     * 获取状态名称
     * @param status 状态值
     * @return 状态名称
     */
    private String getStatusName(String status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case "1":
                return "保存";
            case "2":
                return "已提交";
            case "3":
                return "审核中";
            case "4":
                return "已审核";
            case "5":
                return "退回";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 根据招标申请编号查询招标申请详情（RESTful风格）
     * @param mxxh 招标申请编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByMxxh/{mxxh}")
    public Result<CgzbsqSaveResponseVO> getCgzbsqBymxxh(@PathVariable String mxxh) {
        try {
            LambdaQueryWrapper<GpmCgzbsqnr> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgzbsqnr::getZbmxxh, mxxh);
            GpmCgzbsqnr cgzbsqnr = cgzbsqnrService.getOne(queryWrapper);
            if (cgzbsqnr == null){
                return Result.error("查询采购预算失败：申请编号不存在");
            }
            // 查询招标申请详情
            CgzbsqSaveResponseVO cgzbsqDetail = cgzbsqService.getCgzbsqDetailByZbsqbh(cgzbsqnr.getZbsqbh());

            return Result.success("查询招标申请成功", cgzbsqDetail);
        } catch (Exception e) {
            log.error("查询招标申请异常，招标申请编号：{}，异常信息：{}", mxxh, e.getMessage(), e);
            return Result.error("查询招标申请失败：" + e.getMessage());
        }
    }

}
 