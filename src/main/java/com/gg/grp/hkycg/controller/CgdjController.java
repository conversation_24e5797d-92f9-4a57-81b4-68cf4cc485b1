package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.GpmCgdjnr;
import com.gg.grp.hkycg.service.CgdjService;
import com.gg.grp.hkycg.service.CgjgnrService;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购登记控制器
 * <AUTHOR>
 */
@Tag(name = "采购登记管理")
@Slf4j
@RestController
@RequestMapping("/cgjg")
@Validated
public class CgdjController {

    @Autowired
    private CgdjService cgdjService;

    @Autowired
    private CgjgnrService cgjgnrService;

    /**
     * 保存采购结果
     * @param cgjgSaveDTO 采购结果保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjgSaveResponseVO> save(@Valid @RequestBody CgjgSaveDTO cgjgSaveDTO) {
        try {
            // 检查基础信息是否为空
            if (cgjgSaveDTO.getBaseInfo() == null) {
                return Result.error("基础信息不能为空");
            }

            // 检查是否有明细
            boolean hasDetails = cgjgSaveDTO.getCgjgDetails() != null && !cgjgSaveDTO.getCgjgDetails().isEmpty();
            if (!hasDetails) {
                return Result.error("需要提供采购结果明细");
            }

            // 调用服务层保存
            CgjgSaveResponseVO result = cgdjService.saveCgjg(cgjgSaveDTO);
            CacheStatisticsUtils.clearStatisticsCache();
            return Result.success("保存采购结果成功", result);

        } catch (Exception e) {
            log.error("保存采购结果失败", e);
            throw new GlobalException("保存采购结果失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @param cgjgSaveDTO 采购结果保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/modifyCgjg/{jgdjbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjgSaveResponseVO> updateCgjg(@PathVariable String jgdjbh, @RequestBody CgjgSaveDTO cgjgSaveDTO) {
        try {
            // 手动验证必需字段
            if (cgjgSaveDTO == null) {
                return Result.error("请求数据不能为空");
            }

            // 检查明细信息是否为空
            if (cgjgSaveDTO.getCgjgDetails() == null || cgjgSaveDTO.getCgjgDetails().isEmpty()) {
                return Result.error("采购结果明细不能为空");
            }
            
            // 设置默认值
            if (cgjgSaveDTO.getBaseInfo() != null) {
                if (cgjgSaveDTO.getBaseInfo().getGsdm() == null) {
                    cgjgSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
                }
                if (cgjgSaveDTO.getBaseInfo().getKjnd() == null) {
                    cgjgSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
                }
            }

            // 调用Service层的updateCgjgByJgdjbh方法
            CgjgSaveResponseVO result = cgdjService.updateCgjgByJgdjbh(jgdjbh, cgjgSaveDTO);

            log.info("更新采购结果成功，jgdjbh：{}", jgdjbh);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("更新采购结果失败，jgdjbh: {}", jgdjbh, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据结果登记编号查询采购结果详情（RESTful风格）
     * @param jgdjbh 结果登记编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByJgdjbh/{jgdjbh}")
    public Result<CgjgSaveResponseVO> getCgjgDetail(@PathVariable String jgdjbh) {
        try {
            // 查询采购结果详情
            CgjgSaveResponseVO cgjgDetail = cgdjService.getCgjgDetailByJgdjbh(jgdjbh);
            
            return Result.success("查询采购结果成功", cgjgDetail);
        } catch (Exception e) {
            log.error("查询采购结果异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("查询采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 删除采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @return 统一响应结果
     */
    @GetMapping("/deleteByJgdjbh/{jgdjbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteCgjg(@PathVariable String jgdjbh) {
        try {
            // 删除采购结果
            boolean result = cgdjService.deleteByJgdjbh(jgdjbh);
            CacheStatisticsUtils.clearStatisticsCache();
            return result ? Result.success("删除采购结果成功!") : Result.error("没有找到对应的采购结果或删除失败");
        } catch (Exception e) {
            log.error("删除采购结果异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("删除采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 提交采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @return 统一响应结果
     */
    @GetMapping("/commit/{jgdjbh}")
    public Result<CgjgSaveResponseVO> commitCgjg(@PathVariable String jgdjbh) {
        try {
            log.info("开始提交采购结果，结果登记编号：{}", jgdjbh);
            
            // 1. 查询采购结果详情获取金额
            CgjgSaveResponseVO cgjgDetail = cgdjService.getCgjgDetailByJgdjbh(jgdjbh);
            if (cgjgDetail == null || cgjgDetail.getBaseInfo() == null) {
                return Result.error("采购结果不存在");
            }
            
            BigDecimal money = cgjgDetail.getBaseInfo().getJgdjje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 检查单据状态是否为保存状态（1）
            String status = cgjgDetail.getBaseInfo().getZt();
            if (status == null || !"1".equals(status)) {
                String statusName = getStatusName(status);
                return Result.error("只有保存状态的采购结果才能提交，当前状态为：" + statusName);
            }
            
            log.info("采购结果状态检查通过，开始提交，结果登记编号：{}，金额：{}", jgdjbh, money);
            
            // 3. 调用提交代理方法
            cgdjService.commitProxyByJgdjbh(BillnoType.CGJG, jgdjbh, money.doubleValue(), LoginInfo.getCurrEmployeeName());
            
            log.info("采购结果提交完成，开始查询提交后的详情，结果登记编号：{}", jgdjbh);
            
            return Result.success("提交采购结果成功", cgdjService.getCgjgDetailByJgdjbh(jgdjbh));
            
        } catch (Exception e) {
            log.error("提交采购结果异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("提交采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询所有采购结果（RESTful风格）
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgjgListVO>> getCgjgPageList(CgjgPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgjgPageQueryDTO();
            }

            log.info("分页查询采购结果，查询条件：{}", queryDTO);

            // 执行查询
            List<CgjgListVO> cgjgList = cgdjService.getCgjgPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgjgList.isEmpty()) {
                totalCount = cgjgList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgjgListVO> pageResult = new PageResult<>(cgjgList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购结果成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购结果异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 审核采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCgjg/{jgdjbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjgSaveResponseVO> auditCgjg(@PathVariable String jgdjbh, @RequestBody AuditParamJgdjbh param) {
        try {
            // 设置结果登记编号
            param.setJgdjbh(jgdjbh);
            
            // 1. 查询采购结果详情获取金额
            CgjgSaveResponseVO cgjgDetail = cgdjService.getCgjgDetailByJgdjbh(jgdjbh);
            if (cgjgDetail == null || cgjgDetail.getBaseInfo() == null) {
                return Result.error("采购结果不存在");
            }
            
            BigDecimal money = cgjgDetail.getBaseInfo().getJgdjje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 调用审核方法
            cgdjService.checkByJgdjbh(BillnoType.CGJG, jgdjbh, param.getOpinion(), param.getAuditor(), money);
            
            return Result.success("审核采购结果成功", cgdjService.getCgjgDetailByJgdjbh(jgdjbh));
            
        } catch (Exception e) {
            log.error("审核采购结果异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("审核采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 收回采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @return 统一响应结果
     */
    @GetMapping("/callback/{jgdjbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjgSaveResponseVO> callBackCgjg(@PathVariable String jgdjbh) {
        try {
            log.info("开始收回采购结果，结果登记编号：{}", jgdjbh);
            
            CgjgSaveResponseVO result = cgdjService.callBackByJgdjbh(jgdjbh);
            
            log.info("采购结果收回成功，结果登记编号：{}", jgdjbh);
            
            return Result.success("收回采购结果成功", result);
            
        } catch (Exception e) {
            log.error("收回采购结果异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("收回采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 退审采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCallBack/{jgdjbh}")
    public Result<CgjgSaveResponseVO> rejectCgjg(@PathVariable String jgdjbh, @RequestBody AuditParamJgdjbh param) {
        try {
            log.info("开始退审采购结果，结果登记编号：{}，退审人：{}", jgdjbh, param.getAuditor());
            
            // 设置结果登记编号
            param.setJgdjbh(jgdjbh);
            
            // 调用退审方法
            CgjgSaveResponseVO result = cgdjService.checkCallBackByJgdjbh(jgdjbh, param.getOpinion(), param.getAuditor());
            
            log.info("采购结果退审成功，结果登记编号：{}，退审人：{}", jgdjbh, param.getAuditor());
            
            return Result.success("退审采购结果成功", result);
            
        } catch (Exception e) {
            log.error("退审采购结果异常，结果登记编号：{}，退审人：{}，异常信息：{}", 
                     jgdjbh, param.getAuditor(), e.getMessage(), e);
            return Result.error("退审采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 销审采购结果（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @return 统一响应结果
     */
    @GetMapping("/noAudit/{jgdjbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjgSaveResponseVO> cancelCgjg(@PathVariable String jgdjbh) {
        try {
            log.info("开始销审采购结果，结果登记编号：{}", jgdjbh);
            
            CgjgSaveResponseVO result = cgdjService.noAuditByJgdjbh(jgdjbh);
            
            log.info("采购结果销审成功，结果登记编号：{}", jgdjbh);
            
            return Result.success("销审采购结果成功", result);
            
        } catch (Exception e) {
            log.error("销审采购结果异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("销审采购结果失败：" + e.getMessage());
        }
    }

    /**
     * 查询审核记录（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @return 统一响应结果
     */
    @GetMapping("/auditLog/{jgdjbh}")
    public Result<CgjgAuditLogVO> getAuditLog(@PathVariable String jgdjbh) {
        try {
            CgjgAuditLogVO result = cgdjService.findCheckLogByJgdjbh(BillnoType.CGJG, jgdjbh);
            return Result.success("查询审核记录成功", result);
        } catch (Exception e) {
            log.error("查询采购结果审核记录异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            return Result.error("查询审核记录失败：" + e.getMessage());
        }
    }

    /**
     * 检查采购结果是否有权限审核（RESTful风格，使用结果登记编号）
     * @param jgdjbh 结果登记编号
     * @return 权限检查结果
     */
    @GetMapping("/checkAuthority/{jgdjbh}")
    public Object checkAuthority(@PathVariable String jgdjbh) {
        try {
            return cgdjService.isCheckedByAuthorityByJgdjbh(jgdjbh);
        } catch (Exception e) {
            log.error("检查采购结果审核权限异常，结果登记编号：{}，异常信息：{}", jgdjbh, e.getMessage(), e);
            
            com.alibaba.fastjson.JSONObject errorResult = new com.alibaba.fastjson.JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 分页查询已审核的招标结果
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @PostMapping("/approved")
    public Result<PageResult<CgjgSaveResponseVO>> getApprovedCgzbsqList(@RequestBody CgjgPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgjgPageQueryDTO();
            }

            log.info("分页查询已审核招标申请，查询条件：{}", queryDTO);

            // 调用服务层方法获取已审核列表
            PageResult<CgjgSaveResponseVO> pageResult = cgdjService.getApprovedCgzbsqList(queryDTO);

            log.info("分页查询已审核招标申请成功，总记录数：{}，返回记录数：{}", pageResult.getTotal(), pageResult.getRecords().size());
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询已审核招标申请异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage(), e);
            return Result.error("查询已审核招标申请失败：" + e.getMessage());
        }
    }


    /**
     * 获取状态名称
     * @param status 状态值
     * @return 状态名称
     */
    private String getStatusName(String status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case "1":
                return "保存";
            case "2":
                return "已提交";
            case "3":
                return "审核中";
            case "4":
                return "已审核";
            case "5":
                return "退回";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 根据结果登记编号查询采购结果详情（RESTful风格）
     * @param mxxh
     * @return 统一响应结果
     */
    @GetMapping("/queryByMxxh/{mxxh}")
    public Result<CgjgSaveResponseVO> getCgjgBymxxh(@PathVariable String mxxh) {
        try {
            LambdaQueryWrapper<GpmCgdjnr> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgdjnr::getJgnrbh, mxxh);
            GpmCgdjnr cgjgnr = cgjgnrService.getOne(queryWrapper);
            if (cgjgnr == null){
                return Result.error("查询采购结果内容失败：结果内容编号不存在");
            }
            // 查询采购结果详情
            CgjgSaveResponseVO cgjgDetail = cgdjService.getCgjgDetailByJgdjbh(cgjgnr.getJgdjbh());

            return Result.success("查询采购结果成功", cgjgDetail);
        } catch (Exception e) {
            log.error("查询采购结果异常，结果登记编号：{}，异常信息：{}", mxxh, e.getMessage(), e);
            return Result.error("查询采购结果失败：" + e.getMessage());
        }
    }

} 