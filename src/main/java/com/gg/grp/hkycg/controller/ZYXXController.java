package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.EmployeeDTO;
import com.gg.grp.hkycg.model.pojo.Employee;
import com.gg.grp.hkycg.service.EmployeeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 职员信息控制器
 * <AUTHOR>
 */
@Tag(name = "职员信息管理")
@RestController
@RequestMapping("/zyxx")
public class ZYXXController {
    @Autowired
    private EmployeeService employeeService;

    @PostMapping("/qryEmplyee")
    public Result<Page<Employee>> qrySalaryEmplyee(@RequestBody EmployeeDTO employeeDTO){
        LambdaQueryWrapper<Employee> employeeWrapper = new LambdaQueryWrapper<>();
        employeeWrapper.eq(Employee::getGsdm, StringUtils.isNotBlank(employeeDTO.getGsdm())?employeeDTO.getGsdm(): LoginInfo.getCurrCorpCode());
        employeeWrapper.eq(Employee::getKjnd, StringUtils.isNotBlank(employeeDTO.getKjnd())?employeeDTO.getKjnd(): LoginInfo.getCurrAccountantYear());
        employeeWrapper.and(StringUtils.isNotBlank(employeeDTO.getCondition()),
                wrapper -> wrapper
                        .like(Employee::getEmployeeCode, employeeDTO.getCondition())
                        .or()
                        .like(Employee::getEmployeeName, employeeDTO.getCondition())
        );
        employeeWrapper.ne(Employee::getEmployeeName, "停用");
        Page<Employee> page = new Page<>();
        page.setCurrent(employeeDTO.getPages());
        page.setSize(employeeDTO.getPageNum());
        Page<Employee> employees = employeeService.page(page, employeeWrapper);
        return Result.success(employees);
    }
}
