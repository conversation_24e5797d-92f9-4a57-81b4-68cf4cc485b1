package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.RoleDto;
import com.gg.grp.hkycg.model.pojo.GpmRole;
import com.gg.grp.hkycg.service.GnflService;
import com.gg.grp.hkycg.service.RoleService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 * <AUTHOR>
 */
@Tag(name = "角色管理")
@RestController
@RequestMapping("/role")
public class RoleController {
    @Autowired
    private RoleService roleService;

    @Autowired
    private GnflService gnflService;

    /**
     * 获取角色列表
     * @return
     */
    @GetMapping("/getRoleList")
    public Result getRoleList(){
        LambdaQueryWrapper<GpmRole> rolwWrapper = new LambdaQueryWrapper<>();
        rolwWrapper.like(GpmRole::getRoleCode, "c%");
        List<GpmRole> roleList = roleService.list(rolwWrapper);
        return roleList.isEmpty()?Result.error("无角色"):Result.success("查询角色成功!",roleList);
    }

    @PostMapping(value = "/updateRoleList")
    public Result upRoleList(){
        return roleService.upRoleList();
    }

    /**
     * 保存角色信息
     * @return
     */
    @PostMapping("/saveRole")
    public Result saveRole(RoleDto roleDto){
        if (!roleDto.getRoleCode().startsWith("c")){
            return Result.error("角色编码必须以c开头");
        }
        return roleService.saveRole(roleDto);
    }

    /**
     * 保存角色信息
     * @return
     */
    @PostMapping("/updateRole")
    public Result updateRole(RoleDto roleDto){
        return roleService.updateRole(roleDto);
    }

    /**
     * 删除角色信息
     * @return
     */
    @PostMapping("/deleteRole")
    public Result deleteRole(RoleDto roleDto) {
        return roleService.deleteRole(roleDto);
    }

    /**
     * 角色可显示的页面
     * @return
     */
    @RequestMapping(value = "getShowPages", method = RequestMethod.GET)
    public Result getShowPages(){
        return gnflService.getUserGNFL();
    }

}
