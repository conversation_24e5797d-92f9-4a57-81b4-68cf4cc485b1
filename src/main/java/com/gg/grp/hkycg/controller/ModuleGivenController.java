package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.RoleDto;
import com.gg.grp.hkycg.model.vo.ModuleGivenDTO;
import com.gg.grp.hkycg.model.vo.UserAuthorizeVO;
import com.gg.grp.hkycg.service.RoleUserService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模块授权控制器
 * <AUTHOR>
 */
@Tag(name = "模块授权管理")
@RestController
@RequestMapping("modelGiven")
public class ModuleGivenController {
    
    @Autowired
    private RoleUserService roleUserService;

    /**
     * 查询人员和部门信息
     * @return
     */
    @GetMapping("getAllPersonAndDeptList")
    public Result getAllPersonAndDeptList(ModuleGivenDTO moduleGivenDTO){
        return roleUserService.getAllPersonAndDeptList(moduleGivenDTO);
    }

    /**
     * 查询指标信息
     * @return
     */
    @GetMapping("getIndexList")
    public Result getIndexList(ModuleGivenDTO moduleGivenDTO){
        return roleUserService.getAllIndexList(moduleGivenDTO);
    }


    /**
     * 1-1.通过角色查看角色权限
     * @return
     */
    @GetMapping("/queryRolePermissions")
    public Result queryRolePermissions(RoleDto params){
        return roleUserService.queryRolePermissions(params);
    }

    /**
     * 1-2.保存、修改保存角色权限
     * @return
     */
    @PostMapping("saveRolePermissions")
    public Result saveRolePermissions(RoleDto roleDto){
        return roleUserService.saveRolePermissions(roleDto);
    }

    /**
     * 2-1.查询角色授权人员
     * @return
     */
    @GetMapping("queryRoleUserList")
    public Result queryRoleUserList(ModuleGivenDTO moduleGivenDTO){
        return roleUserService.queryRoleUserList(moduleGivenDTO);
    }

    /**
     * 2-2.保存、修改保存角色授权人员
     * @return
     */
    @PostMapping("/saveRoleUser")
    public Result saveRoleUser(RoleDto roleDto){
        return roleUserService.saveRoleUser(roleDto);
    }

    /**
     * 2-3.删除角色授权人员
     * @return
     */
    @PostMapping("/deleteRoleUser")
    public Result deleteRoleUser(RoleDto roleDto){
        return roleUserService.deleteRoleUser(roleDto);
    }

    /**
     * 3-1.查询角色授权指标
     * @return
     */
    @GetMapping( "/queryRoleIndexList")
    public Result queryRoleIndexList(ModuleGivenDTO moduleGivenDTO){
        return roleUserService.queryRoleIndexList(moduleGivenDTO);
    }

    /**
     * 3-2.保存、修改保存角色授权指标
     * @return
     */
    @PostMapping("/saveRoleIndex")
    public Result saveRoleIndex(RoleDto roleDto){
        return roleUserService.saveRoleIndex(roleDto);
    }

    /**
     * 3-3.删除角色授权指标
     * @return
     */
    @PostMapping("/deleteRoleIndex")
    public Result deleteRoleIndex(RoleDto roleDto){
        return roleUserService.deleteRoleIndex(roleDto);
    }

    /**
     * 3-4.查询角色授权(基础业务和系统配置)
     * @return
     */
    @GetMapping("/queryUserAuthorizeList")
    public Result queryUserAuthorizeList(){
        List<String> authorizeStrings = roleUserService.queryUserAuthorizeList();
        UserAuthorizeVO userAuthorizeVO = new UserAuthorizeVO();
        authorizeStrings.forEach(authorizeString -> {
            if (StringUtils.isBlank(authorizeString)){
                return;
            }
            if (authorizeString.contains("8001")){
                userAuthorizeVO.setJcyw(true);
            } else if (authorizeString.contains("8002")){
                userAuthorizeVO.setXtpz(true);
            }
        });
        return Result.success(userAuthorizeVO);
    }
}