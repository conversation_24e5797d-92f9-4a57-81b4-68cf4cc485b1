package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CgmlPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgmlVO;
import com.gg.grp.hkycg.service.CgmlService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 采购品目控制器
 * <AUTHOR>
 */
@Tag(name = "采购品目管理")
@Slf4j
@RestController
@RequestMapping("/cgml")
@Validated
public class CgmlController {

    @Autowired
    private CgmlService cgmlService;

    /**
     * 分页查询采购品目（POST方式）
     * @param queryDTO 查询条件
     * @return 统一响应结果
     */
    @PostMapping("/list")
    public Result<IPage<CgmlVO>> pageQueryPost(@RequestBody(required = false) CgmlPageQueryDTO queryDTO) {
        try {
            // 如果queryDTO为null，创建默认对象
            if (queryDTO == null) {
                queryDTO = new CgmlPageQueryDTO();
            }
            
            log.info("分页查询采购品目，查询条件：{}", queryDTO);

            // 参数校验
            if (queryDTO.getCurrent() == null || queryDTO.getCurrent() < 1) {
                queryDTO.setCurrent(1);
            }
            if (queryDTO.getSize() == null || queryDTO.getSize() < 1) {
                queryDTO.setSize(10);
            }

            // 执行分页查询
            IPage<CgmlVO> pageResult = cgmlService.pageQuery(queryDTO);

            log.info("分页查询采购品目成功，总记录数：{}", pageResult.getTotal());
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购品目异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购品目失败：" + e.getMessage());
        }
    }
}
