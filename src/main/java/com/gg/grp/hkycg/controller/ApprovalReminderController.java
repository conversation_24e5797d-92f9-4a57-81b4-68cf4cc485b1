package com.gg.grp.hkycg.controller;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.ApprovalReminder;
import com.gg.grp.hkycg.service.ApprovalReminderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审批催办控制器
 * <AUTHOR>
 */
@Tag(name = "审批催办管理")
@Slf4j
@RestController
@RequestMapping("/reminder")
public class ApprovalReminderController {
    
    @Autowired
    private ApprovalReminderService approvalReminderService;
    
    /**
     * 查询需要催办的单据
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Operation(summary = "查询需要催办的单据")
    @PostMapping("/list")
    public Result<PageResult<ApprovalReminderVO>> queryRemindableApprovals(@RequestBody(required = false) ApprovalReminderQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new ApprovalReminderQueryDTO();
            }
            
            log.info("查询需要催办的单据，查询条件：{}", queryDTO);
            
            // 执行查询
            PageResult<ApprovalReminderVO> result = approvalReminderService.queryRemindableApprovals(queryDTO);
            
            log.info("查询需要催办的单据成功，总记录数：{}", result.getTotal());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("查询需要催办的单据异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询需要催办的单据失败：" + e.getMessage());
        }
    }
    
    /**
     * 发送催办通知（支持批量）
     * @param reminderSendDTO 催办发送参数（单据号列表和催办内容）
     * @return 催办结果
     */
    @Operation(summary = "发送催办")
    @PostMapping("/send")
    public Result<Boolean> sendReminder(@RequestBody @JsonProperty("djbhs") List<ReminderSendItemVO> reminderSendDTO) {
        try {
            log.info("发送催办通知，单据数量：{}",
                    !reminderSendDTO.isEmpty() ? reminderSendDTO.size() : 0);
            
            // 执行催办
            boolean result = approvalReminderService.sendReminder(reminderSendDTO);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("发送催办通知异常，异常信息：{}", e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("发送催办通知失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询催办历史记录
     * @param billNo 单据号
     * @param billType 单据类型
     * @param current 当前页码
     * @param size 每页大小
     * @return 催办历史记录
     */
    @GetMapping("/history")
    public Result<PageResult<ApprovalReminder>> getReminderHistory(
            @RequestParam String billNo,
            @RequestParam String billType,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        try {
            log.info("查询催办历史记录，单据号：{}，单据类型：{}", billNo, billType);
            
            // 执行查询
            PageResult<ApprovalReminder> result = approvalReminderService.getReminderHistory(billNo, billType, current, size);
            
            log.info("查询催办历史记录成功，单据号：{}，总记录数：{}", billNo, result.getTotal());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("查询催办历史记录异常，单据号：{}，单据类型：{}，异常信息：{}", billNo, billType, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询催办历史记录失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询当前登录人被催办的单据
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Operation(summary = "查询我被催办的单据")
    @PostMapping("/my")
    public Result<PageResult<MyReminderVO>> queryMyReminders(@RequestBody(required = false) MyReminderQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new MyReminderQueryDTO();
            }
            
            log.info("查询被催办的单据，查询条件：{}", queryDTO);
            
            // 执行查询
            PageResult<MyReminderVO> result = approvalReminderService.queryMyReminders(queryDTO);
            
            log.info("查询被催办的单据成功，总记录数：{}", result.getTotal());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("查询被催办的单据异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询被催办的单据失败：" + e.getMessage());
        }
    }
    
    /**
     * 标记催办为已处理
     * @param grid 催办记录GRID
     * @return 处理结果
     */
    @PostMapping("/process/{grid}")
    public Result<Boolean> markAsProcessed(@PathVariable Long grid) {
        try {
            log.info("标记催办为已处理，催办记录GRID：{}", grid);
            
            // 执行标记
            boolean result = approvalReminderService.markAsProcessed(grid);
            
            log.info("标记催办为已处理成功，催办记录GRID：{}", grid);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("标记催办为已处理异常，催办记录GRID：{}，异常信息：{}", grid, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("标记催办为已处理失败：" + e.getMessage());
        }
    }

    /**
     * 查询离截止日期指定天数内的所有单据
     * @param days 天数参数
     * @return 单据列表
     */
    @GetMapping("/deadlineBills/{days}")
    public Result<List<BusinessReminderVO>> queryBillsNearDeadline(@PathVariable Integer days) {
        try {
            log.info("查询离截止日期内的单据，天数：{}", days);

            // 执行查询
            List<BusinessReminderVO> result = approvalReminderService.queryBillsNearDeadline(days);

            log.info("查询离截止日期内的单据成功，总记录数：{}", result.size());
            return Result.success(result);

        } catch (Exception e) {
            log.error("查询离截止日期内的单据异常，天数：{}，异常信息：{}", days, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询离截止日期内的单据失败：" + e.getMessage());
        }
    }

    /**
     * 查询已审核状态的所有单据
     * @param queryDTO 查询条件
     * @return 已审核单据分页列表
     */
    @PostMapping("/approvedList")
    public Result<PageResult<Object>> queryApprovedBills(@RequestBody ApprovedBillsQueryDTO queryDTO) {
        try {
            log.info("查询已审核状态的单据，查询条件：{}", queryDTO);

            // 执行查询
            PageResult<Object> result = approvalReminderService.queryApprovedBills(queryDTO);

            log.info("查询已审核状态的单据成功，总记录数：{}", result.getTotal());
            return Result.success(result);

        } catch (Exception e) {
            log.error("查询已审核状态的单据异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询已审核状态的单据失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有已审核状态的单据
     * @param queryDTO 查询条件
     * @return 所有已审核单据分页列表
     */
    @PostMapping("/allApproved")
    public Result<PageResult<AllApprovedBillVO>> queryAllApprovedBills(@RequestBody AllApprovedBillsQueryDTO queryDTO) {
        try {
            log.info("查询所有已审核状态的单据，查询条件：{}", queryDTO);

            // 执行查询
            PageResult<AllApprovedBillVO> result = approvalReminderService.queryAllApprovedBills(queryDTO);

            log.info("查询所有已审核状态的单据成功，总记录数：{}", result.getTotal());
            return Result.success(result);

        } catch (Exception e) {
            log.error("查询所有已审核状态的单据异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询所有已审核状态的单据失败：" + e.getMessage());
        }
    }

}