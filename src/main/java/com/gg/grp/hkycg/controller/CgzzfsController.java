package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CgzzfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzzfsListVO;
import com.gg.grp.hkycg.model.vo.CgzzfsVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.service.CgzzfsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 采购组织方式控制器
 * <AUTHOR>
 */
@Tag(name = "采购组织方式管理")
@Slf4j
@RestController
@RequestMapping("/cgzzfs")
@Validated
public class CgzzfsController {

    @Autowired
    private CgzzfsService cgzzfsService;

    /**
     * 查询所有采购组织方式
     * @return 采购组织方式列表
     */
    @GetMapping("/all")
    public Result<List<CgzzfsVO>> getAllCgzzfs() {
        try {
            List<CgzzfsVO> cgzzfsList = cgzzfsService.getAllCgzzfs();
            return Result.success("查询采购组织方式成功", cgzzfsList);
        } catch (Exception e) {
            log.error("查询采购组织方式异常：{}", e.getMessage());
            return Result.error("查询采购组织方式失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询采购组织方式
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgzzfsListVO>> getCgzzfsPageList(CgzzfsPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgzzfsPageQueryDTO();
            }

            log.info("分页查询采购组织方式，查询条件：{}", queryDTO);

            // 执行查询
            List<CgzzfsListVO> cgzzfsList = cgzzfsService.getCgzzfsPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgzzfsList.isEmpty()) {
                totalCount = cgzzfsList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgzzfsListVO> pageResult = new PageResult<>(cgzzfsList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购组织方式成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购组织方式异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购组织方式失败：" + e.getMessage());
        }
    }
} 