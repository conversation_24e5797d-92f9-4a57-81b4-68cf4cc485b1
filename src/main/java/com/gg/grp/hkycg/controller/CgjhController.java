package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.enums.StatusName;
import com.gg.grp.hkycg.model.dto.AuditParam;
import com.gg.grp.hkycg.model.dto.CgjhPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgjhSaveDTO;
import com.gg.grp.hkycg.model.vo.CgjhListVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.model.pojo.GpmCgjhnr;
import com.gg.grp.hkycg.service.CgjhService;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;

import com.gg.grp.hkycg.model.vo.CgjhSaveResponseVO;
import com.gg.grp.hkycg.model.vo.CgjhAuditLogVO;
import com.gg.grp.hkycg.mapper.GpmCgjhnrMapper;

/**
 * 采购计划控制器
 * <AUTHOR>
 */
@Tag(name = "采购计划管理")
@Slf4j
@RestController
@RequestMapping("/cgjh")
@Validated
public class CgjhController {

    @Autowired
    private CgjhService cgjhService;

    @Autowired
    private GpmCgjhnrMapper cgjhnrMapper;

    /**
     * 分页查询所有采购计划
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgjhListVO>> getCgjhPageList(CgjhPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgjhPageQueryDTO();
            }

            log.info("分页查询采购计划，查询条件：{}", queryDTO);

            // 执行查询
            List<CgjhListVO> cgjhList = cgjhService.getCgjhPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgjhList.isEmpty()) {
                totalCount = cgjhList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgjhListVO> pageResult = new PageResult<>(cgjhList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购计划成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购计划异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 保存采购计划（统一多明细模式）
     * @param cgjhSaveDTO 采购计划保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjhSaveResponseVO> save(@Valid @RequestBody CgjhSaveDTO cgjhSaveDTO) {
        try {
            // 检查基础信息是否为空
            if (cgjhSaveDTO.getBaseInfo() == null) {
                return Result.error("基础信息不能为空");
            }

            // 检查多明细信息是否为空
            if (cgjhSaveDTO.getDetailsWithBudget() == null || cgjhSaveDTO.getDetailsWithBudget().isEmpty()) {
                return Result.error("采购计划明细不能为空");
            }

            if (!cgjhSaveDTO.getBaseInfo().getJhbh().isEmpty()) {
                return Result.error("保存后不能再次保存采购计划，请使用更新接口");
            }

            // 设置默认值
            if (cgjhSaveDTO.getBaseInfo().getGsdm() == null) {
                cgjhSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (cgjhSaveDTO.getBaseInfo().getKjnd() == null) {
                cgjhSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 保存采购计划
            CgjhSaveResponseVO savedCgjh = cgjhService.saveCgjh(cgjhSaveDTO);
            
            if (savedCgjh == null || savedCgjh.getBaseInfo() == null || savedCgjh.getBaseInfo().getJhbh() == null) {
                return Result.error("保存采购计划失败");
            }
            CacheStatisticsUtils.clearStatisticsCache();
            return Result.success(savedCgjh);

        } catch (Exception e) {
            log.error("保存采购计划失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 更新采购计划（统一多明细模式）
     * @param jhbh 单据编号
     * @param cgjhSaveDTO 采购计划保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/modifyCgjh/{jhbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgjhSaveResponseVO> updateWithDetailedBudget(@PathVariable String jhbh, @RequestBody CgjhSaveDTO cgjhSaveDTO) {
        try {
            // 手动验证必需字段
            if (cgjhSaveDTO == null) {
                return Result.error("请求数据不能为空");
            }

            // 检查多明细信息是否为空
            if (cgjhSaveDTO.getDetailsWithBudget() == null || cgjhSaveDTO.getDetailsWithBudget().isEmpty()) {
                return Result.error("采购计划明细不能为空");
            }
            
            // 设置默认值
            if (StringUtils.isBlank(cgjhSaveDTO.getBaseInfo().getGsdm())) {
                cgjhSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
            }
            if (StringUtils.isBlank(cgjhSaveDTO.getBaseInfo().getKjnd())) {
                cgjhSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
            }

            // 直接调用Service层的updateCgjh方法，它会处理所有逻辑
            CgjhSaveResponseVO result = cgjhService.updateCgjh(jhbh, cgjhSaveDTO);

            log.info("更新多明细采购计划成功，jhbh：{}", jhbh);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("更新多明细采购计划失败，jhbh: {}", jhbh, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据单据编号查询采购计划详情（包含详细预算指标）
     * @param jhbh 单据编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByJhbh/{jhbh}")
    public Result<CgjhSaveResponseVO> queryWithDetailedBudget(@PathVariable String jhbh){
        try {
            // 查询采购计划基础信息
            CgjhSaveResponseVO cgjhDetail = cgjhService.getCgjhDetailByJhbh(jhbh);
            
            return Result.success("查询采购计划成功", cgjhDetail);
        } catch (Exception e) {
            log.error("查询采购计划异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            return Result.error("查询采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 删除采购计划（包含详细预算指标）
     * @param jhbh 单据编号
     * @return 统一响应结果
     */
    @GetMapping("/deleteByJhbh/{jhbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteWithDetailedBudget(@PathVariable String jhbh){
        try {
            boolean result = cgjhService.deleteByJhbh(jhbh);
            CacheStatisticsUtils.clearStatisticsCache();
            return result ? Result.success("删除采购计划成功!") : Result.error("没有找到对应的采购计划或删除失败");
        } catch (Exception e) {
            log.error("删除采购计划异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            return Result.error("删除采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 提交采购计划
     * @param jhbh 审核参数
     * @return 统一响应结果
     */
    @GetMapping("/commit/{jhbh}")
    public Result<CgjhSaveResponseVO> commit(@PathVariable String jhbh) {
        try {
            log.info("开始提交采购计划，单据编号：{}",jhbh);
            
            // 1. 查询采购计划详情获取金额
            CgjhSaveResponseVO cgjhDetail = cgjhService.getCgjhDetailByJhbh(jhbh);
            if (cgjhDetail == null || cgjhDetail.getBaseInfo() == null) {
                return Result.error("采购计划不存在");
            }
            
            BigDecimal money = cgjhDetail.getBaseInfo().getJhje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 检查单据状态是否为保存状态（1）
            BigDecimal status = cgjhDetail.getBaseInfo().getZt();
            if (status == null || status.intValue() != 1) {
                String statusName = StatusName.findByStatus(status);
                return Result.error("只有保存状态的采购计划才能提交，当前状态为：" + statusName);
            }
            
            log.info("采购计划状态检查通过，开始提交，单据编号：{}，金额：{}", jhbh, money);
            
            // 3. 调用提交代理方法
            cgjhService.commitProxy(BillnoType.CGJH, jhbh, money.doubleValue());

            return Result.success("提交采购计划成功", queryWithDetailedBudget(jhbh).getData());
            
        } catch (Exception e) {
            log.error("提交采购计划异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            return Result.error("提交采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 查询审核记录
     * @param jhbh 单据编号
     * @return 统一响应结果
     */
    @GetMapping("/auditLog/{jhbh}")
    public Result<CgjhAuditLogVO> auditLog(@PathVariable String jhbh) {
        try {
            CgjhAuditLogVO result = cgjhService.findCheckLog(BillnoType.CGJH, jhbh);
            return Result.success("查询审核记录成功", result);
        } catch (Exception e) {
            log.error("查询采购计划审核记录异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            return Result.error("查询审核记录失败：" + e.getMessage());
        }
    }

    /**
     * 检查采购计划是否有权限审核
     * @return 权限检查结果
     */
    @GetMapping("/checkAuthority/{jhbh}")
    public Object isCheckedByAuthority(@PathVariable String jhbh) {
        try {
            return cgjhService.isCheckedByAuthority(jhbh);
        } catch (Exception e) {
            log.error("检查采购计划审核权限异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            
            com.alibaba.fastjson.JSONObject errorResult = new com.alibaba.fastjson.JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 审核采购计划
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCgjh/{jhbh}")
    public Result<CgjhSaveResponseVO> checkCgjh(@PathVariable String jhbh,@RequestBody AuditParam param) {
        try {
            param.setJhbh(jhbh);
            // 1. 查询采购计划详情获取金额
            CgjhSaveResponseVO cgjhDetail = cgjhService.getCgjhDetailByJhbh(param.getJhbh());
            if (cgjhDetail == null || cgjhDetail.getBaseInfo() == null) {
                return Result.error("采购计划不存在");
            }
            
            BigDecimal money = cgjhDetail.getBaseInfo().getJhje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 调用审核方法
            cgjhService.check(BillnoType.CGJH, param.getJhbh(), param.getOpinion(), param.getAuditor(), money);
            
            return Result.success("审核成功!",queryWithDetailedBudget(param.getJhbh()).getData());
            
        } catch (Exception e) {
            log.error("审核采购计划异常，单据编号：{}，异常信息：{}", param.getJhbh(), e.getMessage(), e);
            return Result.error("审核采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 收回采购计划
     * @param jhbh
     * @return 统一响应结果
     */
    @GetMapping("/callBack/{jhbh}")
    public Result<CgjhSaveResponseVO> callBack(@PathVariable String jhbh) {
        try {
            log.info("开始收回采购计划，单据编号：{}", jhbh);
            
            // 调用收回方法
            CgjhSaveResponseVO result = cgjhService.callBack(jhbh);
            
            log.info("采购计划收回成功，单据编号：{}", jhbh);
            
            return Result.success("收回采购计划成功", result);
            
        } catch (Exception e) {
            log.error("收回采购计划异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            return Result.error("收回采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 退审采购计划
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCallBack/{jhbh}")
    public Result<CgjhSaveResponseVO> checkCallBack(@PathVariable String jhbh,@RequestBody AuditParam param) {
        try {
            log.info("开始退审采购计划，单据编号：{}，退审人：{}", jhbh, param.getAuditor());
            
            // 调用退审方法
            CgjhSaveResponseVO result = cgjhService.checkCallBack(jhbh, param.getOpinion(), param.getAuditor());
            
            log.info("采购计划退审成功，单据编号：{}，退审人：{}", jhbh, param.getAuditor());
            
            return Result.success("退审采购计划成功", result);
            
        } catch (Exception e) {
            log.error("退审采购计划异常，单据编号：{}，退审人：{}，异常信息：{}", 
                     param.getJhbh(), param.getAuditor(), e.getMessage(), e);
            return Result.error("退审采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 销审采购计划
     * @param jhbh
     * @return 统一响应结果
     */
    @GetMapping("/noAudit/{jhbh}")
    public Result<CgjhSaveResponseVO> noAudit(@PathVariable String jhbh) {
        try {
            log.info("开始销审采购计划，单据编号：{}", jhbh);
            
            // 调用销审方法
            CgjhSaveResponseVO result = cgjhService.noAudit(jhbh);
            
            log.info("采购计划销审成功，单据编号：{}", jhbh);
            
            return Result.success("销审采购计划成功", result);
            
        } catch (Exception e) {
            log.error("销审采购计划异常，单据编号：{}，异常信息：{}", jhbh, e.getMessage(), e);
            return Result.error("销审采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询已审核状态的采购计划（返回详细信息）
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @PostMapping("/approved")
    public Result<PageResult<CgjhSaveResponseVO>> getApprovedCgjhList(@RequestBody CgjhPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgjhPageQueryDTO();
            }

            log.info("分页查询已审核采购计划，查询条件：{}", queryDTO);

            // 设置查询条件为已审核状态
            queryDTO.setZt("4"); // 固定为已审核状态

            // 先查询基础列表获取总记录数和基础信息
            List<CgjhListVO> basicList = cgjhService.getApprovedCgjhList(queryDTO);
            
            // 获取总记录数
            long totalCount = 0;
            if (!basicList.isEmpty()) {
                totalCount = basicList.get(0).getTotalCount();
            }

            // 转换为详细响应列表
            List<CgjhSaveResponseVO> detailList = new ArrayList<>();
            for (CgjhListVO basicItem : basicList) {
                try {
                    CgjhSaveResponseVO detail = cgjhService.getCgjhDetailByJhbh(basicItem.getJhbh());
                    detailList.add(detail);
                } catch (Exception e) {
                    log.warn("构建采购计划详情失败，jhbh: {}, 错误: {}", basicItem.getJhbh(), e.getMessage());
                    // 继续处理其他记录，不中断整个查询
                }
            }

            // 创建分页结果
            PageResult<CgjhSaveResponseVO> pageResult = new PageResult<>(detailList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询已审核采购计划成功，总记录数：{}，返回记录数：{}", totalCount, detailList.size());
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询已审核采购计划异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询已审核采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询所有采购计划
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/cgjhlist")
    public Result<PageResult<CgjhListVO>> getCgjhList(CgjhPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgjhPageQueryDTO();
            }

            log.info("分页查询采购计划，查询条件：{}", queryDTO);

            // 执行查询
            List<CgjhListVO> cgjhList = cgjhService.getCgjhPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgjhList.isEmpty()) {
                totalCount = cgjhList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgjhListVO> pageResult = new PageResult<>(cgjhList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购计划成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购计划异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购计划失败：" + e.getMessage());
        }
    }

    /**
     * 根据明细编号查询采购计划详情（包含详细预算指标）
     * @param mxxh
     * @return 统一响应结果
     */
    @GetMapping("/queryByMxxh/{mxxh}")
    public Result<CgjhSaveResponseVO> queryByMxxh(@PathVariable String mxxh){
        try {
            LambdaQueryWrapper<GpmCgjhnr> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgjhnr::getJhmxxh, mxxh);
            GpmCgjhnr cgjhnr = cgjhnrMapper.selectOne(queryWrapper);
            if (cgjhnr == null){
                return Result.error("查询采购计划失败：采购计划不存在");
            }
            // 查询采购计划基础信息
            CgjhSaveResponseVO cgjhDetail = cgjhService.getCgjhDetailByJhbh(cgjhnr.getJhbh());

            return Result.success("查询采购计划成功", cgjhDetail);
        } catch (Exception e) {
            log.error("查询采购计划异常，单据编号：{}，异常信息：{}", mxxh, e.getMessage(), e);
            return Result.error("查询采购计划失败：" + e.getMessage());
        }
    }

}