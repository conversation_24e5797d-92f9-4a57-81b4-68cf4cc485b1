package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.BillCountStatisticsDTO;
import com.gg.grp.hkycg.model.vo.BillCountStatisticsVO;
import com.gg.grp.hkycg.service.StatisticsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.*;

/**
 * 统计控制器
 * <AUTHOR>
 */
@Tag(name = "统计管理")
@RestController
@RequestMapping("/statistics")
@Slf4j
public class StatisticsController {
    
    @Autowired
    private StatisticsService statisticsService;
    
    @Autowired
    private CacheManager cacheManager;
    
    /**
     * 获取单据完成情况统计数据（用于柱状图展示）
     * @param dto 查询条件
     * @return 统计结果
     */
    @GetMapping("/bill-completion")
    public Result<BillCountStatisticsVO> getBillCompletionStatistics(BillCountStatisticsDTO dto) {
        try {
            log.info("查询单据完成情况统计数据，参数：{}", dto);
            BillCountStatisticsVO result = statisticsService.getBillCompletionStatistics(dto);
            log.info("查询单据完成情况统计数据完成");
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询单据完成情况统计数据异常", e);
            return Result.error("查询统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 清除统计数据缓存
     * @return 清除结果
     */
    @PostMapping("/clear-cache")
    public Result<Boolean> clearStatisticsCache() {
        try {
            log.info("开始清除统计数据缓存");
            Cache cache = cacheManager.getCache("billStatistics");
            if (cache != null) {
                cache.clear();
                log.info("清除统计数据缓存成功");
                return Result.success(true);
            } else {
                log.warn("未找到统计数据缓存");
                return Result.error("未找到统计数据缓存");
            }
        } catch (Exception e) {
            log.error("清除统计数据缓存异常", e);
            return Result.error("清除统计数据缓存失败：" + e.getMessage());
        }
    }
} 