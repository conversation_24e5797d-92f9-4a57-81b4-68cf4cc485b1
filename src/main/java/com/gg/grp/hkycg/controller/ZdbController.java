package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CgfsPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CglxPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgmlPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgzzfsPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgzjjgPageQueryDTO;
import com.gg.grp.hkycg.model.dto.CgzjlyfsPageQueryDTO;
import com.gg.grp.hkycg.model.dto.DeptPageQueryDTO;
import com.gg.grp.hkycg.model.dto.YssjfsPageQueryDTO;
import com.gg.grp.hkycg.model.dto.ZdQueryDTO;
import com.gg.grp.hkycg.model.dto.ZdbAddDTO;
import com.gg.grp.hkycg.model.dto.ZdbBatchDeleteDTO;
import com.gg.grp.hkycg.model.dto.ZdbDeleteDTO;
import com.gg.grp.hkycg.model.dto.ZdbUpdateDTO;
import com.gg.grp.hkycg.model.vo.DeptListVO;
import com.gg.grp.hkycg.model.vo.ZdbListVO;
import com.gg.grp.hkycg.model.pojo.GpmCgfs;
import com.gg.grp.hkycg.model.pojo.GpmCglx;
import com.gg.grp.hkycg.model.pojo.GpmCgzzfs;
import com.gg.grp.hkycg.model.pojo.GpmCgzjlyfs;
import com.gg.grp.hkycg.model.pojo.GpmCgzjjg;
import com.gg.grp.hkycg.model.pojo.GpmYssjfs;
import com.gg.grp.hkycg.model.pojo.HbgZfcgml;
import com.gg.grp.hkycg.model.pojo.Dept;
import com.gg.grp.hkycg.model.pojo.Zdb;
import com.gg.grp.hkycg.service.CgfsService;
import com.gg.grp.hkycg.service.CglxService;
import com.gg.grp.hkycg.service.CgmlService;
import com.gg.grp.hkycg.service.CgzzfsService;
import com.gg.grp.hkycg.service.CgzjlyfsService;
import com.gg.grp.hkycg.service.CgzjjgService;
import com.gg.grp.hkycg.service.DeptService;
import com.gg.grp.hkycg.service.YssjfsService;
import com.gg.grp.hkycg.service.ZdbService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.gg.grp.hkycg.common.enums.ZdType;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 字段控制器（基础信息配置）
 * <AUTHOR>
 */
@Tag(name = "字段管理")
@RestController
@RequestMapping("/zd")
public class ZdbController {
    @Autowired
    private ZdbService zdbService;
    @Autowired
    private DeptService deptService;
    @Autowired
    private CgfsService cgfsService;
    @Autowired
    private CglxService cglxService;
    @Autowired
    private CgmlService cgmlService;
    @Autowired
    private CgzzfsService cgzzfsService;
    @Autowired
    private YssjfsService yssjfsService;
    @Autowired
    private CgzjjgService cgzjjgService;
    @Autowired
    private CgzjlyfsService cgzjlyfsService;

    @GetMapping("/list")
    public Result<List<Zdb>> getZdList(){
        List<Zdb> zdbList = new ArrayList<>();
        Arrays.stream(ZdType.values()).forEach(zdType -> {
            Zdb zdb = new Zdb();
            zdb.setZddm(zdType.getZddm());
            zdb.setZdmc(zdType.getZdmc());
            zdb.setZdlx(zdType.getZdlx());
            zdbList.add(zdb);
        });
        return Result.success("查询成功", zdbService.list());
    }

    @PostMapping("/getByZddm")
    public Result<IPage<ZdbListVO>> getZdByZddm(@RequestBody ZdQueryDTO zdQueryDTO){
        if (zdQueryDTO.getZdlx().equals(ZdType.BMXX.getZdlx())){
            DeptPageQueryDTO queryDTO = new DeptPageQueryDTO();
            queryDTO.setCurrent(zdQueryDTO.getCurrent());
            queryDTO.setSize(zdQueryDTO.getSize());
            queryDTO.setCondition(zdQueryDTO.getCondition());
            IPage<DeptListVO> deptListVOIPage = deptService.queryAllDeptWithResponsiblePerson(queryDTO);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(deptListVOIPage.getTotal());
            zdbListVOIPage.setPages(deptListVOIPage.getPages());
            zdbListVOIPage.setCurrent(deptListVOIPage.getCurrent());
            zdbListVOIPage.setSize(deptListVOIPage.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            deptListVOIPage.getRecords().forEach(deptListVO -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(deptListVO.getBmid());
                zdbListVO.setDm(deptListVO.getDeptCode());
                zdbListVO.setMc(deptListVO.getDeptName());
                zdbListVO.setFzrdm(deptListVO.getFzrdm());
                zdbListVO.setFzrmc(deptListVO.getFzrmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        }else if (ZdType.CGFS.getZdlx().equals(zdQueryDTO.getZdlx())){
            CgfsPageQueryDTO cgfsPageQueryDTO = new CgfsPageQueryDTO();
            cgfsPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            cgfsPageQueryDTO.setSize(zdQueryDTO.getSize());
            cgfsPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<GpmCgfs> cgfsPage = new Page<>();
            cgfsPage.setCurrent(cgfsPageQueryDTO.getCurrent());
            cgfsPage.setSize(cgfsPageQueryDTO.getSize());
            LambdaQueryWrapper<GpmCgfs> cgfsWrapper = new LambdaQueryWrapper<>();
            cgfsWrapper.eq(GpmCgfs::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmCgfs::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(cgfsPageQueryDTO.getCondition())){
                cgfsWrapper.and(wrapper ->
                        wrapper.like(GpmCgfs::getCgfsmc, cgfsPageQueryDTO.getCondition())
                                .or()
                                .like(GpmCgfs::getCgfsdm, cgfsPageQueryDTO.getCondition()));
            }
            cgfsWrapper.orderByAsc(GpmCgfs::getCgfsdm);
            IPage<GpmCgfs> page = cgfsService.page(cgfsPage, cgfsWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(cgfs -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(cgfs.getCgfsid());
                zdbListVO.setDm(cgfs.getCgfsdm());
                zdbListVO.setMc(cgfs.getCgfsmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        } else if (zdQueryDTO.getZdlx().equals(ZdType.CGLX.getZdlx())) {
            CglxPageQueryDTO cglxPageQueryDTO = new CglxPageQueryDTO();
            cglxPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            cglxPageQueryDTO.setSize(zdQueryDTO.getSize());
            cglxPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<GpmCglx> cglxPage = new Page<>();
            cglxPage.setCurrent(cglxPageQueryDTO.getCurrent());
            cglxPage.setSize(cglxPageQueryDTO.getSize());
            LambdaQueryWrapper<GpmCglx> cglxWrapper = new LambdaQueryWrapper<>();
            cglxWrapper.eq(GpmCglx::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmCglx::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(cglxPageQueryDTO.getCondition())){
                cglxWrapper.and(wrapper ->
                        wrapper.like(GpmCglx::getCglxmc, cglxPageQueryDTO.getCondition())
                                .or()
                                .like(GpmCglx::getCglxdm, cglxPageQueryDTO.getCondition()));
            }
            cglxWrapper.orderByAsc(GpmCglx::getCglxdm);
            IPage<GpmCglx> page = cglxService.page(cglxPage, cglxWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(cglx -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(cglx.getCglxid());
                zdbListVO.setDm(cglx.getCglxdm());
                zdbListVO.setMc(cglx.getCglxmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        } else if (zdQueryDTO.getZdlx().equals(ZdType.CGPM.getZdlx())) {
            CgmlPageQueryDTO cgmlPageQueryDTO = new CgmlPageQueryDTO();
            cgmlPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            cgmlPageQueryDTO.setSize(zdQueryDTO.getSize());
            cgmlPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<HbgZfcgml> hbgZfcgmlPage = new Page<>();
            hbgZfcgmlPage.setCurrent(cgmlPageQueryDTO.getCurrent());
            hbgZfcgmlPage.setSize(cgmlPageQueryDTO.getSize());
            LambdaQueryWrapper<HbgZfcgml> hbgZfcgmlLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hbgZfcgmlLambdaQueryWrapper.eq(HbgZfcgml::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(HbgZfcgml::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(cgmlPageQueryDTO.getCondition())){
                hbgZfcgmlLambdaQueryWrapper.and(wrapper ->
                        wrapper.like(HbgZfcgml::getCgmlmc, cgmlPageQueryDTO.getCondition())
                                .or()
                                .like(HbgZfcgml::getCgmldm, cgmlPageQueryDTO.getCondition()));
            }
            hbgZfcgmlLambdaQueryWrapper.orderByAsc(HbgZfcgml::getCgmldm);
            IPage<HbgZfcgml> page = cgmlService.page(hbgZfcgmlPage, hbgZfcgmlLambdaQueryWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(cgml -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(cgml.getCgmlid());
                zdbListVO.setDm(cgml.getCgmldm());
                zdbListVO.setMc(cgml.getCgmlmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        } else if (zdQueryDTO.getZdlx().equals(ZdType.CGZZFS.getZdlx())) {
            CgzzfsPageQueryDTO cgzzfsPageQueryDTO = new CgzzfsPageQueryDTO();
            cgzzfsPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            cgzzfsPageQueryDTO.setSize(zdQueryDTO.getSize());
            cgzzfsPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<GpmCgzzfs> gpmCgzzfsPage = new Page<>();
            gpmCgzzfsPage.setCurrent(cgzzfsPageQueryDTO.getCurrent());
            gpmCgzzfsPage.setSize(cgzzfsPageQueryDTO.getSize());
            LambdaQueryWrapper<GpmCgzzfs> gpmCgzzfsWrapper = new LambdaQueryWrapper<>();
            gpmCgzzfsWrapper.eq(GpmCgzzfs::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmCgzzfs::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(cgzzfsPageQueryDTO.getCondition())){
                gpmCgzzfsWrapper.and(wrapper ->
                        wrapper.like(GpmCgzzfs::getZzfsdm, cgzzfsPageQueryDTO.getCondition())
                                .or()
                                .like(GpmCgzzfs::getZzfsdm, cgzzfsPageQueryDTO.getCondition()));
            }
            gpmCgzzfsWrapper.orderByAsc(GpmCgzzfs::getZzfsdm);
            IPage<GpmCgzzfs> page = cgzzfsService.page(gpmCgzzfsPage, gpmCgzzfsWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(cgzzfs -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(cgzzfs.getCgzzfsid());
                zdbListVO.setDm(cgzzfs.getZzfsdm());
                zdbListVO.setMc(cgzzfs.getZzfsmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        } else if (zdQueryDTO.getZdlx().equals(ZdType.YSSJFS.getZdlx())) {
            YssjfsPageQueryDTO yssjfsPageQueryDTO = new YssjfsPageQueryDTO();
            yssjfsPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            yssjfsPageQueryDTO.setSize(zdQueryDTO.getSize());
            yssjfsPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<GpmYssjfs> gpmYssjfsPage = new Page<>();
            gpmYssjfsPage.setCurrent(yssjfsPageQueryDTO.getCurrent());
            gpmYssjfsPage.setSize(yssjfsPageQueryDTO.getSize());
            LambdaQueryWrapper<GpmYssjfs> gpmYssjfsWrapper = new LambdaQueryWrapper<>();
            gpmYssjfsWrapper.eq(GpmYssjfs::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmYssjfs::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(yssjfsPageQueryDTO.getCondition())){
                gpmYssjfsWrapper.and(wrapper ->
                        wrapper.like(GpmYssjfs::getYssjfsmc, yssjfsPageQueryDTO.getCondition())
                                .or()
                                .like(GpmYssjfs::getYssjfsdm, yssjfsPageQueryDTO.getCondition()));
            }
            gpmYssjfsWrapper.orderByAsc(GpmYssjfs::getYssjfsdm);
            IPage<GpmYssjfs> page = yssjfsService.page(gpmYssjfsPage, gpmYssjfsWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(yssjfs -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(yssjfs.getYssjfsid());
                zdbListVO.setDm(yssjfs.getYssjfsdm());
                zdbListVO.setMc(yssjfs.getYssjfsmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        } else if (zdQueryDTO.getZdlx().equals(ZdType.DLZJJG.getZdlx())) {
            CgzjjgPageQueryDTO cgzjjgPageQueryDTO = new CgzjjgPageQueryDTO();
            cgzjjgPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            cgzjjgPageQueryDTO.setSize(zdQueryDTO.getSize());
            cgzjjgPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<GpmCgzjjg> gpmCgzjjgPage = new Page<>();
            gpmCgzjjgPage.setCurrent(cgzjjgPageQueryDTO.getCurrent());
            gpmCgzjjgPage.setSize(cgzjjgPageQueryDTO.getSize());
            LambdaQueryWrapper<GpmCgzjjg> gpmCgzjjgWrapper = new LambdaQueryWrapper<>();
            gpmCgzjjgWrapper.eq(GpmCgzjjg::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmCgzjjg::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(cgzjjgPageQueryDTO.getCondition())){
                gpmCgzjjgWrapper.and(wrapper ->
                        wrapper.like(GpmCgzjjg::getZjjgmc, cgzjjgPageQueryDTO.getCondition())
                                .or()
                                .like(GpmCgzjjg::getZjjgdm, cgzjjgPageQueryDTO.getCondition()));
            }
            gpmCgzjjgWrapper.orderByAsc(GpmCgzjjg::getZjjgdm);
            IPage<GpmCgzjjg> page = cgzjjgService.page(gpmCgzjjgPage, gpmCgzjjgWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(cgzjjg -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(cgzjjg.getCgzjjgid());
                zdbListVO.setDm(cgzjjg.getZjjgdm());
                zdbListVO.setMc(cgzjjg.getZjjgmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        } else if (zdQueryDTO.getZdlx().equals(ZdType.ZJLYFS.getZdlx())) {
            CgzjlyfsPageQueryDTO cgzjlyfsPageQueryDTO = new CgzjlyfsPageQueryDTO();
            cgzjlyfsPageQueryDTO.setCurrent(zdQueryDTO.getCurrent());
            cgzjlyfsPageQueryDTO.setSize(zdQueryDTO.getSize());
            cgzjlyfsPageQueryDTO.setCondition(zdQueryDTO.getCondition());
            Page<GpmCgzjlyfs> gpmCgzjlyfsPage = new Page<>();
            gpmCgzjlyfsPage.setCurrent(cgzjlyfsPageQueryDTO.getCurrent());
            gpmCgzjlyfsPage.setSize(cgzjlyfsPageQueryDTO.getSize());
            LambdaQueryWrapper<GpmCgzjlyfs> gpmCgzjlyfsWrapper = new LambdaQueryWrapper<>();
            gpmCgzjlyfsWrapper.eq(GpmCgzjlyfs::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(GpmCgzjlyfs::getKjnd, LoginInfo.getCurrAccountantYear());
            if (StringUtils.isNotBlank(cgzjlyfsPageQueryDTO.getCondition())){
                gpmCgzjlyfsWrapper.and(wrapper ->
                        wrapper.like(GpmCgzjlyfs::getCgzjlyfsmc, cgzjlyfsPageQueryDTO.getCondition())
                                .or()
                                .like(GpmCgzjlyfs::getCgzjlyfsdm, cgzjlyfsPageQueryDTO.getCondition()));
            }
            gpmCgzjlyfsWrapper.orderByAsc(GpmCgzjlyfs::getCgzjlyfsdm);
            IPage<GpmCgzjlyfs> page = cgzjlyfsService.page(gpmCgzjlyfsPage, gpmCgzjlyfsWrapper);
            IPage<ZdbListVO> zdbListVOIPage = new Page<>();
            zdbListVOIPage.setTotal(page.getTotal());
            zdbListVOIPage.setPages(page.getPages());
            zdbListVOIPage.setCurrent(page.getCurrent());
            zdbListVOIPage.setSize(page.getSize());
            List<ZdbListVO> zdbListVOS = new ArrayList<>();
            page.getRecords().forEach(cgzjlyfs -> {
                ZdbListVO zdbListVO = new ZdbListVO();
                zdbListVO.setId(cgzjlyfs.getCgzjlyfsid());
                zdbListVO.setDm(cgzjlyfs.getCgzjlyfsdm());
                zdbListVO.setMc(cgzjlyfs.getCgzjlyfsmc());
                zdbListVOS.add(zdbListVO);
            });
            zdbListVOIPage.setRecords(zdbListVOS);
            return Result.success(zdbListVOIPage);
        }
        return Result.error("查询失败");
    }

    @PostMapping("/add")
    public Result addZd(@RequestBody ZdbAddDTO zdbAddDTO){
        String zdlx = zdbAddDTO.getZdlx();
        String dm = zdbAddDTO.getDm();
        String mc = zdbAddDTO.getMc();
        String fzrdm = zdbAddDTO.getFzrdm();
        ZdQueryDTO zdQueryDTO = new ZdQueryDTO();
        zdQueryDTO.setZdlx(zdlx);
        zdQueryDTO.setCurrent(1);
        zdQueryDTO.setSize(10);
        if (zdlx.equals(ZdType.BMXX.getZdlx())) {
            Dept dept = new Dept();
            dept.setBmid(UUID.randomUUID().toString().replace("-", ""));
            dept.setBmdm(dm);
            dept.setBmmc(mc);
            dept.setFzrdm(fzrdm);
            dept.setGsdm(LoginInfo.getCurrCorpCode());
            dept.setKjnd(LoginInfo.getCurrAccountantYear());
            dept.setJlrId(LoginInfo.getCurrEmployeeCode());
            dept.setJlRq(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dept.setSftck("0");
            dept.setSyzt("1");
            boolean save = deptService.save(dept);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.CGFS.getZdlx())) {
            GpmCgfs gpmCgfs = new GpmCgfs();
            gpmCgfs.setCgfsid(UUID.randomUUID().toString().replace("-", ""));
            gpmCgfs.setCgfsdm(dm);
            gpmCgfs.setCgfsmc(mc);
            gpmCgfs.setGsdm(LoginInfo.getCurrCorpCode());
            gpmCgfs.setKjnd(LoginInfo.getCurrAccountantYear());
            gpmCgfs.setSyzt("1");
            boolean save = cgfsService.save(gpmCgfs);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.CGLX.getZdlx())) {
            GpmCglx gpmCglx = new GpmCglx();
            gpmCglx.setCglxid(UUID.randomUUID().toString().replace("-", ""));
            gpmCglx.setCglxdm(dm);
            gpmCglx.setCglxmc(mc);
            gpmCglx.setGsdm(LoginInfo.getCurrCorpCode());
            gpmCglx.setKjnd(LoginInfo.getCurrAccountantYear());
            gpmCglx.setSyzt("1");
            boolean save = cglxService.save(gpmCglx);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.CGPM.getZdlx())) {
            HbgZfcgml hbgZfcgml = new HbgZfcgml();
            hbgZfcgml.setCgmlid(UUID.randomUUID().toString().replace("-", ""));
            hbgZfcgml.setCgmldm(dm);
            hbgZfcgml.setCgmlmc(mc);
            hbgZfcgml.setGsdm(LoginInfo.getCurrCorpCode());
            hbgZfcgml.setKjnd(LoginInfo.getCurrAccountantYear());
            hbgZfcgml.setSyzt("1");
            boolean save = cgmlService.save(hbgZfcgml);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.CGZZFS.getZdlx())) {
            GpmCgzzfs gpmCgzzfs = new GpmCgzzfs();
            gpmCgzzfs.setCgzzfsid(UUID.randomUUID().toString().replace("-", ""));
            gpmCgzzfs.setZzfsdm(dm);
            gpmCgzzfs.setZzfsmc(mc);
            gpmCgzzfs.setGsdm(LoginInfo.getCurrCorpCode());
            gpmCgzzfs.setKjnd(LoginInfo.getCurrAccountantYear());
            gpmCgzzfs.setSyzt("1");
            boolean save = cgzzfsService.save(gpmCgzzfs);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.YSSJFS.getZdlx())) {
            GpmYssjfs gpmYssjfs = new GpmYssjfs();
            gpmYssjfs.setYssjfsid(UUID.randomUUID().toString().replace("-", ""));
            gpmYssjfs.setYssjfsdm(dm);
            gpmYssjfs.setYssjfsmc(mc);
            gpmYssjfs.setGsdm(LoginInfo.getCurrCorpCode());
            gpmYssjfs.setKjnd(LoginInfo.getCurrAccountantYear());
            gpmYssjfs.setSyzt("1");
            boolean save = yssjfsService.save(gpmYssjfs);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.DLZJJG.getZdlx())) {
            GpmCgzjjg gpmCgzjjg = new GpmCgzjjg();
            gpmCgzjjg.setCgzjjgid(UUID.randomUUID().toString().replace("-", ""));
            gpmCgzjjg.setZjjgdm(dm);
            gpmCgzjjg.setZjjgmc(mc);
            gpmCgzjjg.setGsdm(LoginInfo.getCurrCorpCode());
            gpmCgzjjg.setKjnd(LoginInfo.getCurrAccountantYear());
            gpmCgzjjg.setSyzt("1");
            boolean save = cgzjjgService.save(gpmCgzjjg);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        } else if (zdlx.equals(ZdType.ZJLYFS.getZdlx())) {
            GpmCgzjlyfs gpmCgzjlyfs = new GpmCgzjlyfs();
            gpmCgzjlyfs.setCgzjlyfsid(UUID.randomUUID().toString().replace("-", ""));
            gpmCgzjlyfs.setCgzjlyfsdm(dm);
            gpmCgzjlyfs.setCgzjlyfsmc(mc);
            gpmCgzjlyfs.setGsdm(LoginInfo.getCurrCorpCode());
            gpmCgzjlyfs.setKjnd(LoginInfo.getCurrAccountantYear());
            gpmCgzjlyfs.setSyzt("1");
            boolean save = cgzjlyfsService.save(gpmCgzjlyfs);
            if (save) {
                return Result.success("新增成功",getZdByZddm(zdQueryDTO));
            }
        }
        return Result.error("新增失败");
    }

    @PostMapping("/delete")
    public Result deleteZd(@RequestBody ZdbDeleteDTO zdbDeleteDTO){
        String zdlx = zdbDeleteDTO.getZdlx();
        String id = zdbDeleteDTO.getId();
        ZdQueryDTO zdQueryDTO = new ZdQueryDTO();
        zdQueryDTO.setZdlx(zdlx);
        zdQueryDTO.setCurrent(1);
        zdQueryDTO.setSize(10);
        if (zdlx.equals(ZdType.BMXX.getZdlx())) {
            boolean remove = deptService.remove(new LambdaQueryWrapper<Dept>()
                    .eq(Dept::getBmid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGFS.getZdlx())) {
            boolean remove = cgfsService.remove(new LambdaQueryWrapper<GpmCgfs>()
                    .eq(GpmCgfs::getCgfsid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGLX.getZdlx())) {
            boolean remove = cglxService.remove(new LambdaQueryWrapper<GpmCglx>()
                    .eq(GpmCglx::getCglxid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGPM.getZdlx())) {
            boolean remove = cgmlService.remove(new LambdaQueryWrapper<HbgZfcgml>()
                    .eq(HbgZfcgml::getCgmlid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGZZFS.getZdlx())) {
            boolean remove = cgzzfsService.remove(new LambdaQueryWrapper<GpmCgzzfs>()
                    .eq(GpmCgzzfs::getCgzzfsid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.YSSJFS.getZdlx())) {
            boolean remove = yssjfsService.remove(new LambdaQueryWrapper<GpmYssjfs>()
                    .eq(GpmYssjfs::getYssjfsid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.DLZJJG.getZdlx())) {
            boolean remove = cgzjjgService.remove(new LambdaQueryWrapper<GpmCgzjjg>()
                    .eq(GpmCgzjjg::getCgzjjgid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.ZJLYFS.getZdlx())) {
            boolean remove = cgzjlyfsService.remove(new LambdaQueryWrapper<GpmCgzjlyfs>()
                    .eq(GpmCgzjlyfs::getCgzjlyfsid, id));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        }
        return Result.error("删除失败");
    }

    @PostMapping("/delete/batch")
    public Result deleteBatchZd(@RequestBody ZdbBatchDeleteDTO zdbBatchDeleteDTO){
        String zdlx = zdbBatchDeleteDTO.getZdlx();
        List<String> ids = zdbBatchDeleteDTO.getIds();
        ZdQueryDTO zdQueryDTO = new ZdQueryDTO();
        zdQueryDTO.setZdlx(zdlx);
        zdQueryDTO.setCurrent(1);
        zdQueryDTO.setSize(10);
        if (zdlx.equals(ZdType.BMXX.getZdlx())) {
            boolean remove = deptService.remove(new LambdaQueryWrapper<Dept>()
                    .in(Dept::getBmid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGFS.getZdlx())) {
            boolean remove = cgfsService.remove(new LambdaQueryWrapper<GpmCgfs>()
                    .in(GpmCgfs::getCgfsid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGLX.getZdlx())) {
            boolean remove = cglxService.remove(new LambdaQueryWrapper<GpmCglx>()
                    .in(GpmCglx::getCglxid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGPM.getZdlx())) {
            boolean remove = cgmlService.remove(new LambdaQueryWrapper<HbgZfcgml>()
                    .in(HbgZfcgml::getCgmlid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGZZFS.getZdlx())) {
            boolean remove = cgzzfsService.remove(new LambdaQueryWrapper<GpmCgzzfs>()
                    .in(GpmCgzzfs::getCgzzfsid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.YSSJFS.getZdlx())) {
            boolean remove = yssjfsService.remove(new LambdaQueryWrapper<GpmYssjfs>()
                    .in(GpmYssjfs::getYssjfsid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.DLZJJG.getZdlx())) {
            boolean remove = cgzjjgService.remove(new LambdaQueryWrapper<GpmCgzjjg>()
                    .in(GpmCgzjjg::getCgzjjgid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.ZJLYFS.getZdlx())) {
            boolean remove = cgzjlyfsService.remove(new LambdaQueryWrapper<GpmCgzjlyfs>()
                    .in(GpmCgzjlyfs::getCgzjlyfsid, ids));
            if (remove) {
                return Result.success("删除成功", getZdByZddm(zdQueryDTO).getData());
            }
        }
        return Result.error("删除失败");
    }

    @PostMapping("/update")
    public Result updateZd(@RequestBody ZdbUpdateDTO zdbUpdateDTO){
        String zdlx = zdbUpdateDTO.getZdlx();
        String id = zdbUpdateDTO.getId();
        String dm = zdbUpdateDTO.getDm();
        String mc = zdbUpdateDTO.getMc();
        String fzrdm = zdbUpdateDTO.getFzrdm();
        ZdQueryDTO zdQueryDTO = new ZdQueryDTO();
        zdQueryDTO.setZdlx(zdlx);
        zdQueryDTO.setCurrent(1);
        zdQueryDTO.setSize(10);
        if (zdlx.equals(ZdType.BMXX.getZdlx())) {
            boolean update = deptService.update(new LambdaUpdateWrapper<Dept>()
                    .set(Dept::getBmdm, dm)
                    .set(Dept::getBmmc, mc)
                    .set(Dept::getFzrdm, fzrdm)
                    .eq(Dept::getBmid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGFS.getZdlx())) {
            boolean update = cgfsService.update(new LambdaUpdateWrapper<GpmCgfs>()
                    .set(GpmCgfs::getCgfsdm, dm)
                    .set(GpmCgfs::getCgfsmc, mc)
                    .eq(GpmCgfs::getCgfsid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGLX.getZdlx())) {
            boolean update = cglxService.update(new LambdaUpdateWrapper<GpmCglx>()
                    .set(GpmCglx::getCglxdm, dm)
                    .set(GpmCglx::getCglxmc, mc)
                    .eq(GpmCglx::getCglxid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGPM.getZdlx())) {
            boolean update = cgmlService.update(new LambdaUpdateWrapper<HbgZfcgml>()
                    .set(HbgZfcgml::getCgmldm, dm)
                    .set(HbgZfcgml::getCgmlmc, mc)
                    .eq(HbgZfcgml::getCgmlid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.CGZZFS.getZdlx())) {
            boolean update = cgzzfsService.update(new LambdaUpdateWrapper<GpmCgzzfs>()
                    .set(GpmCgzzfs::getZzfsdm, dm)
                    .set(GpmCgzzfs::getZzfsmc, mc)
                    .eq(GpmCgzzfs::getCgzzfsid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.YSSJFS.getZdlx())) {
            boolean update = yssjfsService.update(new LambdaUpdateWrapper<GpmYssjfs>()
                    .set(GpmYssjfs::getYssjfsdm, dm)
                    .set(GpmYssjfs::getYssjfsmc, mc)
                    .eq(GpmYssjfs::getYssjfsid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.DLZJJG.getZdlx())) {
            boolean update = cgzjjgService.update(new LambdaUpdateWrapper<GpmCgzjjg>()
                    .set(GpmCgzjjg::getZjjgdm, dm)
                    .set(GpmCgzjjg::getZjjgmc, mc)
                    .eq(GpmCgzjjg::getCgzjjgid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        } else if (zdlx.equals(ZdType.ZJLYFS.getZdlx())) {
            boolean update = cgzjlyfsService.update(new LambdaUpdateWrapper<GpmCgzjlyfs>()
                    .set(GpmCgzjlyfs::getCgzjlyfsdm, dm)
                    .set(GpmCgzjlyfs::getCgzjlyfsmc, mc)
                    .eq(GpmCgzjlyfs::getCgzjlyfsid, id));
            if (update) {
                return Result.success("更新成功", getZdByZddm(zdQueryDTO).getData());
            }
        }
        return Result.error("更新失败");
    }
}
