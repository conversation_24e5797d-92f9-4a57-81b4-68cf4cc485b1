package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.YssjfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.model.vo.YssjfsListVO;
import com.gg.grp.hkycg.service.YssjfsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 预算审计方式控制器
 * <AUTHOR>
 */
@Tag(name = "预算审计方式管理")
@Slf4j
@RestController
@RequestMapping("/yssjfs")
@Validated
public class YssjfsController {

    @Autowired
    private YssjfsService yssjfsService;

    /**
     * 分页查询预算审计方式
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<YssjfsListVO>> getYssjfsPageList(YssjfsPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new YssjfsPageQueryDTO();
            }

            log.info("分页查询预算审计方式，查询条件：{}", queryDTO);

            // 执行查询
            List<YssjfsListVO> yssjfsList = yssjfsService.getYssjfsPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!yssjfsList.isEmpty()) {
                totalCount = yssjfsList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<YssjfsListVO> pageResult = new PageResult<>(yssjfsList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询预算审计方式成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询预算审计方式异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询预算审计方式失败：" + e.getMessage());
        }
    }
} 