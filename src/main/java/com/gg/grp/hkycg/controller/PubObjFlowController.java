package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.vo.PubObjFlowTempListVO;
import com.gg.grp.hkycg.model.pojo.PubObjFlowTemp;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程对象控制器
 * <AUTHOR>
 */
@Tag(name = "流程对象管理")
@RestController
@RequestMapping("/poft")
public class PubObjFlowController {
    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    @GetMapping("/list")
    public Result<List<PubObjFlowTempListVO>> getAuditNodeList() {
        List<PubObjFlowTemp> list = pubObjFlowTempService.list();
        List<PubObjFlowTempListVO> voList = list.stream().map(item -> {
            PubObjFlowTempListVO vo = new PubObjFlowTempListVO();
            vo.setJddm(item.getJddm());
            vo.setJdmc(item.getJdmc());
            return vo;
        }).collect(Collectors.toList());
        return Result.success(voList);
    }
}
