package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CgzjlyfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjlyfsListVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.service.CgzjlyfsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 专家来源方式控制器
 * <AUTHOR>
 */
@Tag(name = "专家来源方式管理")
@Slf4j
@RestController
@RequestMapping("/cgzjlyfs")
@Validated
public class CgzjlyfsController {

    @Autowired
    private CgzjlyfsService cgzjlyfsService;

    /**
     * 分页查询所有专家来源方式
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgzjlyfsListVO>> getCgzjlyfsPageList(CgzjlyfsPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgzjlyfsPageQueryDTO();
            }

            log.info("分页查询专家来源方式，查询条件：{}", queryDTO);

            // 执行查询
            List<CgzjlyfsListVO> cgzjlyfsList = cgzjlyfsService.getCgzjlyfsPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgzjlyfsList.isEmpty()) {
                totalCount = cgzjlyfsList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgzjlyfsListVO> pageResult = new PageResult<>(cgzjlyfsList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询专家来源方式成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询专家来源方式异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询专家来源方式失败：" + e.getMessage());
        }
    }
} 