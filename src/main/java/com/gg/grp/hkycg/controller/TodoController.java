package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.TodoQueryDTO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.model.vo.TodoItemVO;
import com.gg.grp.hkycg.service.TodoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 待办事项控制器
 * <AUTHOR>
 */
@Tag(name = "待办事项管理")
@Slf4j
@RestController
@RequestMapping("/todo")
public class TodoController {
    
    @Autowired
    private TodoService todoService;
    
    /**
     * 查询我的申请列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/myApplications")
    public Result<PageResult<TodoItemVO>> getMyApplications(@RequestBody(required = false) TodoQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new TodoQueryDTO();
            }
            
            log.info("查询我的申请列表，查询条件：{}", queryDTO);
            
            // 执行查询
            PageResult<TodoItemVO> result = todoService.getMyApplications(queryDTO);
            
            log.info("查询我的申请列表成功，总记录数：{}", result.getTotal());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("查询我的申请列表异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询我的申请列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询我已审核的列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/myAudited")
    public Result<PageResult<TodoItemVO>> getMyAudited(@RequestBody(required = false) TodoQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new TodoQueryDTO();
            }
            
            log.info("查询我已审核的列表，查询条件：{}", queryDTO);
            
            // 执行查询
            PageResult<TodoItemVO> result = todoService.getMyAudited(queryDTO);
            
            log.info("查询我已审核的列表成功，总记录数：{}", result.getTotal());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("查询我已审核的列表异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询我已审核的列表失败：" + e.getMessage());
        }
    }
} 