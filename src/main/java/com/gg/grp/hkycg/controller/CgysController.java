package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.common.exception.GlobalException;
import com.gg.grp.hkycg.model.dto.*;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.GpmCgysnr;
import com.gg.grp.hkycg.service.CgysService;
import com.gg.grp.hkycg.service.CgysnrService;
import com.gg.grp.hkycg.utils.CacheStatisticsUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购验收控制器
 * <AUTHOR>
 */
@Tag(name = "采购验收管理")
@Slf4j
@RestController
@RequestMapping("/cgys")
@Validated
public class CgysController {

    @Autowired
    private CgysService cgysService;

    @Autowired
    private CgysnrService cgysnrService;

    /**
     * 保存采购验收
     * @param cgysSaveDTO 采购验收保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgysSaveResponseVO> save(@Valid @RequestBody CgysSaveDTO cgysSaveDTO) {
        try {
            // 检查基础信息是否为空
            if (cgysSaveDTO.getBaseInfo() == null) {
                return Result.error("基础信息不能为空");
            }

            // 检查是否有明细
            boolean hasDetails = cgysSaveDTO.getCgysDetails() != null && !cgysSaveDTO.getCgysDetails().isEmpty();
            if (!hasDetails) {
                return Result.error("需要提供采购验收明细");
            }

            // 调用服务层保存
            CgysSaveResponseVO result = cgysService.saveCgys(cgysSaveDTO);
            CacheStatisticsUtils.clearStatisticsCache();
            
            return Result.success("保存采购验收成功", result);

        } catch (Exception e) {
            log.error("保存采购验收失败", e);
            throw new GlobalException("保存采购验收失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @param cgysSaveDTO 采购验收保存DTO
     * @return 统一响应结果
     */
    @PostMapping("/modifyCgys/{ysbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgysSaveResponseVO> updateCgys(@PathVariable String ysbh, @RequestBody CgysSaveDTO cgysSaveDTO) {
        try {
            // 手动验证必需字段
            if (cgysSaveDTO == null) {
                return Result.error("请求数据不能为空");
            }

            // 检查明细信息是否为空
            if (cgysSaveDTO.getCgysDetails() == null || cgysSaveDTO.getCgysDetails().isEmpty()) {
                return Result.error("采购验收明细不能为空");
            }
            
            // 设置默认值
            if (cgysSaveDTO.getBaseInfo() != null) {
                if (cgysSaveDTO.getBaseInfo().getGsdm() == null) {
                    cgysSaveDTO.getBaseInfo().setGsdm(LoginInfo.getCurrCorpCode());
                }
                if (cgysSaveDTO.getBaseInfo().getKjnd() == null) {
                    cgysSaveDTO.getBaseInfo().setKjnd(LoginInfo.getCurrAccountantYear());
                }
            }

            // 调用Service层的updateCgysByYsbh方法
            CgysSaveResponseVO result = cgysService.updateCgysByYsbh(ysbh, cgysSaveDTO);

            log.info("更新采购验收成功，ysbh：{}", ysbh);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("更新采购验收失败，ysbh: {}", ysbh, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据验收申请编号查询采购验收详情（RESTful风格）
     * @param ysbh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByYsbh/{ysbh}")
    public Result<CgysSaveResponseVO> getCgysDetail(@PathVariable String ysbh) {
        try {
            // 查询采购验收详情
            CgysSaveResponseVO cgysDetail = cgysService.getCgysDetailByYsbh(ysbh);
            
            return Result.success("查询采购验收成功", cgysDetail);
        } catch (Exception e) {
            log.error("查询采购验收异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("查询采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 删除采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/deleteByYsbh/{ysbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteCgys(@PathVariable String ysbh) {
        try {
            log.info("开始删除采购验收，验收申请编号：{}", ysbh);
            
            // 1. 查询采购验收详情以检查状态和权限
            CgysSaveResponseVO cgysDetail = cgysService.getCgysDetailByYsbh(ysbh);
            if (cgysDetail == null || cgysDetail.getBaseInfo() == null) {
                return Result.error("采购验收不存在");
            }
            
            // 2. 检查单据状态是否为保存状态（1）
            String status = cgysDetail.getBaseInfo().getZt();
            if (status == null || !"1".equals(status)) {
                String statusName = getStatusName(status);
                return Result.error("只有保存状态的采购验收才能删除，当前状态为：" + statusName);
            }
            
            // 3. 执行删除操作
            boolean result = cgysService.deleteByYsbh(ysbh);
            CacheStatisticsUtils.clearStatisticsCache();
            
            log.info("删除采购验收完成，验收申请编号：{}, 结果: {}", ysbh, result);
            
            return result ? Result.success("删除采购验收成功!") : Result.error("没有找到对应的采购验收或删除失败");
        } catch (Exception e) {
            if (e.getMessage() != null && (
                    e.getMessage().contains("只有保存状态") || 
                    e.getMessage().contains("只有创建人") ||
                    e.getMessage().contains("无法删除")
                )) {
                log.warn("删除采购验收失败，验收申请编号：{}，原因：{}", ysbh, e.getMessage());
                return Result.error(e.getMessage());
            }
            
            log.error("删除采购验收异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("删除采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 提交采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/commit/{ysbh}")
    public Result<CgysSaveResponseVO> commitCgys(@PathVariable String ysbh) {
        try {
            log.info("开始提交采购验收，验收申请编号：{}", ysbh);
            
            // 1. 查询采购验收详情获取金额
            CgysSaveResponseVO cgysDetail = cgysService.getCgysDetailByYsbh(ysbh);
            if (cgysDetail == null || cgysDetail.getBaseInfo() == null) {
                return Result.error("采购验收不存在");
            }
            
            BigDecimal money = cgysDetail.getBaseInfo().getBcysje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 检查单据状态是否为保存状态（1）
            String status = cgysDetail.getBaseInfo().getZt();
            if (status == null || !"1".equals(status)) {
                String statusName = getStatusName(status);
                return Result.error("只有保存状态的采购验收才能提交，当前状态为：" + statusName);
            }
            
            log.info("采购验收状态检查通过，开始提交，验收申请编号：{}，金额：{}", ysbh, money);
            
            // 3. 调用提交代理方法
            cgysService.commitProxyByYsbh(BillnoType.CGYS, ysbh, money.doubleValue(), LoginInfo.getCurrEmployeeName());
            
            log.info("采购验收提交完成，开始查询提交后的详情，验收申请编号：{}", ysbh);
            
            return Result.success("提交采购验收成功", cgysService.getCgysDetailByYsbh(ysbh));
            
        } catch (Exception e) {
            log.error("提交采购验收异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("提交采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询所有采购验收（RESTful风格）
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgysListVO>> getCgysPageList(CgysPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgysPageQueryDTO();
            }

            log.info("分页查询采购验收，查询条件：{}", queryDTO);

            // 执行查询
            List<CgysListVO> cgysList = cgysService.getCgysPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgysList.isEmpty()) {
                totalCount = cgysList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgysListVO> pageResult = new PageResult<>(cgysList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购验收成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购验收异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 审核采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCgys/{ysbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgysSaveResponseVO> auditCgys(@PathVariable String ysbh, @RequestBody AuditParamYsbh param) {
        try {
            // 设置验收申请编号
            param.setYsbh(ysbh);
            
            // 1. 查询采购验收详情获取金额
            CgysSaveResponseVO cgysDetail = cgysService.getCgysDetailByYsbh(ysbh);
            if (cgysDetail == null || cgysDetail.getBaseInfo() == null) {
                return Result.error("采购验收不存在");
            }
            
            BigDecimal money = cgysDetail.getBaseInfo().getBcysje();
            if (money == null) {
                money = BigDecimal.ZERO;
            }
            
            // 2. 调用审核方法
            cgysService.checkByYsbh(BillnoType.CGYS, ysbh, param.getOpinion(), param.getAuditor(), money);
            
            return Result.success("审核采购验收成功", cgysService.getCgysDetailByYsbh(ysbh));
            
        } catch (Exception e) {
            log.error("审核采购验收异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("审核采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 收回采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/callback/{ysbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgysSaveResponseVO> callBackCgys(@PathVariable String ysbh) {
        try {
            log.info("开始收回采购验收，验收申请编号：{}", ysbh);
            
            CgysSaveResponseVO result = cgysService.callBackByYsbh(ysbh);
            
            log.info("采购验收收回成功，验收申请编号：{}", ysbh);
            
            return Result.success("收回采购验收成功", result);
            
        } catch (Exception e) {
            log.error("收回采购验收异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("收回采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 退审采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @param param 审核参数
     * @return 统一响应结果
     */
    @PostMapping("/checkCallBack/{ysbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgysSaveResponseVO> rejectCgys(@PathVariable String ysbh, @RequestBody AuditParamYsbh param) {
        try {
            log.info("开始退审采购验收，验收申请编号：{}，退审人：{}", ysbh, param.getAuditor());
            
            // 设置验收申请编号
            param.setYsbh(ysbh);
            
            // 调用退审方法
            CgysSaveResponseVO result = cgysService.checkCallBackByYsbh(ysbh, param.getOpinion(), param.getAuditor());
            
            log.info("采购验收退审成功，验收申请编号：{}，退审人：{}", ysbh, param.getAuditor());
            
            return Result.success("退审采购验收成功", result);
            
        } catch (Exception e) {
            log.error("退审采购验收异常，验收申请编号：{}，退审人：{}，异常信息：{}", 
                     ysbh, param.getAuditor(), e.getMessage(), e);
            return Result.error("退审采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 销审采购验收（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/noAudit/{ysbh}")
    @Transactional(rollbackFor = Exception.class)
    public Result<CgysSaveResponseVO> cancelCgys(@PathVariable String ysbh) {
        try {
            log.info("开始销审采购验收，验收申请编号：{}", ysbh);
            
            CgysSaveResponseVO result = cgysService.noAuditByYsbh(ysbh);
            
            log.info("采购验收销审成功，验收申请编号：{}", ysbh);
            
            return Result.success("销审采购验收成功", result);
            
        } catch (Exception e) {
            log.error("销审采购验收异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("销审采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 查询审核记录（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/auditLog/{ysbh}")
    public Result<CgysAuditLogVO> getAuditLog(@PathVariable String ysbh) {
        try {
            CgysAuditLogVO result = cgysService.findCheckLogByYsbh(BillnoType.CGYS, ysbh);
            return Result.success("查询审核记录成功", result);
        } catch (Exception e) {
            log.error("查询采购验收审核记录异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            return Result.error("查询审核记录失败：" + e.getMessage());
        }
    }

    /**
     * 检查采购验收是否有权限审核（RESTful风格，使用验收申请编号）
     * @param ysbh 验收申请编号
     * @return 权限检查结果
     */
    @GetMapping("/checkAuthority/{ysbh}")
    public Object checkAuthority(@PathVariable String ysbh) {
        try {
            return cgysService.isCheckedByAuthorityByYsbh(ysbh);
        } catch (Exception e) {
            log.error("检查采购验收审核权限异常，验收申请编号：{}，异常信息：{}", ysbh, e.getMessage(), e);
            
            com.alibaba.fastjson.JSONObject errorResult = new com.alibaba.fastjson.JSONObject();
            errorResult.put("isCheck", true);
            errorResult.put("isDelete", true);
            errorResult.put("result", "系统异常，无法获取审核权限信息");
            return errorResult;
        }
    }

    /**
     * 获取状态名称
     * @param status 状态值
     * @return 状态名称
     */
    private String getStatusName(String status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case "1":
                return "保存";
            case "2":
                return "已提交";
            case "3":
                return "审核中";
            case "4":
                return "已审核";
            case "5":
                return "退回";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 根据验收申请编号查询采购验收详情（RESTful风格）
     * @param mxxh 验收申请编号
     * @return 统一响应结果
     */
    @GetMapping("/queryByMxxh/{mxxh}")
    public Result<CgysSaveResponseVO> getCgysByMxxh(@PathVariable String mxxh) {
        try {
            LambdaQueryWrapper<GpmCgysnr> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GpmCgysnr::getYsmxxh, mxxh);
            GpmCgysnr cgysnr = cgysnrService.getOne(queryWrapper);
            if (cgysnr == null){
                return Result.error("查询采购验收失败：申请编号不存在");
            }
            // 查询采购验收详情
            CgysSaveResponseVO cgysDetail = cgysService.getCgysDetailByYsbh(cgysnr.getYsbh());

            return Result.success("查询采购验收成功", cgysDetail);
        } catch (Exception e) {
            log.error("查询采购验收异常，验收申请编号：{}，异常信息：{}", mxxh, e.getMessage(), e);
            return Result.error("查询采购验收失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询所有采购验收
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/cgyslist")
    public Result<PageResult<CgysListVO>> getCgysList(CgysPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgysPageQueryDTO();
            }

            log.info("分页查询采购验收，查询条件：{}", queryDTO);

            // 执行查询
            List<CgysListVO> cgysList = cgysService.getCgysPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgysList.isEmpty()) {
                totalCount = cgysList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgysListVO> pageResult = new PageResult<>(cgysList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购验收成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购验收异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购验收失败：" + e.getMessage());
        }
    }

}