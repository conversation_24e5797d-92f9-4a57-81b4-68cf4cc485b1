package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CgfsPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgfsListVO;
import com.gg.grp.hkycg.model.vo.CgfsVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.service.CgfsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 采购方式控制器
 * <AUTHOR>
 */
@Tag(name = "采购方式管理")
@Slf4j
@RestController
@RequestMapping("/cgfs")
@Validated
public class CgfsController {

    @Autowired
    private CgfsService cgfsService;

    /**
     * 查询所有采购方式
     * @return 采购方式列表
     */
    @GetMapping("/all")
    public Result<List<CgfsVO>> getAllCgfs() {
        try {
            List<CgfsVO> cgfsList = cgfsService.getAllCgfs();
            return Result.success("查询采购方式成功", cgfsList);
        } catch (Exception e) {
            log.error("查询采购方式异常：{}", e.getMessage());
            return Result.error("查询采购方式失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询采购方式
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgfsListVO>> getCgfsPageList(CgfsPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgfsPageQueryDTO();
            }

            log.info("分页查询采购方式，查询条件：{}", queryDTO);

            // 执行查询
            List<CgfsListVO> cgfsList = cgfsService.getCgfsPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgfsList.isEmpty()) {
                totalCount = cgfsList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgfsListVO> pageResult = new PageResult<>(cgfsList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购方式成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购方式异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购方式失败：" + e.getMessage());
        }
    }
} 