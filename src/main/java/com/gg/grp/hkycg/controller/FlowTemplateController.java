package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.enums.BillnoType;
import com.gg.grp.hkycg.mapper.*;
import com.gg.grp.hkycg.model.dto.CheckFlowDto;
import com.gg.grp.hkycg.model.dto.FlowDto;
import com.gg.grp.hkycg.model.dto.FlowTemplateDTO;
import com.gg.grp.hkycg.model.vo.*;
import com.gg.grp.hkycg.model.pojo.*;
import com.gg.grp.hkycg.service.PubFlowTemplateTemporaryService;
import com.gg.grp.hkycg.service.PubObjFlowTempService;
import com.gg.grp.hkycg.utils.BeanConvertUtils;
import com.gg.grp.hkycg.utils.ConvertUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程模板控制器
 * <AUTHOR>
 */
@Tag(name = "流程模板管理")
@RestController
@RequestMapping("/flow/template")
public class FlowTemplateController {

    /**
     * 审核流程模板推送前临时表
     */
    @Autowired
    private PubFlowTemplateTemporaryService templateTemporaryService;

    /**
     * 审核流程模板表的编辑专用服务
     */
    @Autowired
    private PubObjFlowTempService pubObjFlowTempService;

    /**
     * 获取所有单据类型
     * @return
     */
    @GetMapping("/getBillType")
    public Result<List<BillTypeVO>> getBillType() {
        List<BillnoType> typeList = Arrays.stream(BillnoType.values())
                .collect(Collectors.toList());
        List<BillTypeVO> billTypeVoS = BeanConvertUtils
                .convertListTo(typeList, BillTypeVO::new);
        return Result.success("查询所有单据类型成功!", billTypeVoS);
    }

    /**
     * 获取单据类型的审核节点及其审核人
     * @param billTypeId 单据类型id
     * @return 审核节点列表及其审核人
     */
    @GetMapping("/getNodes")
    public Result getNodes(@RequestParam("billTypeId") Integer billTypeId) {
        CheckFlowTemplateBillVO checkFlowTemplateBillVO = new CheckFlowTemplateBillVO();
        checkFlowTemplateBillVO.setBillTypeId(billTypeId);
        checkFlowTemplateBillVO.setBillTypeName(BillnoType.findByCode(billTypeId).getComment());

        LambdaQueryWrapper<PubObjFlowTemp> pubObjFlowTempWrapper = new LambdaQueryWrapper<>();
        pubObjFlowTempWrapper.eq(PubObjFlowTemp::getGsdm, LoginInfo.getCurrCorpCode());
        pubObjFlowTempWrapper.eq(PubObjFlowTemp::getDjlxid, billTypeId);
        pubObjFlowTempWrapper.orderByAsc(PubObjFlowTemp::getXh);
        List<PubObjFlowTemp> pubObjFlowTempList = pubObjFlowTempService.list(pubObjFlowTempWrapper);
        List<FlowTemplateNodeVO> nodeVoList = new ArrayList<>();

        FlowTemplateNodeVO currentFlowTemplateNodeVO = null;
        String lastXh = null;

        for (PubObjFlowTemp pubObjFlowTemp : pubObjFlowTempList) {
            String currentXh = pubObjFlowTemp.getXh();

            // 如果序号变化或当前节点为空，创建新节点
            if (currentFlowTemplateNodeVO == null || !currentXh.equals(lastXh)) {
                currentFlowTemplateNodeVO = new FlowTemplateNodeVO();
                currentFlowTemplateNodeVO.setJddm(Integer.valueOf(currentXh));
                currentFlowTemplateNodeVO.setJdmc(pubObjFlowTemp.getJdmc());
                currentFlowTemplateNodeVO.setJdshtj(pubObjFlowTemp.getJdshtj());
                currentFlowTemplateNodeVO.setNodeAuditorList(new ArrayList<>());
                currentFlowTemplateNodeVO.setXh(currentXh);
                nodeVoList.add(currentFlowTemplateNodeVO);
                lastXh = currentXh;
            }

            // 添加审批人信息到当前节点
            FlowTemplateAuditorVO auditorVo = new FlowTemplateAuditorVO();
            auditorVo.setShrdm(pubObjFlowTemp.getShrdm());
            auditorVo.setShrxm(pubObjFlowTemp.getShrxm());
            auditorVo.setDynamicAcquisition(ConvertUtils.convertStringToBoolean(pubObjFlowTemp.getDynamicAcquisition()));
            auditorVo.setShtj(pubObjFlowTemp.getShtj());

            currentFlowTemplateNodeVO.getNodeAuditorList().add(auditorVo);
        }

        checkFlowTemplateBillVO.setNodeList(nodeVoList);
        return Result.success("查询成功!", checkFlowTemplateBillVO);
    }

    /**
     * 获取单据类型的审核节点及其审核人
     * @param templateBillVo 模板单信息
     * @return 审核节点列表及其审核人
     */
    @PostMapping("/saveNodes")
    public Result saveNodes(@RequestBody CheckFlowTemplateBillVO templateBillVo) {
        List<FlowTemplateNodeVO> nodeList = templateBillVo.getNodeList();
        templateTemporaryService.checkBeforeSave(nodeList);

        int count = 1;
        List<PubObjFlowTemp> pubObjFlowTempList = new ArrayList<>();
        for (FlowTemplateNodeVO node : nodeList){
            int finalCount = count;
            node.getNodeAuditorList().forEach(auditor -> {
                PubObjFlowTemp pubObjFlowTemp = new PubObjFlowTemp();
                pubObjFlowTemp.setJddm(node.getJddm());
                pubObjFlowTemp.setJdmc(node.getJdmc());
                pubObjFlowTemp.setJdshtj(node.getJdshtj());
                pubObjFlowTemp.setDjlxid(templateBillVo.getBillTypeId());
                pubObjFlowTemp.setXh(String.valueOf(finalCount));
                pubObjFlowTemp.setGsdm(LoginInfo.getCurrCorpCode());
                pubObjFlowTemp.setShrdm(StringUtils.isNotBlank(auditor.getShrdm())?auditor.getShrdm():"");
                pubObjFlowTemp.setShrxm(StringUtils.isNotBlank(auditor.getShrxm())?auditor.getShrxm():"");
                pubObjFlowTemp.setShtj(auditor.getShtj());
                pubObjFlowTemp.setDynamicAcquisition(ConvertUtils.convertBooleanToString(auditor.getDynamicAcquisition()));
                pubObjFlowTempList.add(pubObjFlowTemp);
            });
            count++;
        }
        LambdaQueryWrapper<PubObjFlowTemp> pubObjFlowTempWrapper = new LambdaQueryWrapper<>();
        pubObjFlowTempWrapper.eq(PubObjFlowTemp::getDjlxid, templateBillVo.getBillTypeId());
        pubObjFlowTempService.remove(pubObjFlowTempWrapper);
        pubObjFlowTempService.saveBatch(pubObjFlowTempList);
        return Result.success("保存成功!", getNodes(templateBillVo.getBillTypeId()));
    }

    /**
     * 删除临时保存的节点列表
     * @param templateBillVo 模板单信息
     * @return 正式流程的节点
     */
    @PostMapping("/deleteNodes")
    public Result deleteNodes(@RequestBody CheckFlowTemplateBillVO templateBillVo) {
        LambdaQueryWrapper<PubObjFlowTemp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PubObjFlowTemp::getGsdm, LoginInfo.getCurrCorpCode());
        wrapper.eq(PubObjFlowTemp::getDjlxid, templateBillVo.getBillTypeId());
        pubObjFlowTempService.remove(wrapper);
        return Result.success("删除成功!", getNodes(templateBillVo.getBillTypeId()));
    }

    @Autowired
    private GpmCgjhmlMapper cgjhmlMapper;
    @Autowired
    private GpmCgsqmlMapper cgsqmlMapper;
    @Autowired
    private GpmCgzbsqmlMapper cgzbsqmlMapper;
    @Autowired
    private GpmCgdjmlMapper cgdjmlMapper;
    @Autowired
    private GpmCgysmlMapper cgysmlMapper;

    /**
     * 获取审核流程在使用某一单据生成的流程，并校验其合法性
     * @return 审核节点列表及其审核人
     */
    @PostMapping("/cfl")
    public Result checkFlowLegally(@RequestBody CheckFlowDto checkFlowDto) {
        Integer billTypeId = checkFlowDto.getBillTypeId();
        String billNo = checkFlowDto.getBillNo();
        BillHeadVO billHeadVo = new BillHeadVO();
        switch (BillnoType.findByCode(billTypeId)) {
            case CGJH:
                LambdaQueryWrapper<GpmCgjhml> cgjhWrapper = new LambdaQueryWrapper<>();
                cgjhWrapper.eq(GpmCgjhml::getJhbh, billNo);
                GpmCgjhml cgjhml = cgjhmlMapper.selectOne(cgjhWrapper);
                billHeadVo.setDjh(cgjhml.getJhbh());
                billHeadVo.setBillType(BillnoType.CGJH);
                billHeadVo.setBillNo(cgjhml.getJhbh());
                billHeadVo.setBillAmount(cgjhml.getJhje());
                billHeadVo.setCreator(String.valueOf(cgjhml.getLrrId()));
                billHeadVo.setBillNote(cgjhml.getSqyjjly());
                break;
            case CGSQ:
                LambdaQueryWrapper<GpmCgsqml> cgsqWrapper = new LambdaQueryWrapper<>();
                cgsqWrapper.eq(GpmCgsqml::getSqbh, billNo);
                GpmCgsqml cgsqml = cgsqmlMapper.selectOne(cgsqWrapper);
                billHeadVo.setDjh(cgsqml.getSqbh());
                billHeadVo.setBillType(BillnoType.CGSQ);
                billHeadVo.setBillNo(cgsqml.getSqbh());
                billHeadVo.setBillAmount(cgsqml.getSqje());
                billHeadVo.setCreator(String.valueOf(cgsqml.getCjrdm()));
                billHeadVo.setBillNote(cgsqml.getSqyjjly());
                break;
            case CGZB:
                LambdaQueryWrapper<GpmCgzbsqml> cgzbWrapper = new LambdaQueryWrapper<>();
                cgzbWrapper.eq(GpmCgzbsqml::getZbsqbh, billNo);
                GpmCgzbsqml cgzbml = cgzbsqmlMapper.selectOne(cgzbWrapper);
                billHeadVo.setDjh(cgzbml.getZbsqbh());
                billHeadVo.setBillType(BillnoType.CGZB);
                billHeadVo.setBillNo(cgzbml.getZbsqbh());
                billHeadVo.setBillAmount(cgzbml.getCgsqje());
                billHeadVo.setCreator(String.valueOf(cgzbml.getCjrdm()));
                billHeadVo.setBillNote(cgzbml.getBz());
                break;
            case CGJG:
                LambdaQueryWrapper<GpmCgdjml> cgjgWrapper = new LambdaQueryWrapper<>();
                cgjgWrapper.eq(GpmCgdjml::getJgdjbh, billNo);
                GpmCgdjml cgjgml = cgdjmlMapper.selectOne(cgjgWrapper);
                billHeadVo.setDjh(cgjgml.getJgdjbh());
                billHeadVo.setBillType(BillnoType.CGJG);
                billHeadVo.setBillNo(cgjgml.getJgdjbh());
                billHeadVo.setBillAmount(cgjgml.getJgdjje());
                billHeadVo.setCreator(String.valueOf(cgjgml.getCreateUserDm()));
                billHeadVo.setBillNote(cgjgml.getBz());
                break;
            case CGYS:
                LambdaQueryWrapper<GpmCgysml> cgysWrapper = new LambdaQueryWrapper<>();
                cgysWrapper.eq(GpmCgysml::getYsbh, billNo);
                GpmCgysml cgysml = cgysmlMapper.selectOne(cgysWrapper);
                billHeadVo.setDjh(cgysml.getYsbh());
                billHeadVo.setBillType(BillnoType.CGYS);
                billHeadVo.setBillNo(cgysml.getYsbh());
                billHeadVo.setBillAmount(cgysml.getCgsqje());
                billHeadVo.setCreator(String.valueOf(cgysml.getCreateUserDm()));
                billHeadVo.setBillNote(cgysml.getBz());
                break;
        }
        List<FlowTemplateDTO> flowTemplateDtoList = templateTemporaryService
                .queryCacheNodes(billTypeId);
        FlowDto flowDto = new FlowDto();
        flowDto.setDjh(billHeadVo.getDjh());
        flowDto.setBillType(billHeadVo.getBillType());
        try {
            List<ObjflowVO> objflowVoList = pubObjFlowTempService
                    .commitTemporary(flowTemplateDtoList, flowDto);
            CheckFlowVO checkFlowVO = new CheckFlowVO();
            checkFlowVO.setBillHead(billHeadVo);
            checkFlowVO.setObjflowVoList(objflowVoList);
            //无异常获取到单据的流程后表示流程合法
            templateTemporaryService.legal(billTypeId);
            return Result.success("校验成功!", checkFlowVO);
        } catch (Exception exception) {
            exception.printStackTrace();
            return Result.error("使用单据" + billHeadVo.getBillNo()
                    + "生成流程时，" + exception.getMessage());
        }
    }

}
