package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CgzjjgPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CgzjjgListVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.service.CgzjjgService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购中介机构控制器
 * <AUTHOR>
 */
@Tag(name = "中介机构管理")
@Slf4j
@RestController
@RequestMapping("/cgzjjg")
@Validated
public class CgzjjgController {

    @Autowired
    private CgzjjgService cgzjjgService;

    /**
     * 分页查询所有采购中介机构
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @GetMapping("/list")
    public Result<PageResult<CgzjjgListVO>> getCgzjjgPageList(CgzjjgPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CgzjjgPageQueryDTO();
            }

            log.info("分页查询采购中介机构，查询条件：{}", queryDTO);

            // 执行查询
            List<CgzjjgListVO> cgzjjgList = cgzjjgService.getCgzjjgPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cgzjjgList.isEmpty()) {
                totalCount = cgzjjgList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CgzjjgListVO> pageResult = new PageResult<>(cgzjjgList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购中介机构成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购中介机构异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购中介机构失败：" + e.getMessage());
        }
    }
}