package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.IndexParams;
import com.gg.grp.hkycg.model.vo.IndexVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.service.IndexService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 预算指标控制器
 * <AUTHOR>
 */
@Tag(name = "预算指标管理")
@Slf4j
@RestController
@RequestMapping("/index")
@Validated
public class IndexController {

    @Autowired
    private IndexService indexService;

    /**
     * 指标查询
     * @param params 查询参数
     * @return 查询结果
     */
    @ResponseBody
    @RequestMapping(value = "qryIndex", method = RequestMethod.GET)
    public Object qryIndex(IndexParams params){
        try {
            // 执行查询获取IndexVO列表
            List<IndexVO> indexList = indexService.qryIndex(params);
            
            // 获取总记录数
            long totalCount = 0;
            if (!indexList.isEmpty()) {
                totalCount = indexList.get(0).getTotalCount();
            }
            
            // 创建分页结果 - 参考JsonResult.renderSuccess的格式
            PageResult<IndexVO> pageResult = new PageResult<>(indexList, totalCount, params.getPages(), params.getPageNum());
            
            // 直接返回分页结果，不包装在Result中，类似参考接口
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("指标查询异常：{}", e.getMessage(), e);
            return Result.error("查询指标失败：" + e.getMessage());
        }
    }
} 