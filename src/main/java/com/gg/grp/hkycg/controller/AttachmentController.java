package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.AttachmentUploadDTO;
import com.gg.grp.hkycg.model.vo.AttachmentVO;
import com.gg.grp.hkycg.service.AttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 附件控制器
 * <AUTHOR>
 */
@Tag(name = "附件管理")
@RestController
@RequestMapping("/attachment")
@Slf4j
public class AttachmentController {
    
    @Autowired
    private AttachmentService attachmentService;
    
    /**
     * 上传单个附件
     * @param file 文件
     * @return 附件信息
     */
    @Operation(summary = "上传单个附件")
    @PostMapping("/upload")
    public Result<AttachmentVO> uploadAttachment(
            @RequestParam("file") MultipartFile file,
            AttachmentUploadDTO attachmentUploadDTO) {
        try {
            log.info("上传附件，单据类型：{}，单据号：{}，是否明细：{}", attachmentUploadDTO.getDjlx(), attachmentUploadDTO.getDjh(), attachmentUploadDTO.getSfmx());
            
            // 参数校验
            if (file == null || file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }
            
            // 调用服务上传附件
            AttachmentVO result = attachmentService.uploadAttachment(file, attachmentUploadDTO);
            
            log.info("上传附件成功，附件ID：{}", result.getFjid());
            return Result.success(result);
        } catch (Exception e) {
            log.error("上传附件异常", e);
            return Result.error("上传附件失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量上传附件
     * @param files 文件列表
     * @return 附件信息列表
     */
    @Operation(summary = "批量上传附件")
    @PostMapping("/batch-upload")
    public Result<List<AttachmentVO>> batchUploadAttachments(
            @RequestParam("files") MultipartFile[] files,
            AttachmentUploadDTO attachmentUploadDTO) {
        try {
            log.info("批量上传附件，单据类型：{}，单据号：{}，是否明细：{}", attachmentUploadDTO.getDjlx(), attachmentUploadDTO.getDjh(), attachmentUploadDTO.getSfmx());
            
            // 参数校验
            if (files == null || files.length == 0) {
                return Result.error("上传文件不能为空");
            }

            // 转换文件数组为列表
            List<MultipartFile> fileList = new ArrayList<>();
            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    fileList.add(file);
                }
            }
            
            // 调用服务批量上传附件
            List<AttachmentVO> result = attachmentService.batchUploadAttachments(fileList, attachmentUploadDTO);
            
            log.info("批量上传附件成功，数量：{}", result.size());
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量上传附件异常", e);
            return Result.error("批量上传附件失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询单据关联的附件列表
     * @param djlx 单据类型
     * @param djh 单据号
     * @param sfmx 是否明细
     * @return 附件列表
     */
    @Operation(summary = "查询单据关联的附件列表")
    @GetMapping("/list")
    public Result<List<AttachmentVO>> queryAttachmentsByBill(
            @RequestParam("djlx") String djlx,
            @RequestParam("djh") String djh,
            @RequestParam(value = "sfmx", required = false, defaultValue = "0") String sfmx) {
        try {
            log.info("查询单据关联的附件列表，单据类型：{}，单据号：{}，是否明细：{}", djlx, djh, sfmx);
            
            // 调用服务查询附件列表
            List<AttachmentVO> result = attachmentService.queryAttachmentsByBill(djlx, djh, sfmx);
            
            log.info("查询单据关联的附件列表成功，数量：{}", result.size());
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询单据关联的附件列表异常", e);
            return Result.error("查询单据关联的附件列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询单据的目录、明细和所有相关附件
     * 先查询目录和明细，再查询所有相关附件
     * @param djlx 单据类型
     * @param djh 单据号
     * @return 包含目录明细和附件列表的结果
     */
    @Operation(summary = "查询单据的目录、明细和所有相关附件")
    @GetMapping("/list-with-directory")
    public Result<Map<String, Object>> queryAttachmentsWithDirectoryAndDetail(
            @RequestParam("djlx") String djlx,
            @RequestParam("djh") String djh) {
        try {
            log.info("查询单据的目录、明细和所有相关附件，单据类型：{}，单据号：{}", djlx, djh);
            
            // 调用服务查询附件列表
            Map<String, Object> result = attachmentService.queryAttachmentsWithDirectoryAndDetail(djlx, djh);
            
            log.info("查询单据的目录、明细和所有相关附件成功");
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询单据的目录、明细和所有相关附件异常", e);
            return Result.error("查询单据的目录、明细和所有相关附件失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除附件
     * @param fjid 附件ID
     * @return 是否成功
     */
    @Operation(summary = "删除附件")
    @DeleteMapping("/{fjid}")
    public Result<Boolean> deleteAttachment(@PathVariable("fjid") String fjid) {
        try {
            log.info("删除附件，附件ID：{}", fjid);
            
            // 调用服务删除附件
            boolean result = attachmentService.deleteAttachment(fjid);
            
            if (result) {
                log.info("删除附件成功，附件ID：{}", fjid);
                return Result.success(true);
            } else {
                log.warn("删除附件失败，附件ID：{}", fjid);
                return Result.error("删除附件失败");
            }
        } catch (Exception e) {
            log.error("删除附件异常", e);
            return Result.error("删除附件失败：" + e.getMessage());
        }
    }
    
    /**
     * 下载附件
     * @param fjid 附件ID
     * @param response HTTP响应
     */
    @Operation(summary = "下载附件")
    @GetMapping("/download/{fjid}")
    public void downloadAttachment(
            @PathVariable("fjid") String fjid,
            HttpServletResponse response) {
        try {
            log.info("下载附件，附件ID：{}", fjid);
            
            // 调用服务下载附件
            attachmentService.downloadAttachment(fjid, response);
            
            log.info("下载附件成功，附件ID：{}", fjid);
        } catch (Exception e) {
            log.error("下载附件异常", e);
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("下载附件失败：" + e.getMessage());
                response.getWriter().flush();
            } catch (Exception ex) {
                log.error("设置下载错误响应异常", ex);
            }
        }
    }
    
    /**
     * 预览附件
     * @param fjid 附件ID
     * @param response HTTP响应
     */
    @Operation(summary = "预览附件")
    @GetMapping("/preview/{fjid}")
    public void previewAttachment(
            @PathVariable("fjid") String fjid,
            HttpServletResponse response) {
        try {
            log.info("预览附件，附件ID：{}", fjid);
            
            // 调用服务预览附件
            attachmentService.previewAttachment(fjid, response);
            
            log.info("预览附件成功，附件ID：{}", fjid);
        } catch (Exception e) {
            log.error("预览附件异常", e);
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("预览附件失败：" + e.getMessage());
                response.getWriter().flush();
            } catch (Exception ex) {
                log.error("设置预览错误响应异常", ex);
            }
        }
    }
} 