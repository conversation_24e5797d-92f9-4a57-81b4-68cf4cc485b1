package com.gg.grp.hkycg.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gg.grp.hkycg.model.dto.LoginDTO;
import com.gg.grp.hkycg.model.pojo.Dept;
import com.gg.grp.hkycg.model.pojo.Employee;
import com.gg.grp.hkycg.service.EmployeeService;
import com.gg.grp.hkycg.service.DeptService;
import com.gg.grp.hkycg.annotation.RateLimit;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.redis.RedisSessionManager;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.consts.StaticValue;
import com.gg.grp.hkycg.common.security.JwtUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录Controller
 * <AUTHOR>
 */
@Tag(name = "登录管理")
@RestController
public class LoginController {
    @Autowired
    private EmployeeService employeeService;
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private RedisSessionManager redisSessionManager;

    @Autowired
    private DeptService deptService;

    @SuppressWarnings("unchecked")
    @ResponseBody
    @PostMapping("/login")
    @RateLimit(
        key = "login",
        window = 60,
        maxRequests = 5,
        maxFailures = 3,
        blacklistDuration = 30,
        message = "登录请求过于频繁，请稍后再试"
    )
    public Object loginPost(LoginDTO loginDTO, HttpServletRequest request){
        String userName = loginDTO.getUserCode();
        String password = loginDTO.getPassword();
        
        if (StringUtils.isEmpty(userName) || StringUtils.isEmpty(password)) {
            // 参数错误，抛出异常以便记录失败次数
            throw new BadCredentialsException("用户名或密码不能为空");
        }
        
        try {
            // 将登录信息打包成JSON字符串作为用户名传递
            Map<String, String> loginInfomap = new HashMap<>();
            loginInfomap.put("userCode", userName);
            loginInfomap.put("businessDate", loginDTO.getBusinessDate());
            loginInfomap.put("isCheck", loginDTO.getIsCheck());
            String loginInfoJson = JSON.toJSONString(loginInfomap);

            // 创建认证令牌，businessDate和isCheck信息已包含在loginInfoJson中
            UsernamePasswordAuthenticationToken authToken =
                    new UsernamePasswordAuthenticationToken(loginInfoJson, password);

            // 执行认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 设置安全上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 然后生成JWT token，businessDate和isCheck信息已包含在userName(loginInfoJson)中
            UsernamePasswordAuthenticationToken jwtToken = new UsernamePasswordAuthenticationToken(
                    loginInfoJson, password, authentication.getAuthorities());
            String jwt = jwtUtils.generateJwtToken(jwtToken);

            // 获取用户信息
            LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Employee::getEmployeeCode, userName);
            queryWrapper.eq(Employee::getGsdm, StaticValue.getGsdm());
            queryWrapper.eq(Employee::getKjnd,loginDTO.getBusinessDate().substring(0,4));
            List<Employee> employees = employeeService.list(queryWrapper);
            Employee employee = employees.get(0);

            if (employee != null) {
                // 获取部门名称
                String deptName = null;
                if (employee.getBmdm() != null) {
                    try {
                        deptName = deptService.getDeptNameByCode(employee.getBmdm());
                    } catch (Exception e) {
                        System.err.println("获取部门名称失败: " + e.getMessage());
                        deptName = ""; // 设置为空字符串而不是null
                    }
                }

                // 使用新的JWT生成方法，包含完整用户信息
                String jwtWithUserInfo = jwtUtils.generateJwtTokenWithUserInfo(
                    employee.getEmployeeCode(),
                    employee.getEmployeeName(),
                    employee.getGsdm(),
                    employee.getBmdm(),
                    deptName,
                    employee.getKjnd(),
                    loginDTO.getBusinessDate(),
                    loginDTO.getIsCheck()
                );

                // 设置登录信息到ThreadLocal
                LoginInfo loginInfo = new LoginInfo();
                loginInfo.setEmployeeCode(employee.getEmployeeCode());
                loginInfo.setEmployeeName(employee.getEmployeeName());
                loginInfo.setCorpCode(employee.getGsdm());
                loginInfo.setDeptCode(employee.getBmdm());
                loginInfo.setDeptName(deptName);
                loginInfo.setAccountantYear(employee.getKjnd());
                loginInfo.setBusiDate(loginDTO.getBusinessDate());
                loginInfo.setCzyId(employee.getEmployeeCode());
                LoginInfo.setLoginInfo(loginInfo);

                // 将用户信息存入Redis
                try {
                    // 使用管道版本提高性能
                    redisSessionManager.saveUserSessionWithPipeline(employee, loginDTO.getBusinessDate(), loginDTO.getIsCheck(), jwtWithUserInfo);
                } catch (Exception redisException) {
                    System.err.println("Redis存储失败: " + redisException.getMessage());
                    // Redis失败不影响登录流程，降级使用普通版本
                    try {
                        redisSessionManager.saveUserSession(employee, loginDTO.getBusinessDate(), jwtWithUserInfo);
                    } catch (Exception fallbackException) {
                        System.err.println("Redis降级存储也失败: " + fallbackException.getMessage());
                    }
                }
                return Result.success("登陆成功!",jwtWithUserInfo);
            }

//            System.out.println("用户登录成功: " + userName + ", IP: " + getClientIp(request));
//            return Result.success("登陆成功!",jwtWithUserInfo);

        } catch (AuthenticationException e) {
            // 认证失败，抛出异常以便RateLimitAspect记录失败次数
            System.err.println("登录认证失败: " + userName + ", IP: " + getClientIp(request) + ", 原因: " + e.getMessage());
            throw new BadCredentialsException("用户名或密码错误");
        } catch (Exception e) {
            System.err.println("登录异常: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            e.printStackTrace();
            // 其他异常也抛出，以便记录失败次数
            throw new RuntimeException("登录系统异常");
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 用户登出
     */
    @GetMapping(value = "loginOut")
    public Object loginOut(HttpServletRequest req, HttpServletResponse resp){
        try {
            // 获取当前用户信息
            LoginInfo currentLoginInfo = LoginInfo.getLoginInfo();
            String employeeCode = null;
            if (currentLoginInfo != null) {
                employeeCode = currentLoginInfo.getEmployeeCode();
            }
            
            // 清除安全上下文
            SecurityContextHolder.clearContext();
            
            // 清除登录信息
            LoginInfo.loginOut();
            
            // 清除session信息
            HttpSession session = req.getSession(false);
            if (session != null) {
                session.invalidate();
            }
            
            // 清除Redis中的用户信息
            if (employeeCode != null) {
                try {
                    redisSessionManager.clearUserSession(employeeCode);
                } catch (Exception redisException) {
                    System.err.println("清理Redis失败: " + redisException.getMessage());
                }
            }
            
            return Result.success("登出成功");
        } catch (Exception e) {
            return Result.error("登出失败");
        }
    }

    @PostMapping("/main")
    public Result getSession() {
        // 获取当前用户信息
        JSONObject loginInfo = (JSONObject) JSONObject.toJSON(LoginInfo.getLoginInfo());
        if (loginInfo!=null){
            LambdaQueryWrapper<Dept> deptWrapper = new LambdaQueryWrapper<>();
            deptWrapper.eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                    .eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                    .eq(Dept::getBmdm, LoginInfo.getCurrDeptCode());
            Dept dept = deptService.getOne(deptWrapper);
            loginInfo.put("tel",StringUtils.isNotBlank(dept.getTel())?dept.getTel():"");
            loginInfo.put("deptName",StringUtils.isNotBlank(dept.getBmmc())?dept.getBmmc():"");
        }
        return Result.success("查询成功!",loginInfo);
    }

}
