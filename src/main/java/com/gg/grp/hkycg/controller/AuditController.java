package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.PageResult;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.UnifiedAuditQueryDTO;
import com.gg.grp.hkycg.model.vo.UnifiedAuditListVO;
import com.gg.grp.hkycg.service.AuditService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统一审核控制器
 * <AUTHOR>
 */
@Tag(name = "所有单据的审核管理")
@RestController
@RequestMapping("/audit")
@Slf4j
public class AuditController {

    @Autowired
    private AuditService auditService;

    @GetMapping("/list")
    public Result<PageResult<UnifiedAuditListVO>> getUnifiedAuditList(UnifiedAuditQueryDTO queryDTO) {
        PageResult<UnifiedAuditListVO> pageResult = auditService.getUnifiedAuditList(queryDTO);
        return Result.success(pageResult);
    }
}