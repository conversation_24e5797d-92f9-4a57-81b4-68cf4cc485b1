package com.gg.grp.hkycg.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gg.grp.hkycg.common.LoginInfo;
import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.DeptPageQueryDTO;
import com.gg.grp.hkycg.model.dto.DeptSaveDTO;
import com.gg.grp.hkycg.model.dto.DeptDeleteDTO;
import com.gg.grp.hkycg.model.vo.DeptListVO;
import com.gg.grp.hkycg.model.pojo.Dept;
import com.gg.grp.hkycg.service.DeptService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门信息控制器
 * <AUTHOR>
 */
@Tag(name = "部门信息管理")
@Slf4j
@RestController
@RequestMapping("/dept")
public class BMXXController {

    @Autowired
    private DeptService deptService;

    /**
     * 分页查询部门信息（支持模糊搜索）
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Result<IPage<DeptListVO>> getDeptPage(@RequestBody(required = false) DeptPageQueryDTO queryDTO) {
        try {
            // 如果请求体为空，创建默认参数
            if (queryDTO == null) {
                queryDTO = new DeptPageQueryDTO();
            }

            // 创建分页对象
            Page<Dept> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());

            // 构建查询条件
            LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear());
            queryWrapper.eq(Dept::getGsdm, LoginInfo.getCurrCorpCode());
            queryWrapper.ne(Dept::getBmmc, "停用");

            // 部门代码模糊查询
            if (StringUtils.hasText(queryDTO.getDeptCode())) {
                queryWrapper.like(Dept::getBmdm, queryDTO.getDeptCode());
            }

            // 部门名称模糊查询
            if (StringUtils.hasText(queryDTO.getDeptName())) {
                queryWrapper.like(Dept::getBmmc, queryDTO.getDeptName());
            }

            // 按部门代码排序
            queryWrapper.orderByAsc(Dept::getBmdm);

            // 执行分页查询
            IPage<Dept> deptPage = deptService.page(page, queryWrapper);

            // 转换为VO对象
            Page<DeptListVO> resultPage = new Page<>(deptPage.getCurrent(), deptPage.getSize(), deptPage.getTotal());
            List<DeptListVO> voList = new ArrayList<>();
            deptPage.getRecords().forEach(dept -> {
                DeptListVO vo = new DeptListVO();
                BeanUtils.copyProperties(dept, vo);
                vo.setDeptName(dept.getBmmc());
                vo.setDeptCode(dept.getBmdm());
                vo.setFzrdm(dept.getFzrdm());
                voList.add(vo);
            });
            resultPage.setRecords(voList);

            return Result.success("查询部门信息成功", resultPage);

        } catch (Exception e) {
            log.error("分页查询部门信息异常：{}", e.getMessage(), e);
            return Result.error("查询部门信息失败：" + e.getMessage());
        }
    }

    /**
     * 新增部门
     * @param deptSaveDTO 部门保存信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<String> addDept(@Valid @RequestBody DeptSaveDTO deptSaveDTO) {
        try {
            // 检查部门代码是否已存在
            LambdaQueryWrapper<Dept> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                       .eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                       .eq(Dept::getBmdm, deptSaveDTO.getBmdm());

            if (deptService.count(checkWrapper) > 0) {
                return Result.error("部门代码已存在");
            }

            // 创建部门实体
            Dept dept = new Dept();
            BeanUtils.copyProperties(deptSaveDTO, dept);

            // 设置必要字段
            dept.setKjnd(LoginInfo.getCurrAccountantYear());
            dept.setGsdm(LoginInfo.getCurrCorpCode());
            dept.setJlrId(LoginInfo.getCurrEmployeeCode());
            dept.setJlRq(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 保存部门
            boolean success = deptService.save(dept);

            if (success) {
                return Result.success("新增部门成功");
            } else {
                return Result.error("新增部门失败");
            }

        } catch (Exception e) {
            log.error("新增部门异常：{}", e.getMessage(), e);
            return Result.error("新增部门失败：" + e.getMessage());
        }
    }

    /**
     * 批量新增部门
     * @param deptSaveDTOList 部门保存信息列表
     * @return 操作结果
     */
    @PostMapping("/batchAdd")
    public Result<String> batchAddDept(@Valid @RequestBody List<DeptSaveDTO> deptSaveDTOList) {
        try {
            if (deptSaveDTOList == null || deptSaveDTOList.isEmpty()) {
                return Result.error("部门数据不能为空");
            }

            List<Dept> deptList = new ArrayList<>();
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            for (DeptSaveDTO deptSaveDTO : deptSaveDTOList) {
                // 检查部门代码是否已存在
                LambdaQueryWrapper<Dept> checkWrapper = new LambdaQueryWrapper<>();
                checkWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                           .eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                           .eq(Dept::getBmdm, deptSaveDTO.getBmdm());

                if (deptService.count(checkWrapper) > 0) {
                    return Result.error("部门代码【" + deptSaveDTO.getBmdm() + "】已存在");
                }

                // 创建部门实体
                Dept dept = new Dept();
                BeanUtils.copyProperties(deptSaveDTO, dept);

                // 设置必要字段
                dept.setKjnd(LoginInfo.getCurrAccountantYear());
                dept.setGsdm(LoginInfo.getCurrCorpCode());
                dept.setJlrId(LoginInfo.getCurrEmployeeCode());
                dept.setJlRq(currentTime);

                deptList.add(dept);
            }

            // 批量保存部门
            boolean success = deptService.saveBatch(deptList);

            if (success) {
                return Result.success("批量新增部门成功，共新增" + deptList.size() + "个部门");
            } else {
                return Result.error("批量新增部门失败");
            }

        } catch (Exception e) {
            log.error("批量新增部门异常：{}", e.getMessage(), e);
            return Result.error("批量新增部门失败：" + e.getMessage());
        }
    }

    /**
     * 删除部门
     * @param deptDeleteDTO 部门删除信息
     * @return 操作结果
     */
    @PostMapping("/delete")
    public Result<String> deleteDept(@Valid @RequestBody DeptDeleteDTO deptDeleteDTO) {
        try {
            // 构建删除条件
            LambdaQueryWrapper<Dept> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                        .eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                        .eq(Dept::getBmdm, deptDeleteDTO.getBmdm());

            // 检查部门是否存在
            if (deptService.count(deleteWrapper) == 0) {
                return Result.error("部门不存在");
            }

            // 删除部门
            boolean success = deptService.remove(deleteWrapper);

            if (success) {
                return Result.success("删除部门成功");
            } else {
                return Result.error("删除部门失败");
            }

        } catch (Exception e) {
            log.error("删除部门异常：{}", e.getMessage(), e);
            return Result.error("删除部门失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除部门
     * @param bmdmList 部门代码列表
     * @return 操作结果
     */
    @PostMapping("/batchDelete")
    public Result<String> batchDeleteDept(@RequestBody List<String> bmdmList) {
        try {
            if (bmdmList == null || bmdmList.isEmpty()) {
                return Result.error("删除的部门代码列表不能为空");
            }

            // 构建删除条件
            LambdaQueryWrapper<Dept> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                        .eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                        .in(Dept::getBmdm, bmdmList);

            // 检查要删除的部门数量
            long count = deptService.count(deleteWrapper);
            if (count == 0) {
                return Result.error("没有找到要删除的部门");
            }

            // 批量删除部门
            boolean success = deptService.remove(deleteWrapper);

            if (success) {
                return Result.success("批量删除部门成功，共删除" + count + "个部门");
            } else {
                return Result.error("批量删除部门失败");
            }

        } catch (Exception e) {
            log.error("批量删除部门异常：{}", e.getMessage(), e);
            return Result.error("批量删除部门失败：" + e.getMessage());
        }
    }

    /**
     * 修改部门
     * @param deptSaveDTO 部门保存信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public Result<String> updateDept(@Valid @RequestBody DeptSaveDTO deptSaveDTO) {
        try {
            // 检查部门是否存在
            LambdaQueryWrapper<Dept> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(Dept::getKjnd, LoginInfo.getCurrAccountantYear())
                       .eq(Dept::getGsdm, LoginInfo.getCurrCorpCode())
                       .eq(Dept::getBmdm, deptSaveDTO.getBmdm());

            Dept existingDept = deptService.getOne(checkWrapper);
            if (existingDept == null) {
                return Result.error("部门不存在");
            }

            // 更新部门信息
            BeanUtils.copyProperties(deptSaveDTO, existingDept);
            existingDept.setZgrId(LoginInfo.getCurrEmployeeCode());
            existingDept.setXgRq(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 保存更新
            boolean success = deptService.updateById(existingDept);

            if (success) {
                return Result.success("修改部门成功");
            } else {
                return Result.error("修改部门失败");
            }

        } catch (Exception e) {
            log.error("修改部门异常：{}", e.getMessage(), e);
            return Result.error("修改部门失败：" + e.getMessage());
        }
    }
}
