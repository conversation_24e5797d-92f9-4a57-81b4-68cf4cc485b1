package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.CglxPageQueryDTO;
import com.gg.grp.hkycg.model.vo.CglxListVO;
import com.gg.grp.hkycg.model.vo.CglxVO;
import com.gg.grp.hkycg.model.vo.PageResult;
import com.gg.grp.hkycg.service.CglxService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购类型控制器
 * <AUTHOR>
 */
@Tag(name = "采购类型管理")
@Slf4j
@RestController
@RequestMapping("/cglx")
@Validated
public class CglxController {

    @Autowired
    private CglxService cglxService;

    /**
     * 查询所有采购类型
     * @return 采购类型列表
     */
    @GetMapping("/all")
    public Result<List<CglxVO>> getAllCglx() {
        try {
            List<CglxVO> cglxList = cglxService.getAllCglx();
            return Result.success("查询采购类型成功", cglxList);
        } catch (Exception e) {
            log.error("查询采购类型异常：{}", e.getMessage());
            return Result.error("查询采购类型失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询采购类型
     * @param queryDTO 查询参数
     * @return 统一响应结果
     */
    @PostMapping("/list")
    public Result<PageResult<CglxListVO>> getCglxPageList(@RequestBody CglxPageQueryDTO queryDTO) {
        try {
            if (queryDTO == null) {
                queryDTO = new CglxPageQueryDTO();
            }

            log.info("分页查询采购类型，查询条件：{}", queryDTO);

            // 执行查询
            List<CglxListVO> cglxList = cglxService.getCglxPageList(queryDTO);

            // 获取总记录数
            long totalCount = 0;
            if (!cglxList.isEmpty()) {
                totalCount = cglxList.get(0).getTotalCount();
            }

            // 创建分页结果
            PageResult<CglxListVO> pageResult = new PageResult<>(cglxList, totalCount, queryDTO.getCurrent(), queryDTO.getSize());

            log.info("分页查询采购类型成功，总记录数：{}", totalCount);
            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询采购类型异常，查询条件：{}，异常信息：{}", queryDTO, e.getMessage());
            if (e.getCause() != null) {
                log.error("底层异常：{}", e.getCause().getMessage());
            }
            return Result.error("查询采购类型失败：" + e.getMessage());
        }
    }
} 