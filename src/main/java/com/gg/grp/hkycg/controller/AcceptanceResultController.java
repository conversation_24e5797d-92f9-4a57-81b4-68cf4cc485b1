package com.gg.grp.hkycg.controller;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.model.dto.AcceptanceQueryDTO;
import com.gg.grp.hkycg.model.vo.ProcurementLinkSummaryVO;
import com.gg.grp.hkycg.model.vo.YsjgListVO;
import com.gg.grp.hkycg.model.vo.YsjgTitleVO;
import com.gg.grp.hkycg.service.AcceptanceResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 验收结果控制器
 * <AUTHOR>
 */
@Tag(name = "验收结果管理")
@RestController
@RequestMapping("/ysjg")
public class AcceptanceResultController {

    @Autowired
    private AcceptanceResultService acceptanceResultService;
    
    /**
     * 带条件查询采购全流程
     */
    @Operation(summary = "查询验收结果")
    @PostMapping("/list/{ysbh}")
    public Result searchWithCondition(@PathVariable String ysbh, @RequestBody AcceptanceQueryDTO queryDTO) {
        // 设置验收编号
        queryDTO.setYsbh(ysbh);
        
        YsjgListVO ysjgListVO = new YsjgListVO();
        List<ProcurementLinkSummaryVO> result = acceptanceResultService.getAcceptanceResultWithCondition(queryDTO);
        ysjgListVO.setCgjgList(result);
        ysjgListVO.setCgjgTitleList(getTitleList());
        return Result.success(ysjgListVO);
    }

    public List<YsjgTitleVO> getTitleList() {
        List<YsjgTitleVO> list = new ArrayList<>();
        list.add(new YsjgTitleVO("ysbh", "采购验收编号","cgys", 1));
        list.add(new YsjgTitleVO("ysmxxh", "采购验收明细序号","cgys", 2));
        list.add(new YsjgTitleVO("jgdjbh", "采购登记编号","cgjg", 3));
        list.add(new YsjgTitleVO("jgnrbh", "采购登记内容编号","cgjg", 4));
        list.add(new YsjgTitleVO("zbsqbh", "采购招标编号","cgzb", 5));
        list.add(new YsjgTitleVO("zbmxxh", "采购招标明细序号","cgzb", 6));
        list.add(new YsjgTitleVO("sqbh", "采购申请编号", "cgsq", 7));
        list.add(new YsjgTitleVO("sqmxxh", "采购申请明细序号","cgsq", 8));
        list.add(new YsjgTitleVO("jhbh", "采购计划编号","cgjh", 9));
        list.add(new YsjgTitleVO("jhmxxh", "采购计划明细序号", "cgjh", 10));
        return list;
    }
} 