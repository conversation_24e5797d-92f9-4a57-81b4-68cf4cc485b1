package com.gg.grp.hkycg.exception;

/**
 * 限流异常
 */
public class RateLimitException extends RuntimeException {
    
    private String clientIp;
    private String limitKey;
    
    public RateLimitException(String message) {
        super(message);
    }
    
    public RateLimitException(String message, String clientIp, String limitKey) {
        super(message);
        this.clientIp = clientIp;
        this.limitKey = limitKey;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public String getLimitKey() {
        return limitKey;
    }
} 