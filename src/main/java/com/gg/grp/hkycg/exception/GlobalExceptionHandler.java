package com.gg.grp.hkycg.exception;

import com.gg.grp.hkycg.common.Result;
import com.gg.grp.hkycg.common.exception.GlobalException;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义全局异常
     */
    @ExceptionHandler(GlobalException.class)
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleGlobalException(GlobalException e, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        System.err.println("业务异常: " + e.getMessage() + ", IP: " + clientIp + ", Code: " + e.getCode());
        
        // 返回自定义错误码和错误信息
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理运行时异常（业务异常）
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        System.err.println("运行时异常: " + e.getMessage() + ", IP: " + clientIp);

        // 返回400状态码和具体错误信息
        return Result.error(400, e.getMessage());
    }

    /**
     * 处理限流异常
     */
    @ExceptionHandler(RateLimitException.class)
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    public Result handleRateLimitException(RateLimitException e, HttpServletRequest request) {
        System.err.println("限流异常: " + e.getMessage() + ", IP: " + e.getClientIp() + ", Key: " + e.getLimitKey());
        
        // 返回429状态码和错误信息
        return Result.error(429, e.getMessage());
    }

    /**
     * 处理认证异常（登录失败）
     */
    @ExceptionHandler(BadCredentialsException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result handleBadCredentialsException(BadCredentialsException e, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        System.err.println("登录认证失败: " + e.getMessage() + ", IP: " + clientIp);
        
        // 返回401状态码和错误信息
        return Result.error(401, e.getMessage());
    }

    /**
     * 处理其他认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        System.err.println("认证异常: " + e.getMessage() + ", IP: " + clientIp);
        
        // 返回401状态码和错误信息
        return Result.error(401, "认证失败，请检查用户名和密码");
    }

    /**
     * 处理其他异常（兜底处理）
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleException(Exception e, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        System.err.println("系统异常: " + e.getMessage() + ", IP: " + clientIp);
        e.printStackTrace();
        
        return Result.error("系统异常，请稍后重试");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
} 