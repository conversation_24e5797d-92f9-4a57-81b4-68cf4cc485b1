spring:
  jmx:
    enabled: false
  profiles:
    active: dev
  application:
    name: hky-cg
  redis:
    jedis:
      pool:
        max-active: 200
        max-idle: 100
        max-wait: 100
    timeout: 600000
    database: 8
    host: 127.0.0.1
    port: 9009

server:
  port: 8082
  servlet:
    context-path: /grp

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  mapper-locations: classpath*:mybatis/**/*.xml
  type-aliases-package: com.gg.grp.hkycg.model.pojo

accessory:
  stockpile-url: /Users/<USER>/IdeaProjects/hky-cg/src/main/resources/file/

#发票
invoice:
  user: 台州职业技术学院
  #OCR应用的税号
  duty-paragraph: 91330300999999001
  #OCR应用的唯一身份
  app-key: 11437272
  #应用中用于保障数据安全的钥匙
  app-secret: B806E4488CA6413D
  #身份认证，在诺诺网备案后，由诺诺网提供，每个企业一个
  identity: 9D9F9F13858908464DDBC68829BD09295CC1E0896024F731
  #token
  access-token: a3c58ac9507ec66d1548500uzppn0ups
  #文件上传
  upload-do: https://file.nuonuo.com/api/v1/file/open/upload.do
  #诺诺票据-识别地址
  recognition-url: https://sdk.nuonuo.com/open/v2/ocr
  #票据识别-仅识别
  recognition-method: nuonuo.Bill.recognizeBill
  #诺诺票据-查询识别结果地址
  identify-result-url: https://sdk.nuonuo.com/open/v2/ocr
  #票据识别-查询识别结果
  identify-result-method: nuonuo.Bill.queryBillResult
  #验真
  inspection:
    #验真的税号
    duty-paragraph: 123310004725826864
    #验真的唯一身份
    app-key: f9663b53357942b4870d8c32fffd8f12
    #诺诺票据-发票验真地址方法
    url: https://qiusuofp.com:8088/api/InvApi/Check

#合合发票
invoices:
  x-ti-app-id: 40e0fb12d4616fd6b055d8f323783ea1
  x-ti-secret-code: 1ae9c0e7cf576702c1dfe201916b1081
  #合合国内通用票据识别地址
  recognition-url: https://api.textin.com/robot/v1.0/api/bills_crop
    #合合国内通用票据验真地址
  inspection-url: https://api.textin.com/robot/v1.0/api/verify_vat


#收入来款单发票
income-invoice:
  saler-name: 杭州科技职业技术学院
  saler-tel: ***********
  saler-address: 浙江省杭州市西湖区文一西路37号
  saler-account: 33050161722709123456
  saler-bank: 中国建设银行股份有限公司杭州富阳支行
  #杭科院税号
  taxnum: ***************
  #诺诺开票应用的唯一身份
  app-key: ********
  #应用中用于保障数据安全的钥匙
  app-secret: 867918776E3C4487
  #token
  access-token: 98a0d315862dfbbc8dd1870nanavqmks
  #诺诺票据-正式环境接口地址
  service-prod-url: https://sandbox.nuonuocs.cn/open/v1/services
  #诺诺票据-沙箱环境接口地址
  service-test-url: https://sandbox.nuonuocs.cn/open/v1/services
  #诺诺请求开具发票接口
  billing-method: nuonuo.OpeMplatform.requestBillingNew
  #诺诺开票结果查询接口
  billing-result-method: nuonuo.OpeMplatform.queryInvoiceResult


project:
  gsdm: "001"
  zydm: "2014001"
  zymc: "rmx"

#微信发送通知
wxsent:
  sentMsgUrl: http://127.0.0.1:8080/grp/wxSentMessage
  sentDDMsgUrl: http://127.0.0.1:8080/grp/dingdingSentMessage
  openPort  : false
  openPortTest: true
  sentMsgUrlTest: http://localhost:9011/grp/wxSentMessage

#附件处理
attachment:
  url: http://************:8084/grp-attachment/fileFormat/convert
#  base-url: http://localhost:8082/grp  # 应用基础URL，用于构建完整下载和预览链接
  upload:
    path: upload/attachment  # 附件上传存储路径（相对于项目根目录）
  download:
    url: /download/  # 附件下载URL前缀，已移除重复的/attachment
  preview:
    url: /preview/  # 附件预览URL前缀，已移除重复的/attachment

JRMobile:
  url: http://*************:9011/h5/hky/index.html

#学校信息
school:
  #标识码
  id_code: "4133013026"
  #名称
  name: 杭州科技职业技术学院
  #统一社会信用代码
  credit_code: "12330100470131064X"

# JWT配置
app:
  # JWT密钥
  jwtSecret: hkyCgSecretKey2024ForSpringSecurity
  # JWT过期时间（毫秒）24小时
  jwtExpirationInMs: 864000000

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    show-extensions: true
  api-docs:
    path: /v3/api-docs
    enable: true
knife4j:
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: Swagger Models
  basic:
    enable: false