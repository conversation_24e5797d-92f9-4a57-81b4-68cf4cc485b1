webpackJsonp([1],{2824:function(e,t,i){i(2991);var a=i(1)(i(2920),i(2997),"data-v-a60d57dc",null);e.exports=a.exports},2920:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=i(304),o=i.n(a),n=i(11),l=i.n(n),s=i(3),c=i.n(s),r=i(22),u=i.n(r),p=i(305),h=i.n(p),d=i(2);t.default={data:function(){return{currentButton:"",printAble:!1,addAble:!1,editAble:!1,tempSaveAble:!1,saveAble:!1,cancelAble:!1,submitAble:!1,recoverAble:!1,deleteAble:!1,verifyAble:!1,callBackAble:!1,cancelVerifyAble:!1,bookkeepingAble:!1,deBookkeepingAble:!1,fileAble:!1,auxiliaryDisplayMap:new h.a([["0",{prop:"deptDisplay",title:"部门"}],["1",{prop:"projectDisplay",title:"项目"}],["2",{prop:"personalDisplay",title:"个人往来"}],["3",{prop:"companyDisplay",title:"单位往来"}],["4",{prop:"funcSubjectDisplay",title:"功能科目"}],["5",{prop:"economicSubjectDisplay",title:"部门经济科目"}],["11",{prop:"governmentEconomicsDisplay",title:"政府经济科目"}],["12",{prop:"economicNatureDisplay",title:"经济性质"}],["13",{prop:"appropriationTypeDisplay",title:"拨款种类"}],["14",{prop:"budgetItemsDisplay",title:"预算项目"}],["15",{prop:"assetTypeDisplay",title:"资产类型"}],["16",{prop:"assetDisposalTypeDisplay",title:"资产处置类型"}],["17",{prop:"billTypeDisplay",title:"票据类型"}],["18",{prop:"materialCategoryDisplay",title:"物资类别"}],["19",{prop:"specialFundCategoryDisplay",title:"专用基金类别"}],["20",{prop:"revenueCategoryDisplay",title:"收入类别"}],["21",{prop:"otherExpenditureCategoriesDisplay",title:"其他支出类别"}],["22",{prop:"moneyCategoryDisplay",title:"费用类别"}],["23",{prop:"businessActivitiesCategoryDisplay",title:"经营活动类别"}],["24",{prop:"businessActivityCategoryDisplay",title:"业务活动类别"}],["25",{prop:"loanBorrowingTypeDisplay",title:"贷（借）款种类"}],["26",{prop:"investmentTypeDisplay",title:"投资种类"}],["28",{prop:"lotteryVarietiesDisplay",title:"彩票品种"}]]),deptDisplay:!1,projectDisplay:!1,personalDisplay:!1,companyDisplay:!1,funcSubjectDisplay:!1,economicSubjectDisplay:!1,governmentEconomicsDisplay:!1,economicNatureDisplay:!1,appropriationTypeDisplay:!1,budgetItemsDisplay:!1,assetTypeDisplay:!1,assetDisposalTypeDisplay:!1,billTypeDisplay:!1,materialCategoryDisplay:!1,specialFundCategoryDisplay:!1,revenueCategoryDisplay:!1,otherExpenditureCategoriesDisplay:!1,moneyCategoryDisplay:!1,businessActivitiesCategoryDisplay:!1,businessActivityCategoryDisplay:!1,loanBorrowingTypeDisplay:!1,investmentTypeDisplay:!1,lotteryVarietiesDisplay:!1,tableLoading:!1,changeAble:!1,reasonVis:!1,reasonTitle:"",opinion:"",reasonPlaceHolder:"",reasonType:!0,voucherForm:{billNo:"",billDate:"",fileNumber:0,handlerPeople:"",preparerPeople:"",billStatus:"",remark:"",manager:"",cwkjjfjehj:"",cwkjdfjehj:"",yskjjfjehj:"",yskjdfjehj:""},accountSubjectVis:!1,accountSubjectFilterText:"",accountSubjectTreeData:[],accountSubjectLoading:!1,TreeProps:{children:"children",label:"name"},auxiliaryVis:!1,auxiliaryTitle:"",auxiliaryTreeData:[],auxiliaryLoading:!1,selAuxiliary:{},auxiliaryFilterText:"",budgetAccountRadio:"debit",currentAccountSubject:"",currentAccountMoney:0,accountList:[],accountLists:[],voucherList:[],curTableRow:{},currentAuxiliaryRow:{},multipleSelection:[],selTableRow:{},currentColumn:"",jumpObj:{billNo:"",source:""}}},methods:{disableAllButton:function(){this.printAble=!1,this.addAble=!1,this.editAble=!1,this.tempSaveAble=!1,this.saveAble=!1,this.cancelAble=!1,this.submitAble=!1,this.recoverAble=!1,this.deleteAble=!1,this.verifyAble=!1,this.callBackAble=!1,this.cancelVerifyAble=!1,this.bookkeepingAble=!1,this.deBookkeepingAble=!1,this.fileAble=!1,this.changeAble=!1},disableAllList:function(){var e=this;this.auxiliaryDisplayMap.forEach(function(t){e[t.prop]=!1})},changeButtonStatus:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.disableAllButton(),"已保存"===e||"未审核"===e?(this.editAble=!0,this.submitAble=!0,this.deleteAble=!0,this.fileAble=!0,this.verifyAble=!0):"已暂存"===e?(this.editAble=!0,this.deleteAble=!0,this.fileAble=!0):"审核中"===e?this.fileAble=!0:"已审核"===e&&(this.fileAble=!0,this.printAble=!0,this.bookkeepingAble=!0,this.cancelVerifyAble=!0)},changeButtonAbleByCurrentButton:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;this.disableAllButton(),e&&(this.currentButton=e),"add"===e?(this.saveAble=!0,this.tempSaveAble=!0,this.cancelAble=!0,this.fileAble=!0,this.changeAble=!0,this.currentButton="add"):"edit"===e&&(this.cancelAble=!0,this.saveAble=!0,this.fileAble=!0,this.changeAble=!0,this.currentButton="edit")},resetAllPageData:function(){this.$refs.voucherForm.resetFields(),this.accountList=[],this.voucherList=[],this.curTableRow="",this.multipleSelection=[]},resetPageToAllDisable:function(){this.disableAllButton(),this.resetAllPageData(),this.addAble=!0},pageData:function(){return{voucherForm:this.voucherForm,accountList:this.accountList}},getResultData:function(e){for(var t=[],i=0;i<e.accountList.length;i++)t.push(u()(e.accountList[i],e.accountLists[i]));this.accountList=t,this.voucherForm=e.voucherForm},commonAdd:function(){this.resetAllPageData();for(var e=0;e<5;e++)this.addRowClick();this.addRowClickAuxiliaryAccount(),this.changeButtonAbleByCurrentButton("add"),this.voucherForm.billDate=d.a.transFormatDate(new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate()))},commonEdit:function(){this.changeButtonAbleByCurrentButton("edit")},commonSave:function(){var e=this;this.tableLoading=!0,this.$message({type:"warning",message:"数据正在保存中，请稍后..."});var t="";"add"===this.currentButton?t="saveBill":"edit"===this.currentButton&&(t="updateBill"),http.POST_JSON({url:"/accountant/voucher/"+t,data:this.pageData()}).then(function(t){t.data.success?(e.$message({type:"success",message:"保存成功"}),e.getPageData()):e.changeButtonAbleByCurrentButton(e.currentButton),e.tableLoading=!1})},commonTempSave:function(){var e=this;this.changeButtonAbleByCurrentButton(),this.tableLoading=!0,this.$message({type:"warning",message:"数据正在保存中，请稍后..."}),http.POST_JSON({url:"/accountant/voucher/tempSave",data:this.pageData()}).then(function(t){t.data.success?(e.$message({type:"success",message:"暂存成功"}),e.getPageData()):e.changeButtonAbleByCurrentButton(e.currentButton),e.tableLoading=!1})},commonCancel:function(){""===this.voucherForm.billStatus&&"add"===this.currentButton?this.resetPageToAllDisable():this.getPageData()},commonDelete:function(){var e=this;this.$confirm("是否确定作废？","提示",{type:"warning"}).then(function(){e.disableAllButton(),http.POST_JSON({url:"/accountant/voucher/deleteBill",data:e.pageData()}).then(function(t){t.data.success?(e.$message({message:"作废成功！",type:"success"}),e.resetPageToAllDisable()):e.getPageData()})})},commonSubmit:function(){var e=this;this.tableLoading=!0,this.disableAllButton(),this.$message({type:"warning",message:"数据正在提交中，请稍后..."}),http.POST_JSON({url:"/accountant/voucher/commitBill",data:this.pageData()}).then(function(t){t.data.success&&e.$message({type:"success",message:"提交成功"}),e.getPageData(),e.tableLoading=!1})},commonRecover:function(){var e=this;this.$confirm("是否确定收回？","提示",{type:"warning"}).then(function(){e.disableAllButton(),e.$http({method:"",url:""}).then(function(t){t.data.success&&(e.$message({message:"收回成功！",type:"success"}),e.getPageData()),e.tableLoading=!1})})},showReasonDialogVerify:function(){this.reasonTitle="提示",this.reasonType=!0,this.opinion="通过",this.reasonPlaceHolder="请填写审核意见...(选填)",this.reasonVis=!0},commonVerify:function(){var e=this;this.tableLoading=!0,http.POST_JSON({url:"/accountant/voucher/checkBill",data:this.pageData()}).then(function(t){t.data.success&&(e.$message({type:"success",message:"审核成功！"}),e.getPageData(),e.callBackAble=!1,e.verifyAble=!1,e.cancelVerifyAble=!0),e.tableLoading=!1,e.reasonVis=!1})},commonCancelVerify:function(){var e=this;http.POST_JSON({url:"/accountant/voucher/callBack",data:this.pageData()}).then(function(t){t.data.success&&(d.a.showMessage("操作成功！","success"),e.getPageData())})},commonBookkeeping:function(){var e=this;this.tableLoading=!0,http.POST_JSON({url:"/accountant/voucher/bookKeeping ",data:this.pageData()}).then(function(t){t.data.success&&(d.a.showMessage("记账成功！","success"),e.getPageData()),e.tableLoading=!1})},commonDeBookkeeping:function(){var e=this;http.POST_JSON({url:"/accountant/voucher/turnBookkeeping",data:this.pageData()}).then(function(t){t.data.success&&(d.a.showMessage("操作成功！","success"),e.getPageData())})},showReasonDialogCallBack:function(){this.reasonTitle="退回意见",this.reasonType=!1,this.opinion="退回意见",this.reasonPlaceHolder="请填写退回意见...",this.reasonVis=!0},commonCallBack:function(){var e=this;this.tableLoading=!0,this.$http({method:"post",url:this.baseURL+"/voucher/VoucherRebackServlet",data:this.pageData()}).then(function(t){t.data.success&&(e.reasonVis=!1,e.tableLoading=!1,e.$message({type:"success",message:"退回成功！"}),e.getPageData()),e.tableLoading=!1})},backCancel:function(){this.reasonVis=!1,this.opinion=""},backSave:function(){this.reasonType||""!==this.opinion?this.reasonType?this.commonVerify():this.commonCallBack():this.$message({type:"warning",message:"请填写退回意见！"})},commonPrint:function(){var e=window.open("","_blank"),t=this.$router.resolve({name:"voucher_make_print",query:{printData:c()({accountList:this.accountList})}});e.location=t.href},getPageData:function(){var e=this;if(this.tableLoading=!0,void 0!==this.$route.params.billNo||""!==this.voucherForm.billNo){var t=this.voucherForm.billDate;this.$http({method:"post",url:this.baseURL+"/grp/accountant/voucher/queryBill",data:{billNo:this.$route.params.billNo.replace(/\s+/g," ")||this.voucherForm.billNo,accountingPeriod:this.$route.params.accountingPeriod||t,voucherType:this.$route.params.DJLX||""}}).then(function(t){t.data.success&&(e.accountList=[],e.getResultData(t.data.data),e.changeButtonStatus(e.voucherForm.billStatus),e.changeAble=!1,e.jumpObj.billNo=t.data.data.billNo,e.jumpObj.source=t.data.data),e.tableLoading=!1})}else this.tableLoading=!1},jumpPage:function(){this.$router.push({name:""+d.a.getPageToPage(this.jumpObj.source,"billTypeId","menuId"),params:{billno:this.jumpObj.billNo,name:"凭证处理"}})},accountSubjectClick:function(e,t){var i=this;this.curTableRow=e,this.currentColumn=t,this.accountSubjectVis=!0,this.accountSubjectLoading=!0,this.$http({method:"get",url:this.baseURL+"/grp/base/resourceTree/queryAccountSubject"}).then(function(e){e.data.success&&(i.accountSubjectTreeData=e.data.data.treeSplitter(),i.accountSubjectLoading=!1)}).catch(function(){})},accountSubjectSave:function(){0===l()(this.selTableRow).length?this.$message({showClose:!0,message:"请选择会计科目!",type:"warning"}):this.selTableRow.children?this.$message({showClose:!0,message:"请选择末级会计科目!",type:"warning"}):("财务会计科目"===this.currentColumn?this.curTableRow.financeAccountSubject=this.selTableRow.name:"预算会计科目"===this.currentColumn&&(this.curTableRow.budgetAccountSubject=this.selTableRow.name),this.showAuxiliaryAccountInfoList({},this.selTableRow.listComma),this.currentAccountSubject=this.selTableRow.name,this.accountSubjectCancel())},accountSubjectCancel:function(){this.accountSubjectVis=!1},getAuxiliaryCommaList:function(e){var t=[];if(""!==e){var i=!0,a=!1,n=void 0;try{for(var l,s=o()(this.accountSubjectOptions);!(i=(l=s.next()).done);i=!0){var c=l.value;if(c.value===e){t=c.listComma;break}}}catch(e){a=!0,n=e}finally{try{!i&&s.return&&s.return()}finally{if(a)throw n}}}return t},auxiliaryClick:function(e,t){var i=this;this.currentAuxiliaryRow=e,this.auxiliaryTitle=t,this.auxiliaryVis=!0,this.auxiliaryLoading=!0;var a="",n="/queryAccessory";"单位往来"===t&&(n="/queryDealings");var l=!0,s=!1,c=void 0;try{for(var r,u=o()(this.auxiliaryDisplayMap.keys());!(l=(r=u.next()).done);l=!0){var p=r.value;if(this.auxiliaryDisplayMap.get(p).title===t){a=p;break}}}catch(e){s=!0,c=e}finally{try{!l&&u.return&&u.return()}finally{if(s)throw c}}var h=this.currentAccountSubject.split(" ")[0];http.POST_JSON({url:"/base/resourceTree"+n,data:{subjects:h,details:a}}).then(function(e){e.data.success&&(i.auxiliaryTreeData=e.data.data.treeSplitter()||[]),i.auxiliaryLoading=!1})},auxiliaryChoose:function(e){this.selAuxiliary=e},auxiliarySave:function(){var e=this;if(0===l()(this.selAuxiliary).length)this.$message({showClose:!0,message:"请选择"+this.auxiliaryTitle,type:"warning"});else if(this.selAuxiliary.children)this.$message({showClose:!0,message:"请选择末级"+this.auxiliaryTitle,type:"warning"});else{var t=this.selAuxiliary.name;this.auxiliaryDisplayMap.forEach(function(i){if(i.title===e.auxiliaryTitle){var a=i.prop.slice(0,-7);e.currentAuxiliaryRow[a]=t}}),"财务会计科目"===this.currentColumn?(this.curTableRow.financeAuxiliary=[],this.curTableRow.financeAuxiliary.push(this.currentAuxiliaryRow)):"预算会计科目"===this.currentColumn&&(this.curTableRow.budgetAuxiliary=[],this.curTableRow.budgetAuxiliary.push(this.currentAuxiliaryRow)),this.auxiliaryCancel()}},auxiliaryCancel:function(){this.auxiliaryVis=!1},rowClick:function(e){this.curTableRow=e},accountSubjectTreeNodeChoose:function(e){this.selTableRow=e},commonTableRowClick:function(e){this.selTableRow=e},selectionChange:function(e){this.multipleSelection=e},relationFinanceClick:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.curTableRow=e,this.currentAccountSubject=e.financeAccountSubject,this.currentAccountMoney=e.financeDebitAmount||e.financeCreditAmount||0,""!==e.financeDebitAmount?this.budgetAccountRadio="debit":""!==e.financeCreditAmount&&(this.budgetAccountRadio="credit");var t=this.getAuxiliaryCommaList(this.curTableRow.financeAccountSubject);t.length>0?this.showAuxiliaryAccountInfoList(e.financeAuxiliary[0],t):this.showAuxiliaryAccountInfoList()},relationBudgetClick:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.curTableRow=e,this.currentAccountSubject=e.budgetAccountSubject,this.currentAccountMoney=e.budgetDebitAmount||e.budgetCreditAmount||0,""!==e.budgetDebitAmount?this.budgetAccountRadio="debit":""!==e.budgetCreditAmount&&(this.budgetAccountRadio="credit");var t=this.getAuxiliaryCommaList(this.curTableRow.budgetAccountSubject);t.length>0?this.showAuxiliaryAccountInfoList(e.budgetAuxiliary[0],t):this.showAuxiliaryAccountInfoList()},showAuxiliaryAccountInfoList:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.disableAllList(),this.voucherList=[],0===l()(t).length?this.addRowClickAuxiliaryAccount():this.voucherList.push(t),i.forEach(function(t){e[e.auxiliaryDisplayMap.get(t).prop]=!0})},addRowClick:function(){this.accountList.push({id:d.a.setUuid(),financeAbstract:"",financeAccountSubject:"",financeDebitAmount:"",financeCreditAmount:"",financeAuxiliary:[this.createAuxiliaryAccountNode()],budgetAbstract:"",budgetAccountSubject:"",budgetDebitAmount:"",budgetCreditAmount:"",budgetAuxiliary:[this.createAuxiliaryAccountNode()]})},createAuxiliaryAccountNode:function(){var e={id:d.a.setUuid()};return this.auxiliaryDisplayMap.forEach(function(t){var i=t.prop.slice(0,-7);e[i]=""}),e},addRowClickAuxiliaryAccount:function(){this.voucherList.push(this.createAuxiliaryAccountNode())},delRowClick:function(){var e=this;0===this.multipleSelection.length?this.$message({showClose:!0,message:"请选择您想要删除的数据!",type:"warning"}):this.multipleSelection.map(function(t){e.accountList.map(function(i,a){t.id===i.id&&e.accountList.splice(a,1)})})},exportClick:function(){var e;e=document.querySelector("#accountList");var t=XLSX.utils.table_to_book(e,{raw:!0}),i=XLSX.write(t,{bookType:"xlsx",bookSST:!0,type:"array"});try{FileSaver.saveAs(new Blob([i],{type:"application/octet-stream"}),"凭证.xls")}catch(e){throw new Error(e)}return i},loadAll:function(){var e=this;this.$http({method:"get",url:this.baseURL+"/grp/base/resourceTree/queryAccounts"}).then(function(t){t.data.success&&(e.accountSubjectOptions=t.data.data)})},handleFianceSelect:function(e){this.currentColumn="财务会计科目",this.currentAccountSubject=e.value,this.showAuxiliaryAccountInfoList({},e.listComma)},handleBudgetSelect:function(e){this.currentColumn="预算会计科目",this.currentAccountSubject=e.value,this.showAuxiliaryAccountInfoList({},e.listComma)},commonFileClick:function(){},beforeFileUpload:function(){},afterFileUploadSuccess:function(){},treeFilterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},getSummaries:function(e){var t=this,i=e.columns,a=e.data,o=[];return i.forEach(function(e,i){if(1!==i){var n=a.map(function(t){return Number(t[e.property])});n.every(function(e){return isNaN(e)})||("financeDebitAmount"===e.property||"financeCreditAmount"===e.property||"budgetDebitAmount"===e.property||"budgetCreditAmount"===e.property?(o[i]=n.reduce(function(e,t){var i=Number(t);return isNaN(i)?e:e+t},0),o[i]=o[i]%1==0?o[i]:o[i].toFixed(2),t.voucherForm.cwkjjfjehj=o[4],t.voucherForm.cwkjdfjehj=o[5],t.voucherForm.yskjjfjehj=o[8],t.voucherForm.yskjdfjehj=o[9]):o[i]="")}else o[i]="合计"}),o},querySearch:function(e,t){var i=this.accountSubjectOptions;t(e?i.filter(this.createFilter(e)):i)},createFilter:function(e){return function(t){return 0===t.value.toLowerCase().indexOf(e.toLowerCase())}}},mounted:function(){this.commonAdd(),this.getPageData(),this.loadAll()},computed:{filterAuxiliaryDisplay:function(){var e=this,t=[];return this.auxiliaryDisplayMap.forEach(function(i){if(e[i.prop]){var a=JSON.parse(c()(i));a.prop=a.prop.slice(0,-7),t.push(a)}}),t}},created:function(){this.$nextTick(function(){d.a.autoHeight(".auto-height","height",400)})}}},2990:function(e,t,i){(e.exports=i(2822)()).push([e.i,"","",{version:3,sources:[],names:[],mappings:"",file:"apst_voucher_handle.vue",sourceRoot:""}])},2991:function(e,t,i){var a=i(2990);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);i(2825)("2b08365f",a,!0)},2997:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("section",[i("div",{staticClass:"page-container"},[i("el-row",{staticClass:"toolbar"},[i("el-button",{attrs:{type:"text",disabled:!e.printAble},on:{click:e.commonPrint}},[e._v("打印")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.addAble},on:{click:e.commonAdd}},[e._v("新增")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.editAble},on:{click:e.commonEdit}},[e._v("修改")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.tempSaveAble},on:{click:e.commonTempSave}},[e._v("暂存")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.saveAble},on:{click:e.commonSave}},[e._v("保存")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.cancelAble},on:{click:e.commonCancel}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.recoverAble},on:{click:e.commonRecover}},[e._v("收回")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.deleteAble},on:{click:e.commonDelete}},[e._v("作废")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.verifyAble},on:{click:e.showReasonDialogVerify}},[e._v("审核")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.cancelVerifyAble},on:{click:e.commonCancelVerify}},[e._v("销审")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.bookkeepingAble},on:{click:e.commonBookkeeping}},[e._v("记账")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.deBookkeepingAble},on:{click:e.commonDeBookkeeping}},[e._v("反记账")]),e._v(" "),i("el-button",{attrs:{type:"text",disabled:!e.fileAble},on:{click:e.commonFileClick}},[e._v("附件")]),e._v(" "),e.jumpObj.billNo?i("el-button",{attrs:{type:"text"},on:{click:e.jumpPage}},[e._v("关联单据号:"+e._s(e.jumpObj.billNo))]):e._e()],1),e._v(" "),i("el-form",{ref:"voucherForm",staticClass:"page-form",attrs:{model:e.voucherForm,size:"mini","label-width":"85px"}},[i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"单据编号",prop:"billNo"}},[i("el-input",{attrs:{readonly:"",placeholder:"自动生成"},model:{value:e.voucherForm.billNo,callback:function(t){e.$set(e.voucherForm,"billNo",t)},expression:"voucherForm.billNo"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"日期",prop:"billDate"}},[i("el-date-picker",{attrs:{type:"date",placeholder:"选择日期",disabled:!e.changeAble,"value-format":"yyyy-MM-dd"},model:{value:e.voucherForm.billDate,callback:function(t){e.$set(e.voucherForm,"billDate",t)},expression:"voucherForm.billDate"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"附件数",prop:"fileNumber"}},[i("el-input",{attrs:{readonly:!e.changeAble,placeholder:"请填写"},model:{value:e.voucherForm.fileNumber,callback:function(t){e.$set(e.voucherForm,"fileNumber",t)},expression:"voucherForm.fileNumber"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"经手人",prop:"handlerPeople"}},[i("el-input",{attrs:{readonly:!e.changeAble,placeholder:"请输入经手人"},model:{value:e.voucherForm.handlerPeople,callback:function(t){e.$set(e.voucherForm,"handlerPeople",t)},expression:"voucherForm.handlerPeople"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"制单人",prop:"preparerPeople"}},[i("el-input",{attrs:{readonly:"",placeholder:"自动生成"},model:{value:e.voucherForm.preparerPeople,callback:function(t){e.$set(e.voucherForm,"preparerPeople",t)},expression:"voucherForm.preparerPeople"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"单据状态",prop:"billStatus"}},[i("el-input",{attrs:{readonly:"",placeholder:"自动生成"},model:{value:e.voucherForm.billStatus,callback:function(t){e.$set(e.voucherForm,"billStatus",t)},expression:"voucherForm.billStatus"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"主管",prop:"manager"}},[i("el-input",{attrs:{readonly:!e.changeAble,placeholder:"自动生成"},model:{value:e.voucherForm.manager,callback:function(t){e.$set(e.voucherForm,"manager",t)},expression:"voucherForm.manager"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"备注",prop:"remark"}},[i("el-input",{attrs:{readonly:!e.changeAble,placeholder:"请输入备注"},model:{value:e.voucherForm.remark,callback:function(t){e.$set(e.voucherForm,"remark",t)},expression:"voucherForm.remark"}})],1)],1)],1)],1),e._v(" "),i("div",[i("el-row",{staticClass:"margin-bottom-10"},[i("el-col",{attrs:{span:5}},[i("el-button-group",[i("el-button",{attrs:{disabled:!e.changeAble,size:"mini"},on:{click:e.addRowClick}},[e._v("增行")]),e._v(" "),i("el-button",{attrs:{disabled:!e.changeAble,size:"mini"},on:{click:e.delRowClick}},[e._v("删行")]),e._v(" "),i("el-button",{staticClass:"uploadFile",attrs:{disabled:!e.changeAble,size:"mini"}},[i("el-upload",{attrs:{action:this.baseURL+"",method:"post",disabled:!e.changeAble,data:{},"on-progress":e.beforeFileUpload,"on-success":e.afterFileUploadSuccess,"auto-upload":"","show-file-list":!1,accept:".xls,.xlsx",enctype:"multipart/form-data"}},[i("span",[e._v("导入")])])],1),e._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:e.exportClick}},[e._v("导出")])],1)],1),e._v(" "),i("el-col",{attrs:{span:4}},[i("div",{staticClass:"document-download"},[i("div",{staticClass:"item"},[i("el-tooltip",{attrs:{content:"点击下载",placement:"right",effect:"light"}},[i("a",{staticClass:"link",attrs:{href:"static/img/top.jpg",download:"测试模板.png"}},[e._v("《模板》")])])],1)])])],1),e._v(" "),i("div",{staticClass:"auto-height"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"accountList",staticClass:"show-summary",attrs:{data:e.accountList,id:"accountList","show-summary":"","summary-method":e.getSummaries,size:"mini",border:"","highlight-current-row":"",height:"100%"},on:{"row-click":e.rowClick,"selection-change":e.selectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"40",align:"center"}}),e._v(" "),i("el-table-column",{attrs:{type:"index",label:"序",width:"60",align:"center"}}),e._v(" "),i("el-table-column",{attrs:{label:"财务会计",align:"center"}},[i("el-table-column",{attrs:{prop:"financeAbstract",label:"摘要","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",{on:{click:function(i){return e.relationFinanceClick(t.row)}}},[e._v(e._s(t.row.financeAbstract))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"financeAccountSubject",label:"会计科目",width:"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.changeAble?i("el-autocomplete",{staticClass:"inline-input",attrs:{"fetch-suggestions":e.querySearch,placeholder:"请输入内容",size:"mini"},on:{select:e.handleFianceSelect},model:{value:t.row.financeAccountSubject,callback:function(i){e.$set(t.row,"financeAccountSubject",i)},expression:"scope.row.financeAccountSubject"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},nativeOn:{click:function(i){return e.accountSubjectClick(t.row,"财务会计科目")}},slot:"append"})],1):i("span",[e._v(e._s(t.row.financeAccountSubject))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"financeDebitAmount",label:"借方金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.changeAble?i("el-input",{attrs:{size:"mini"},nativeOn:{click:function(i){return e.relationFinanceClick(t.row)}},model:{value:t.row.financeDebitAmount,callback:function(i){e.$set(t.row,"financeDebitAmount",e._n(i))},expression:"scope.row.financeDebitAmount"}}):i("span",[e._v(e._s(t.row.financeDebitAmount))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"financeCreditAmount",label:"贷方金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.changeAble?i("el-input",{attrs:{size:"mini"},nativeOn:{click:function(i){return e.relationFinanceClick(t.row)}},model:{value:t.row.financeCreditAmount,callback:function(i){e.$set(t.row,"financeCreditAmount",e._n(i))},expression:"scope.row.financeCreditAmount"}}):i("span",[e._v(e._s(t.row.financeCreditAmount))])]}}])})],1),e._v(" "),i("el-table-column",{attrs:{label:"预算会计",align:"center"}},[i("el-table-column",{attrs:{prop:"budgetAbstract",label:"摘要","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",{on:{click:function(i){return e.relationBudgetClick(t.row)}}},[e._v(e._s(t.row.budgetAbstract))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"budgetAccountSubject",label:"会计科目",width:"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.changeAble?i("el-autocomplete",{staticClass:"inline-input",attrs:{"fetch-suggestions":e.querySearch,placeholder:"请输入内容",size:"mini","show-overflow-tooltip":""},on:{select:e.handleBudgetSelect},model:{value:t.row.budgetAccountSubject,callback:function(i){e.$set(t.row,"budgetAccountSubject",i)},expression:"scope.row.budgetAccountSubject"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},nativeOn:{click:function(i){return e.accountSubjectClick(t.row,"预算会计科目")}},slot:"append"})],1):i("span",[e._v(e._s(t.row.budgetAccountSubject))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"budgetDebitAmount",label:"借方金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.changeAble?i("el-input",{attrs:{size:"mini",prop:"budgetDebitAmount"},nativeOn:{click:function(i){return e.relationBudgetClick(t.row)}},model:{value:t.row.budgetDebitAmount,callback:function(i){e.$set(t.row,"budgetDebitAmount",e._n(i))},expression:"scope.row.budgetDebitAmount"}}):i("span",[e._v(e._s(t.row.budgetDebitAmount))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"budgetCreditAmount",label:"贷方金额",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.changeAble?i("el-input",{attrs:{size:"mini",prop:"budgetCreditAmount"},nativeOn:{click:function(i){return e.relationBudgetClick(t.row)}},model:{value:t.row.budgetCreditAmount,callback:function(i){e.$set(t.row,"budgetCreditAmount",e._n(i))},expression:"scope.row.budgetCreditAmount"}}):i("span",[e._v(e._s(t.row.budgetCreditAmount))])]}}])})],1)],1)],1)],1),e._v(" "),i("div",[i("el-row",{staticClass:"margin-bottom-10 margin-top-10"},[i("el-col",{attrs:{span:6}},[i("strong",[e._v("预算会计：")]),e._v(" "),i("el-radio",{attrs:{label:"debit"},model:{value:e.budgetAccountRadio,callback:function(t){e.budgetAccountRadio=t},expression:"budgetAccountRadio"}},[e._v("借方")]),e._v(" "),i("el-radio",{attrs:{label:"credit"},model:{value:e.budgetAccountRadio,callback:function(t){e.budgetAccountRadio=t},expression:"budgetAccountRadio"}},[e._v("贷方")])],1),e._v(" "),i("el-col",{attrs:{span:6}},[i("strong",[e._v("会计科目：")]),e._v(e._s(e.currentAccountSubject)+"\n                ")]),e._v(" "),i("el-col",{attrs:{span:6}},[i("strong",[e._v("金额：")]),e._v(e._s(e.currentAccountMoney)+"\n                ")])],1),e._v(" "),i("el-table",{attrs:{data:e.voucherList,id:"voucherList",border:"",height:"70",size:"mini"}},[i("el-table-column",{attrs:{type:"index",label:"序",width:"60"}}),e._v(" "),e._l(e.filterAuxiliaryDisplay,function(t){return i("el-table-column",{key:t.prop,attrs:{prop:t.prop,label:t.title,width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[e.changeAble?i("el-input",{attrs:{size:"mini"},nativeOn:{click:function(i){return e.auxiliaryClick(a.row,t.title)}},model:{value:a.row[t.prop],callback:function(i){e.$set(a.row,t.prop,i)},expression:"scope.row[item.prop]"}}):i("span",[e._v(e._s(a.row[t.prop]))])]}}],null,!0)})})],2)],1),e._v(" "),i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.reasonTitle,visible:e.reasonVis,width:"35%"},on:{"update:visible":function(t){e.reasonVis=t},close:e.backCancel}},[i("div",{staticClass:"dialog-content"},[i("div",{staticClass:"content-header"},[e.reasonType?i("p",{staticClass:"margin-bottom-10 red"},[e._v("审核之后不能退回，确定要审核吗？")]):e._e()]),e._v(" "),i("div",{staticClass:"content-body"},[i("el-form",{attrs:{size:"mini"}},[i("el-form-item",{staticClass:"margin-bottom-0"},[i("el-input",{attrs:{type:"textarea",rows:5,placeholder:e.reasonPlaceHolder},model:{value:e.opinion,callback:function(t){e.opinion=t},expression:"opinion"}})],1)],1)],1)]),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"mini"},on:{click:e.backCancel}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.backSave}},[e._v("确定")])],1)]),e._v(" "),i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:"会计科目",visible:e.accountSubjectVis,width:"50%"},on:{"update:visible":function(t){e.accountSubjectVis=t},close:e.accountSubjectCancel}},[i("div",{staticClass:"dialog-content height-scrollbar"},[i("div",{staticClass:"content-header"},[i("el-form",{staticClass:"page-form",attrs:{size:"mini"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",{staticClass:"margin-bottom-5",attrs:{"label-width":"0px"}},[i("el-input",{attrs:{placeholder:"请输入关键字..."},model:{value:e.accountSubjectFilterText,callback:function(t){e.accountSubjectFilterText=t},expression:"accountSubjectFilterText"}})],1)],1)],1),e._v(" "),i("div",{staticClass:"content-body"},[i("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.accountSubjectLoading,expression:"accountSubjectLoading"}],ref:"accountSubjectTree",staticClass:"referTree",attrs:{data:e.accountSubjectTreeData,props:e.TreeProps,"filter-node-method":e.treeFilterNode},on:{"node-click":e.accountSubjectTreeNodeChoose}})],1)]),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"mini"},on:{click:e.accountSubjectCancel}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.accountSubjectSave}},[e._v("确定")])],1)]),e._v(" "),i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.auxiliaryTitle,visible:e.auxiliaryVis,width:"50%"},on:{"update:visible":function(t){e.auxiliaryVis=t},close:e.auxiliaryCancel}},[i("div",{staticClass:"dialog-content height-scrollbar"},[i("div",{staticClass:"content-header"},[i("el-form",{staticClass:"page-form",attrs:{size:"mini"}},[i("el-form-item",{staticClass:"margin-bottom-5",attrs:{"label-width":"0px"}},[i("el-input",{attrs:{placeholder:"请输入关键字..."},model:{value:e.auxiliaryFilterText,callback:function(t){e.auxiliaryFilterText=t},expression:"auxiliaryFilterText"}})],1)],1)],1),e._v(" "),i("div",{staticClass:"content-body"},[i("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.auxiliaryLoading,expression:"auxiliaryLoading"}],ref:"auxiliaryTree",staticClass:"referTree",attrs:{data:e.auxiliaryTreeData,props:e.TreeProps,"filter-node-method":e.treeFilterNode},on:{"node-click":e.auxiliaryChoose}})],1)]),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"mini"},on:{click:e.auxiliaryCancel}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.auxiliarySave}},[e._v("确定")])],1)]),e._v(" "),i("common-dialog",{ref:"commonDialog",attrs:{accountList:e.accountList}})],1)])},staticRenderFns:[]}}});