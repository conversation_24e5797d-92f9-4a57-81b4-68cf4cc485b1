spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ****************************************************
          username: sa
          password: <EMAIL>
          type: com.alibaba.druid.pool.DruidDataSource
  redis:
    port: 6379
log4j:
  rootLogger: INFO,stdout
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
accessory:
  stockpile-url: D:/uploadFiles

#附件处理
attachment:
  url: http://127.0.0.1:8084/grp-attachment/fileFormat/convert

collection:
  url:
    ip : http://*************:13333
    #主动推送采集数据
    active_url: /da/api/pushcollection/collectionData
    #接收信息接口（异步）
    asynchronous_url: /da/api/collection/collectionData
  voucher:
    组织编码: "1m"
    资料类型: "记账凭证"
    报文传输方式: "1"
    来源系统编码: "U8"
    凭证类型编码: "记"
    凭证类型名称: "记账凭证"
    凭证来源: "总账"
    凭证类型: "记账凭证"
    路径: "/mnt/windows_upload/"