spring:
  datasource:
    dynamic:
      enabled: true
      primary: master
      datasource:
        master:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: *******************************************************
          username: sa
          password: <EMAIL>
          type: com.alibaba.druid.pool.DruidDataSource
        yzyDB:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ********************************************************
          username: sa
          password: <EMAIL>
          type: com.alibaba.druid.pool.DruidDataSource
        slaver_1:
          driver-class-name: org.postgresql.Driver
          url: ********************************************************************************************************************************************
          username: postgres
          password: test12345
          type: com.alibaba.druid.pool.DruidDataSource
        slyxt:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ******************************************************
          username: sa
          password: <EMAIL>
          type: com.alibaba.druid.pool.DruidDataSource
