spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ***************************************************
          username: sa
          password: hkyxt
          type: com.alibaba.druid.pool.DruidDataSource
        slaver_1:
          driver-class-name: org.postgresql.Driver
          url: *************************************************************************************************************************************************
          username: pg
          password: 01mm}VINFZ
          type: com.alibaba.druid.pool.DruidDataSource
        slyxt:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ******************************************************
          username: yxt_sjzxcx
          password: yxt_sjzxcx_Hky@2025
          type: com.alibaba.druid.pool.DruidDataSource
log4j:
  rootLogger: INFO

#微信发送通知
wxsent:
  sentMsgUrl: https://cw-m.hzpt.edu.cn/grp/wxSentMessage
  sentDDMsgUrl: https://cw-m.hzpt.edu.cn/grp/dingdingSentMessage
  openPort  : true

#附件处理
attachment:
  url: http://*************:8084/grp-attachment/fileFormat/convert

JRMobile:
  url: https://cw-m.hzpt.edu.cn/h5/hky/index.html

#收入来款单发票
income-invoice:
  saler-name: 杭州科技职业技术学院
  saler-tel: ***********
  saler-address: 浙江省杭州市西湖区文一西路37号
  saler-account: 33050161722709123456
  saler-bank: 中国建设银行股份有限公司杭州富阳支行
  #杭科院税号
  taxnum: 12330100470131064X
  #诺诺开票应用的唯一身份
  app-key: ********
  #应用中用于保障数据安全的钥匙
  app-secret: 867918776E3C4487
  #token
  access-token: 98a0d315862dfbbc8dd1870nanavqmks
  #诺诺票据-正式环境接口地址
  service-prod-url: https://sdk.nuonuo.com/open/v1/services
  #诺诺票据-沙箱环境接口地址
  service-test-url: https://sandbox.nuonuocs.cn/open/v1/services
  #诺诺请求开具发票接口
  billing-method: nuonuo.OpeMplatform.requestBillingNew
  #诺诺开票结果查询接口
  billing-result-method: nuonuo.OpeMplatform.queryInvoiceResult


collection:
  url:
    ip : http://*************:13333
    #主动推送采集数据
    active_url: /da/api/pushcollection/collectionData
    #接收信息接口（异步）
    asynchronous_url: /da/api/collection/collectionData
  voucher:
    组织编码: "1m"
    资料类型: "记账凭证"
    报文传输方式: "1"
    来源系统编码: "U8"
    凭证类型编码: "记"
    凭证类型名称: "记账凭证"
    凭证来源: "总账"
    凭证类型: "记账凭证"
    路径: "/mnt/windows_upload/"