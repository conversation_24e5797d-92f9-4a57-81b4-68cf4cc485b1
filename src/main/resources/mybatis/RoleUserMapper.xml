<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.RoleUserMapper">

    <select id="getAllPersonAndDeptList" resultType="com.gg.grp.hkycg.model.vo.AllPersonAndDeptListVO">
        select top ${pageNum} *
        from (
        SELECT
        row_number() over (order by zydm) as rownumber,
        count(1) over () as totalCount,
        zydm, zyxm, bmxx.bmdm, bmmc
        from PUBZYXX zyxx
        left join pubbmxx bmxx on zyxx.GSDM = bmxx.GSDM and zyxx.KJND = bmxx.KJND and zyxx.BMDM = bmxx.bmdm
        where zyxx.GSDM = #{gsdm}
        and zyxx.KJND = #{kjnd}
        and zydm != '999'
        and not exists(
        SELECT a.zydm
        FROM GPM_ROLEUSER a
        WHERE a.gsdm = #{gsdm}
        and a.kjnd = #{kjnd}
        and a.ROLECODE = #{roleCode}
        and zyxx.zydm = a.zydm
        )
        <if test="condition != null and condition !=''">
            and  (zydm like '%'+ #{condition} + '%' or zyxx.zyxm like '%'+ #{condition} + '%' or bmxx.bmdm like '%'+ #{condition} + '%' or bmxx.bmmc like '%'+ #{condition} + '%')
        </if>
        ) temp_row
        where rownumber > #{pageNum} * (#{pages} - 1)

    </select>

    <select id="queryRoleUserList" resultType="com.gg.grp.hkycg.model.vo.RoleUserListVO" parameterType="com.gg.grp.hkycg.model.vo.ModuleGivenDTO">
        select top ${pageNum} *
        from (
                 select
                     row_number() over (order by a.ROLECODE,a.zydm) as rownumber,
                         count(1) over () as totalCount,
                         a.ROLECODE,a.zydm,zyxx.zyxm,bmxx.bmdm,
                         bmxx.bmmc,data.DATACODE as datacode,data.DATANAME as dataname
                 from gpm_roleuser a
                          left join PUBZYXX zyxx on a.ZYDM = zyxx.zydm and a.GSDM = zyxx.gsdm and a.KJND = zyxx.kjnd
                          left join pubbmxx bmxx on zyxx.BMDM = bmxx.bmdm and zyxx.GSDM = bmxx.GSDM and zyxx.KJND = bmxx.KJND
                          LEFT JOIN GPM_USERDATA userdata ON zyxx.zydm = userdata.zydm
                          LEFT JOIN GPM_DATA data ON userdata.DATACODE = data.DATACODE
                 where a.GSDM = #{gsdm}
                   and a.KJND = #{kjnd}
                   and a.ROLECODE = #{roleCode}
             ) temp_row
        where rownumber > #{pageNum} * (#{pages} - 1)
    </select>

    <select id="getGNFLByRole" resultType="com.alibaba.fastjson.JSONObject" parameterType="com.gg.grp.hkycg.model.dto.RoleDto">
        select b.GNFLCODE,a.CODE,
               CASE len(a.CODE)
                   WHEN 0 THEN
                       ''
                   WHEN 2 THEN
                       SUBSTRING(a.CODE, 0, 1)
                   WHEN 4 THEN
                       SUBSTRING(a.CODE, 0, 3)
                   WHEN 6 THEN
                       SUBSTRING(a.CODE, 0, 5)
                   WHEN 8 THEN
                       SUBSTRING(a.CODE, 0, 7)
                   END pCode,
               a.NAME
        from GPM_GNFL a
                 left join GPM_ROLEGNFL b on a.CODE = b.GNFLCODE and b.ROLECODE=#{roleCode}
        where CODE like '80%'

    </select>

    <select id="queryUserAuthorizeList" resultType="java.lang.String">
        select DISTINCT SUBSTRING(gnfl.CODE,0,5) from GPM_ROLEUSER roleuser
        LEFT JOIN GPM_ROLEGNFL rolegnfl ON roleuser.ROLECODE = rolegnfl.ROLECODE
        LEFT JOIN GPM_GNFL gnfl ON rolegnfl.GNFLCODE = gnfl.CODE
        WHERE roleuser.GSDM = #{gsdm}
        AND roleuser.KJND = #{kjnd}
        AND roleuser.ZYDM = #{zydm}
    </select>
</mapper>