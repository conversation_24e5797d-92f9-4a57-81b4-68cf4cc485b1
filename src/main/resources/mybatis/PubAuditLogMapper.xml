<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.PubAuditLogMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gg.grp.hkycg.model.pojo.PubAuditLog">
        <result column="GSDM" property="gsdm" jdbcType="VARCHAR"/>
        <result column="KJND" property="kjnd" jdbcType="VARCHAR"/>
        <result column="LOGID" property="logid" jdbcType="NUMERIC"/>
        <result column="BILLID" property="billid" jdbcType="VARCHAR"/>
        <result column="BILLNAME" property="billname" jdbcType="VARCHAR"/>
        <result column="FLOWCODE" property="flowcode" jdbcType="VARCHAR"/>
        <result column="FLOWNAME" property="flowname" jdbcType="VARCHAR"/>
        <result column="MODNAME" property="modname" jdbcType="VARCHAR"/>
        <result column="BIZNAME" property="bizname" jdbcType="VARCHAR"/>
        <result column="NODESEQ" property="nodeseq" jdbcType="INTEGER"/>
        <result column="NODENAME" property="nodename" jdbcType="VARCHAR"/>
        <result column="AUDITORID" property="auditorid" jdbcType="VARCHAR"/>
        <result column="AUDITOR" property="auditor" jdbcType="VARCHAR"/>
        <result column="CERTIGIERID" property="certigierid" jdbcType="INTEGER"/>
        <result column="CERTIGIER" property="certigier" jdbcType="VARCHAR"/>
        <result column="ADATETIME" property="adatetime" jdbcType="VARCHAR"/>
        <result column="AMT" property="amt" jdbcType="DECIMAL"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="ATYPE" property="atype" jdbcType="VARCHAR"/>
        <result column="LOGSEQ" property="logseq" jdbcType="INTEGER"/>
        <result column="ServDateTime" property="servDateTime" jdbcType="VARCHAR"/>
        <result column="ComputerName" property="computerName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 获取最大日志ID -->
    <select id="selectMaxLogID" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(LOGID), 0) + 1 FROM PUBAUDITLOG
    </select>

    <!-- 根据单据号查询审核日志 -->
    <select id="selectByBillId" resultMap="BaseResultMap">
        SELECT * FROM PUBAUDITLOG
        WHERE GSDM = #{gsdm}
          AND KJND = #{kjnd}
          AND BILLID = #{billid}
          AND FLOWCODE = #{flowcode}
        ORDER BY LOGSEQ
    </select>

    <!-- 删除提交相关的审核日志 -->
    <delete id="deleteSubmitLog">
        DELETE FROM PUBAUDITLOG
        WHERE GSDM = #{gsdm}
          AND KJND = #{kjnd}
          AND BILLID = #{billid}
          AND FLOWCODE = #{flowcode}
          AND NODESEQ = -9
          AND ATYPE = '送审'
    </delete>

    <!-- 删除所有审核日志（销审用） -->
    <delete id="deleteAllAuditLogs">
        DELETE FROM PUBAUDITLOG
        WHERE GSDM = #{gsdm}
          AND KJND = #{kjnd}
          AND BILLID = #{billid}
          AND FLOWCODE = #{flowcode}
    </delete>

</mapper> 