<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.BudgetIndexMapper">

    <!-- 基础结果映射 - 根据新的字段结构 -->
    <resultMap id="BaseResultMap" type="com.gg.grp.hkycg.model.pojo.BudgetIndex">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="lid" property="lid" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="index_source" property="indexSource" jdbcType="VARCHAR"/>
        <result column="economic_subject" property="economicSubject" jdbcType="VARCHAR"/>
        <result column="account_title" property="accountTitle" jdbcType="VARCHAR"/>
        <result column="func_subject" property="funcSubject" jdbcType="VARCHAR"/>
        <result column="index" property="index" jdbcType="INTEGER"/>
        <result column="project" property="project" jdbcType="VARCHAR"/>
        <result column="fZ9DM" property="fZ9DM" jdbcType="VARCHAR"/>
        <result column="expense_remark" property="expenseRemark" jdbcType="VARCHAR"/>
        <result column="residual" property="residual" jdbcType="DECIMAL"/>
        <result column="fZ8DM" property="fZ8DM" jdbcType="VARCHAR"/>
        <result column="fZ7DM" property="fZ7DM" jdbcType="VARCHAR"/>
        <result column="outlay_dept" property="outlayDept" jdbcType="VARCHAR"/>
        <result column="employee" property="employee" jdbcType="VARCHAR"/>
        <result column="cyskzfs" property="cyskzfs" jdbcType="INTEGER"/>
        <result column="execute_way" property="executeWay" jdbcType="VARCHAR"/>
        <result column="fZ9MC" property="fZ9MC" jdbcType="VARCHAR"/>
        <result column="fZ8MC" property="fZ8MC" jdbcType="VARCHAR"/>
        <result column="fZ7MC" property="fZ7MC" jdbcType="VARCHAR"/>
        <result column="bid" property="bid" jdbcType="VARCHAR"/>
        <result column="del" property="del" jdbcType="BIT"/>
        <result column="employee_code" property="employeeCode" jdbcType="VARCHAR"/>
        <result column="gsdm" property="gsdm" jdbcType="VARCHAR"/>
        <result column="kjnd" property="kjnd" jdbcType="VARCHAR"/>
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 根据业务ID查询预算指标列表 - 从OER_YSZB表查询 -->
    <select id="selectByLid" resultMap="BaseResultMap">
        SELECT 
            o.GSDM + '_' + o.KJND + '_' + CAST(o.MLID AS VARCHAR) as id,
            #{lid} as lid,
            o.JE as amount,
            '预算指标' as index_source,
            o.ZBJJKMMC as economic_subject,
            o.YSKJKMMC as account_title,
            o.ZBGNKMMC as func_subject,
            o.XH as [index],
            o.ZY as project,
            o.ZBFZ9DM as fZ9DM,
            o.ZY as expense_remark,
            o.JE as residual,
            o.ZBFZ8DM as fZ8DM,
            o.ZBFZ7DM as fZ7DM,
            '默认部门' as outlay_dept,
            '默认员工' as employee,
            0 as cyskzfs,
            '预算执行' as execute_way,
            o.ZBFZ9MC as fZ9MC,
            o.ZBFZ8MC as fZ8MC,
            o.ZBFZ7MC as fZ7MC,
            #{lid} as bid,
            0 as del,
            '000' as employee_code,
            o.GSDM as gsdm,
            o.KJND as kjnd,
            '系统' as employee_name
        FROM OER_YSZB o
        WHERE o.DJBH = #{lid}
        ORDER BY o.XH
    </select>

    <!-- 根据业务ID删除预算指标 - 删除OER_YSZB和GBI_ZBSYREC -->
    <delete id="deleteByLid">
        <!-- 删除指标使用记录 -->
        DELETE FROM GBI_ZBSYREC 
        WHERE DJID = #{lid}
          AND MODULE = '${@<EMAIL>()}'
          AND DJLX = '${@<EMAIL>()};'
        
        <!-- 删除预算指标 -->
        DELETE FROM OER_YSZB
        WHERE DJBH = #{lid};
    </delete>

    <!-- 批量插入预算指标 - 插入OER_YSZB和GBI_ZBSYREC -->
    <insert id="batchInsert" parameterType="java.util.List">
        <!-- 插入OER_YSZB预算指标表 -->
        <foreach collection="list" item="item" index="index">
            INSERT INTO OER_YSZB (
                GSDM, KJND, MLID, XH, JE, ZY, ZBGNKMMC, ZBJJKMMC, YSKJKMMC,
                ZBFZ7DM, ZBFZ7MC, ZBFZ8DM, ZBFZ8MC, ZBFZ9DM, ZBFZ9MC,
                ZBDM, ZBID, DJBH
            ) VALUES (
                #{item.gsdm}, #{item.kjnd}, 
                (SELECT ISNULL(MAX(MLID), 0) + ${index} + 1 FROM OER_YSZB WHERE GSDM = #{item.gsdm} AND KJND = #{item.kjnd}),
                #{item.index}, #{item.amount}, #{item.project}, 
                #{item.funcSubject}, #{item.economicSubject}, #{item.accountTitle},
                #{item.fZ7DM}, #{item.fZ7MC}, #{item.fZ8DM}, #{item.fZ8MC}, 
                #{item.fZ9DM}, #{item.fZ9MC},
                (SELECT DISTINCT TOP 1 JHBH FROM GPM_CGJHML WHERE DJBH = #{item.lid}),
                'ZB' + #{item.gsdm} + #{item.kjnd} + RIGHT('000000' + CAST((SELECT ISNULL(MAX(MLID), 0) + ${index} + 1 FROM OER_YSZB WHERE GSDM = #{item.gsdm} AND KJND = #{item.kjnd}) AS VARCHAR), 6),
                (SELECT ISNULL(MAX(ZBID), 0) + ${index} + 1 FROM OER_YSZB WHERE GSDM = #{item.gsdm} AND KJND = #{item.kjnd}),
                #{item.lid}
            );
            
            <!-- 插入GBI_ZBSYREC指标使用记录表 -->
            INSERT INTO GBI_ZBSYREC (
                GSDM, KJND, MODULE, DJLX, DJID, DJFLH, DJFLMX, DJYWRQ, DJZT,
                JHID, DJJE, DJZY, SZFX, ZBLB, CZLX, JDBZ, ZZBZ, SL
            ) VALUES (
                #{item.gsdm}, #{item.kjnd}, 'GPM', 'CGJH', #{item.lid}, 
                #{item.index}, 1, CONVERT(VARCHAR(8), GETDATE(), 112), '1',
                (SELECT DISTINCT TOP 1 JHBH FROM GPM_CGJHML WHERE JHBH = #{item.lid}),
                #{item.amount}, #{item.expenseRemark}, '1', 'MXZB', '1', 
                '1', '0', 1
            );
        </foreach>
    </insert>

</mapper> 