<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgfsMapper">

    <!-- 分页查询采购方式列表 -->
    <select id="getCgfsPageList" resultType="com.gg.grp.hkycg.model.vo.CgfsListVO">
        WITH PagedResults AS (
            SELECT 
                cgfs.GSDM as gsdm,
                cgfs.KJND as kjnd,
                cgfs.CGFSDM as cgfsdm,
                cgfs.CGFSMC as cgfsmc,
                cgfs.SYZT as syzt,
                CASE 
                    WHEN cgfs.SYZT = '1' THEN '启用'
                    WHEN cgfs.SYZT = '0' THEN '停用'
                    ELSE '未知'
                END as syztmc,
                cgfs.ZJM as zjm,
                cgfs.BZ as bz,
                cgfs.PXH as pxh,
                cgfs.JC as jc,
                COUNT(*) OVER() as totalCount,
                ROW_NUMBER() OVER(ORDER BY cgfs.CGFSDM ASC , cgfs.PXH ASC ) as rn
            FROM GPM_CGFS cgfs
            WHERE cgfs.GSDM = #{query.gsdm}
                AND cgfs.KJND = #{query.kjnd}
                <if test="query.condition != null and query.condition != ''">
                    AND (
                        cgfs.CGFSDM LIKE '%' + #{query.condition} + '%'
                        OR cgfs.CGFSMC LIKE '%' + #{query.condition} + '%'
                        OR cgfs.ZJM LIKE '%' + #{query.condition} + '%'
                    )
                </if>
                <if test="query.syzt != null and query.syzt != ''">
                    AND cgfs.SYZT = #{query.syzt}
                </if>
        )
        SELECT *
        FROM PagedResults
        WHERE rn BETWEEN (#{query.current} - 1) * #{query.size} + 1 AND #{query.current} * #{query.size}
        ORDER BY rn
    </select>

</mapper> 