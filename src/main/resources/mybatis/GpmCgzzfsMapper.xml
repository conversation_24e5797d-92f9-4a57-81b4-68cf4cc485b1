<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgzzfsMapper">

    <!-- 分页查询采购组织方式列表 -->
    <select id="getCgzzfsPageList" resultType="com.gg.grp.hkycg.model.vo.CgzzfsListVO">
        WITH PagedResults AS (
            SELECT 
                cgzzfs.GSDM as gsdm,
                cgzzfs.KJND as kjnd,
                cgzzfs.ZZFSDM as zzfsdm,
                cgzzfs.ZZFSMC as zzfsmc,
                cgzzfs.SYZT as syzt,
                CASE 
                    WHEN cgzzfs.SYZT = '1' THEN '启用'
                    WHEN cgzzfs.SYZT = '0' THEN '停用'
                    ELSE '未知'
                END as syztmc,
                cgzzfs.ZJM as zjm,
                cgzzfs.BZ as bz,
                cgzzfs.PXH as pxh,
                cgzzfs.sfxs as sfxs,
                CASE 
                    WHEN cgzzfs.sfxs = '1' THEN '是'
                    WHEN cgzzfs.sfxs = '0' THEN '否'
                    ELSE '未知'
                END as sfxsmc,
                COUNT(*) OVER() as totalCount,
                ROW_NUMBER() OVER(ORDER BY cgzzfs.ZZFSDM ASC , cgzzfs.PXH ASC ) as rn
            FROM GPM_CGZZFS cgzzfs
            WHERE cgzzfs.GSDM = #{query.gsdm}
                AND cgzzfs.KJND = #{query.kjnd}
                <if test="query.condition != null and query.condition != ''">
                    AND (
                        cgzzfs.ZZFSDM LIKE '%' + #{query.condition} + '%'
                        OR cgzzfs.ZZFSMC LIKE '%' + #{query.condition} + '%'
                        OR cgzzfs.ZJM LIKE '%' + #{query.condition} + '%'
                    )
                </if>
                <if test="query.syzt != null and query.syzt != ''">
                    AND cgzzfs.SYZT = #{query.syzt}
                </if>
                <if test="query.sfxs != null and query.sfxs != ''">
                    AND cgzzfs.sfxs = #{query.sfxs}
                </if>
        )
        SELECT *
        FROM PagedResults
        WHERE rn BETWEEN (#{query.current} - 1) * #{query.size} + 1 AND #{query.current} * #{query.size}
        ORDER BY rn
    </select>

</mapper> 