<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GbiZbxmbMapper">

    <sql id="Swap_Column_List">
        ZBID AS indexID,
        GSDM   AS corpCode,
        KJND   AS accountantYear,
        ZBDM   AS indexCode,
        ZBLB   AS indexType,
        SJZBID AS realIndexCode,
        ZY     AS note,
        YSFADM,
        YSFAMC,
        JFLXDM AS expenditureTypeCode,
        JFLXMC AS expenditureTypeName,
        WHDM,
        WHMC,
        ZBLYDM AS indexSourceCode,
        ZBLYMC AS indexSourceName,
        ZJLYDM,
        ZJLYMC,
        ZJXZDM,
        ZJXZMC,
        JSFSDM,
        JSFSMC,
        ZFFSDM,
        ZFFSMC,
        YSDWDM,
        YSDWMC,
        BMDM   AS deptCode,
        BMMC   AS deptName,
        ZYDM   AS employeeCode,
        ZYMC   AS employeeName,
        XMFLDM AS projectClassifyCode,
        XMFLMC AS projectClassifyName,
        XMDM   AS projectCode,
        XMMC   AS projectName,
        GNKMDM AS funcSubjectCode,
        GNKMMC AS funcSubjectName,
        JJKMDM AS economicSubjectCode,
        JJKMMC AS economicSubjectName,
        FZ6DM,
        FZ6MC,
        FZ7DM,
        FZ7MC,
        FZ8DM,
        FZ8MC,
        FZ9DM,
        FZ9MC,
        FZADM,
        FZAMC,
        ZTH,
        KMDM   AS subjectCode,
        KMMC   AS subjectName,
        ZT     AS state,
        SFZFCG,
        JE     AS amt,
        YSJE,
        NCYSY,
        DJJE,
        LRRID  AS inputCode,
        LRR    AS inputName,
        LRRQ   AS inputDate,
        LRSJ   AS inputTime,
        SHRID  AS approverCode,
        SHR    AS approverName,
        SHRQ   AS approverDate,
        SHSJ   AS approverTime,
        PFRID,
        PFR,
        PFRQ,
        PFSJ,
        CurSHJD,
        NextSHJD,
        FlowCode,
        YJBFB,
        CYSKZFS,
        SFJZ,
        module,
        BZ     AS remarks,
        IDZBBH,
        XFZT,
        SJLY   AS realSource,
        BYGKZ
    </sql>

    <select id="getAllIndexList" resultType="com.alibaba.fastjson.JSONObject">
        select top ${pageSize} *
        from (
        SELECT
        row_number() over (order by zbxmb_OUT.ZBID) as rownumber,
        count(1) over () as totalCount,
        --余额 等于 年初金额减去使用金额与在途金额
        (YE - ISNULL((select
        SUM(syje+ztje) AS SYJE
        from
        (
        select
        tmp.*,
        tmp.ncysy as ncysyje,
        tmp.qcje-tmp.ncysy + tmp.tzje-tmp.tjje + tmp.tjzje-tmp.tjjje-tmp.syje-tmp.ztje-SQJE as kyje,
        case
        when (tmp.zbje + tmp.ncysy)= 0 then 0
        else (tmp.ncysy + tmp.syje + SQJE )* 100 / (tmp.zbje + tmp.ncysy)
        end as zxbl
        from
        (
        select
        jh.zbid,
        jh.module,
        jh.zt,
        jh.YSJE,
        jh.je qcje,
        jh.ncysy,
        jh.djje,
        (jh.je-jh.ncysy + isnull(tzzb.tzje,
        0)+ isnull(tzzb2.tzje,
        0)-isnull(tzzb.tjje,
        0)-isnull(tzzb2.tjje,
        0)+ isnull(tjzb.tjzje,
        0)-isnull(tjzb.tjjje,
        0)) as zbje,
        isnull(syjh.syje,
        0) as syje,
        isnull(syjh2.bqzcje,
        0) as bqzcje,
        isnull(syjh.syje2,
        0) as ztje,
        isnull(tzzb.tzje,
        0)+ isnull(tzzb2.tzje,
        0) as tzje,
        isnull(tzzb.tjje,
        0)+ isnull(tzzb2.tjje,
        0) as tjje,
        isnull(tjzb.tjzje,
        0) as tjzje,
        isnull(tjzb.tjjje,
        0) as tjjje,
        isnull(JFSQ.SQJE,
        0) as SQJE
        from
        gbi_zbxmb jh
        left join (
        select
        zbid,
        sum(case when szsx = 1 then je else 0 end) as tzje,
        sum(case when szsx =-1 then je else 0 end) as tjje
        from
        gbi_tzdnr nr
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and exists(
        select
        tzdid
        from
        gbi_tzdml ml
        where
        ml.gsdm = nr.GSDM
        and ml.kjnd = nr.KJND
        and ml.TZRQ &lt;= #{endDate}
        and ml.tzdzt in ('1', '2')
        and ml.zblb = 'MXZB'
        and ml.TZDID = nr.TZDID
        and ml.DJLX = 'TZD')
        group by
        zbid) tzzb on
        tzzb.zbid = jh.zbid
        left join (
        select
        jhid,
        Sum(case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then djje else 0 end) tzje,
        Sum(case when (SZFX = '1' and JDBZ = '0') or (SZFX = '0' and JDBZ = '1') then djje else 0 end) tjje
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '1'
        and djzt in ('1', '2', '3')
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) tzzb2 on
        jh.zbid = tzzb2.jhid
        left join (
        select
        zbid,
        sum(case when szsx = 1 then je else 0 end) as tjzje,
        sum(case when szsx =-1 then je else 0 end) as tjjje
        from
        gbi_tzdnr nr
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and exists(
        select
        tzdid
        from
        gbi_tzdml ml
        where
        ml.gsdm = nr.GSDM
        and ml.kjnd = nr.KJND
        and ml.TZRQ &lt;= #{endDate}
        and ml.tzdzt in ('1', '2')
        and ml.zblb = 'MXZB'
        and ml.TZDID = nr.TZDID
        and ml.DJLX = 'TJD')
        group by
        zbid) tjzb on
        tjzb.zbid = jh.zbid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) syje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh on
        jh.zbid = syjh.jhid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) bqzcje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh2 on
        jh.zbid = syjh2.jhid
        left join (
        select
        SQ.*,
        SQJE0 as SQJE
        from
        (
        select
        GSDM,
        KJND,
        jhid as ZBID,
        ZBLB,
        SUM(DJJE) AS SQJE0
        from
        GBI_ZBSYRec rec
        where
        1 = 1
        and Module = 'GSP'
        and (DJZT in ('1', '2')
        or (YWDJID like 'GL_PZ_%'
        and DJZT in ('1', '2', '3')))
        and ZBLB = 'MXZB'
        GROUP BY
        GSDM,
        KJND,
        JHID,
        ZBLB) SQ) JFSQ on
        jh.ZBID = JFSQ.ZBID
        and jh.GSDM = JFSQ.GSDM
        and jh.KJND = JFSQ.KJND
        and jh.ZBLB = 'MXZB'
        where
        jh.gsdm = #{gsdm}
        and jh.kjnd = #{kjnd}
        and jh.zt = '3'
        and jh.zblb = 'MXZB') tmp) XMB
        where
        ZBID = zbxmb_OUT.ZBID
        ),
        0))
        AS residual,
        <include refid="Swap_Column_List"/>
        FROM gbi_zbxmb zbxmb_OUT
        WHERE GSDM = #{gsdm}
        and KJND = #{kjnd}
        and not exists(
        select rolezb.ZBID
        from GPM_ROLEZB rolezb
        where rolezb.GSDM = #{gsdm}
        and rolezb.KJND = #{kjnd}
        and ROLECODE = #{roleCode}
        and rolezb.ZBID = zbxmb_OUT.ZBID
        )
        <if test="condition !=null and condition !=''">
            and (
            ZBID like #{condition}
            or BMMC like #{condition}
            or XMMC like #{condition}
            or ZY like #{condition}
            or ZYMC like #{condition}
            )
        </if>
        ) temp_row
        where rownumber > #{pageSize} * ( #{pageNo} - 1 )
    </select>
</mapper>