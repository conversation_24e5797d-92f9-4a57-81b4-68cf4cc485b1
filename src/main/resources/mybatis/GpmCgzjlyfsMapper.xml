<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgzjlyfsMapper">

    <!-- 分页查询专家来源方式列表 -->
    <select id="getCgzjlyfsPageList" resultType="com.gg.grp.hkycg.model.vo.CgzjlyfsListVO">
        WITH PagedResults AS (
            SELECT 
                cgzjlyfs.GSDM as gsdm,
                cgzjlyfs.KJND as kjnd,
                cgzjlyfs.CGZJLYFSDM as cgzjlyfsdm,
                cgzjlyfs.CGZJLYFSMC as cgzjlyfsmc,
                cgzjlyfs.SYZT as syzt,
                CASE 
                    WHEN cgzjlyfs.SYZT = '1' THEN '启用'
                    WHEN cgzjlyfs.SYZT = '0' THEN '停用'
                    ELSE '未知'
                END as syztmc,
                cgzjlyfs.ZJM as zjm,
                cgzjlyfs.BZ as bz,
                cgzjlyfs.PXH as pxh,
                cgzjlyfs.JC as jc,
                COUNT(*) OVER() as totalCount,
                ROW_NUMBER() OVER(ORDER BY cgzjlyfs.CGZJLYFSDM ASC , cgzjlyfs.PXH ASC ) as rn
            FROM GPM_CGZJLYFS cgzjlyfs
            WHERE cgzjlyfs.GSDM = #{query.gsdm}
                AND cgzjlyfs.KJND = #{query.kjnd}
                <if test="query.condition != null and query.condition != ''">
                    AND (
                        cgzjlyfs.CGZJLYFSDM LIKE '%' + #{query.condition} + '%'
                        OR cgzjlyfs.CGZJLYFSMC LIKE '%' + #{query.condition} + '%'
                        OR cgzjlyfs.ZJM LIKE '%' + #{query.condition} + '%'
                    )
                </if>
                <if test="query.syzt != null and query.syzt != ''">
                    AND cgzjlyfs.SYZT = #{query.syzt}
                </if>
        )
        SELECT *
        FROM PagedResults
        WHERE rn BETWEEN (#{query.current} - 1) * #{query.size} + 1 AND #{query.current} * #{query.size}
        ORDER BY rn
    </select>

</mapper> 