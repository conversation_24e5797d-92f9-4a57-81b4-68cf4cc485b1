<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.AuditMapper">

    <sql id="unifiedWhereClause">
        <if test="condition != null and condition != ''">
            AND (main.billName LIKE '%' + #{condition} + '%' OR main.applicant LIKE '%' + #{condition} + '%')
        </if>
        <if test="billType != null and billType != ''">
            AND main.billType = #{billType}
        </if>
        <if test="zt != null and zt != ''">
            AND main.statusCode = #{zt}
        </if>
        <if test="startDate != null and startDate != ''">
            AND CAST(main.submitTime AS DATE) &gt;= CONVERT(DATE, #{startDate}, 23)
        </if>
        <if test="endDate != null and endDate != ''">
            AND CAST(main.submitTime AS DATE) &lt;= CONVERT(DATE, #{endDate}, 23)
        </if>
    </sql>

    <select id="getUnifiedAuditList" resultType="com.gg.grp.hkycg.model.vo.UnifiedAuditListVO">
        WITH current_audit_flow AS (
            SELECT DJH, GSDM, KJND
            FROM (
                SELECT
                    DJH, GSDM, KJND, SHR,
                    ROW_NUMBER() OVER(PARTITION BY GSDM, KJND, DJH ORDER BY AUDIT_FLAG ASC) as rn
                FROM PUB_OBJ_FLOW
                WHERE ISAUDIT = '0' AND GSDM = #{gsdm} AND KJND = #{kjnd}
            ) flow
            WHERE rn = 1 AND flow.SHR LIKE '%,' + #{auditorCode} + ',%'
        ),
        CombinedResults AS (
            <!-- 采购计划 -->
            SELECT
                '采购计划' AS billType,
                ml.JHBH AS billNo,
                ml.JHMC AS billName,
                ml.JBR AS applicant,
                ml.BMMC AS applyDept,
                ml.JHJE AS amount,
                CONVERT(DATETIME,ml.LR_RQ, 120) AS submitTime,
                CAST(ml.ZT AS VARCHAR(20)) AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                SQYJJLY AS billContent
            FROM GPM_CGJHML ml
            JOIN current_audit_flow pof ON ml.JHBH = pof.DJH AND ml.GSDM = pof.GSDM AND ml.KJND = pof.KJND
            WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd} AND ml.ZT IN ('2', '3')

            UNION ALL

            <!-- 采购申请 -->
            SELECT
                '采购申请' AS billType,
                ml.SQBH AS billNo,
                ml.SQMC AS billName,
                ml.JBR AS applicant,
                ml.SQBMMC AS applyDept,
                ml.SQJE AS amount,
                CONVERT(DATETIME,ml.CJRQ, 120) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                SQYJJLY AS billContent
            FROM GPM_CGSQML ml
            JOIN current_audit_flow pof ON ml.SQBH = pof.DJH AND ml.GSDM = pof.GSDM AND ml.KJND = pof.KJND
            WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd} AND ml.ZT IN ('2', '3')

            UNION ALL

            <!-- 招标申请 -->
            SELECT
                '采购招标' AS billType,
                ml.ZBSQBH AS billNo,
                ml.ZBSQMC AS billName,
                ml.JBR AS applicant,
                ml.ZBSQBMMC AS applyDept,
                ml.XMJE AS amount,
                CONVERT(DATETIME,ml.CJSJ, 120) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CGLY AS billContent
            FROM GPM_CGZBSQML ml
            JOIN current_audit_flow pof ON ml.ZBSQBH = pof.DJH AND ml.GSDM = pof.GSDM AND ml.KJND = pof.KJND
            WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd} AND ml.ZT IN ('2', '3')

            UNION ALL

            <!-- 采购登记 -->
            SELECT
                '采购登记' AS billType,
                ml.JGDJBH AS billNo,
                ml.JGDJMC AS billName,
                ml.JBR AS applicant,
                ml.CGBMMC AS applyDept,
                ml.JGDJJE AS amount,
                CONVERT(DATETIME, ml.CREATE_TIME, 120) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CGLY AS billContent
            FROM GPM_CGDJML ml
            JOIN current_audit_flow pof ON ml.JGDJBH = pof.DJH AND ml.GSDM = pof.GSDM AND ml.KJND = pof.KJND
            WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd} AND ml.ZT IN ('2', '3')

            UNION ALL

            <!-- 采购验收 -->
            SELECT
                '采购验收' AS billType,
                ml.YSBH AS billNo,
                ml.YSMC AS billName,
                ml.JBR AS applicant,
                ml.YSBMMC AS applyDept,
                ml.BCYSJE AS amount,
                CONVERT(DATETIME, ml.CREATE_TIME, 120) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                BZ AS billContent
            FROM GPM_CGYSML ml
            JOIN current_audit_flow pof ON ml.YSBH = pof.DJH AND ml.GSDM = pof.GSDM AND ml.KJND = pof.KJND
            WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd} AND ml.ZT IN ('2', '3')
        ),
        FilteredResults AS (
            SELECT *,
                   ROW_NUMBER() OVER (ORDER BY submitTime DESC) as row_num,
                   COUNT(*) OVER() as totalCount
            FROM CombinedResults main
            WHERE 1=1
            <include refid="unifiedWhereClause"/>
        )
        SELECT *
        FROM FilteredResults
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>

</mapper>
