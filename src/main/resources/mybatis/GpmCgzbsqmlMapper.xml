<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgzbsqmlMapper">

    <!-- 分页查询招标申请列表 -->
    <select id="getCgzbsqPageList" resultType="com.gg.grp.hkycg.model.vo.CgzbsqListVO">
        SELECT * FROM (
            SELECT
                ROW_NUMBER() OVER (ORDER BY t.CJSJ DESC, t.ZBSQBH DESC) AS rowNum,
                COUNT(*) OVER() AS totalCount,
                t.ZBSQBH AS zbsqbh,
                t.ZBSQMC AS zbsqmc,
                t.GSDM AS gsdm,
                t.KJND AS kjnd,
                t.ZT AS zt,
                CASE
                    WHEN t.ZT = '1' THEN '已保存'
                    WHEN t.ZT = '2' THEN '已提交'
                    WHEN t.ZT = '3' THEN '审核中'
                    WHEN t.ZT = '4' THEN '已审核'
                    WHEN t.ZT = '5' THEN '退回'
                    ELSE '未知'
                END AS ztmc,
                t.CJSJ AS cjsj,
                t.ZBSQBMDM AS zbsqbmdm,
                t.ZBSQBMMC AS zbsqbmmc,
                t.XMJE AS xmje,
                t.JBR AS jbr,
                t.ZBFZR AS zbfzr,
                t.NZBRQ AS nzbrq,
                t.ZFCGFSDM AS zfcgfsdm,
                t.ZFCGFSMC AS zfcgfsmc
            FROM GPM_CGZBSQML t
            LEFT JOIN PUBZYXX emp ON t.CJRDM = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
            WHERE 1=1
              <if test="gsdm != null and gsdm != ''">
                  AND t.GSDM = #{gsdm}
              </if>
              <if test="kjnd != null and kjnd != ''">
                  AND t.KJND = #{kjnd}
              </if>
              <!-- 数据权限控制 -->
              <choose>
                  <!-- 职员权限：只能查看自己的数据 -->
                  <when test="dataPermission == '9001'">
                      AND t.CJRDM = #{currEmployeeCode}
                  </when>
                  <!-- 部长权限：可以查看本部门的数据 -->
                  <when test="dataPermission == '9002'">
                      AND emp.BMDM = #{currDeptCode}
                  </when>
                  <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
                  <when test="dataPermission == '9003'">
                      <!-- 管理员可以查看所有数据 -->
                  </when>
                  <!-- 默认权限：按职员权限处理 -->
                  <otherwise>
                      AND t.CJRDM = #{currEmployeeCode}
                  </otherwise>
              </choose>
              <if test="condition != null and condition != ''">
                  <![CDATA[ AND (t.ZBSQMC LIKE '%' + #{condition} + '%' OR t.ZBSQBH LIKE '%' + #{condition} + '%') ]]>
              </if>
              <if test="zt != null and zt != ''">
                  AND t.ZT = #{zt}
              </if>
              <if test="startDate != null and startDate != ''">
                  AND t.CJSJ &gt;= #{startDate}
              </if>
              <if test="endDate != null and endDate != ''">
                  AND t.CJSJ &lt;= #{endDate}
              </if>
        ) AS PagedResult
        WHERE rowNum > #{offset} AND rowNum <![CDATA[<=]]> (#{offset} + #{size})
    </select>

    <!-- 查询当前用户需要审核的招标申请列表 -->
    <select id="getCgzbsqAuditList" resultType="com.gg.grp.hkycg.model.vo.CgzbsqAuditListVO">
        <![CDATA[
        SELECT * FROM (
            SELECT 
                ROW_NUMBER() OVER (ORDER BY t.CJSJ DESC, t.ZBSQBH DESC) AS rowNum,
                COUNT(*) OVER() AS totalCount,
                t.ZBSQBH AS zbsqbh,
                t.ZBSQMC AS zbsqmc,
                t.XMJE AS xmje,
                t.JBR AS jbr,
                t.ZBFZR AS zbfzr,
                t.CJSJ AS cjsj,
                t.NZBRQ AS nzbrq,
                t.ZT AS zt,
                CASE 
                    WHEN t.ZT = '1' THEN '保存'
                    WHEN t.ZT = '2' THEN '已提交'
                    WHEN t.ZT = '3' THEN '审核中'
                    WHEN t.ZT = '4' THEN '已审核'
                    WHEN t.ZT = '5' THEN '退回'
                    ELSE '未知'
                END AS ztmc,
                t.GSDM AS gsdm,
                t.KJND AS kjnd,
                ISNULL(pof.SHRMC, '无审核节点') AS currentNodeName,
                ISNULL(pof.SHR, '') AS currentAuditor,
                CASE 
                    WHEN pof.SHR IS NOT NULL AND pof.SHR LIKE '%' + #{auditorCode} + '%' THEN CAST(1 AS BIT)
                    ELSE CAST(0 AS BIT)
                END AS canAudit,
                ISNULL(CAST(pof.AUDIT_FLAG AS INT), 0) AS shjb,
                ISNULL(t.ZBSQBMMC, '') AS zbsqbmmc,
                ISNULL(t.ZFCGFSDM, '') AS cgfs,
                ISNULL(nr.BZ, '') AS bz,
                '采购招标' AS djlx,
                t.CJSJ AS tjrq,
                ISNULL(pof.SHR, '') AS shr
            FROM GPM_CGZBSQML t
            LEFT JOIN PUB_OBJ_FLOW pof ON t.ZBSQBH = pof.DJH 
                AND pof.MODCODE = '${@<EMAIL>()}'
                AND pof.DJLX = '${@<EMAIL>()}'
                AND pof.ISAUDIT = '0'
            LEFT JOIN (
                SELECT DISTINCT 
                    ZBSQBH, 
                    BZ, 
                    GSDM, 
                    KJND
                FROM GPM_CGZBSQNR nr1
                WHERE nr1.ZBMXXH = (
                    SELECT MIN(nr2.ZBMXXH)
                    FROM GPM_CGZBSQNR nr2
                    WHERE nr2.ZBSQBH = nr1.ZBSQBH 
                      AND nr2.GSDM = nr1.GSDM 
                      AND nr2.KJND = nr1.KJND
                )
            ) nr ON t.ZBSQBH = nr.ZBSQBH AND t.GSDM = nr.GSDM AND t.KJND = nr.KJND
            WHERE 1=1
        ]]>
              <if test="gsdm != null and gsdm != ''">
                  AND t.GSDM = #{gsdm}
              </if>
              <if test="kjnd != null and kjnd != ''">
                  AND t.KJND = #{kjnd}
              </if>
              AND t.ZT IN ('2', '3')
              AND (pof.DJH IS NOT NULL OR t.ZT = '2')
              AND (pof.SHR IS NULL OR pof.SHR LIKE '%' + #{auditorCode} + '%' OR t.ZT = '2')
              <if test="applicationName != null and applicationName != ''">
                  <![CDATA[ AND (t.ZBSQMC LIKE '%' + #{applicationName} + '%' OR t.ZBSQBH LIKE '%' + #{applicationName} + '%') ]]>
              </if>
              <if test="applicant != null and applicant != ''">
                  <![CDATA[ AND t.JBR LIKE '%' + #{applicant} + '%' ]]>
              </if>
              <if test="billStatus != null">
                  AND t.ZT = #{billStatus}
              </if>
              <if test="startDate != null and startDate != ''">
                  AND t.CJSJ &gt;= #{startDate}
              </if>
              <if test="endDate != null and endDate != ''">
                  AND t.CJSJ &lt;= #{endDate}
              </if>
        ) AS PagedResult
        WHERE rowNum > #{offset} AND rowNum <![CDATA[<=]]> (#{offset} + #{size})
    </select>

</mapper> 