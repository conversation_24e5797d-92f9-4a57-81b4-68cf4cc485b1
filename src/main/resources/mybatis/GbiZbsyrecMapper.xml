<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GbiZbsyrecMapper">

    <!-- 批量插入指标使用记录到GBI_ZBSYREC表 -->
    <insert id="batchInsertGbiZbsyrec" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO GBI_ZBSYREC (
                GSDM, KJND, MODULE, DJLX, DJID, DJFLH, DJFLMX, DJYWRQ, DJZT,
                JHID, DJJE, DJZY, SZFX, ZBLB, CZLX, JDBZ, ZZBZ, SL
            ) VALUES (
                #{item.gsdm}, #{item.kjnd}, #{item.module}, #{item.djlx}, #{item.djid}, 
                #{item.djflh}, #{item.djflmx}, #{item.djywrq}, #{item.djzt},
                #{item.jhid}, #{item.djje}, #{item.djzy}, #{item.szfx}, #{item.zblb}, 
                #{item.czlx}, #{item.jdbz}, #{item.zzbz}, #{item.sl}
            )
        </foreach>
    </insert>

</mapper> 