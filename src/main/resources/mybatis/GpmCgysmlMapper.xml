<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgysmlMapper">

    <!-- 分页查询采购验收列表 -->
    <select id="getCgysPageList" resultType="com.gg.grp.hkycg.model.vo.CgysListVO">
        WITH PagedResults AS (
            SELECT
                COUNT(*) OVER() AS totalCount,
                t.YSBH as ysbh,
                t.YSMC as ysmc,
                t.GSDM as gsdm,
                t.K<PERSON> as kjnd,
                t.ZT as zt,
                CASE
                    WHEN t.ZT = '1' THEN '已保存'
                    WHEN t.ZT = '2' THEN '已提交'
                    WHEN t.ZT = '3' THEN '审核中'
                    WHEN t.ZT = '4' THEN '已审核'
                    WHEN t.ZT = '5' THEN '退回'
                    ELSE '未知状态'
                END as ztmc,
                CONVERT(varchar, t.CREATE_TIME, 120) as createTime,
                t.YSBMDM as ysbmdm,
                t.YSBMMC as ysbmmc,
                t.JBR as jbr,
                t.YSFZR as ysfzr,
                t.CGFSDM as cgfsdm,
                t.CGFSMC as cgfsmc,
                t.YSZZBMDM as yszzbmdm,
                t.YSZZBMMC as yszzbmmc,
                (SELECT MAX(CASE WHEN nr.SFCGJGYR = '1' THEN 1 ELSE 0 END) FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH) as sfcgjgyr,
                STUFF((SELECT DISTINCT ',' + nr.JGDJBH FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.JGDJBH IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as jgdjbh,
                STUFF((SELECT DISTINCT ',' + nr.JGDJMC FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.JGDJMC IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as jgdjmc,
                STUFF((SELECT DISTINCT ',' + nr.CGMLDM FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.CGMLDM IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as cgmldm,
                STUFF((SELECT DISTINCT ',' + nr.CGMLMC FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.CGMLMC IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as cgmlmc,
                STUFF((SELECT DISTINCT ',' + nr.GKGLBMDM FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.GKGLBMDM IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as gkglbmdm,
                STUFF((SELECT DISTINCT ',' + nr.GKGLBMMC FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.GKGLBMMC IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as gkglbmmc,
                STUFF((SELECT DISTINCT ',' + nr.XMMC FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH AND nr.XMMC IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as xmmc,
                (SELECT MAX(CASE WHEN nr.ZFCG = '1' THEN 1 ELSE 0 END) FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH) as zfcg,
                (SELECT MAX(CASE WHEN nr.JKCP = '1' THEN 1 ELSE 0 END) FROM GPM_CGYSNR nr WHERE nr.YSBH = t.YSBH) as jkcp,
                t.CGSQJE as cgsqje,
                t.DYSJJE as dysjje,
                t.BCYSJE as bcysje,
                t.BZ as bz,
                t.CREATE_USER as createUser,
                t.NYSRQ as nysrq,
                ROW_NUMBER() OVER (ORDER BY t.CREATE_TIME DESC) as row_num
            FROM GPM_CGYSML t
            LEFT JOIN PUBZYXX emp ON t.CREATE_USERDM = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
            WHERE t.GSDM = #{gsdm}
              AND t.KJND = #{kjnd}
              <!-- 数据权限控制 -->
              <choose>
                  <!-- 职员权限：只能查看自己的数据 -->
                  <when test="dataPermission == '9001'">
                      AND t.CREATE_USERDM = #{currEmployeeCode}
                  </when>
                  <!-- 部长权限：可以查看本部门的数据 -->
                  <when test="dataPermission == '9002'">
                      AND emp.BMDM = #{currDeptCode}
                  </when>
                  <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
                  <when test="dataPermission == '9003'">
                      <!-- 管理员可以查看所有数据 -->
                  </when>
                  <!-- 默认权限：按职员权限处理 -->
                  <otherwise>
                      AND t.CREATE_USERDM = #{currEmployeeCode}
                  </otherwise>
              </choose>
            <if test="condition != null and condition != ''">
                <![CDATA[ AND (t.YSMC LIKE '%' + #{condition} + '%' OR t.JBR LIKE '%' + #{condition} + '%') ]]>
            </if>
            <if test="zt != null and zt != ''">
                AND t.ZT = #{zt}
            </if>
            <if test="startDate != null and startDate != ''">
                AND t.CREATE_TIME &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND t.CREATE_TIME &lt;= #{endDate}
            </if>
        )
        SELECT *
        FROM PagedResults
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>
</mapper>