<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.AttachmentMapper">

    <!-- 查询单据关联的附件列表 -->
    <select id="queryAttachmentsByBill" resultType="com.gg.grp.hkycg.model.vo.AttachmentVO">
        SELECT 
            a.FJID as fjid,
            a.FJMC as fjmc,
            a.FJLX as fjlx,
            a.FJDX as fjdx,
            a.LRR as lrr,
            a.LR_RQ as lrRq,
            r.DJL<PERSON> as djlx,
            r.<PERSON><PERSON> as djh,
            r.SFMX as sfmx
        FROM GPM_ATTACHMENT a
        INNER JOIN GPM_ATTACHMENT_REL r ON a.FJID = r.FJID
        WHERE r.GSDM = #{gsdm}
          AND r.KJND = #{kjnd}
          AND r.DJLX = #{djlx}
          AND r.DJH = #{djh}
          <if test="sfmx != null and sfmx != ''">
              AND r.SFMX = #{sfmx}
          </if>
        ORDER BY a.LR_RQ DESC
    </select>
    
    <!-- 查询单据的目录和明细 -->
    <select id="queryBillDirectoryAndDetail" resultType="java.util.Map">
        SELECT DISTINCT
            r.DJH as djh,
            r.SFMX as sfmx
        FROM GPM_ATTACHMENT_REL r
        WHERE r.GSDM = #{gsdm}
          AND r.KJND = #{kjnd}
          AND r.DJLX = #{djlx}
          AND r.DJH = #{djh}
        ORDER BY r.SFMX ASC
    </select>
    
    <!-- 查询与单据编号相关的所有附件 -->
    <select id="queryAllAttachmentsByBillNo" resultType="com.gg.grp.hkycg.model.vo.AttachmentVO">
        SELECT 
            a.FJID as fjid,
            a.FJMC as fjmc,
            a.FJLX as fjlx,
            a.FJDX as fjdx,
            a.LRR as lrr,
            a.LR_RQ as lrRq,
            r.DJLX as djlx,
            r.DJH as djh,
            r.SFMX as sfmx
        FROM GPM_ATTACHMENT a
        INNER JOIN GPM_ATTACHMENT_REL r ON a.FJID = r.FJID
        WHERE r.GSDM = #{gsdm}
          AND r.KJND = #{kjnd}
          AND r.DJLX = #{djlx}
          AND r.DJH = #{djh}
        ORDER BY r.SFMX ASC, a.LR_RQ DESC
    </select>
    
    <!-- 根据附件ID查询附件信息 -->
    <select id="getAttachmentById" resultType="com.gg.grp.hkycg.model.pojo.GpmAttachment">
        SELECT * FROM GPM_ATTACHMENT
        WHERE FJID = #{fjid}
    </select>
    
</mapper> 