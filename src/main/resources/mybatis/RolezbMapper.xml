<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.RolezbMapper">
    <select id="queryRoleIndexList" resultType="com.gg.grp.hkycg.model.vo.RoleUserIndexListVO">

        select top ${pageNum} *
        from (
                 SELECT
                     row_number() over (order by role.ZBID) as rownumber,
                         count(1) over () as totalCount,
                         role.GSDM,
                     role.KJND,
                     role.ROLECODE,
                     role.ZBID,
                     role.SQREN,
                     role.JZRQ,
                     role.SYQX,
                     zb.XMMC,
                     zb.BMDM,
                     zb.BMMC,
                     zb.ZY,
                     zb.JFLXMC
                 FROM GPM_ROLEZB role
                          left join GBI_ZBXMB zb
                                    on role.GSDM = zb.GSDM and role.KJND = zb.KJND and role.ZBID = zb.ZBID and zb.ZBLB = 'MXZB'
                 where role.ROLECODE = #{roleCode}
                   and role.GSDM = #{gsdm}
                   and role.KJND = #{kjnd}
                   and zb.zblb = 'MXZB'
             ) temp_row
        where rownumber > #{pageNum} * ( #{pages} - 1 )
    </select>
</mapper>