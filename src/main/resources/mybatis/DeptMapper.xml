<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.DeptMapper">

    <select id="selectDeptWithResponsiblePerson" resultType="com.gg.grp.hkycg.model.vo.DeptListVO">
        SELECT 
            d.GSDM as gsdm,
            d.KJ<PERSON> as kjnd,
            d.BMDM as deptCode,
            d.BMM<PERSON> as deptName,
            d.fzrdm as fzrdm,
            e.ZYXM as fzrmc,
            d.BMID as bmid
        FROM PUBBMXX d
        LEFT JOIN PUBZYXX e ON d.fzrdm = e.ZYDM 
            AND d.GSDM = e.gsdm 
            AND d.KJND = e.kjnd
        WHERE d.GSDM = #{gsdm}
            AND d.KJND = #{kjnd}
            AND d.BMMC != '停用'
            <if test="condition != null and condition != ''">
                AND (d.BMDM LIKE CONCAT('%', #{condition}, '%') 
                     OR d.BMMC LIKE CONCAT('%', #{condition}, '%'))
            </if>
        ORDER BY d.BMDM ASC
    </select>

</mapper>