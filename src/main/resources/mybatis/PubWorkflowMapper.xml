<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.PubWorkflowMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gg.grp.hkycg.model.pojo.PubWorkflow">
        <result column="GSDM" property="gsdm" jdbcType="VARCHAR"/>
        <result column="KJND" property="kjnd" jdbcType="VARCHAR"/>
        <result column="FLOWCODE" property="flowcode" jdbcType="VARCHAR"/>
        <result column="FLOWNAME" property="flowname" jdbcType="VARCHAR"/>
        <result column="MODNAME" property="modname" jdbcType="VARCHAR"/>
        <result column="BIZCODE" property="bizcode" jdbcType="VARCHAR"/>
        <result column="BIZNAME" property="bizname" jdbcType="VARCHAR"/>
        <result column="ISSTART" property="isstart" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 根据模块代码和业务代码查询工作流程 -->
    <select id="selectByModAndBiz" resultMap="BaseResultMap">
        SELECT * FROM PUBWORKFLOW
        WHERE GSDM = #{gsdm}
          AND KJND = #{kjnd}
          AND MODNAME = #{modname}
          AND BIZCODE = #{bizcode}
          AND ISSTART = '是'
    </select>

</mapper> 