<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.PubObjFlowMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gg.grp.hkycg.model.pojo.PubObjFlow">
        <result column="POF_ID" property="pofId" jdbcType="VARCHAR"/>
        <result column="GSDM" property="gsdm" jdbcType="VARCHAR"/>
        <result column="KJND" property="kjnd" jdbcType="VARCHAR"/>
        <result column="MODCODE" property="modcode" jdbcType="VARCHAR"/>
        <result column="DJLX" property="djlx" jdbcType="VARCHAR"/>
        <result column="DJH" property="djh" jdbcType="VARCHAR"/>
        <result column="FLOWCODE" property="flowcode" jdbcType="VARCHAR"/>
        <result column="SHR" property="shr1" jdbcType="VARCHAR"/>
        <result column="SHRMC" property="shrmc" jdbcType="VARCHAR"/>
        <result column="AUDIT_FLAG" property="auditFlag" jdbcType="VARCHAR"/>
        <result column="AUDIT_AFT_FLAG" property="auditAftFlag" jdbcType="VARCHAR"/>
        <result column="ISAUDIT" property="isaudit" jdbcType="VARCHAR"/>
        <result column="specificCheckPerson" property="specificCheckPerson" jdbcType="VARCHAR"/>
        <result column="node_name" property="nodeName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 根据条件删除工作流程实例 -->
    <delete id="deleteByCon">
        DELETE FROM PUB_OBJ_FLOW
        WHERE MODCODE = #{modCode}
          AND DJLX = #{djlx}
          AND DJH = #{djh}
    </delete>

    <!-- 根据单据号查询工作流程实例 -->
    <select id="selectByBillno" resultMap="BaseResultMap">
        SELECT * FROM PUB_OBJ_FLOW
        WHERE DJH = #{djh}
        ORDER BY AUDIT_FLAG
    </select>

    <!-- 根据条件查询当前审核节点 -->
    <select id="selectNowNodeByDjh" resultMap="BaseResultMap">
        SELECT TOP 1 * FROM PUB_OBJ_FLOW
        WHERE MODCODE = #{modCode}
          AND DJLX = #{djlx}
          AND DJH = #{djh}
          AND ISAUDIT = '0'
        ORDER BY AUDIT_FLAG
    </select>
    
    <!-- 根据条件查询最后一个已审核节点 -->
    <select id="selectLastAuditNodeByDjh" resultMap="BaseResultMap">
        SELECT TOP 1 * FROM PUB_OBJ_FLOW
        WHERE MODCODE = #{modCode}
          AND DJLX = #{djlx}
          AND DJH = #{djh}
          AND ISAUDIT = '1'
        ORDER BY AUDIT_FLAG DESC
    </select>

</mapper> 