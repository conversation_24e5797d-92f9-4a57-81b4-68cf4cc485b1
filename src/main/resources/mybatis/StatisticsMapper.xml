<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.StatisticsMapper">

    <!-- 采购计划统计（合并已办结和未办结查询） -->
    <select id="countCgjhStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ZT = '4' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN ZT != '4' THEN 1 ELSE 0 END) as incomplete
        FROM GPM_CGJHML cgjh
        LEFT JOIN PUBZYXX emp ON CAST(cgjh.LRR_ID AS VARCHAR(20)) = emp.ZYDM AND cgjh.GSDM = emp.gsdm AND cgjh.KJND = emp.kjnd
        WHERE cgjh.GSDM = #{gsdm}
          AND cgjh.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgjh.LRR_ID = CAST(#{CurrEmployeeCode} AS NUMERIC)
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgjh.LRR_ID = CAST(#{CurrEmployeeCode} AS NUMERIC)
              </otherwise>
          </choose>
          <if test="startDate != null and startDate != ''">
              AND cgjh.LR_RQ &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgjh.LR_RQ &lt;= #{endDate}
          </if>
    </select>

    <!-- 采购申请统计（合并已办结和未办结查询） -->
    <select id="countCgsqStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ZT = '4' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN ZT != '4' THEN 1 ELSE 0 END) as incomplete
        FROM GPM_CGSQML cgsq
        LEFT JOIN PUBZYXX emp ON cgsq.CJRDM = emp.ZYDM AND cgsq.GSDM = emp.gsdm AND cgsq.KJND = emp.kjnd
        WHERE cgsq.GSDM = #{gsdm}
          AND cgsq.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgsq.CJRDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgsq.CJRDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="startDate != null and startDate != ''">
              AND cgsq.CJRQ &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgsq.CJRQ &lt;= #{endDate}
          </if>
    </select>

    <!-- 采购招标统计（合并已办结和未办结查询） -->
    <select id="countCgzbStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ZT = '4' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN ZT != '4' THEN 1 ELSE 0 END) as incomplete
        FROM GPM_CGZBSQML cgzb
        LEFT JOIN PUBZYXX emp ON cgzb.CJRDM = emp.ZYDM AND cgzb.GSDM = emp.gsdm AND cgzb.KJND = emp.kjnd
        WHERE cgzb.GSDM = #{gsdm}
          AND cgzb.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgzb.CJRDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgzb.CJRDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="startDate != null and startDate != ''">
              AND cgzb.CJSJ &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgzb.CJSJ &lt;= #{endDate}
          </if>
    </select>

    <!-- 采购登记统计（合并已办结和未办结查询） -->
    <select id="countCgjgStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ZT = '4' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN ZT != '4' THEN 1 ELSE 0 END) as incomplete
        FROM GPM_CGDJML cgjg
        LEFT JOIN PUBZYXX emp ON cgjg.CREATE_USERDM = emp.ZYDM AND cgjg.GSDM = emp.gsdm AND cgjg.KJND = emp.kjnd
        WHERE cgjg.GSDM = #{gsdm}
          AND cgjg.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgjg.CREATE_USERDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgjg.CREATE_USERDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="startDate != null and startDate != ''">
              AND cgjg.CREATE_TIME &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgjg.CREATE_TIME &lt;= #{endDate}
          </if>
    </select>

    <!-- 采购验收统计（合并已办结和未办结查询） -->
    <select id="countCgysStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ZT = '4' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN ZT != '4' THEN 1 ELSE 0 END) as incomplete
        FROM GPM_CGYSML cgys
        LEFT JOIN PUBZYXX emp ON cgys.CREATE_USERDM = emp.ZYDM AND cgys.GSDM = emp.gsdm AND cgys.KJND = emp.kjnd
        WHERE cgys.GSDM = #{gsdm}
          AND cgys.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgys.CREATE_USERDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgys.CREATE_USERDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="startDate != null and startDate != ''">
              AND cgys.CREATE_TIME &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgys.CREATE_TIME &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询采购计划数量 -->
    <select id="countCgjh" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM GPM_CGJHML cgjh
        LEFT JOIN PUBZYXX emp ON CAST(cgjh.LRR_ID AS VARCHAR(20)) = emp.ZYDM AND cgjh.GSDM = emp.gsdm AND cgjh.KJND = emp.kjnd
        WHERE cgjh.GSDM = #{gsdm}
          AND cgjh.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgjh.LRR_ID = CAST(#{CurrEmployeeCode} AS NUMERIC)
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgjh.LRR_ID = CAST(#{CurrEmployeeCode} AS NUMERIC)
              </otherwise>
          </choose>
          <if test="completed == true">
              AND cgjh.ZT = '4'
          </if>
          <if test="completed == false">
              AND cgjh.ZT != '4'
          </if>
          <if test="startDate != null and startDate != ''">
              AND cgjh.LR_RQ &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgjh.LR_RQ &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询采购申请数量 -->
    <select id="countCgsq" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM GPM_CGSQML cgsq
        LEFT JOIN PUBZYXX emp ON cgsq.CJRDM = emp.ZYDM AND cgsq.GSDM = emp.gsdm AND cgsq.KJND = emp.kjnd
        WHERE cgsq.GSDM = #{gsdm}
          AND cgsq.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgsq.CJRDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgsq.CJRDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="completed == true">
              AND cgsq.ZT = '4'
          </if>
          <if test="completed == false">
              AND cgsq.ZT != '4'
          </if>
          <if test="startDate != null and startDate != ''">
              AND cgsq.CJRQ &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgsq.CJRQ &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询采购招标数量 -->
    <select id="countCgzb" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM GPM_CGZBSQML cgzb
        LEFT JOIN PUBZYXX emp ON cgzb.CJRDM = emp.ZYDM AND cgzb.GSDM = emp.gsdm AND cgzb.KJND = emp.kjnd
        WHERE cgzb.GSDM = #{gsdm}
          AND cgzb.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgzb.CJRDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgzb.CJRDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="completed == true">
              AND cgzb.ZT = '4'
          </if>
          <if test="completed == false">
              AND cgzb.ZT != '4'
          </if>
          <if test="startDate != null and startDate != ''">
              AND cgzb.CJSJ &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgzb.CJSJ &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询采购登记数量 -->
    <select id="countCgjg" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM GPM_CGDJML cgjg
        LEFT JOIN PUBZYXX emp ON cgjg.CREATE_USERDM = emp.ZYDM AND cgjg.GSDM = emp.gsdm AND cgjg.KJND = emp.kjnd
        WHERE cgjg.GSDM = #{gsdm}
          AND cgjg.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgjg.CREATE_USERDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgjg.CREATE_USERDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="completed == true">
              AND cgjg.ZT = '4'
          </if>
          <if test="completed == false">
              AND cgjg.ZT != '4'
          </if>
          <if test="startDate != null and startDate != ''">
              AND cgjg.CREATE_TIME &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgjg.CREATE_TIME &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询采购验收数量 -->
    <select id="countCgys" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM GPM_CGYSML cgys
        LEFT JOIN PUBZYXX emp ON cgys.CREATE_USERDM = emp.ZYDM AND cgys.GSDM = emp.gsdm AND cgys.KJND = emp.kjnd
        WHERE cgys.GSDM = #{gsdm}
          AND cgys.KJND = #{kjnd}
          <!-- 数据权限控制 -->
          <choose>
              <!-- 职员权限：只能查看自己的数据 -->
              <when test="dataPermission == '9001'">
                  AND cgys.CREATE_USERDM = #{CurrEmployeeCode}
              </when>
              <!-- 部长权限：可以查看本部门的数据 -->
              <when test="dataPermission == '9002'">
                  AND emp.bmdm = #{CurrDeptCode}
              </when>
              <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
              <when test="dataPermission == '9003'">
                  <!-- 管理员可以查看所有数据 -->
              </when>
              <!-- 默认权限：按职员权限处理 -->
              <otherwise>
                  AND cgys.CREATE_USERDM = #{CurrEmployeeCode}
              </otherwise>
          </choose>
          <if test="completed == true">
              AND cgys.ZT = '4'
          </if>
          <if test="completed == false">
              AND cgys.ZT != '4'
          </if>
          <if test="startDate != null and startDate != ''">
              AND cgys.CREATE_TIME &gt;= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND cgys.CREATE_TIME &lt;= #{endDate}
          </if>
    </select>

</mapper>