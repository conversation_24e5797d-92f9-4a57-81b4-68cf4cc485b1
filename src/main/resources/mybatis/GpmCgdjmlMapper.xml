<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgdjmlMapper">

    <!-- 分页查询采购登记列表 -->
    <select id="getCgjgPageList" resultType="com.gg.grp.hkycg.model.vo.CgjgListVO">
        WITH PagedResults AS (
            SELECT
                COUNT(*) OVER() AS totalCount,
                t.JGDJBH as jgdjbh,
                t.JGD<PERSON> as jgdjmc,
                t.GSDM as gsdm,
                t.KJND as kjnd,
                t.ZT as zt,
                CASE
                    WHEN t.ZT = '1' THEN '保存'
                    WHEN t.ZT = '2' THEN '已提交'
                    WHEN t.ZT = '3' THEN '审核中'
                    WHEN t.ZT = '4' THEN '已审核'
                    WHEN t.ZT = '5' THEN '退回'
                    ELSE '未知状态'
                END as ztmc,
                CONVERT(varchar, t.CREATE_TIME, 120) as createTime,
                t.CGBMDM as cgbmdm,
                t.CGBMMC as cgbmmc,
                t.JBR as jbr,
                t.ZBFZR as zbfzr,
                t.CGFSDM as cgfsdm,
                t.CGFSMC as cgfsmc,
                t.ZJJGDM as zjjgdm,
                t.ZJJGMC as zjjgmc,
                (SELECT MAX(CASE WHEN nr.SFCGSQYR = '1' THEN 1 ELSE 0 END) FROM GPM_CGDJNR nr WHERE nr.JGDJBH = t.JGDJBH) as sfcgsqyr,
                (SELECT MAX(CASE WHEN nr.SFCGZBYR = '1' THEN 1 ELSE 0 END) FROM GPM_CGDJNR nr WHERE nr.JGDJBH = t.JGDJBH) as sfcgzbyr,
                STUFF((SELECT DISTINCT ',' + nr.CGSQBH FROM GPM_CGDJNR nr WHERE nr.JGDJBH = t.JGDJBH AND nr.CGSQBH IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as cgsqbh,
                STUFF((SELECT DISTINCT ',' + nr.CGSQMC FROM GPM_CGDJNR nr WHERE nr.JGDJBH = t.JGDJBH AND nr.CGSQMC IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as cgsqmc,
                STUFF((SELECT DISTINCT ',' + nr.CGZBBH FROM GPM_CGDJNR nr WHERE nr.JGDJBH = t.JGDJBH AND nr.CGZBBH IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as cgzbbh,
                STUFF((SELECT DISTINCT ',' + nr.CGZBMC FROM GPM_CGDJNR nr WHERE nr.JGDJBH = t.JGDJBH AND nr.CGZBMC IS NOT NULL FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, '') as cgzbmc,
                t.CGSQJE as cgsqje,
                t.YYJE as yyje,
                t.KYJE as kyje,
                t.JGDJJE as jgdjje,
                t.CGLY as cgly,
                t.BZ as bz,
                ROW_NUMBER() OVER (ORDER BY t.CREATE_TIME DESC) as row_num
            FROM GPM_CGDJML t
            LEFT JOIN PUBZYXX emp ON t.CREATE_USERDM = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
            WHERE t.GSDM = #{gsdm}
              AND t.KJND = #{kjnd}
              <!-- 数据权限控制 -->
              <choose>
                  <!-- 职员权限：只能查看自己的数据 -->
                  <when test="dataPermission == '9001'">
                      AND t.CREATE_USERDM = #{currEmployeeCode}
                  </when>
                  <!-- 部长权限：可以查看本部门的数据 -->
                  <when test="dataPermission == '9002'">
                      AND emp.BMDM = #{currDeptCode}
                  </when>
                  <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
                  <when test="dataPermission == '9003'">
                      <!-- 管理员可以查看所有数据 -->
                  </when>
                  <!-- 默认权限：按职员权限处理 -->
                  <otherwise>
                      AND t.CREATE_USERDM = #{currEmployeeCode}
                  </otherwise>
              </choose>
            <if test="condition != null and condition != ''">
                <![CDATA[ AND (t.JGDJMC LIKE '%' + #{condition} + '%' OR t.JBR LIKE '%' + #{condition} + '%') ]]>
            </if>
            <if test="zt != null and zt != ''">
                AND t.ZT = #{zt}
            </if>
            <if test="startDate != null and startDate != ''">
                AND t.CREATE_TIME &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND t.CREATE_TIME &lt;= #{endDate}
            </if>
        )
        SELECT *
        FROM PagedResults
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>
</mapper> 