<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgzjjgMapper">

    <!-- 分页查询采购中介机构列表 -->
    <select id="getCgzjjgPageList" resultType="com.gg.grp.hkycg.model.vo.CgzjjgListVO">
        WITH PagedResults AS (
            SELECT 
                cgzjjg.GSDM as gsdm,
                cgzjjg.KJND as kjnd,
                cgzjjg.ZJJGDM as zjjgdm,
                cgzjjg.ZJJGMC as zjjgmc,
                cgzjjg.SYZT as syzt,
                CASE 
                    WHEN cgzjjg.SYZT = '1' THEN '启用'
                    WHEN cgzjjg.SYZT = '0' THEN '停用'
                    ELSE '未知'
                E<PERSON> as syztmc,
                cgzjjg.ZJM as zjm,
                cgzjjg.BZ as bz,
                cgzjjg.PXH as pxh,
                cgzjjg.JC as jc,
                cgzjjg.SFHW as sfhw,
                CASE 
                    WHEN cgzjjg.SFHW = '1' THEN '是'
                    WHEN cgzjjg.SFHW = '0' THEN '否'
                    ELSE '未知'
                END as sfhwmc,
                cgzjjg.SFFW as sffw,
                CASE 
                    WHEN cgzjjg.SFFW = '1' THEN '是'
                    WHEN cgzjjg.SFFW = '0' THEN '否'
                    ELSE '未知'
                END as sffwmc,
                cgzjjg.SFGC as sfgc,
                CASE 
                    WHEN cgzjjg.SFGC = '1' THEN '是'
                    WHEN cgzjjg.SFGC = '0' THEN '否'
                    ELSE '未知'
                END as sfgcmc,
                cgzjjg.LXR as lxr,
                cgzjjg.LXDH as lxdh,
                COUNT(*) OVER() as totalCount,
                ROW_NUMBER() OVER(ORDER BY cgzjjg.ZJJGDM ASC , cgzjjg.PXH ASC ) as rn
            FROM GPM_CGZJJG cgzjjg
            WHERE cgzjjg.GSDM = #{query.gsdm}
                AND cgzjjg.KJND = #{query.kjnd}
                <if test="query.condition != null and query.condition != ''">
                    AND (
                        cgzjjg.ZJJGDM LIKE '%' + #{query.condition} + '%'
                        OR cgzjjg.ZJJGMC LIKE '%' + #{query.condition} + '%'
                        OR cgzjjg.ZJM LIKE '%' + #{query.condition} + '%'
                        OR cgzjjg.LXR LIKE '%' + #{query.condition} + '%'
                    )
                </if>
                <if test="query.syzt != null and query.syzt != ''">
                    AND cgzjjg.SYZT = #{query.syzt}
                </if>
                <if test="query.sfhw != null and query.sfhw != ''">
                    AND cgzjjg.SFHW = #{query.sfhw}
                </if>
                <if test="query.sffw != null and query.sffw != ''">
                    AND cgzjjg.SFFW = #{query.sffw}
                </if>
                <if test="query.sfgc != null and query.sfgc != ''">
                    AND cgzjjg.SFGC = #{query.sfgc}
                </if>
        )
        SELECT *
        FROM PagedResults
        WHERE rn BETWEEN (#{query.current} - 1) * #{query.size} + 1 AND #{query.current} * #{query.size}
        ORDER BY rn
    </select>

</mapper> 