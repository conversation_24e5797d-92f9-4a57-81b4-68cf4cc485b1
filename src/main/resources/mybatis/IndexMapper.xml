<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.IndexMapper">

    <!-- 完整列映射 -->
    <sql id="Swap_Column_List">
        zbxmb_OUT.ZBID AS indexID,
        zbxmb_OUT.GSDM AS corpCode,
        zbxmb_OUT.KJND AS accountantYear,
        zbxmb_OUT.ZBDM AS indexCode,
        zbxmb_OUT.ZBLB AS indexType,
        zbxmb_OUT.SJZBID AS realIndexCode,
        zbxmb_OUT.ZY AS note,
        zbxmb_OUT.YSFADM,
        zbxmb_OUT.YSFAMC,
        zbxmb_OUT.JFLXDM AS expenditureTypeCode,
        zbxmb_OUT.JFLXMC AS expenditureTypeName,
        zbxmb_OUT.WHDM,
        zbxmb_OUT.WHMC,
        zbxmb_OUT.ZBLYDM AS indexSourceCode,
        zbxmb_OUT.ZBLYMC AS indexSourceName,
        zbxmb_OUT.ZJLYDM,
        zbxmb_OUT.ZJLYMC,
        zbxmb_OUT.ZJXZDM,
        zbxmb_OUT.ZJXZMC,
        zbxmb_OUT.JSFSDM,
        zbxmb_OUT.JSFSMC,
        zbxmb_OUT.ZFFSDM,
        zbxmb_OUT.ZFFSMC,
        zbxmb_OUT.YSDWDM,
        zbxmb_OUT.YSDWMC,
        zbxmb_OUT.BMDM AS deptCode,
        zbxmb_OUT.BMMC AS deptName,
        zbxmb_OUT.ZYDM AS employeeCode,
        zbxmb_OUT.ZYMC AS employeeName,
        zbxmb_OUT.XMFLDM AS projectClassifyCode,
        zbxmb_OUT.XMFLMC AS projectClassifyName,
        zbxmb_OUT.XMDM AS projectCode,
        zbxmb_OUT.XMMC AS projectName,
        zbxmb_OUT.GNKMDM AS funcSubjectCode,
        zbxmb_OUT.GNKMMC AS funcSubjectName,
        zbxmb_OUT.JJKMDM AS economicSubjectCode,
        zbxmb_OUT.JJKMMC AS economicSubjectName,
        zbxmb_OUT.FZ6DM,
        zbxmb_OUT.FZ6MC,
        zbxmb_OUT.FZ7DM,
        zbxmb_OUT.FZ7MC,
        zbxmb_OUT.FZ8DM,
        zbxmb_OUT.FZ8MC,
        zbxmb_OUT.FZ9DM,
        zbxmb_OUT.FZ9MC,
        zbxmb_OUT.FZADM,
        zbxmb_OUT.FZAMC,
        zbxmb_OUT.ZTH,
        zbxmb_OUT.KMDM AS subjectCode,
        zbxmb_OUT.KMMC AS subjectName,
        zbxmb_OUT.ZT AS state,
        zbxmb_OUT.SFZFCG,
        zbxmb_OUT.JE AS amt,
        zbxmb_OUT.YSJE,
        zbxmb_OUT.NCYSY,
        zbxmb_OUT.DJJE,
        zbxmb_OUT.LRRID AS inputCode,
        zbxmb_OUT.LRR AS inputName,
        zbxmb_OUT.LRRQ AS inputDate,
        zbxmb_OUT.LRSJ AS inputTime,
        zbxmb_OUT.SHRID AS approverCode,
        zbxmb_OUT.SHR AS approverName,
        zbxmb_OUT.SHRQ AS approverDate,
        zbxmb_OUT.SHSJ AS approverTime,
        zbxmb_OUT.PFRID,
        zbxmb_OUT.PFR,
        zbxmb_OUT.PFRQ,
        zbxmb_OUT.PFSJ,
        zbxmb_OUT.CurSHJD,
        zbxmb_OUT.NextSHJD,
        zbxmb_OUT.FlowCode,
        zbxmb_OUT.YJBFB,
        zbxmb_OUT.CYSKZFS,
        zbxmb_OUT.SFJZ,
        zbxmb_OUT.module,
        zbxmb_OUT.BZ AS remarks,
        zbxmb_OUT.IDZBBH,
        zbxmb_OUT.XFZT,
        zbxmb_OUT.SJLY AS realSource,
        zbxmb_OUT.BYGKZ
    </sql>

    <!-- 分页查询预算指标 (使用Map参数，返回JSONObject) -->
    <select id="qryIndex" resultType="com.alibaba.fastjson.JSONObject">
        select top ${pageSize} *
        from (
        SELECT
        row_number() over (order by zbxmb_OUT.ZBID) as rownumber,
        count(1) over () as totalCount,
        --余额 等于 年初金额减去使用金额与在途金额
        (YE - ISNULL((select
        SUM(syje+ztje) AS SYJE
        from
        (
        select
        tmp.*,
        tmp.ncysy as ncysyje,
        tmp.qcje-tmp.ncysy + tmp.tzje-tmp.tjje + tmp.tjzje-tmp.tjjje-tmp.syje-tmp.ztje-SQJE as kyje,
        case
        when (tmp.zbje + tmp.ncysy)= 0 then 0
        else (tmp.ncysy + tmp.syje + SQJE )* 100 / (tmp.zbje + tmp.ncysy)
        end as zxbl
        from
        (
        select
        jh.zbid,
        jh.module,
        jh.zt,
        jh.YSJE,
        jh.je qcje,
        jh.ncysy,
        jh.djje,
        (jh.je-jh.ncysy + isnull(tzzb.tzje,
        0)+ isnull(tzzb2.tzje,
        0)-isnull(tzzb.tjje,
        0)-isnull(tzzb2.tjje,
        0)+ isnull(tjzb.tjzje,
        0)-isnull(tjzb.tjjje,
        0)) as zbje,
        isnull(syjh.syje,
        0) as syje,
        isnull(syjh2.bqzcje,
        0) as bqzcje,
        isnull(syjh.syje2,
        0) as ztje,
        isnull(tzzb.tzje,
        0)+ isnull(tzzb2.tzje,
        0) as tzje,
        isnull(tzzb.tjje,
        0)+ isnull(tzzb2.tjje,
        0) as tjje,
        isnull(tjzb.tjzje,
        0) as tjzje,
        isnull(tjzb.tjjje,
        0) as tjjje,
        isnull(JFSQ.SQJE,
        0) as SQJE
        from
        gbi_zbxmb jh
        left join (
        select
        zbid,
        sum(case when szsx = 1 then je else 0 end) as tzje,
        sum(case when szsx =-1 then je else 0 end) as tjje
        from
        gbi_tzdnr nr
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and exists(
        select
        tzdid
        from
        gbi_tzdml ml
        where
        ml.gsdm = nr.GSDM
        and ml.kjnd = nr.KJND
        and ml.TZRQ &lt;= #{endDate}
        and ml.tzdzt in ('1', '2')
        and ml.zblb = 'MXZB'
        and ml.TZDID = nr.TZDID
        and ml.DJLX = 'TZD')
        group by
        zbid) tzzb on
        tzzb.zbid = jh.zbid
        left join (
        select
        jhid,
        Sum(case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then djje else 0 end) tzje,
        Sum(case when (SZFX = '1' and JDBZ = '0') or (SZFX = '0' and JDBZ = '1') then djje else 0 end) tjje
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '1'
        and djzt in ('1', '2', '3')
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) tzzb2 on
        jh.zbid = tzzb2.jhid
        left join (
        select
        zbid,
        sum(case when szsx = 1 then je else 0 end) as tjzje,
        sum(case when szsx =-1 then je else 0 end) as tjjje
        from
        gbi_tzdnr nr
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and exists(
        select
        tzdid
        from
        gbi_tzdml ml
        where
        ml.gsdm = nr.GSDM
        and ml.kjnd = nr.KJND
        and ml.TZRQ &lt;= #{endDate}
        and ml.tzdzt in ('1', '2')
        and ml.zblb = 'MXZB'
        and ml.TZDID = nr.TZDID
        and ml.DJLX = 'TJD')
        group by
        zbid) tjzb on
        tjzb.zbid = jh.zbid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) syje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh on
        jh.zbid = syjh.jhid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) bqzcje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh2 on
        jh.zbid = syjh2.jhid
        left join (
        select
        SQ.*,
        SQJE0 as SQJE
        from
        (
        select
        GSDM,
        KJND,
        jhid as ZBID,
        ZBLB,
        SUM(DJJE) AS SQJE0
        from
        GBI_ZBSYRec rec
        where
        1 = 1
        and Module = 'GSP'
        and (DJZT in ('1', '2')
        or (YWDJID like 'GL_PZ_%'
        and DJZT in ('1', '2', '3')))
        and ZBLB = 'MXZB'
        GROUP BY
        GSDM,
        KJND,
        JHID,
        ZBLB) SQ) JFSQ on
        jh.ZBID = JFSQ.ZBID
        and jh.GSDM = JFSQ.GSDM
        and jh.KJND = JFSQ.KJND
        and jh.ZBLB = 'MXZB'
        where
        jh.gsdm = #{gsdm}
        and jh.kjnd = #{kjnd}
        and jh.zt = '3'
        and jh.zblb = 'MXZB') tmp) XMB
        where
        ZBID = zbxmb_OUT.ZBID
        ),
        0))
        AS residual,
        <include refid="Swap_Column_List"/>
        FROM gbi_zbxmb zbxmb_OUT
        WHERE GSDM = #{gsdm}
        and KJND = #{kjnd}
        and (ZBID IN (
        SELECT CAST(ZBID AS INT)
        FROM GPM_SQB
        WHERE GSDM = #{gsdm}
        and KJND = #{kjnd}
        and bsqren = #{employeeCode}
        )
        <if test="deptfzrdm!=null and deptfzrdm!=''">
            or ZBID IN(
            select
            zb.zbid AS ZBID
            FROM
            GBI_ZBXMB zb
            left join GPM_SQB AS cs on
            cs.gsdm = zb.gsdm
            and cs.kjnd = zb.kjnd
            and CAST(cs.zbid AS INT) = zb.zbid
            where
            cs.gsdm = #{gsdm}
            and cs.kjnd = #{kjnd}
            and cs.bsqren = #{deptfzrdm}
            and zb.zt = '3'
            and zb.zydm =#{deptfzrdm}
            and zb.zbid not in(
            SELECT
            CAST(ZBID AS INT)
            FROM
            GPM_SQB
            WHERE
            GSDM = #{gsdm}
            and KJND = #{kjnd}
            and bsqren = #{employeeCode}
            AND SYQX = '全部' )
            group by
            zb.zbid
            )
        </if>
        )
        AND zt = '3'
        AND MODULE = N'全部'
        <if test="condition !=null and condition !=''">
            and (
            ZBID like #{condition}
            or BMMC like #{condition}
            or XMMC like #{condition}
            or ZY like #{condition}
            or ZYMC like #{condition}
            )
        </if>
        ) temp_row
        where rownumber > #{pageSize} * ( #{pageNo} - 1 )

    </select>

    <!-- 批量插入预算指标到OER_YSZB表 -->
    <insert id="batchInsertOerYszb" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO OER_YSZB (
                GSDM, KJND, MLID, BNXID, DataID, XH, JE, ZY, 
                ZBGNKMDM, ZBGNKMMC, ZBJJKMDM, ZBJJKMMC, YSKJKMMC,
                ZBFZ7DM, ZBFZ7MC, ZBFZ8DM, ZBFZ8MC, ZBFZ9DM, ZBFZ9MC,
                 ZBDM, ZBID, DJBH
            ) VALUES (
                #{item.gsdm}, #{item.kjnd}, #{item.mlid}, #{item.bnxid}, #{item.dataId}, #{item.xh}, #{item.je}, #{item.zy},
                #{item.zbgnkmdm}, #{item.zbgnkmmc}, #{item.zbjjkmdm}, #{item.zbjjkmmc}, #{item.yskjkmmc},
                #{item.zbfz7dm}, #{item.zbfz7mc}, #{item.zbfz8dm}, #{item.zbfz8mc}, 
                #{item.zbfz9dm}, #{item.zbfz9mc},
                #{item.zbdm}, #{item.zbid}, #{item.djbh}
            )
        </foreach>
    </insert>

    <!-- 获取最大MLID -->
    <select id="selectMaxMlid" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(MLID), 0) FROM OER_YSZB
        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
    </select>
    
    <!-- 获取最大ZBID -->
    <select id="selectMaxZbid" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(ZBID), 0) FROM OER_YSZB
        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
    </select>
    
    <!-- 获取最大BNXID -->
    <select id="selectMaxBnxid" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(BNXID), 0) FROM OER_YSZB
        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
    </select>
    
    <!-- 获取最大DataID -->
    <select id="selectMaxDataId" resultType="java.lang.Integer">
        SELECT ISNULL(MAX(DataID), 0) FROM OER_YSZB
        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
    </select>

    <!-- 检查预算指标是否存在 -->
    <select id="checkBudgetIndexExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM gbi_zbxmb 
        WHERE GSDM = #{gsdm} 
        AND KJND = #{kjnd} 
        AND ZBID = #{zbid}
        AND zt = '3'
    </select>

    <!-- 根据ZBID查询预算指标（使用复杂residual逻辑） -->
    <select id="getIndexByZbid" resultType="com.gg.grp.hkycg.model.pojo.GbiZbxmb">
        SELECT
        --余额 等于 年初金额减去使用金额与在途金额
        (YE - ISNULL((select
        SUM(syje+ztje) AS SYJE
        from
        (
        select
        tmp.*,
        tmp.ncysy as ncysyje,
        tmp.qcje-tmp.ncysy + tmp.tzje-tmp.tjje + tmp.tjzje-tmp.tjjje-tmp.syje-tmp.ztje-SQJE as kyje,
        case
        when (tmp.zbje + tmp.ncysy)= 0 then 0
        else (tmp.ncysy + tmp.syje + SQJE )* 100 / (tmp.zbje + tmp.ncysy)
        end as zxbl
        from
        (
        select
        jh.zbid,
        jh.module,
        jh.zt,
        jh.YSJE,
        jh.je qcje,
        jh.ncysy,
        jh.djje,
        (jh.je-jh.ncysy + isnull(tzzb.tzje,
        0)+ isnull(tzzb2.tzje,
        0)-isnull(tzzb.tjje,
        0)-isnull(tzzb2.tjje,
        0)+ isnull(tjzb.tjzje,
        0)-isnull(tjzb.tjjje,
        0)) as zbje,
        isnull(syjh.syje,
        0) as syje,
        isnull(syjh2.bqzcje,
        0) as bqzcje,
        isnull(syjh.syje2,
        0) as ztje,
        isnull(tzzb.tzje,
        0)+ isnull(tzzb2.tzje,
        0) as tzje,
        isnull(tzzb.tjje,
        0)+ isnull(tzzb2.tjje,
        0) as tjje,
        isnull(tjzb.tjzje,
        0) as tjzje,
        isnull(tjzb.tjjje,
        0) as tjjje,
        isnull(JFSQ.SQJE,
        0) as SQJE
        from
        gbi_zbxmb jh
        left join (
        select
        zbid,
        sum(case when szsx = 1 then je else 0 end) as tzje,
        sum(case when szsx =-1 then je else 0 end) as tjje
        from
        gbi_tzdnr nr
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and exists(
        select
        tzdid
        from
        gbi_tzdml ml
        where
        ml.gsdm = nr.GSDM
        and ml.kjnd = nr.KJND
        and ml.TZRQ &lt;= #{endDate}
        and ml.tzdzt in ('1', '2')
        and ml.zblb = 'MXZB'
        and ml.TZDID = nr.TZDID
        and ml.DJLX = 'TZD')
        group by
        zbid) tzzb on
        tzzb.zbid = jh.zbid
        left join (
        select
        jhid,
        Sum(case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then djje else 0 end) tzje,
        Sum(case when (SZFX = '1' and JDBZ = '0') or (SZFX = '0' and JDBZ = '1') then djje else 0 end) tjje
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '1'
        and djzt in ('1', '2', '3')
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) tzzb2 on
        jh.zbid = tzzb2.jhid
        left join (
        select
        zbid,
        sum(case when szsx = 1 then je else 0 end) as tjzje,
        sum(case when szsx =-1 then je else 0 end) as tjjje
        from
        gbi_tzdnr nr
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and exists(
        select
        tzdid
        from
        gbi_tzdml ml
        where
        ml.gsdm = nr.GSDM
        and ml.kjnd = nr.KJND
        and ml.TZRQ &lt;= #{endDate}
        and ml.tzdzt in ('1', '2')
        and ml.zblb = 'MXZB'
        and ml.TZDID = nr.TZDID
        and ml.DJLX = 'TJD')
        group by
        zbid) tjzb on
        tjzb.zbid = jh.zbid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) syje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh on
        jh.zbid = syjh.jhid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) bqzcje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh2 on
        jh.zbid = syjh2.jhid
        left join (
        select
        SQ.*,
        SQJE0 as SQJE
        from
        (
        select
        GSDM,
        KJND,
        jhid as ZBID,
        ZBLB,
        SUM(DJJE) AS SQJE0
        from
        GBI_ZBSYRec rec
        where
        1 = 1
        and Module = 'GSP'
        and (DJZT in ('1', '2')
        or (YWDJID like 'GL_PZ_%'
        and DJZT in ('1', '2', '3')))
        and ZBLB = 'MXZB'
        GROUP BY
        GSDM,
        KJND,
        JHID,
        ZBLB) SQ) JFSQ on
        jh.ZBID = JFSQ.ZBID
        and jh.GSDM = JFSQ.GSDM
        and jh.KJND = JFSQ.KJND
        and jh.ZBLB = 'MXZB'
        where
        jh.gsdm = #{gsdm}
        and jh.kjnd = #{kjnd}
        and jh.zt = '3'
        and jh.zblb = 'MXZB') tmp) XMB
        where
        ZBID = #{zbid}
        ),
        0))
        AS residual,
        ye as qcje,
        zbxmb.ZBID AS indexID,
        zbxmb.GSDM AS corpCode,
        zbxmb.KJND AS accountantYear,
        zbxmb.ZBDM AS indexCode,
        zbxmb.ZBLB AS indexType,
        zbxmb.SJZBID AS realIndexCode,
        zbxmb.ZY AS note,
        zbxmb.YSFADM,
        zbxmb.YSFAMC,
        zbxmb.JFLXDM AS expenditureTypeCode,
        zbxmb.JFLXMC AS expenditureTypeName,
        zbxmb.WHDM,
        zbxmb.WHMC,
        zbxmb.ZBLYDM AS indexSourceCode,
        zbxmb.ZBLYMC AS indexSourceName,
        zbxmb.ZJLYDM,
        zbxmb.ZJLYMC,
        zbxmb.ZJXZDM,
        zbxmb.ZJXZMC,
        zbxmb.JSFSDM,
        zbxmb.JSFSMC,
        zbxmb.ZFFSDM,
        zbxmb.ZFFSMC,
        zbxmb.YSDWDM,
        zbxmb.YSDWMC,
        zbxmb.BMDM AS deptCode,
        zbxmb.BMMC AS deptName,
        zbxmb.ZYDM AS employeeCode,
        zbxmb.ZYMC AS employeeName,
        zbxmb.XMFLDM AS projectClassifyCode,
        zbxmb.XMFLMC AS projectClassifyName,
        zbxmb.XMDM AS projectCode,
        zbxmb.XMMC AS projectName,
        zbxmb.GNKMDM AS funcSubjectCode,
        zbxmb.GNKMMC AS funcSubjectName,
        zbxmb.JJKMDM AS economicSubjectCode,
        zbxmb.JJKMMC AS economicSubjectName,
        zbxmb.FZ6DM,
        zbxmb.FZ6MC,
        zbxmb.FZ7DM,
        zbxmb.FZ7MC,
        zbxmb.FZ8DM,
        zbxmb.FZ8MC,
        zbxmb.FZ9DM,
        zbxmb.FZ9MC,
        zbxmb.FZADM,
        zbxmb.FZAMC,
        zbxmb.ZTH,
        zbxmb.KMDM AS subjectCode,
        zbxmb.KMMC AS subjectName,
        zbxmb.ZT AS state,
        zbxmb.SFZFCG,
        zbxmb.JE AS amt,
        zbxmb.YSJE,
        zbxmb.NCYSY,
        zbxmb.DJJE,
        zbxmb.LRRID AS inputCode,
        zbxmb.LRR AS inputName,
        zbxmb.LRRQ AS inputDate,
        zbxmb.LRSJ AS inputTime,
        zbxmb.SHRID AS approverCode,
        zbxmb.SHR AS approverName,
        zbxmb.SHRQ AS approverDate,
        zbxmb.SHSJ AS approverTime,
        zbxmb.PFRID,
        zbxmb.PFR,
        zbxmb.PFRQ,
        zbxmb.PFSJ,
        zbxmb.CurSHJD,
        zbxmb.NextSHJD,
        zbxmb.FlowCode,
        zbxmb.YJBFB,
        zbxmb.CYSKZFS,
        zbxmb.SFJZ,
        zbxmb.module,
        zbxmb.BZ AS remarks,
        zbxmb.IDZBBH,
        zbxmb.XFZT,
        zbxmb.SJLY AS realSource,
        zbxmb.BYGKZ
        FROM gbi_zbxmb zbxmb
        WHERE GSDM = #{gsdm}
        AND KJND = #{kjnd}
        AND ZBID = #{zbid}
        AND zt = '3'
        AND MODULE = N'全部'
    </select>

    <!-- 根据DJBH查询采购计划的占用金额 -->
    <select id="getCgjhOccupiedAmount" resultType="java.math.BigDecimal">
        SELECT ISNULL(SUM(djje), 0) 
        FROM gbi_zbsyrec zbsyrec
        WHERE zbsyrec.gsdm = #{gsdm}
        AND zbsyrec.kjnd = #{kjnd}
        AND zbsyrec.module = 'GPM'
        AND zbsyrec.djlx = 'CGJH'
        AND zbsyrec.djid = #{djbh}
        AND zbsyrec.jhid = #{zbid}
        AND zbsyrec.djzt in ('1', '2', '3')
    </select>

    <!-- 根据DJBH查询采购计划预算指标余额（通过djbh关联占用记录） -->
    <select id="getCgjhBudgetIndexByDjbh" resultType="com.gg.grp.hkycg.model.pojo.GbiZbxmb">
        SELECT
        -- 使用原有的复杂余额计算，但排除当前采购计划的占用记录
        (YE - ISNULL((select
        SUM(syje+ztje) AS SYJE
        from
        (
        select
        tmp.*,
        tmp.ncysy as ncysyje,
        tmp.qcje-tmp.ncysy + tmp.tzje-tmp.tjje-tmp.syje-tmp.ztje-SQJE as kyje,
        case
        when (tmp.zbje + tmp.ncysy)= 0 then 0
        else (tmp.ncysy + tmp.syje + SQJE )* 100 / (tmp.zbje + tmp.ncysy)
        end as zxbl
        from
        (
        select
        jh.zbid,
        jh.module,
        jh.zt,
        jh.YSJE,
        jh.je qcje,
        jh.ncysy,
        jh.djje,
        (jh.je-jh.ncysy + isnull(tzzb2.tzje,
        0)-isnull(tzzb2.tjje,
        0)) as zbje,
        isnull(syjh.syje,
        0) as syje,
        isnull(syjh2.bqzcje,
        0) as bqzcje,
        isnull(syjh.syje2,
        0) as ztje,
        isnull(tzzb2.tzje,
        0) as tzje,
        isnull(tzzb2.tjje,
        0) as tjje,
        isnull(JFSQ.SQJE,
        0) as SQJE
        from
        gbi_zbxmb jh
        left join (
        select
        jhid,
        Sum(case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then djje else 0 end) tzje,
        Sum(case when (SZFX = '1' and JDBZ = '0') or (SZFX = '0' and JDBZ = '1') then djje else 0 end) tjje
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '1'
        and djzt in ('1', '2', '3')
        and DJYWRQ &lt;= #{endDate}
        -- 排除当前采购计划的记录
        and not (module = 'GPM' and djlx = 'CGJH' and djid = #{djbh})
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) tzzb2 on
        jh.zbid = tzzb2.jhid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) syje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        -- 排除当前采购计划的记录
        and not (module = 'GPM' and djlx = 'CGJH' and djid = #{djbh})
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh on
        jh.zbid = syjh.jhid
        left join (
        select
        jhid,
        Sum(case when (1 = 1 and ((module = 'GL' and DJLX = 'PZ'))) then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) bqzcje,
        Sum(case when (1 = 1 and not (1 = 1 and ((module = 'GL' and DJLX = 'PZ')))) and Module != 'GSP' then (case when (SZFX = '1' and JDBZ = '1') or (SZFX = '0' and JDBZ = '0') then -1 * djje else djje end) else 0 end) as syje2
        from
        gbi_zbsyrec
        where
        gsdm = #{gsdm}
        and kjnd = #{kjnd}
        and CZLX = '0'
        and (ZZBZ is null
        or ZZBZ = '0')
        and ((module != 'GL'
        and djzt in ('1', '2', '3'))
        or (module = 'GL'
        and djzt in ('1', '2', '3')))
        and DJYWRQ &lt;= #{endDate}
        -- 排除当前采购计划的记录
        and not (module = 'GPM' and djlx = 'CGJH' and djid = #{djbh})
        AND ( EXISTS(
        SELECT
        *
        FROM
        GBI_ZBSYREC ybxd
        WHERE
        ( 1 = 1
        AND YBXD.GSDM = GBI_ZBSYREC.GSDM
        AND YBXD.KJND = GBI_ZBSYREC.KJND)
        AND YBXD.DJZT in ('1', '2', '3')
        AND ybxd.MODULE = 'OER'
        AND ybxd.DJFLMX LIKE '-%' )
        OR NOT ( MODULE = 'OER'
        AND DJFLMX LIKE '-%'))
        group by
        jhid) syjh2 on
        jh.zbid = syjh2.jhid
        left join (
        select
        SQ.*,
        SQJE0 as SQJE
        from
        (
        select
        GSDM,
        KJND,
        jhid as ZBID,
        ZBLB,
        SUM(DJJE) AS SQJE0
        from
        GBI_ZBSYRec rec
        where
        1 = 1
        and Module = 'GSP'
        and (DJZT in ('1', '2')
        or (YWDJID like 'GL_PZ_%'
        and DJZT in ('1', '2', '3')))
        and ZBLB = 'MXZB'
        -- 排除当前采购计划的记录
        and not (module = 'GPM' and djlx = 'CGJH' and djid = #{djbh})
        GROUP BY
        GSDM,
        KJND,
        JHID,
        ZBLB) SQ) JFSQ on
        jh.ZBID = JFSQ.ZBID
        and jh.GSDM = JFSQ.GSDM
        and jh.KJND = JFSQ.KJND
        and jh.ZBLB = 'MXZB'
        where
        jh.gsdm = #{gsdm}
        and jh.kjnd = #{kjnd}
        and jh.zt = '3'
        and jh.zblb = 'MXZB') tmp) XMB
        where
        ZBID = #{zbid}
        ),
        0))
        AS residual,
        ye as qcje,
        zbxmb.ZBID AS indexID,
        zbxmb.GSDM AS corpCode,
        zbxmb.KJND AS accountantYear,
        zbxmb.ZBDM AS indexCode,
        zbxmb.ZBLB AS indexType,
        zbxmb.SJZBID AS realIndexCode,
        zbxmb.ZY AS note,
        zbxmb.YSFADM,
        zbxmb.YSFAMC,
        zbxmb.JFLXDM AS expenditureTypeCode,
        zbxmb.JFLXMC AS expenditureTypeName,
        zbxmb.WHDM,
        zbxmb.WHMC,
        zbxmb.ZBLYDM AS indexSourceCode,
        zbxmb.ZBLYMC AS indexSourceName,
        zbxmb.ZJLYDM,
        zbxmb.ZJLYMC,
        zbxmb.ZJXZDM,
        zbxmb.ZJXZMC,
        zbxmb.JSFSDM,
        zbxmb.JSFSMC,
        zbxmb.ZFFSDM,
        zbxmb.ZFFSMC,
        zbxmb.YSDWDM,
        zbxmb.YSDWMC,
        zbxmb.BMDM AS deptCode,
        zbxmb.BMMC AS deptName,
        zbxmb.ZYDM AS employeeCode,
        zbxmb.ZYMC AS employeeName,
        zbxmb.XMFLDM AS projectClassifyCode,
        zbxmb.XMFLMC AS projectClassifyName,
        zbxmb.XMDM AS projectCode,
        zbxmb.XMMC AS projectName,
        zbxmb.GNKMDM AS funcSubjectCode,
        zbxmb.GNKMMC AS funcSubjectName,
        zbxmb.JJKMDM AS economicSubjectCode,
        zbxmb.JJKMMC AS economicSubjectName,
        zbxmb.FZ6DM,
        zbxmb.FZ6MC,
        zbxmb.FZ7DM,
        zbxmb.FZ7MC,
        zbxmb.FZ8DM,
        zbxmb.FZ8MC,
        zbxmb.FZ9DM,
        zbxmb.FZ9MC,
        zbxmb.FZADM,
        zbxmb.FZAMC,
        zbxmb.ZTH,
        zbxmb.KMDM AS subjectCode,
        zbxmb.KMMC AS subjectName,
        zbxmb.ZT AS state,
        zbxmb.SFZFCG,
        zbxmb.JE AS amt,
        zbxmb.YSJE,
        zbxmb.NCYSY,
        zbxmb.DJJE,
        zbxmb.LRRID AS inputCode,
        zbxmb.LRR AS inputName,
        zbxmb.LRRQ AS inputDate,
        zbxmb.LRSJ AS inputTime,
        zbxmb.SHRID AS approverCode,
        zbxmb.SHR AS approverName,
        zbxmb.SHRQ AS approverDate,
        zbxmb.SHSJ AS approverTime,
        zbxmb.PFRID,
        zbxmb.PFR,
        zbxmb.PFRQ,
        zbxmb.PFSJ,
        zbxmb.CurSHJD,
        zbxmb.NextSHJD,
        zbxmb.FlowCode,
        zbxmb.YJBFB,
        zbxmb.CYSKZFS,
        zbxmb.SFJZ,
        zbxmb.module,
        zbxmb.BZ AS remarks,
        zbxmb.IDZBBH,
        zbxmb.XFZT,
        zbxmb.SJLY AS realSource,
        zbxmb.BYGKZ
        FROM gbi_zbxmb zbxmb
        WHERE GSDM = #{gsdm}
        AND KJND = #{kjnd}
        AND ZBID = #{zbid}
        AND zt = '3'
        AND MODULE = N'全部'
    </select>

</mapper> 