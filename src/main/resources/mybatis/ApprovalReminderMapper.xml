<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.ApprovalReminderMapper">

    <!-- 查询需要催办的单据 -->
    <select id="queryRemindableApprovals" resultType="com.gg.grp.hkycg.model.vo.ApprovalReminderVO">
        WITH PendingApprovals AS (
            -- 采购计划
            SELECT DISTINCT
                pof.node_name AS nodeName,
                '采购计划' AS billTypeName,
                ml.JHMC AS billName,
                ml.JHBH AS billNo,
                ml.LR_RQ AS createTime,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND ATYPE = '送审' ORDER BY ADATETIME DESC) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                ml.BMMC AS createDept,
                ml.JBR AS creator,
                pof.SHRMC AS nextAuditor,
                ml.JHJE AS amount,
                'CGJH' AS billType,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND NODESEQ = (CONVERT(INT, pof.AUDIT_FLAG) - 1) 
                 ORDER BY ADATETIME DESC) AS lastAuditTime
            FROM (
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER(PARTITION BY DJH ORDER BY AUDIT_FLAG) as rn
                    FROM PUB_OBJ_FLOW 
                    WHERE GSDM = #{gsdm}
                      AND KJND = #{kjnd}
                      AND ISAUDIT = '0'
                      AND DJLX = '${@<EMAIL>()}'
                      AND DJH IN (
                        SELECT JHBH FROM GPM_CGJHML
                        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
                        AND (ZT = '2' OR ZT = '3')
                      )
                ) ranked WHERE rn = 1
            ) pof
            JOIN GPM_CGJHML ml ON pof.DJH = ml.JHBH AND pof.GSDM = ml.GSDM AND pof.KJND = ml.KJND
            WHERE (ml.ZT = '2' OR ml.ZT = '3')
            
            UNION ALL
            
            -- 采购申请
            SELECT DISTINCT
                pof.node_name AS nodeName,
                '采购申请' AS billTypeName,
                ml.SQMC AS billName,
                ml.SQBH AS billNo,
                ml.CJRQ AS createTime,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND ATYPE = '送审' ORDER BY ADATETIME DESC) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                ml.BMMC AS createDept,
                ml.JBR AS creator,
                pof.SHRMC AS nextAuditor,
                ml.SQJE AS amount,
                'CGSQ' AS billType,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND NODESEQ = (CONVERT(INT, pof.AUDIT_FLAG) - 1) 
                 ORDER BY ADATETIME DESC) AS lastAuditTime
            FROM (
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER(PARTITION BY DJH ORDER BY AUDIT_FLAG) as rn
                    FROM PUB_OBJ_FLOW 
                    WHERE GSDM = #{gsdm}
                      AND KJND = #{kjnd}
                      AND ISAUDIT = '0'
                       AND DJLX = '${@<EMAIL>()}'
                      AND DJH IN (
                        SELECT SQBH FROM GPM_CGSQML
                        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
                        AND (ZT = '2' OR ZT = '3')
                      )
                ) ranked WHERE rn = 1
            ) pof
            JOIN GPM_CGSQML ml ON pof.DJH = ml.SQBH AND pof.GSDM = ml.GSDM AND pof.KJND = ml.KJND
            WHERE (ml.ZT = '2' OR ml.ZT = '3')
            
            UNION ALL
            
            -- 采购招标
            SELECT DISTINCT
                pof.node_name AS nodeName,
                '采购招标' AS billTypeName,
                ml.ZBSQMC AS billName,
                ml.ZBSQBH AS billNo,
                ml.CJSJ AS createTime,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND ATYPE = '送审' ORDER BY ADATETIME DESC) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                ml.ZBSQBMMC AS createDept,
                ml.JBR AS creator,
                pof.SHRMC AS nextAuditor,
                ml.CGSQJE AS amount,
                'CGZB' AS billType,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND NODESEQ = (CONVERT(INT, pof.AUDIT_FLAG) - 1) 
                 ORDER BY ADATETIME DESC) AS lastAuditTime
            FROM (
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER(PARTITION BY DJH ORDER BY AUDIT_FLAG) as rn
                    FROM PUB_OBJ_FLOW 
                    WHERE GSDM = #{gsdm}
                      AND KJND = #{kjnd}
                      AND ISAUDIT = '0'
                       AND DJLX = '${@<EMAIL>()}'
                      AND DJH IN (
                        SELECT ZBSQBH FROM GPM_CGZBSQML
                        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
                        AND (ZT = '2' OR ZT = '3')
                      )
                ) ranked WHERE rn = 1
            ) pof
            JOIN GPM_CGZBSQML ml ON pof.DJH = ml.ZBSQBH AND pof.GSDM = ml.GSDM AND pof.KJND = ml.KJND
            WHERE (ml.ZT = '2' OR ml.ZT = '3')
            
            UNION ALL
            
            -- 采购登记
            SELECT DISTINCT
                pof.node_name AS nodeName,
                '采购登记' AS billTypeName,
                ml.JGDJMC AS billName,
                ml.JGDJBH AS billNo,
                ml.CREATE_TIME AS createTime,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND ATYPE = '送审' ORDER BY ADATETIME DESC) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                ml.CGBMMC AS createDept,
                ml.JBR AS creator,
                pof.SHRMC AS nextAuditor,
                ml.JGDJJE AS amount,
                'CGDJ' AS billType,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND NODESEQ = (CONVERT(INT, pof.AUDIT_FLAG) - 1) 
                 ORDER BY ADATETIME DESC) AS lastAuditTime
            FROM (
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER(PARTITION BY DJH ORDER BY AUDIT_FLAG) as rn
                    FROM PUB_OBJ_FLOW 
                    WHERE GSDM = #{gsdm}
                      AND KJND = #{kjnd}
                      AND ISAUDIT = '0'
                       AND DJLX = '${@<EMAIL>()}'
                      AND DJH IN (
                        SELECT JGDJBH FROM GPM_CGDJML
                        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
                        AND (ZT = '2' OR ZT = '3')
                      )
                ) ranked WHERE rn = 1
            ) pof
            JOIN GPM_CGDJML ml ON pof.DJH = ml.JGDJBH AND pof.GSDM = ml.GSDM AND pof.KJND = ml.KJND
            WHERE (ml.ZT = '2' OR ml.ZT = '3')
            
            UNION ALL
            
            -- 采购验收
            SELECT DISTINCT
                pof.node_name AS nodeName,
                '采购验收' AS billTypeName,
                ml.YSMC AS billName,
                ml.YSBH AS billNo,
                ml.CREATE_TIME AS createTime,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND ATYPE = '送审' ORDER BY ADATETIME DESC) AS submitTime,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                ml.YSBMMC AS createDept,
                ml.JBR AS creator,
                pof.SHRMC AS nextAuditor,
                ml.BCYSJE AS amount,
                'CGYS' AS billType,
                (SELECT TOP 1 ServDateTime FROM PUBAUDITLOG 
                 WHERE GSDM = pof.GSDM AND KJND = pof.KJND AND BILLID = pof.DJH 
                 AND NODESEQ = (CONVERT(INT, pof.AUDIT_FLAG) - 1) 
                 ORDER BY ADATETIME DESC) AS lastAuditTime
            FROM (
                SELECT * FROM (
                    SELECT *, ROW_NUMBER() OVER(PARTITION BY DJH ORDER BY AUDIT_FLAG) as rn
                    FROM PUB_OBJ_FLOW 
                    WHERE GSDM = #{gsdm}
                      AND KJND = #{kjnd}
                      AND ISAUDIT = '0'
                      AND DJLX = '${@<EMAIL>()}'
                      AND DJH IN (
                        SELECT YSBH FROM GPM_CGYSML
                        WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
                        AND (ZT = '2' OR ZT = '3')
                      )
                ) ranked WHERE rn = 1
            ) pof
            JOIN GPM_CGYSML ml ON pof.DJH = ml.YSBH AND pof.GSDM = ml.GSDM AND pof.KJND = ml.KJND
            WHERE (ml.ZT = '2' OR ml.ZT = '3')
        ),
        FilteredApprovals AS (
            SELECT 
                a.*,
                ROW_NUMBER() OVER(ORDER BY a.submitTime DESC) AS row_num,
                COUNT(*) OVER() AS total
            FROM PendingApprovals a
            WHERE 1=1
            -- 根据showReminded参数过滤单据
            <if test="showReminded == null or showReminded == false">
                -- 只显示未催办过的单据
                AND NOT EXISTS (
                    SELECT 1 FROM GPM_REMINDER r 
                    WHERE r.GSDM = #{gsdm}
                    AND r.KJND = #{kjnd}
                    AND r.DJBH = a.billNo 
                    AND r.DJLX = a.billType
                    AND r.SHJD = a.nodeName
                )
            </if>
            <if test="showReminded != null and showReminded == true">
                -- 只显示已催办过的单据
                AND EXISTS (
                    SELECT 1 FROM GPM_REMINDER r 
                    WHERE r.GSDM = #{gsdm}
                    AND r.KJND = #{kjnd}
                    AND r.DJBH = a.billNo 
                    AND r.DJLX = a.billType
                    AND r.SHJD = a.nodeName
                )
            </if>
            <if test="billNo != null and billNo != ''">
                AND a.billNo LIKE '%' + #{billNo} + '%'
            </if>
            <if test="billType != null and billType != ''">
                AND a.billType = #{billType}
            </if>
            <if test="status != null and status != ''">
                AND a.statusCode = #{status}
            </if>
            <if test="nodeName != null and nodeName != ''">
                AND a.nodeName LIKE '%' + #{nodeName} + '%'
            </if>
            <if test="startDate != null">
                AND a.submitTime &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND a.submitTime &lt;= #{endDate}
            </if>
            <if test="stagnationStartTime != null">
                AND a.lastAuditTime &gt;= #{stagnationStartTime} OR #{stagnationStartTime} &gt;= a.submitTime
            </if>
        )
        SELECT * FROM FilteredApprovals
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>
    
    <resultMap id="reminderVO" type="com.gg.grp.hkycg.model.pojo.ApprovalReminder">
        <id property="grid" column="GRID"/>
        <result property="gsdm" column="GSDM"/>
        <result property="kjnd" column="KJND"/>
        <result property="djbh" column="DJBH"/>
        <result property="djlx" column="DJLX"/>
        <result property="reminderTime" column="CBSJ"/>
        <result property="reminderUserDm" column="CBRDM"/>
        <result property="reminderUser" column="CBR"/>
        <result property="receiverDm" column="BCBRDM"/>
        <result property="receiver" column="BCBR"/>
        <result property="reminderContent" column="CBNR"/>
        <result property="reminderResult" column="CBZT"/>
        <result property="nodeName" column="SHJD"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="cfcs" column="CFCS"/>
    </resultMap>
    
    <!-- 查询单据的最后一次催办记录 -->
    <select id="getLastReminderByBill" resultMap="reminderVO">
        SELECT TOP 1 * FROM GPM_REMINDER
        WHERE GSDM = #{gsdm}
          AND KJND = #{kjnd}
          AND DJBH = #{djbh}
          AND DJLX = #{djlx}
          AND SHJD = #{nodeName}
        ORDER BY CREATE_TIME DESC
    </select>
    
    <!-- 查询当前登录人被催办的单据 -->
    <select id="queryMyReminders" resultType="com.gg.grp.hkycg.model.vo.MyReminderVO">
        WITH ReminderWithBillInfo AS (
            SELECT 
                r.GRID,
                r.DJBH AS billNo,
                r.DJLX AS billType,
                CASE r.DJLX
                    WHEN 'CGJH' THEN '采购计划'
                    WHEN 'CGSQ' THEN '采购申请'
                    WHEN 'CGZB' THEN '采购招标'
                    WHEN 'CGDJ' THEN '采购登记'
                    WHEN 'CGYS' THEN '采购验收'
                    ELSE '未知'
                END AS billTypeName,
                r.CREATE_TIME AS reminderTime,
                r.CBR AS reminderUser,
                r.CBNR AS reminderContent,
                r.CBZT AS reminderResult,
                r.SHJD AS nodeName,
                r.CFCS AS CFCS,
                
                -- 根据单据类型查询单据名称和状态
                CASE r.DJLX
                    WHEN 'CGJH' THEN (SELECT JHMC FROM GPM_CGJHML WHERE JHBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGSQ' THEN (SELECT SQMC FROM GPM_CGSQML WHERE SQBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGZB' THEN (SELECT ZBSQMC FROM GPM_CGZBSQML WHERE ZBSQBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGDJ' THEN (SELECT JGDJMC FROM GPM_CGDJML WHERE JGDJBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGYS' THEN (SELECT YSMC FROM GPM_CGYSML WHERE YSBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    ELSE '未知'
                END AS billName,
                
                -- 查询单据状态编码
                CASE r.DJLX
                    WHEN 'CGJH' THEN (SELECT ZT FROM GPM_CGJHML WHERE JHBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGSQ' THEN (SELECT ZT FROM GPM_CGSQML WHERE SQBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGZB' THEN (SELECT ZT FROM GPM_CGZBSQML WHERE ZBSQBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGDJ' THEN (SELECT ZT FROM GPM_CGDJML WHERE JGDJBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    WHEN 'CGYS' THEN (SELECT ZT FROM GPM_CGYSML WHERE YSBH = r.DJBH AND GSDM = r.GSDM AND KJND = r.KJND)
                    ELSE '0'
                END AS statusCode,
                
                -- 添加行号，按单据号和单据类型分组，按催办时间倒序排列
                ROW_NUMBER() OVER(PARTITION BY r.DJBH, r.DJLX ORDER BY r.CREATE_TIME DESC) as bill_rn
            FROM GPM_REMINDER r
            WHERE r.GSDM = #{gsdm}
              AND r.KJND = #{kjnd}
              AND r.BCBRDM = #{receiverDm}
        ),
        LatestReminderPerBill AS (
            SELECT *
            FROM ReminderWithBillInfo
            WHERE bill_rn = 1  -- 只保留每个单据的最新催办记录
        ),
        ReminderWithStatusName AS (
            SELECT 
                r.*,
                CASE 
                    WHEN r.statusCode = '1' THEN '已保存'
                    WHEN r.statusCode = '2' THEN '已提交'
                    WHEN r.statusCode = '3' THEN '审核中'
                    WHEN r.statusCode = '4' THEN '已审核'
                    WHEN r.statusCode = '5' THEN '已退回'
                    ELSE '未知'
                END AS statusName
            FROM LatestReminderPerBill r
        ),
        FilteredReminders AS (
            SELECT 
                r.*,
                ROW_NUMBER() OVER(ORDER BY r.reminderTime DESC) AS row_num,
                COUNT(*) OVER() AS totalCount
            FROM ReminderWithStatusName r
            WHERE 1=1
            <if test="billNo != null and billNo != ''">
                AND r.billNo LIKE '%' + #{billNo} + '%'
            </if>
            <if test="billType != null and billType != ''">
                AND r.billType = #{billType}
            </if>
            <if test="reminderResult != null and reminderResult != ''">
                AND r.reminderResult = #{reminderResult}
            </if>
            <if test="startDate != null">
                AND r.reminderTime &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND r.reminderTime &lt;= #{endDate}
            </if>
        )
        SELECT 
            billNo, billType, billTypeName, billName, 
            statusCode, statusName, reminderUser, reminderTime,
            reminderContent, reminderResult, nodeName, totalCount,
            CFCS as cfcs
        FROM FilteredReminders
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询离截止日期指定天数内的所有单据 -->
    <select id="queryBillsNearDeadline" resultType="com.gg.grp.hkycg.model.vo.BusinessReminderVO">
        WITH AllBills AS (
            -- 采购计划
            SELECT
                '采购计划' AS billTypeName,
                ml.JHMC AS billName,
                ml.JHBH AS billNo,
                ml.ZT AS statusCode,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                    END AS statusName,
                'CGJH' AS billType,
                DATEADD(DAY, #{days}, GETDATE()) AS deadlineDate,
                ml.LR_RQ AS createTime
            FROM GPM_CGJHML ml
            WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd}
              AND ml.ZT != '4' -- 不显示已审核的单据
            AND ml.LRR_ID = #{employeeCode} -- 只查询当前登录人创建的单据

        UNION ALL

        -- 采购申请
        SELECT
            '采购申请' AS billTypeName,
            ml.SQMC AS billName,
            ml.SQBH AS billNo,
            ml.ZT AS statusCode,
            CASE ml.ZT
                WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
            'CGSQ' AS billType,
            DATEADD(DAY, #{days}, GETDATE()) AS deadlineDate,
            ml.CJRQ AS createTime
        FROM GPM_CGSQML ml
        WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd}
          AND ml.ZT != '4' -- 不显示已审核的单据
            AND ml.CJRDM = #{employeeCode} -- 只查询当前登录人创建的单据

        UNION ALL

        -- 采购招标
        SELECT
            '采购招标' AS billTypeName,
            ml.ZBSQMC AS billName,
            ml.ZBSQBH AS billNo,
            ml.ZT AS statusCode,
            CASE ml.ZT
                WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
            'CGZB' AS billType,
            DATEADD(DAY, #{days}, GETDATE()) AS deadlineDate,
            ml.CJSJ AS createTime
        FROM GPM_CGZBSQML ml
        WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd}
          AND ml.ZT != '4' -- 不显示已审核的单据
            AND ml.CJRDM = #{employeeCode} -- 只查询当前登录人创建的单据

        UNION ALL

        -- 采购登记
        SELECT
            '采购登记' AS billTypeName,
            ml.JGDJMC AS billName,
            ml.JGDJBH AS billNo,
            ml.ZT AS statusCode,
            CASE ml.ZT
                WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
            'CGDJ' AS billType,
            DATEADD(DAY, #{days}, GETDATE()) AS deadlineDate,
            ml.CREATE_TIME AS createTime
        FROM GPM_CGDJML ml
        WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd}
          AND ml.ZT != '4' -- 不显示已审核的单据
            AND ml.CREATE_USERDM = #{employeeCode} -- 只查询当前登录人创建的单据

        UNION ALL

        -- 采购验收
        SELECT
            '采购验收' AS billTypeName,
            ml.YSMC AS billName,
            ml.YSBH AS billNo,
            ml.ZT AS statusCode,
            CASE ml.ZT
                WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
            'CGYS' AS billType,
            DATEADD(DAY, #{days}, GETDATE()) AS deadlineDate,
            ml.CREATE_TIME AS createTime
        FROM GPM_CGYSML ml
        WHERE ml.GSDM = #{gsdm} AND ml.KJND = #{kjnd}
          AND ml.ZT != '4' -- 不显示已审核的单据
            AND ml.CREATE_USERDM = #{employeeCode} -- 只查询当前登录人创建的单据
            )

        SELECT
            billNo, billName, billType, billTypeName, statusCode, statusName
        FROM AllBills
        WHERE createTime &lt;= deadlineDate
        ORDER BY createTime DESC
    </select>

    <!-- 查询已审核状态的采购计划 -->
    <select id="queryApprovedPlans" resultType="com.gg.grp.hkycg.model.vo.ApprovedPlanVO">
        SELECT * FROM (
            SELECT
                CONVERT(VARCHAR(10), ml.NCGRQ, 120) AS purchaseDeadline,
                'CGJH' AS billType,
                ml.JHBH AS planNo,
                ml.JHMC AS planName,
                CONVERT(VARCHAR(10), ml.LR_RQ, 120) AS createDate,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS billStatus,
                ml.BMMC AS planDept,
                ml.JBR AS handler,
                ml.SQYJJLY AS applicationBasisAndReason,
                ml.JHJE AS planAmount,
                ROW_NUMBER() OVER (ORDER BY ml.LR_RQ DESC) AS row_num
            FROM GPM_CGJHML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'
              <if test="startDate != null">
                  AND CONVERT(DATE, ml.NCGRQ) &gt;= #{startDate}
              </if>
              <if test="endDate != null">
                  AND CONVERT(DATE, ml.NCGRQ) &lt;= #{endDate}
              </if>
        ) t
        WHERE t.row_num > #{offset} AND t.row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询已审核状态的采购计划总数 -->
    <select id="countApprovedPlans" resultType="long">
        SELECT COUNT(1)
        FROM GPM_CGJHML ml
        WHERE ml.GSDM = #{gsdm}
          AND ml.KJND = #{kjnd}
          AND ml.ZT = '4'
          <if test="startDate != null">
              AND CONVERT(DATE, ml.NCGRQ) &gt;= #{startDate}
          </if>
          <if test="endDate != null">
              AND CONVERT(DATE, ml.NCGRQ) &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询已审核状态的采购申请 -->
    <select id="queryApprovedApplications" resultType="com.gg.grp.hkycg.model.vo.ApprovedApplicationVO">
        SELECT * FROM (
            SELECT
                CONVERT(VARCHAR(10), ml.NCGRQ, 120) AS purchaseDate,
                'CGSQ' AS billType,
                ml.SQBH AS applicationNo,
                ml.SQMC AS applicationName,
                CONVERT(VARCHAR(10), ml.CJRQ, 120) AS createDate,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS billStatus,
                ml.SQBMMC AS applicationDept,
                ml.JBR AS handler,
                ml.CGLYMC AS purchaseReason,
                ml.SQJE AS applicationAmount,
                ROW_NUMBER() OVER (ORDER BY ml.CJRQ DESC) AS row_num
            FROM GPM_CGSQML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'
              <if test="startDate != null">
                  AND CONVERT(DATE, ml.NCGRQ) &gt;= #{startDate}
              </if>
              <if test="endDate != null">
                  AND CONVERT(DATE, ml.NCGRQ) &lt;= #{endDate}
              </if>
        ) t
        WHERE t.row_num > #{offset} AND t.row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询已审核状态的采购申请总数 -->
    <select id="countApprovedApplications" resultType="long">
        SELECT COUNT(1)
        FROM GPM_CGSQML ml
        WHERE ml.GSDM = #{gsdm}
          AND ml.KJND = #{kjnd}
          AND ml.ZT = '4'
          <if test="startDate != null">
              AND CONVERT(DATE, ml.NCGRQ) &gt;= #{startDate}
          </if>
          <if test="endDate != null">
              AND CONVERT(DATE, ml.NCGRQ) &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询已审核状态的采购招标 -->
    <select id="queryApprovedBiddings" resultType="com.gg.grp.hkycg.model.vo.ApprovedBiddingVO">
        SELECT * FROM (
            SELECT
                CONVERT(VARCHAR(10), ml.NZBRQ, 120) AS plannedBiddingDate,
                'CGZB' AS billType,
                ml.ZBSQBH AS biddingNo,
                ml.ZBSQMC AS biddingApplicationName,
                CONVERT(VARCHAR(10), ml.CJSJ, 120) AS createDate,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS billStatus,
                ml.ZBSQBMMC AS biddingApplicationDept,
                ml.ZBFZR AS biddingManager,
                ml.JBR AS handler,
                ml.CGLY AS biddingReason,
                ml.CGSQJE AS purchaseApplicationAmount,
                ROW_NUMBER() OVER (ORDER BY ml.CJSJ DESC) AS row_num
            FROM GPM_CGZBSQML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'
              <if test="startDate != null">
                  AND CONVERT(DATE, ml.CJSJ) &gt;= #{startDate}
              </if>
              <if test="endDate != null">
                  AND CONVERT(DATE, ml.CJSJ) &lt;= #{endDate}
              </if>
        ) t
        WHERE t.row_num > #{offset} AND t.row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询已审核状态的采购招标总数 -->
    <select id="countApprovedBiddings" resultType="long">
        SELECT COUNT(1)
        FROM GPM_CGZBSQML ml
        WHERE ml.GSDM = #{gsdm}
          AND ml.KJND = #{kjnd}
          AND ml.ZT = '4'
          <if test="startDate != null">
              AND CONVERT(DATE, ml.CJSJ) &gt;= #{startDate}
          </if>
          <if test="endDate != null">
              AND CONVERT(DATE, ml.CJSJ) &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询已审核状态的采购登记 -->
    <select id="queryApprovedResults" resultType="com.gg.grp.hkycg.model.vo.ApprovedResultVO">
        SELECT * FROM (
            SELECT
                'CGDJ' AS billType,
                ml.JGDJBH AS resultRegistrationNo,
                ml.JGDJMC AS resultRegistrationName,
                CONVERT(VARCHAR(10), ml.CREATE_TIME, 120) AS createDate,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS billStatus,
                ml.CGBMMC AS purchaseDept,
                ml.ZBFZR AS biddingManager,
                ml.JBR AS handler,
                ml.CGLY AS purchaseReason,
                ml.JGDJJE AS resultRegistrationAmount,
                ROW_NUMBER() OVER (ORDER BY ml.CREATE_TIME DESC) AS row_num
            FROM GPM_CGDJML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'
              <if test="startDate != null">
                  AND CONVERT(DATE, ml.CREATE_TIME) &gt;= #{startDate}
              </if>
              <if test="endDate != null">
                  AND CONVERT(DATE, ml.CREATE_TIME) &lt;= #{endDate}
              </if>
        ) t
        WHERE t.row_num > #{offset} AND t.row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询已审核状态的采购登记总数 -->
    <select id="countApprovedResults" resultType="long">
        SELECT COUNT(1)
        FROM GPM_CGDJML ml
        WHERE ml.GSDM = #{gsdm}
          AND ml.KJND = #{kjnd}
          AND ml.ZT = '4'
          <if test="startDate != null">
              AND CONVERT(DATE, ml.CREATE_TIME) &gt;= #{startDate}
          </if>
          <if test="endDate != null">
              AND CONVERT(DATE, ml.CREATE_TIME) &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询已审核状态的采购验收 -->
    <select id="queryApprovedAcceptances" resultType="com.gg.grp.hkycg.model.vo.ApprovedAcceptanceVO">
        SELECT * FROM (
            SELECT
                CONVERT(VARCHAR(10), ml.NYSRQ, 120) AS plannedAcceptanceDate,
                'CGYS' AS billType,
                ml.YSBH AS acceptanceApplicationNo,
                ml.YSMC AS acceptanceApplicationName,
                CONVERT(VARCHAR(10), ml.CREATE_TIME, 120) AS createDate,
                CASE ml.ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS billStatus,
                ml.YSBMMC AS acceptanceApplicationDept,
                ml.YSFZR AS acceptanceManager,
                ml.CYRY AS participants,
                ml.BZ AS remarks,
                ml.BCYSJE AS currentAcceptanceAmount,
                ROW_NUMBER() OVER (ORDER BY ml.CREATE_TIME DESC) AS row_num
            FROM GPM_CGYSML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'
              <if test="startDate != null">
                  AND CONVERT(DATE, ml.CREATE_TIME) &gt;= #{startDate}
              </if>
              <if test="endDate != null">
                  AND CONVERT(DATE, ml.CREATE_TIME) &lt;= #{endDate}
              </if>
        ) t
        WHERE t.row_num > #{offset} AND t.row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询已审核状态的采购验收总数 -->
    <select id="countApprovedAcceptances" resultType="long">
        SELECT COUNT(1)
        FROM GPM_CGYSML ml
        WHERE ml.GSDM = #{gsdm}
          AND ml.KJND = #{kjnd}
          AND ml.ZT = '4'
          <if test="startDate != null">
              AND CONVERT(DATE, ml.CREATE_TIME) &gt;= #{startDate}
          </if>
          <if test="endDate != null">
              AND CONVERT(DATE, ml.CREATE_TIME) &lt;= #{endDate}
          </if>
    </select>

    <!-- 查询所有已审核状态的单据 -->
    <select id="queryAllApprovedBills" resultType="com.gg.grp.hkycg.model.vo.AllApprovedBillVO">
        SELECT
            billType,
            billNo,
            billName,
            billDate
        FROM (
            SELECT
                billType,
                billNo,
                billName,
                billDate,
                CASE 
                    WHEN billDate > CONVERT(DATE, GETDATE()) THEN 1 -- 未来日期
                    ELSE 0 -- 过去日期
                END AS isFutureDate,
                ROW_NUMBER() OVER (
                    ORDER BY 
                        -- 第一排序：未来日期(1)排在前面，过去日期(0)排在后面
                        CASE 
                            WHEN billDate > CONVERT(DATE, GETDATE()) THEN 1 -- 未来日期
                            ELSE 0 -- 过去日期
                        END DESC, 
                        -- 第二排序：对未来日期按升序排列（离当前日期近的在前面）
                        CASE 
                            WHEN billDate > CONVERT(DATE, GETDATE()) THEN billDate
                        END ASC, 
                        -- 第三排序：对过去日期按降序排列（离当前日期近的在前面）
                        CASE 
                            WHEN billDate &lt;= CONVERT(DATE, GETDATE()) THEN billDate
                        END DESC
                ) AS row_num
            FROM (
                -- 采购计划
                SELECT
                    '采购计划' AS billType,
                    ml.JHBH AS billNo,
                    ml.JHMC AS billName,
                    ml.NCGRQ AS billDate
                FROM GPM_CGJHML ml
                WHERE ml.GSDM = #{gsdm}
                  AND ml.KJND = #{kjnd}
                  AND ml.ZT = '4'

                UNION ALL

                -- 采购申请
                SELECT
                    '采购申请' AS billType,
                    ml.SQBH AS billNo,
                    ml.SQMC AS billName,
                    CONVERT(DATE, ml.NCGRQ) AS billDate
                FROM GPM_CGSQML ml
                WHERE ml.GSDM = #{gsdm}
                  AND ml.KJND = #{kjnd}
                  AND ml.ZT = '4'

                UNION ALL

                -- 采购招标
                SELECT
                    '采购招标' AS billType,
                    ml.ZBSQBH AS billNo,
                    ml.ZBSQMC AS billName,
                    CONVERT(DATE, ml.NZBRQ) AS billDate
                FROM GPM_CGZBSQML ml
                WHERE ml.GSDM = #{gsdm}
                  AND ml.KJND = #{kjnd}
                  AND ml.ZT = '4'

                UNION ALL

                -- 采购登记
                SELECT
                    '采购登记' AS billType,
                    ml.JGDJBH AS billNo,
                    ml.JGDJMC AS billName,
                    CONVERT(DATE, ml.CREATE_TIME) AS billDate
                FROM GPM_CGDJML ml
                WHERE ml.GSDM = #{gsdm}
                  AND ml.KJND = #{kjnd}
                  AND ml.ZT = '4'

                UNION ALL

                -- 采购验收
                SELECT
                    '采购验收' AS billType,
                    ml.YSBH AS billNo,
                    ml.YSMC AS billName,
                    CONVERT(DATE, ml.NYSRQ) AS billDate
                FROM GPM_CGYSML ml
                WHERE ml.GSDM = #{gsdm}
                  AND ml.KJND = #{kjnd}
                  AND ml.ZT = '4'
            ) AllBills
        ) t
        WHERE t.row_num > #{offset} AND t.row_num &lt;= (#{offset} + #{size})
    </select>

    <!-- 查询所有已审核状态的单据总数 -->
    <select id="countAllApprovedBills" resultType="long">
        SELECT COUNT(1) FROM (
            -- 采购计划
            SELECT ml.JHBH
            FROM GPM_CGJHML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'

            UNION ALL

            -- 采购申请
            SELECT ml.SQBH
            FROM GPM_CGSQML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'

            UNION ALL

            -- 采购招标
            SELECT ml.ZBSQBH
            FROM GPM_CGZBSQML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'

            UNION ALL

            -- 采购登记
            SELECT ml.JGDJBH
            FROM GPM_CGDJML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'

            UNION ALL

            -- 采购验收
            SELECT ml.YSBH
            FROM GPM_CGYSML ml
            WHERE ml.GSDM = #{gsdm}
              AND ml.KJND = #{kjnd}
              AND ml.ZT = '4'
        ) AllBills
    </select>

</mapper>