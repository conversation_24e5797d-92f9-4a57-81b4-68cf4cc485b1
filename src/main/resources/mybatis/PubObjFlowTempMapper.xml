<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.PubObjFlowTempMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gg.grp.hkycg.model.pojo.PubObjFlowTemp">
        <result column="GSDM" property="gsdm" jdbcType="VARCHAR"/>
        <result column="KJND" property="kjnd" jdbcType="VARCHAR"/>
        <result column="FLOWCODE" property="flowcode" jdbcType="VARCHAR"/>
        <result column="DJLXID" property="djlxid" jdbcType="INTEGER"/>
        <result column="JDDM" property="jddm" jdbcType="INTEGER"/>
        <result column="JDMC" property="jdmc" jdbcType="VARCHAR"/>
        <result column="SHRDM" property="shrdm" jdbcType="VARCHAR"/>
        <result column="SHRXM" property="shrxm" jdbcType="VARCHAR"/>
        <result column="SHTJ" property="shtj" jdbcType="VARCHAR"/>
        <result column="JDSHTJ" property="jdshtj" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 根据单据类型ID查询工作流程模板列表 -->
    <select id="selectListByDjlxid" resultMap="BaseResultMap">
        SELECT * FROM PUB_OBJ_FLOW_TEMP
        WHERE DJLXID = #{djlxid}
        ORDER BY JDDM
    </select>

    <!-- 根据条件查询工作流程模板 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT * FROM PUB_OBJ_FLOW_TEMP
        WHERE GSDM = #{gsdm}
          AND KJND = #{kjnd}
          AND FLOWCODE = #{flowcode}
        <if test="jddm != null">
            AND JDDM = #{jddm}
        </if>
        ORDER BY JDDM
    </select>

</mapper> 