<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.AcceptanceResultMapper">
    <select id="getFullProcurementLinkWithCondition" resultType="com.gg.grp.hkycg.model.vo.ProcurementLinkSummaryVO">
        SELECT 
            ysnr.YSMXXH AS ysmxxh,
            ys.YSBH AS ysbh,
            
            MAX(jg.JGDJBH) AS jgdjbh,
            MAX(jgnr.JGNRBH) AS jgnrbh,
            
            MAX(zbsq.ZBSQBH) AS zbsqbh,
            MAX(zbsqnr.ZBMXXH) AS zbmxxh,
            
            MAX(sq.SQBH) AS sqbh,
            MAX(sqnr.SQMXXH) AS sqmxxh,
            
            MAX(jh.JHBH) AS jhbh,
            MAX(jhnr.JHMXXH) AS jhmxxh
            
        FROM GPM_CGYSML ys
        LEFT JOIN GPM_CGYSNR ysnr ON ys.YSBH = ysnr.YSBH
        LEFT JOIN GPM_CGDJNR jgnr ON ysnr.JGNRBH = jgnr.JGNRBH
        LEFT JOIN GPM_CGDJML jg ON jgnr.JGDJBH = jg.JGDJBH
        LEFT JOIN GPM_CGZBSQNR zbsqnr ON jgnr.ZBMXXH = zbsqnr.ZBMXXH
        LEFT JOIN GPM_CGZBSQML zbsq ON zbsqnr.ZBSQBH = zbsq.ZBSQBH
        LEFT JOIN GPM_CGSQNR sqnr ON zbsqnr.SQMXXH = sqnr.SQMXXH
        LEFT JOIN GPM_CGSQML sq ON sqnr.SQBH = sq.SQBH
        LEFT JOIN GPM_CGJHNR jhnr ON sq.JHBH = jhnr.JHBH
        LEFT JOIN GPM_CGJHML jh ON jhnr.JHBH = jh.JHBH
        WHERE ys.YSBH = #{ysbh}
        <if test="condition != null and condition != ''">
            AND (ys.YSBH LIKE '%' + #{condition} + '%' OR ys.YSMC LIKE '%' + #{condition} + '%')
        </if>
        <if test="yslxdm != null and yslxdm != ''">
            AND ys.YSLXDM = #{yslxdm}
        </if>
        <if test="ysbmdm != null and ysbmdm != ''">
            AND ys.YSBMDM = #{ysbmdm}
        </if>
        <if test="xmmc != null and xmmc != ''">
            AND jh.XMMC LIKE '%' + #{xmmc} + '%'
        </if>
        GROUP BY ysnr.YSMXXH, ys.YSBH
    </select>
</mapper> 