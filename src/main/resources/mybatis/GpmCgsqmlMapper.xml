<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgsqmlMapper">

    <!-- 分页查询采购申请列表 -->
    <select id="getCgsqPageList" resultType="com.gg.grp.hkycg.model.vo.CgsqListVO">
        SELECT * FROM (
            SELECT
                ROW_NUMBER() OVER (ORDER BY t.CJRQ DESC, t.SQBH DESC) AS rowNum,
                COUNT(*) OVER() AS totalCount,
                t.SQBH AS sqbh,
                t.SQMC AS sqmc,
                t.GSDM AS gsdm,
                t.KJND AS kjnd,
                t.ZT AS zt,
                CASE
                    WHEN t.ZT = '1' THEN '保存'
                    WHEN t.ZT = '2' THEN '已提交'
                    WHEN t.ZT = '3' THEN '审核中'
                    WHEN t.ZT = '4' THEN '已审核'
                    WHEN t.ZT = '5' THEN '已退回'
                    ELSE '未知'
                END AS ztmc,
                t.CGLXDM AS cglxdm,
                t.CGLXMC AS cglxmc,
                t.CJRQ AS cjrq,
                t.SQBMDM AS sqbmdm,
                t.SQBMMC AS sqbmmc,
                t.SQJE AS sqje,
                t.JBR AS jbr,
                t.CJRMC AS cjrmc,
                t.NCGRQ AS ncgrq
            FROM GPM_CGSQML t
            LEFT JOIN PUBZYXX emp ON t.CJRDM = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
            WHERE t.GSDM = #{gsdm}
              AND t.KJND = #{kjnd}
              <!-- 数据权限控制 -->
              <choose>
                  <!-- 职员权限：只能查看自己的数据 -->
                  <when test="dataPermission == '9001'">
                      AND t.CJRDM = #{currEmployeeCode}
                  </when>
                  <!-- 部长权限：可以查看本部门的数据 -->
                  <when test="dataPermission == '9002'">
                      AND emp.BMDM = #{currDeptCode}
                  </when>
                  <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
                  <when test="dataPermission == '9003'">
                      <!-- 管理员可以查看所有数据 -->
                  </when>
                  <!-- 默认权限：按职员权限处理 -->
                  <otherwise>
                      AND t.CJRDM = #{currEmployeeCode}
                  </otherwise>
              </choose>
              <if test="condition != null and condition != ''">
                  AND (t.SQMC LIKE #{condition} OR t.SQBH LIKE #{condition} OR t.JBR LIKE #{condition} OR t.CJRMC LIKE #{condition})
              </if>
              <if test="zt != null and zt != ''">
                  AND t.ZT = #{zt}
              </if>
              <if test="startDate != null and startDate != ''">
                  AND CONVERT(date, t.CJRQ) &gt;= CONVERT(date, #{startDate})
              </if>
              <if test="endDate != null and endDate != ''">
                  AND CONVERT(date, t.CJRQ) &lt;= CONVERT(date, #{endDate})
              </if>
        ) AS PagedResult
        WHERE rowNum > #{offset} AND rowNum <![CDATA[<=]]> (#{offset} + #{size})
    </select>

    <!-- 分页查询采购申请列表 -->
    <select id="getCgsqApprovedPageList" resultType="com.gg.grp.hkycg.model.vo.CgsqListVO">
        SELECT * FROM (
        SELECT
        ROW_NUMBER() OVER (ORDER BY t.CJRQ DESC, t.SQBH DESC) AS rowNum,
        COUNT(*) OVER() AS totalCount,
        t.SQBH AS sqbh,
        t.SQMC AS sqmc,
        t.GSDM AS gsdm,
        t.KJND AS kjnd,
        t.ZT AS zt,
        CASE
        WHEN t.ZT = '1' THEN '保存'
        WHEN t.ZT = '2' THEN '已提交'
        WHEN t.ZT = '3' THEN '审核中'
        WHEN t.ZT = '4' THEN '已审核'
        WHEN t.ZT = '5' THEN '已退回'
        ELSE '未知'
        END AS ztmc,
        t.CGLXDM AS cglxdm,
        t.CGLXMC AS cglxmc,
        t.CJRQ AS cjrq,
        t.SQBMDM AS sqbmdm,
        t.SQBMMC AS sqbmmc,
        t.SQJE AS sqje,
        t.JBR AS jbr,
        t.CJRMC AS cjrmc
        FROM GPM_CGSQML t
        LEFT JOIN PUBZYXX emp ON t.CJRDM = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
        WHERE t.GSDM = #{gsdm}
        AND t.KJND = #{kjnd}
        <if test="cgfssfzb==true">
            AND t.CGFSMC IN ('公开招标')
        </if>
        <if test="cgfssfzb==false">
            AND t.CGFSMC NOT IN ('公开招标')
        </if>
        <!-- 数据权限控制 -->
        <choose>
            <!-- 职员权限：只能查看自己的数据 -->
            <when test="dataPermission == '9001'">
                AND t.CJRDM = #{currEmployeeCode}
            </when>
            <!-- 部长权限：可以查看本部门的数据 -->
            <when test="dataPermission == '9002'">
                AND emp.BMDM = #{currDeptCode}
            </when>
            <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
            <when test="dataPermission == '9003'">
                <!-- 管理员可以查看所有数据 -->
            </when>
            <!-- 默认权限：按职员权限处理 -->
            <otherwise>
                AND t.CJRDM = #{currEmployeeCode}
            </otherwise>
        </choose>
        <if test="condition != null and condition != ''">
            AND (t.SQMC LIKE #{condition} OR t.SQBH LIKE #{condition} OR t.JBR LIKE #{condition} OR t.CJRMC LIKE #{condition})
        </if>
        <if test="zt != null and zt != ''">
            AND t.ZT = #{zt}
        </if>
        <if test="startDate != null and startDate != ''">
            AND CONVERT(date, t.CJRQ) &gt;= CONVERT(date, #{startDate})
        </if>
        <if test="endDate != null and endDate != ''">
            AND CONVERT(date, t.CJRQ) &lt;= CONVERT(date, #{endDate})
        </if>
        ) AS PagedResult
        WHERE rowNum > #{offset} AND rowNum <![CDATA[<=]]> (#{offset} + #{size})
    </select>

</mapper> 