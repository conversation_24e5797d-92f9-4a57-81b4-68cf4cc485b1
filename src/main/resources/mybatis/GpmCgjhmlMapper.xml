<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCgjhmlMapper">

    <!-- 分页查询采购计划列表 -->
    <select id="getCgjhPageList" resultType="com.gg.grp.hkycg.model.vo.CgjhListVO">
        SELECT * FROM (
            SELECT
                ROW_NUMBER() OVER (ORDER BY t.LR_RQ DESC, t.JHBH DESC) AS rowNum,
                COUNT(*) OVER() AS totalCount,
                t.JHBH AS jhbh,
                t.JHMC AS jhmc,
                t.GSDM AS gsdm,
                t.KJND AS kjnd,
                t.BMD<PERSON> AS bmdm,
                t.BMMC AS bmmc,
                t.ZT AS zt,
                CASE
                    WHEN t.ZT = 1 THEN '保存'
                    WHEN t.ZT = 2 THEN '已提交'
                    WHEN t.ZT = 3 THEN '审核中'
                    WHEN t.ZT = 4 THEN '已审核'
                    WHEN t.ZT = 5 THEN '退回'
                    ELSE '未知'
                END AS ztmc,
                t.LR_RQ AS lrRq,
                t.JHJE AS jhje,
                ISNULL(nr.CGMLDM, '') AS cgmldm,
                ISNULL(nr.CGMLMC, '') AS cgmlmc,
                ISNULL(t.CGLXDM, '') AS cglxdm,
                ISNULL(t.CGLXMC, '') AS cglxmc,
                t.JHJE AS ysje,
                t.LRR AS lrr,
                t.LRR_ID AS lrrId,
                t.NCGRQ AS ncgrq
            FROM GPM_CGJHML t
            LEFT JOIN (
                SELECT DISTINCT
                    JHBH,
                    CGMLDM,
                    CGMLMC,
                    GSDM,
                    KJND
                FROM GPM_CGJHNR nr1
                WHERE nr1.JHMXXH = (
                    SELECT MIN(nr2.JHMXXH)
                    FROM GPM_CGJHNR nr2
                    WHERE nr2.JHBH = nr1.JHBH
                      AND nr2.GSDM = nr1.GSDM
                      AND nr2.KJND = nr1.KJND
                )
            ) nr ON t.JHBH = nr.JHBH AND t.GSDM = nr.GSDM AND t.KJND = nr.KJND
            LEFT JOIN PUBZYXX emp ON CAST(t.LRR_ID AS VARCHAR(50)) = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
            WHERE t.GSDM = #{gsdm}
              AND t.KJND = #{kjnd}
              <!-- 数据权限控制 -->
              <choose>
                  <!-- 职员权限：只能查看自己的数据 -->
                  <when test="dataPermission == '9001'">
                      AND CAST(t.LRR_ID AS VARCHAR(50)) = #{currEmployeeCode}
                  </when>
                  <!-- 部长权限：可以查看本部门的数据 -->
                  <when test="dataPermission == '9002'">
                      AND emp.BMDM = #{currDeptCode}
                  </when>
                  <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
                  <when test="dataPermission == '9003'">
                      <!-- 管理员可以查看所有数据 -->
                  </when>
                  <!-- 默认权限：按职员权限处理 -->
                  <otherwise>
                      AND CAST(t.LRR_ID AS VARCHAR(50)) = #{currEmployeeCode}
                  </otherwise>
              </choose>
              <if test="condition != null and condition != ''">
                  AND (t.JHMC LIKE #{condition} OR t.LRR LIKE #{condition} OR t.JHBH LIKE #{condition})
              </if>
              <if test="zt != null and zt != ''">
                  AND t.ZT = #{zt}
              </if>
              <if test="startDate != null and startDate != ''">
                  AND t.LR_RQ &gt;= #{startDate}
              </if>
              <if test="endDate != null and endDate != ''">
                  AND t.LR_RQ &lt;= #{endDate}
              </if>
        ) AS PagedResult
        WHERE rowNum > #{offset} AND rowNum <![CDATA[<=]]> (#{offset} + #{size})
    </select>

    <select id="findAuditorInfo" resultType="com.alibaba.fastjson.JSONObject">
        select top(1) shrdm, shrmc
        from(
        ${con}
        ) AS a
        where gsdm = #{gsdm} and kjnd = #{kjnd} and billNo = #{billNo}
    </select>

    <select id="getApprovedCgjhList" resultType="com.gg.grp.hkycg.model.vo.CgjhListVO">
        SELECT * FROM (
        SELECT
        ROW_NUMBER() OVER (ORDER BY t.LR_RQ DESC, t.JHBH DESC) AS rowNum,
        COUNT(*) OVER() AS totalCount,
        t.JHBH AS jhbh,
        t.JHMC AS jhmc,
        t.GSDM AS gsdm,
        t.KJND AS kjnd,
        t.BMDM AS bmdm,
        t.BMMC AS bmmc,
        t.ZT AS zt,
        CASE
        WHEN t.ZT = 1 THEN '保存'
        WHEN t.ZT = 2 THEN '已提交'
        WHEN t.ZT = 3 THEN '审核中'
        WHEN t.ZT = 4 THEN '已审核'
        WHEN t.ZT = 5 THEN '退回'
        ELSE '未知'
        END AS ztmc,
        t.LR_RQ AS lrRq,
        t.JHJE AS jhje,
        ISNULL(nr.CGMLDM, '') AS cgmldm,
        ISNULL(nr.CGMLMC, '') AS cgmlmc,
        ISNULL(t.CGLXDM, '') AS cglxdm,
        ISNULL(t.CGLXMC, '') AS cglxmc,
        t.JHJE AS ysje,
        t.LRR AS lrr,
        t.LRR_ID AS lrrId,
        t.NCGRQ AS ncgrq
        FROM GPM_CGJHML t
        LEFT JOIN (
        SELECT DISTINCT
        JHBH,
        CGMLDM,
        CGMLMC,
        GSDM,
        KJND
        FROM GPM_CGJHNR nr1
        WHERE nr1.JHMXXH = (
        SELECT MIN(nr2.JHMXXH)
        FROM GPM_CGJHNR nr2
        WHERE nr2.JHBH = nr1.JHBH
        AND nr2.GSDM = nr1.GSDM
        AND nr2.KJND = nr1.KJND
        )
        ) nr ON t.JHBH = nr.JHBH AND t.GSDM = nr.GSDM AND t.KJND = nr.KJND
        LEFT JOIN PUBZYXX emp ON CAST(t.LRR_ID AS VARCHAR(50)) = emp.ZYDM AND t.GSDM = emp.gsdm AND t.KJND = emp.kjnd
        WHERE t.GSDM = #{gsdm}
        AND t.KJND = #{kjnd}
        AND NOT EXISTS (
        select 1 from GPM_CGSQML sqml
        where sqml.JHBH = t.JHBH AND sqml.GSDM = t.GSDM AND sqml.KJND = t.KJND
        )
        <!-- 数据权限控制 -->
        <choose>
            <!-- 职员权限：只能查看自己的数据 -->
            <when test="dataPermission == '9001'">
                AND CAST(t.LRR_ID AS VARCHAR(50)) = #{currEmployeeCode}
            </when>
            <!-- 部长权限：可以查看本部门的数据 -->
            <when test="dataPermission == '9002'">
                AND emp.BMDM = #{currDeptCode}
            </when>
            <!-- 管理员权限：可以查看所有数据，不添加额外条件 -->
            <when test="dataPermission == '9003'">
                <!-- 管理员可以查看所有数据 -->
            </when>
            <!-- 默认权限：按职员权限处理 -->
            <otherwise>
                AND CAST(t.LRR_ID AS VARCHAR(50)) = #{currEmployeeCode}
            </otherwise>
        </choose>
        <if test="condition != null and condition != ''">
            AND (t.JHMC LIKE #{condition} OR t.LRR LIKE #{condition} OR t.JHBH LIKE #{condition})
        </if>
        <if test="zt != null and zt != ''">
            AND t.ZT = #{zt}
        </if>
        <if test="startDate != null and startDate != ''">
            AND t.LR_RQ &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND t.LR_RQ &lt;= #{endDate}
        </if>
        ) AS PagedResult
        WHERE rowNum > #{offset} AND rowNum <![CDATA[<=]]> (#{offset} + #{size})
    </select>

</mapper> 