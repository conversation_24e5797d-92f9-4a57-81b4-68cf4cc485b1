<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GnflMapper">

    <select id="getUserGnflUrl" resultType="com.gg.grp.hkycg.model.vo.UserGnflUrlVO">
        SELECT DISTINCT
            g.code,
            g.url
        FROM
            GPM_GNFL g
                JOIN GPM_ROLEGNFL rg ON g.CODE = rg.GNFLCODE
                JOIN GPM_ROLEUSER ru ON rg.ROLECODE = ru.ROLECODE
        WHERE
            ru.zydm = #{zydm}
          AND ru.gsdm = #{gsdm}
          AND ru.kjnd = #{kjnd}
          AND g.url != ''
        ORDER BY
            g.code
    </select>
</mapper>