<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmYssjfsMapper">

    <!-- 分页查询预算审计方式列表 -->
    <select id="getYssjfsPageList" resultType="com.gg.grp.hkycg.model.vo.YssjfsListVO">
        WITH PagedResults AS (
            SELECT 
                yssjfs.GSDM as gsdm,
                yssjfs.KJND as kjnd,
                yssjfs.YSSJFSDM as yssjfsdm,
                yssjfs.YSSJFSMC as yssjfsmc,
                yssjfs.SYZT as syzt,
                CASE 
                    WHEN yssjfs.SYZT = '1' THEN '启用'
                    WHEN yssjfs.SYZT = '0' THEN '停用'
                    ELSE '未知'
                END as syztmc,
                yssjfs.ZJM as zjm,
                yssjfs.BZ as bz,
                yssjfs.PXH as pxh,
                yssjfs.JC as jc,
                COUNT(*) OVER() as totalCount,
                ROW_NUMBER() OVER(ORDER BY yssjfs.YSSJFSDM ASC , yssjfs.PXH ASC ) as rn
            FROM GPM_YSSJFS yssjfs
            WHERE yssjfs.GSDM = #{query.gsdm}
                AND yssjfs.KJND = #{query.kjnd}
                <if test="query.condition != null and query.condition != ''">
                    AND (
                        yssjfs.YSSJFSDM LIKE '%' + #{query.condition} + '%'
                        OR yssjfs.YSSJFSMC LIKE '%' + #{query.condition} + '%'
                        OR yssjfs.ZJM LIKE '%' + #{query.condition} + '%'
                    )
                </if>
                <if test="query.syzt != null and query.syzt != ''">
                    AND yssjfs.SYZT = #{query.syzt}
                </if>
        )
        SELECT *
        FROM PagedResults
        WHERE rn BETWEEN (#{query.current} - 1) * #{query.size} + 1 AND #{query.current} * #{query.size}
        ORDER BY rn
    </select>

</mapper> 