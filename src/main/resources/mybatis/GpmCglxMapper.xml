<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.GpmCglxMapper">

    <!-- 分页查询采购类型列表 -->
    <select id="getCglxPageList" resultType="com.gg.grp.hkycg.model.vo.CglxListVO">
        WITH PagedResults AS (
            SELECT 
                cglx.GSDM as gsdm,
                cglx.KJND as kjnd,
                cglx.CGLXDM as cglxdm,
                cglx.CGLXMC as cglxmc,
                cglx.SYZT as syzt,
                CASE 
                    WHEN cglx.SYZT = '1' THEN '启用'
                    WHEN cglx.SYZT = '0' THEN '停用'
                    ELSE '未知'
                END as syztmc,
                cglx.ZJM as zjm,
                cglx.BZ as bz,
                cglx.PXH as pxh,
                cglx.JC as jc,
                COUNT(*) OVER() as totalCount,
                ROW_NUMBER() OVER(ORDER BY cglx.CGLXDM ASC,cglx.PXH ASC ) as rn
            FROM GPM_CGLX cglx
            WHERE cglx.GSDM = #{query.gsdm}
                AND cglx.KJND = #{query.kjnd}
                <if test="query.condition != null and query.condition != ''">
                    AND (
                        cglx.CGLXDM LIKE '%' + #{query.condition} + '%'
                        OR cglx.CGLXMC LIKE '%' + #{query.condition} + '%'
                        OR cglx.ZJM LIKE '%' + #{query.condition} + '%'
                    )
                </if>
                <if test="query.syzt != null and query.syzt != ''">
                    AND cglx.SYZT = #{query.syzt}
                </if>
        )
        SELECT *
        FROM PagedResults
        WHERE rn BETWEEN (#{query.current} - 1) * #{query.size} + 1 AND #{query.current} * #{query.size}
        ORDER BY rn
    </select>

</mapper> 