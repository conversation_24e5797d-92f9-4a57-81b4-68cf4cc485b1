<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gg.grp.hkycg.mapper.TodoMapper">

    <!-- 查询我的申请通用片段 -->
    <sql id="myApplications_where_conditions">
        <if test="keyword != null and keyword != ''">
            AND (bill.billNo LIKE '%' + #{keyword} + '%' OR bill.billName LIKE '%' + #{keyword} + '%')
        </if>
        <if test="billType != null and billType != ''">
            AND bill.billTypeCode = #{billType}
        </if>
        <if test="status != null and status != ''">
            AND bill.statusCode = #{status}
        </if>
        <if test="startDate != null and startDate != ''">
            AND bill.createTime &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND bill.createTime &lt;= #{endDate} + ' 23:59:59'
        </if>
    </sql>
    
    <!-- 查询我的申请列表 -->
    <select id="getMyApplications" resultType="com.gg.grp.hkycg.model.vo.TodoItemVO">
        WITH CombinedBills AS (
            -- 采购计划
            SELECT
                JHBH AS billNo,
                JHMC AS billName,
                '采购计划' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                LR_RQ AS createTime,
                SQYJJLY AS billContent
            FROM GPM_CGJHML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND LRR_ID = #{employeeCode}

            UNION ALL

            -- 采购申请
            SELECT
                SQBH AS billNo,
                SQMC AS billName,
                '采购申请' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CJRQ AS createTime,
                CGLYMC AS billContent
            FROM GPM_CGSQML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CJRDM = #{employeeCode}

            UNION ALL

            -- 采购招标
            SELECT
                ZBSQBH AS billNo,
                ZBSQMC AS billName,
                '采购招标' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CJSJ AS createTime,
                CGLY AS billContent
            FROM GPM_CGZBSQML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CJRDM = #{employeeCode}

            UNION ALL

            -- 采购登记
            SELECT
                JGDJBH AS billNo,
                JGDJMC AS billName,
                '采购登记' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CREATE_TIME AS createTime,
                CGLY AS billContent
            FROM GPM_CGDJML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CREATE_USERDM = #{employeeCode}

            UNION ALL

            -- 采购验收
            SELECT
                YSBH AS billNo,
                YSMC AS billName,
                '采购验收' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CREATE_TIME AS createTime,
                BZ AS billContent
            FROM GPM_CGYSML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CREATE_USERDM = #{employeeCode}
        ),
        PagedResults AS (
            SELECT
                *,
                ROW_NUMBER() OVER(ORDER BY createTime DESC) AS row_num,
                COUNT(*) OVER() AS totalCount
            FROM CombinedBills AS bill
            WHERE 1=1
            <include refid="myApplications_where_conditions" />
        )
        SELECT
            billNo,
            billName,
            billTypeName,
            statusCode,
            statusName,
            createTime,
            billContent,
            totalCount
        FROM PagedResults
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>
    
    <!-- 查询我已审核的列表 -->
    <select id="getMyAudited" resultType="com.gg.grp.hkycg.model.vo.TodoItemVO">
        WITH AuditedBills AS (
            -- 采购计划
            SELECT
                JHBH AS billNo,
                JHMC AS billName,
                '采购计划' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                LR_RQ AS createTime,
                SQYJJLY AS billContent
            FROM GPM_CGJHML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND LRR_ID = #{employeeCode}
            AND ZT = '4'  -- 只查询已审核的

            UNION ALL

            -- 采购申请
            SELECT
                SQBH AS billNo,
                SQMC AS billName,
                '采购申请' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CJRQ AS createTime,
                CGLYMC AS billContent
            FROM GPM_CGSQML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CJRDM = #{employeeCode}
            AND ZT = '4'  -- 只查询已审核的

            UNION ALL

            -- 采购招标
            SELECT
                ZBSQBH AS billNo,
                ZBSQMC AS billName,
                '采购招标' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CJSJ AS createTime,
                CGLY AS billContent
            FROM GPM_CGZBSQML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CJRDM = #{employeeCode}
            AND ZT = '4'  -- 只查询已审核的

            UNION ALL

            -- 采购登记
            SELECT
                JGDJBH AS billNo,
                JGDJMC AS billName,
                '采购登记' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CREATE_TIME AS createTime,
                CGLY AS billContent
            FROM GPM_CGDJML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CREATE_USERDM = #{employeeCode}
            AND ZT = '4'  -- 只查询已审核的

            UNION ALL

            -- 采购验收
            SELECT
                YSBH AS billNo,
                YSMC AS billName,
                '采购验收' AS billTypeName,
                ZT AS statusCode,
                CASE ZT
                    WHEN '1' THEN '已保存' WHEN '2' THEN '已提交' WHEN '3' THEN '审核中'
                    WHEN '4' THEN '已审核' WHEN '5' THEN '已退回' ELSE '未知'
                END AS statusName,
                CREATE_TIME AS createTime,
                BZ AS billContent
            FROM GPM_CGYSML
            WHERE GSDM = #{gsdm} AND KJND = #{kjnd}
            AND CREATE_USERDM = #{employeeCode}
            AND ZT = '4'  -- 只查询已审核的
        ),
        FilteredBills AS (
            SELECT 
                *,
                ROW_NUMBER() OVER(ORDER BY createTime DESC) AS row_num,
                COUNT(*) OVER() AS totalCount
            FROM AuditedBills AS bill
            WHERE 1=1
            <if test="keyword != null and keyword != ''">
                AND (bill.billNo LIKE '%' + #{keyword} + '%' OR bill.billName LIKE '%' + #{keyword} + '%')
            </if>
            <if test="billType != null and billType != ''">
                AND bill.billTypeName = 
                CASE #{billType}
                    WHEN 'CGJH' THEN '采购计划'
                    WHEN 'CGSQ' THEN '采购申请'
                    WHEN 'CGZB' THEN '采购招标'
                    WHEN 'CGDJ' THEN '采购登记'
                    WHEN 'CGYS' THEN '采购验收'
                END
            </if>
            <if test="startDate != null and startDate != ''">
                AND bill.createTime &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND bill.createTime &lt;= #{endDate} + ' 23:59:59'
            </if>
        )
        SELECT 
            billNo,
            billName,
            billTypeName,
            statusCode,
            statusName,
            createTime,
            billContent,
            totalCount
        FROM FilteredBills
        WHERE row_num > #{offset} AND row_num &lt;= (#{offset} + #{size})
    </select>
</mapper> 