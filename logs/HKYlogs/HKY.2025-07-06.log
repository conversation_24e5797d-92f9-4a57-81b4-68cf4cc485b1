2025-07-06 00:00:17.417 [redisson-netty-2-3] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:00:17.432 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:00:22.434 [redisson-netty-2-4] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:00:22.436 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:16:58.243 [redisson-netty-2-5] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:16:58.246 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:21:01.897 [redisson-netty-2-6] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:21:01.898 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:21:06.904 [redisson-netty-2-7] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:21:06.905 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:31:08.186 [redisson-netty-2-8] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:31:08.187 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:34:11.095 [redisson-netty-2-9] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:34:11.096 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:34:16.099 [redisson-netty-2-10] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:34:16.101 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 00:49:28.572 [redisson-netty-2-11] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 00:49:28.574 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 01:07:04.293 [redisson-netty-2-12] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 01:07:04.294 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 01:22:39.967 [redisson-netty-2-14] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 01:22:39.969 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 01:38:15.634 [redisson-netty-2-13] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 01:38:15.637 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 01:39:27.851 [redisson-netty-2-15] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 01:39:27.853 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 01:39:32.856 [redisson-netty-2-16] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 01:39:32.858 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 01:55:51.775 [redisson-netty-2-17] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 01:55:51.777 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 02:05:56.691 [redisson-netty-2-18] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 02:05:56.691 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 02:06:01.693 [redisson-netty-2-19] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 02:06:01.695 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 02:06:06.697 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 02:06:06.698 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 02:06:11.699 [redisson-netty-2-23] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 02:06:11.701 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 02:06:16.700 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-06 02:06:16.705 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-06 02:06:18.098 [SpringApplicationShutdownHook] DEBUG io.lettuce.core.resource.DefaultClientResources - Initiate shutdown (0, 2, SECONDS)
2025-07-06 02:06:18.102 [SpringApplicationShutdownHook] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Initiate shutdown (0, 2, SECONDS)
2025-07-06 02:06:18.192 [redisson-netty-2-7] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-7
2025-07-06 02:06:18.192 [redisson-netty-2-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-4
2025-07-06 02:06:18.192 [redisson-netty-2-15] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-15
2025-07-06 02:06:18.192 [redisson-netty-2-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-2
2025-07-06 02:06:18.193 [redisson-netty-2-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-1
2025-07-06 02:06:18.193 [redisson-netty-2-14] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-14
2025-07-06 02:06:18.193 [redisson-netty-2-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-6
2025-07-06 02:06:18.194 [redisson-netty-2-29] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-29
2025-07-06 02:06:18.194 [redisson-netty-2-32] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-32
2025-07-06 02:06:18.194 [redisson-netty-2-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-3
2025-07-06 02:06:18.194 [redisson-netty-2-28] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-28
2025-07-06 02:06:18.194 [redisson-netty-2-24] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-24
2025-07-06 02:06:18.194 [redisson-netty-2-26] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-26
2025-07-06 02:06:18.194 [redisson-netty-2-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-5
2025-07-06 02:06:18.194 [redisson-netty-2-12] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-12
2025-07-06 02:06:18.194 [redisson-netty-2-30] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-30
2025-07-06 02:06:18.194 [redisson-netty-2-10] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-10
2025-07-06 02:06:18.194 [redisson-netty-2-8] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-8
2025-07-06 02:06:18.195 [redisson-netty-2-31] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-31
2025-07-06 02:06:18.195 [redisson-netty-2-27] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-27
2025-07-06 02:06:18.195 [redisson-netty-2-25] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-25
2025-07-06 02:06:18.194 [redisson-netty-2-11] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-11
2025-07-06 02:06:18.195 [redisson-netty-2-13] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-13
2025-07-06 02:06:18.195 [redisson-netty-2-9] DEBUG io.netty.buffer.PoolThreadCache - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-9
2025-07-06 02:06:18.200 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-06 02:06:18.203 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-06 02:06:18.204 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-06 02:06:18.204 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-07-06 02:06:18.204 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-07-06 02:06:18.204 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-06 02:06:18.204 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-06 02:06:18.204 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
