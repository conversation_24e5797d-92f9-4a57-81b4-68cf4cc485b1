2025-07-20 01:24:10.075 [background-preinit] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-07-20 01:24:10.077 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-20 01:24:10.085 [background-preinit] DEBUG o.h.v.internal.xml.config.ValidationXmlParser - Trying to load META-INF/validation.xml for XML based Validator configuration.
2025-07-20 01:24:10.087 [background-preinit] DEBUG o.h.v.internal.xml.config.ResourceLoaderHelper - Trying to load META-INF/validation.xml via TCCL
2025-07-20 01:24:10.087 [background-preinit] DEBUG o.h.v.internal.xml.config.ResourceLoader<PERSON>elper - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
2025-07-20 01:24:10.088 [background-preinit] DEBUG o.h.v.internal.xml.config.ValidationXmlParser - No META-INF/validation.xml found. Using annotation based configuration only.
2025-07-20 01:24:10.092 [background-preinit] DEBUG o.h.v.i.engine.resolver.TraversableResolvers - Cannot find javax.persistence.Persistence on classpath. Assuming non JPA 2 environment. All properties will per default be traversable.
2025-07-20 01:24:10.115 [background-preinit] DEBUG o.h.v.m.ResourceBundleMessageInterpolator - Loaded expression factory via original TCCL
2025-07-20 01:24:10.121 [main] INFO  com.gg.grp.hkycg.HkyCgApplication - Starting HkyCgApplication using Java 1.8.0_462 on ranmaoxindeMBP.bbrouter with PID 34447 (/Users/<USER>/IdeaProjects/hky-cg/target/classes started by ranmaoxin in /Users/<USER>/IdeaProjects/hky-cg)
2025-07-20 01:24:10.122 [main] DEBUG com.gg.grp.hkycg.HkyCgApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-20 01:24:10.122 [main] INFO  com.gg.grp.hkycg.HkyCgApplication - The following 1 profile is active: "dev"
2025-07-20 01:24:10.122 [main] DEBUG org.springframework.boot.SpringApplication - Loading source class com.gg.grp.hkycg.HkyCgApplication
2025-07-20 01:24:10.154 [main] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@16b2bb0c
2025-07-20 01:24:10.258 [background-preinit] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
2025-07-20 01:24:10.266 [background-preinit] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
2025-07-20 01:24:10.267 [background-preinit] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
2025-07-20 01:24:10.267 [background-preinit] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
2025-07-20 01:24:10.267 [background-preinit] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
2025-07-20 01:24:10.267 [background-preinit] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
2025-07-20 01:24:10.910 [main] DEBUG o.s.boot.autoconfigure.AutoConfigurationPackages - @EnableAutoConfiguration was declared on a class in the package 'com.gg.grp.hkycg'. Automatic @Repository and @Entity scanning is enabled.
2025-07-20 01:24:10.911 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-20 01:24:10.911 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.gg.grp.hkycg'
2025-07-20 01:24:11.106 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-20 01:24:11.108 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-20 01:24:11.144 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-07-20 01:24:11.246 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/AcceptanceResultMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/ApprovalReminderMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/AttachmentMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/AttachmentRelMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/AuditMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/BudgetIndexMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/CgdjnrMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/CgsqnrMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/CgysnrMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/CgzbsqnrMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/CimRoleMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/CimRolegnflMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/DeptMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/DynamicQueryMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/EmployeeMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GbiZbsyrecMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GbiZbxmbMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GnflMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgdjmlMapper.class]
2025-07-20 01:24:11.247 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgdjnrMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgfsMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgjhYszbMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgjhmlMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgjhnrMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCglxMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgsqmlMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgsqnrMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgysmlMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgysnrMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgzbsqmlMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgzbsqnrMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgzjjgMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgzjlyfsMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmCgzzfsMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/GpmYssjfsMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/HbgZfcgmlMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/IndexMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/PubAuditLogMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/PubFlowTemplateTemporaryMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/PubObjFlowMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/PubObjFlowTempMapper.class]
2025-07-20 01:24:11.248 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/PubWorkflowMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/RoleGnflMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/RoleMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/RoleUserMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/RolezbMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/SqbMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/StatisticsMapper.class]
2025-07-20 01:24:11.249 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/com/gg/grp/hkycg/mapper/TodoMapper.class]
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'acceptanceResultMapper' and 'com.gg.grp.hkycg.mapper.AcceptanceResultMapper' mapperInterface
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'acceptanceResultMapper'.
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'approvalReminderMapper' and 'com.gg.grp.hkycg.mapper.ApprovalReminderMapper' mapperInterface
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'approvalReminderMapper'.
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'attachmentMapper' and 'com.gg.grp.hkycg.mapper.AttachmentMapper' mapperInterface
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'attachmentMapper'.
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'attachmentRelMapper' and 'com.gg.grp.hkycg.mapper.AttachmentRelMapper' mapperInterface
2025-07-20 01:24:11.250 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'attachmentRelMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'auditMapper' and 'com.gg.grp.hkycg.mapper.AuditMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'auditMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'budgetIndexMapper' and 'com.gg.grp.hkycg.mapper.BudgetIndexMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'budgetIndexMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'cgdjnrMapper' and 'com.gg.grp.hkycg.mapper.CgdjnrMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'cgdjnrMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'cgsqnrMapper' and 'com.gg.grp.hkycg.mapper.CgsqnrMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'cgsqnrMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'cgysnrMapper' and 'com.gg.grp.hkycg.mapper.CgysnrMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'cgysnrMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'cgzbsqnrMapper' and 'com.gg.grp.hkycg.mapper.CgzbsqnrMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'cgzbsqnrMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'cimRoleMapper' and 'com.gg.grp.hkycg.mapper.CimRoleMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'cimRoleMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'cimRolegnflMapper' and 'com.gg.grp.hkycg.mapper.CimRolegnflMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'cimRolegnflMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'deptMapper' and 'com.gg.grp.hkycg.mapper.DeptMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'deptMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dynamicQueryMapper' and 'com.gg.grp.hkycg.mapper.DynamicQueryMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'dynamicQueryMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'com.gg.grp.hkycg.mapper.EmployeeMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'employeeMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gbiZbsyrecMapper' and 'com.gg.grp.hkycg.mapper.GbiZbsyrecMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gbiZbsyrecMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gbiZbxmbMapper' and 'com.gg.grp.hkycg.mapper.GbiZbxmbMapper' mapperInterface
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gbiZbxmbMapper'.
2025-07-20 01:24:11.251 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gnflMapper' and 'com.gg.grp.hkycg.mapper.GnflMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gnflMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgdjmlMapper' and 'com.gg.grp.hkycg.mapper.GpmCgdjmlMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgdjmlMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgdjnrMapper' and 'com.gg.grp.hkycg.mapper.GpmCgdjnrMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgdjnrMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgfsMapper' and 'com.gg.grp.hkycg.mapper.GpmCgfsMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgfsMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgjhYszbMapper' and 'com.gg.grp.hkycg.mapper.GpmCgjhYszbMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgjhYszbMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgjhmlMapper' and 'com.gg.grp.hkycg.mapper.GpmCgjhmlMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgjhmlMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgjhnrMapper' and 'com.gg.grp.hkycg.mapper.GpmCgjhnrMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgjhnrMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCglxMapper' and 'com.gg.grp.hkycg.mapper.GpmCglxMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCglxMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgsqmlMapper' and 'com.gg.grp.hkycg.mapper.GpmCgsqmlMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgsqmlMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgsqnrMapper' and 'com.gg.grp.hkycg.mapper.GpmCgsqnrMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgsqnrMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgysmlMapper' and 'com.gg.grp.hkycg.mapper.GpmCgysmlMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgysmlMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgysnrMapper' and 'com.gg.grp.hkycg.mapper.GpmCgysnrMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgysnrMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgzbsqmlMapper' and 'com.gg.grp.hkycg.mapper.GpmCgzbsqmlMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgzbsqmlMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgzbsqnrMapper' and 'com.gg.grp.hkycg.mapper.GpmCgzbsqnrMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgzbsqnrMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgzjjgMapper' and 'com.gg.grp.hkycg.mapper.GpmCgzjjgMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgzjjgMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgzjlyfsMapper' and 'com.gg.grp.hkycg.mapper.GpmCgzjlyfsMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgzjlyfsMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmCgzzfsMapper' and 'com.gg.grp.hkycg.mapper.GpmCgzzfsMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmCgzzfsMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'gpmYssjfsMapper' and 'com.gg.grp.hkycg.mapper.GpmYssjfsMapper' mapperInterface
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'gpmYssjfsMapper'.
2025-07-20 01:24:11.252 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'hbgZfcgmlMapper' and 'com.gg.grp.hkycg.mapper.HbgZfcgmlMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'hbgZfcgmlMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'indexMapper' and 'com.gg.grp.hkycg.mapper.IndexMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'indexMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'pubAuditLogMapper' and 'com.gg.grp.hkycg.mapper.PubAuditLogMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'pubAuditLogMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'pubFlowTemplateTemporaryMapper' and 'com.gg.grp.hkycg.mapper.PubFlowTemplateTemporaryMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'pubFlowTemplateTemporaryMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'pubObjFlowMapper' and 'com.gg.grp.hkycg.mapper.PubObjFlowMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'pubObjFlowMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'pubObjFlowTempMapper' and 'com.gg.grp.hkycg.mapper.PubObjFlowTempMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'pubObjFlowTempMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'pubWorkflowMapper' and 'com.gg.grp.hkycg.mapper.PubWorkflowMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'pubWorkflowMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleGnflMapper' and 'com.gg.grp.hkycg.mapper.RoleGnflMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleGnflMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'com.gg.grp.hkycg.mapper.RoleMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleUserMapper' and 'com.gg.grp.hkycg.mapper.RoleUserMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'roleUserMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'rolezbMapper' and 'com.gg.grp.hkycg.mapper.RolezbMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'rolezbMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sqbMapper' and 'com.gg.grp.hkycg.mapper.SqbMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'sqbMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'statisticsMapper' and 'com.gg.grp.hkycg.mapper.StatisticsMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'statisticsMapper'.
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'todoMapper' and 'com.gg.grp.hkycg.mapper.TodoMapper' mapperInterface
2025-07-20 01:24:11.253 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'todoMapper'.
2025-07-20 01:24:11.609 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-20 01:24:11.614 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$f94d80b5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-20 01:24:11.631 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-20 01:24:11.979 [main] DEBUG o.s.b.w.e.tomcat.TomcatServletWebServerFactory - Code archive: /Users/<USER>/apache-maven-3.5.4/repository/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar
2025-07-20 01:24:11.979 [main] DEBUG o.s.b.w.e.tomcat.TomcatServletWebServerFactory - Code archive: /Users/<USER>/apache-maven-3.5.4/repository/org/springframework/boot/spring-boot/2.7.18/spring-boot-2.7.18.jar
2025-07-20 01:24:11.979 [main] DEBUG o.s.b.w.e.tomcat.TomcatServletWebServerFactory - Document root: /Users/<USER>/IdeaProjects/hky-cg/src/main/webapp
2025-07-20 01:24:12.001 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8082 (http)
2025-07-20 01:24:12.111 [main] DEBUG o.s.b.w.s.c.ServletWebServerApplicationContext - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
2025-07-20 01:24:12.111 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1957 ms
2025-07-20 01:24:12.160 [main] DEBUG i.m.c.util.internal.logging.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-20 01:24:12.293 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-07-20 01:24:12.294 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2,yzyDB} inited
2025-07-20 01:24:12.295 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3,slaver_1} inited
2025-07-20 01:24:12.295 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4,slyxt} inited
2025-07-20 01:24:12.295 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slaver_1] success
2025-07-20 01:24:12.296 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [yzyDB] success
2025-07-20 01:24:12.296 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slyxt] success
2025-07-20 01:24:12.296 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-20 01:24:12.296 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [4] datasource,primary datasource named [master]
2025-07-20 01:24:12.312 [main] DEBUG c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-20 01:24:12.319 [main] DEBUG c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-20 01:24:12.359 [main] DEBUG o.s.b.c.p.s.ConfigurationPropertySourcesPropertyResolver$DefaultResolver - Found key 'app.jwtSecret' in PropertySource 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/'' with value of type String
2025-07-20 01:24:12.359 [main] DEBUG o.s.b.c.p.s.ConfigurationPropertySourcesPropertyResolver$DefaultResolver - Found key 'app.jwtExpirationInMs' in PropertySource 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/'' with value of type Integer
2025-07-20 01:24:12.444 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@62cbc478'
2025-07-20 01:24:12.500 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/AcceptanceResultMapper.xml]'
2025-07-20 01:24:12.586 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/ApprovalReminderMapper.xml]'
2025-07-20 01:24:12.605 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/AttachmentMapper.xml]'
2025-07-20 01:24:12.619 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/AttachmentRelMapper.xml]'
2025-07-20 01:24:12.623 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/AuditMapper.xml]'
2025-07-20 01:24:12.641 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/BudgetIndexMapper.xml]'
2025-07-20 01:24:12.644 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/DynamicQueryMapper.xml]'
2025-07-20 01:24:12.649 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GbiZbsyrec".
2025-07-20 01:24:12.649 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GbiZbsyrec ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.657 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GbiZbsyrecMapper.xml]'
2025-07-20 01:24:12.671 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GbiZbxmb".
2025-07-20 01:24:12.671 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GbiZbxmb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.683 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GbiZbxmbMapper.xml]'
2025-07-20 01:24:12.697 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GnflMapper.xml]'
2025-07-20 01:24:12.716 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgdjmlMapper.xml]'
2025-07-20 01:24:12.730 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgfsMapper.xml]'
2025-07-20 01:24:12.749 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgjhmlMapper.xml]'
2025-07-20 01:24:12.754 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmCglx".
2025-07-20 01:24:12.754 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCglx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.763 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCglxMapper.xml]'
2025-07-20 01:24:12.779 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgsqmlMapper.xml]'
2025-07-20 01:24:12.794 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgysmlMapper.xml]'
2025-07-20 01:24:12.821 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgzbsqmlMapper.xml]'
2025-07-20 01:24:12.836 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgzjjgMapper.xml]'
2025-07-20 01:24:12.848 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgzjlyfsMapper.xml]'
2025-07-20 01:24:12.860 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmCgzzfsMapper.xml]'
2025-07-20 01:24:12.871 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/GpmYssjfsMapper.xml]'
2025-07-20 01:24:12.878 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.OerYszb".
2025-07-20 01:24:12.878 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.OerYszb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.888 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/IndexMapper.xml]'
2025-07-20 01:24:12.902 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/PubAuditLogMapper.xml]'
2025-07-20 01:24:12.905 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.PubObjFlow".
2025-07-20 01:24:12.906 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.PubObjFlow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.912 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/PubObjFlowMapper.xml]'
2025-07-20 01:24:12.916 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.PubObjFlowTemp".
2025-07-20 01:24:12.916 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.PubObjFlowTemp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.923 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/PubObjFlowTempMapper.xml]'
2025-07-20 01:24:12.926 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.PubWorkflow".
2025-07-20 01:24:12.926 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.PubWorkflow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.933 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/PubWorkflowMapper.xml]'
2025-07-20 01:24:12.943 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/RoleMapper.xml]'
2025-07-20 01:24:12.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmRoleUser".
2025-07-20 01:24:12.947 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmRoleUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.953 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/RoleUserMapper.xml]'
2025-07-20 01:24:12.956 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmRolezb".
2025-07-20 01:24:12.956 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmRolezb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:12.962 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/RolezbMapper.xml]'
2025-07-20 01:24:12.965 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/StatisticsMapper.xml]'
2025-07-20 01:24:12.967 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/IdeaProjects/hky-cg/target/classes/mybatis/TodoMapper.xml]'
2025-07-20 01:24:12.983 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.Employee".
2025-07-20 01:24:12.984 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.Employee ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:13.049 [main] DEBUG o.s.b.web.servlet.ServletContextInitializerBeans - Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, jwtAuthenticationFilter urls=[/*] order=2147483647
2025-07-20 01:24:13.049 [main] DEBUG o.s.b.web.servlet.ServletContextInitializerBeans - Mapping servlets: dispatcherServlet urls=[/]
2025-07-20 01:24:13.066 [main] DEBUG o.s.b.a.metrics.web.servlet.WebMvcMetricsFilter - Filter 'webMvcMetricsFilter' configured for use
2025-07-20 01:24:13.066 [main] DEBUG o.s.b.w.servlet.filter.OrderedRequestContextFilter - Filter 'requestContextFilter' configured for use
2025-07-20 01:24:13.066 [main] DEBUG com.gg.grp.hkycg.filter.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-20 01:24:13.066 [main] DEBUG o.s.b.w.s.filter.OrderedCharacterEncodingFilter - Filter 'characterEncodingFilter' configured for use
2025-07-20 01:24:13.066 [main] DEBUG o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1 - Filter 'springSecurityFilterChain' configured for use
2025-07-20 01:24:13.067 [main] DEBUG o.s.b.web.servlet.filter.OrderedFormContentFilter - Filter 'formContentFilter' configured for use
2025-07-20 01:24:13.140 [main] DEBUG i.n.util.internal.logging.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-20 01:24:13.140 [main] DEBUG io.netty.util.internal.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-20 01:24:13.140 [main] DEBUG io.netty.util.internal.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-20 01:24:13.151 [main] DEBUG io.netty.util.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-20 01:24:13.158 [main] INFO  org.redisson.Version - Redisson 3.13.3
2025-07-20 01:24:13.160 [main] DEBUG io.netty.channel.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 16
2025-07-20 01:24:13.167 [main] DEBUG io.netty.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-20 01:24:13.167 [main] DEBUG io.netty.util.internal.PlatformDependent0 - Java version: 8
2025-07-20 01:24:13.167 [main] DEBUG io.netty.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-20 01:24:13.167 [main] DEBUG io.netty.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent0 - direct buffer constructor: available
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, {int,long}): available
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-20 01:24:13.168 [main] DEBUG io.netty.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/5y/rwlp81ld7xb0b9sdn0_z6q3w0000gn/T (java.io.tmpdir)
2025-07-20 01:24:13.169 [main] DEBUG io.netty.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-20 01:24:13.169 [main] DEBUG io.netty.util.internal.PlatformDependent - Platform: MacOS
2025-07-20 01:24:13.169 [main] DEBUG io.netty.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: 3817865216 bytes
2025-07-20 01:24:13.169 [main] DEBUG io.netty.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-20 01:24:13.169 [main] DEBUG io.netty.util.internal.CleanerJava6 - java.nio.ByteBuffer.cleaner(): available
2025-07-20 01:24:13.169 [main] DEBUG io.netty.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-20 01:24:13.170 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-20 01:24:13.170 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-20 01:24:13.171 [main] DEBUG io.netty.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-20 01:24:13.182 [main] WARN  i.n.resolver.dns.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-20 01:24:13.184 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-20 01:24:13.184 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-20 01:24:13.186 [main] DEBUG io.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-20 01:24:13.186 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-20 01:24:13.192 [main] DEBUG io.netty.resolver.dns.DnsNameResolver - Default ResolvedAddressTypes: IPV4_ONLY
2025-07-20 01:24:13.192 [main] DEBUG io.netty.resolver.dns.DnsNameResolver - Localhost address: localhost/127.0.0.1
2025-07-20 01:24:13.192 [main] DEBUG io.netty.resolver.dns.DnsNameResolver - Windows hostname: null
2025-07-20 01:24:13.194 [main] DEBUG io.netty.resolver.dns.DnsNameResolver - Default search domains: [bbrouter]
2025-07-20 01:24:13.194 [main] DEBUG io.netty.resolver.dns.DnsNameResolver - Default UnixResolverOptions{ndots=1, timeout=5, attempts=16}
2025-07-20 01:24:13.200 [main] DEBUG io.netty.resolver.DefaultHostsFileEntriesResolver - -Dio.netty.hostsFileRefreshInterval: 0
2025-07-20 01:24:13.206 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-20 01:24:13.207 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-20 01:24:13.207 [main] DEBUG io.netty.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@3513d214
2025-07-20 01:24:13.236 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 34447 (auto-detected)
2025-07-20 01:24:13.237 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: 3c:06:30:ff:fe:4f:f9:fe (auto-detected)
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 16
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 16
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-20 01:24:13.250 [main] DEBUG io.netty.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-20 01:24:13.256 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-20 01:24:13.256 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-20 01:24:13.256 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-20 01:24:13.261 [main] DEBUG io.netty.bootstrap.ChannelInitializerExtensions - -Dio.netty.bootstrap.extensions: null
2025-07-20 01:24:13.294 [redisson-netty-2-6] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-20 01:24:13.294 [redisson-netty-2-6] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-20 01:24:13.294 [redisson-netty-2-6] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-20 01:24:13.294 [redisson-netty-2-6] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-20 01:24:13.294 [redisson-netty-2-6] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-20 01:24:13.298 [redisson-netty-2-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-20 01:24:13.298 [redisson-netty-2-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-20 01:24:13.298 [redisson-netty-2-2] DEBUG io.netty.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@e5b29e6
2025-07-20 01:24:13.311 [redisson-netty-2-23] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@********** [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x4fe8a53a, L:/127.0.0.1:59663 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-20] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1794517182 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xdcaeff45, L:/127.0.0.1:59657 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-18] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@32723810 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x89ec6862, L:/127.0.0.1:59662 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-15] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@2092172621 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xe9c75867, L:/127.0.0.1:59659 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-16] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1717912295 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x58e50f78, L:/127.0.0.1:59655 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-19] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1376058199 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x24610a3d, L:/127.0.0.1:59656 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-13] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@122389590 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x790ad4f6, L:/127.0.0.1:59654 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-21] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1602179239 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xa58b1188, L:/127.0.0.1:59661 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-14] DEBUG org.redisson.connection.ClientConnectionsEntry - new pubsub connection created: RedisPubSubConnection@1833415555 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xdfdbd9c7, L:/127.0.0.1:59660 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-17] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@452654348 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xeaa18176, L:/127.0.0.1:59664 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.311 [redisson-netty-2-22] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1737457295 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xa6de778e, L:/127.0.0.1:59658 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.316 [redisson-netty-2-14] INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6379
2025-07-20 01:24:13.318 [redisson-netty-2-1] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1870321163 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xb4d712a8, L:/127.0.0.1:59665 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.318 [redisson-netty-2-5] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1677955995 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x6cef5eb5, L:/127.0.0.1:59668 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.319 [redisson-netty-2-4] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@445217182 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x6b134982, L:/127.0.0.1:59667 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.319 [redisson-netty-2-6] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1017987966 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x581d64f6, L:/127.0.0.1:59666 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-12] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@124470746 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xe889f793, L:/127.0.0.1:59669 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-11] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@930995130 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xf48b9b82, L:/127.0.0.1:59670 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-10] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1942825804 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x703ac806, L:/127.0.0.1:59671 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-13] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@160197327 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x99f05948, L:/127.0.0.1:59673 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-16] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1755488347 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x35e507e6, L:/127.0.0.1:59672 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-14] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1835116982 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x7ad32e68, L:/127.0.0.1:59675 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-17] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@339548195 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xe384fd35, L:/127.0.0.1:59674 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-18] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@255110444 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0xbab48e6a, L:/127.0.0.1:59678 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-15] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1308251326 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x44a27bcd, L:/127.0.0.1:59676 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.320 [redisson-netty-2-20] DEBUG org.redisson.connection.ClientConnectionsEntry - new connection created: RedisConnection@1858707768 [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x08ccfe16, L:/127.0.0.1:59677 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:13.321 [redisson-netty-2-20] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6379
2025-07-20 01:24:13.325 [main] DEBUG org.redisson.connection.DNSMonitor - DNS monitoring enabled; Current masters: {redis://127.0.0.1:6379=/127.0.0.1:6379}, slaves: {}
2025-07-20 01:24:13.748 [main] INFO  c.gg.grp.hkycg.service.impl.AttachmentServiceImpl - 附件上传路径初始化为: /Users/<USER>/IdeaProjects/hky-cg/upload/attachment
2025-07-20 01:24:13.748 [main] INFO  c.gg.grp.hkycg.service.impl.AttachmentServiceImpl - 附件 base URL: http://localhost:8082/grp
2025-07-20 01:24:13.748 [main] INFO  c.gg.grp.hkycg.service.impl.AttachmentServiceImpl - 附件下载URL前缀: http://localhost:8082/grp/download/
2025-07-20 01:24:13.749 [main] INFO  c.gg.grp.hkycg.service.impl.AttachmentServiceImpl - 附件预览URL前缀: http://localhost:8082/grp/preview/
2025-07-20 01:24:13.762 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.Dept".
2025-07-20 01:24:13.762 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.Dept ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:13.890 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmCgjhnr".
2025-07-20 01:24:13.890 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCgjhnr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:13.905 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmCgjhYszb".
2025-07-20 01:24:13.905 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCgjhYszb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:14.075 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmCgysnr".
2025-07-20 01:24:14.075 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCgysnr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:14.109 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCgysnr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:14.144 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.GpmCgzbsqnr".
2025-07-20 01:24:14.144 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCgzbsqnr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:14.177 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.GpmCgzbsqnr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:14.249 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.gg.grp.hkycg.model.pojo.PubFlowTemplateTemporary".
2025-07-20 01:24:14.249 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.gg.grp.hkycg.model.pojo.PubFlowTemplateTemporary ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-20 01:24:14.635 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/upload/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/attachment/download/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/attachment/preview/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/v3/api-docs/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/login']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/loginOut']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/test-user']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/test-redis']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/redis-pool-status']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/test/public']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v2/api-docs']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/assets/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/css/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/js/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/images/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/index.html']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/WEB-INF/**']
2025-07-20 01:24:14.637 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for any request
2025-07-20 01:24:14.646 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@29f8134, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@77a9ac36, org.springframework.security.web.context.SecurityContextPersistenceFilter@69f69078, org.springframework.security.web.header.HeaderWriterFilter@590dea35, org.springframework.web.filter.CorsFilter@3c743d40, com.gg.grp.hkycg.filter.JwtAuthenticationFilter@681de87f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@25589735, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6106dfb6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5aac6f9f, org.springframework.security.web.session.SessionManagementFilter@79d14037, org.springframework.security.web.access.ExceptionTranslationFilter@1115be8c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@12266084]
2025-07-20 01:24:14.738 [main] DEBUG o.h.v.m.ResourceBundleMessageInterpolator - Loaded expression factory via original TCCL
2025-07-20 01:24:14.740 [main] DEBUG o.h.v.internal.engine.AbstractConfigurationImpl - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
2025-07-20 01:24:14.741 [main] DEBUG o.h.v.internal.engine.AbstractConfigurationImpl - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
2025-07-20 01:24:14.741 [main] DEBUG o.h.v.internal.engine.AbstractConfigurationImpl - Setting custom ParameterNameProvider of type org.springframework.validation.beanvalidation.LocalValidatorFactoryBean$1
2025-07-20 01:24:14.741 [main] DEBUG o.h.v.internal.xml.config.ValidationXmlParser - Trying to load META-INF/validation.xml for XML based Validator configuration.
2025-07-20 01:24:14.741 [main] DEBUG o.h.v.internal.xml.config.ResourceLoaderHelper - Trying to load META-INF/validation.xml via user class loader
2025-07-20 01:24:14.741 [main] DEBUG o.h.v.internal.xml.config.ResourceLoaderHelper - Trying to load META-INF/validation.xml via TCCL
2025-07-20 01:24:14.742 [main] DEBUG o.h.v.internal.xml.config.ResourceLoaderHelper - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
2025-07-20 01:24:14.742 [main] DEBUG o.h.v.internal.xml.config.ValidationXmlParser - No META-INF/validation.xml found. Using annotation based configuration only.
2025-07-20 01:24:14.742 [main] DEBUG o.h.v.i.engine.resolver.TraversableResolvers - Cannot find javax.persistence.Persistence on classpath. Assuming non JPA 2 environment. All properties will per default be traversable.
2025-07-20 01:24:14.743 [main] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
2025-07-20 01:24:14.743 [main] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
2025-07-20 01:24:14.743 [main] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
2025-07-20 01:24:14.743 [main] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
2025-07-20 01:24:14.743 [main] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
2025-07-20 01:24:14.743 [main] DEBUG o.h.v.i.engine.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
2025-07-20 01:24:14.928 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-20 01:24:14.954 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: ServletContext resource [/index.html]
2025-07-20 01:24:14.992 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.AcceptanceResultController:
	{POST [/api/ysjg/list/{ysbh}]}: searchWithCondition(String,AcceptanceQueryDTO)
2025-07-20 01:24:14.996 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.ApprovalReminderController:
	{POST [/api/reminder/list]}: queryRemindableApprovals(ApprovalReminderQueryDTO)
	{POST [/api/reminder/send]}: sendReminder(List)
	{GET [/api/reminder/history]}: getReminderHistory(String,String,Integer,Integer)
	{POST [/api/reminder/my]}: queryMyReminders(MyReminderQueryDTO)
	{POST [/api/reminder/process/{grid}]}: markAsProcessed(Long)
	{GET [/api/reminder/deadlineBills/{days}]}: queryBillsNearDeadline(Integer)
	{POST [/api/reminder/approvedList]}: queryApprovedBills(ApprovedBillsQueryDTO)
	{POST [/api/reminder/allApproved]}: queryAllApprovedBills(AllApprovedBillsQueryDTO)
2025-07-20 01:24:14.998 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.AttachmentController:
	{POST [/api/attachment/upload]}: uploadAttachment(MultipartFile,AttachmentUploadDTO)
	{POST [/api/attachment/batch-upload]}: batchUploadAttachments(MultipartFile[],AttachmentUploadDTO)
	{GET [/api/attachment/list]}: queryAttachmentsByBill(String,String,String)
	{DELETE [/api/attachment/{fjid}]}: deleteAttachment(String)
	{GET [/api/attachment/download/{fjid}]}: downloadAttachment(String,HttpServletResponse)
	{GET [/api/attachment/preview/{fjid}]}: previewAttachment(String,HttpServletResponse)
	{GET [/api/attachment/list-with-directory]}: queryAttachmentsWithDirectoryAndDetail(String,String)
2025-07-20 01:24:14.999 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.AuditController:
	{GET [/api/audit/list]}: getUnifiedAuditList(UnifiedAuditQueryDTO)
2025-07-20 01:24:14.999 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.BMXXController:
	{POST [/api/dept/list]}: getDeptPage(DeptPageQueryDTO)
2025-07-20 01:24:15.001 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgdjController:
	{POST [/api/cgjg/save]}: save(CgjgSaveDTO)
	{GET [/api/cgjg/checkAuthority/{jgdjbh}]}: checkAuthority(String)
	{POST [/api/cgjg/modifyCgjg/{jgdjbh}]}: updateCgjg(String,CgjgSaveDTO)
	{GET [/api/cgjg/queryByJgdjbh/{jgdjbh}]}: getCgjgDetail(String)
	{GET [/api/cgjg/deleteByJgdjbh/{jgdjbh}]}: deleteCgjg(String)
	{GET [/api/cgjg/commit/{jgdjbh}]}: commitCgjg(String)
	{GET [/api/cgjg/list]}: getCgjgPageList(CgjgPageQueryDTO)
	{POST [/api/cgjg/checkCgjg/{jgdjbh}]}: auditCgjg(String,AuditParamJgdjbh)
	{GET [/api/cgjg/callback/{jgdjbh}]}: callBackCgjg(String)
	{POST [/api/cgjg/checkCallBack/{jgdjbh}]}: rejectCgjg(String,AuditParamJgdjbh)
	{GET [/api/cgjg/noAudit/{jgdjbh}]}: cancelCgjg(String)
	{GET [/api/cgjg/auditLog/{jgdjbh}]}: getAuditLog(String)
	{POST [/api/cgjg/approved]}: getApprovedCgzbsqList(CgjgPageQueryDTO)
	{GET [/api/cgjg/queryByMxxh/{mxxh}]}: getCgjgBymxxh(String)
2025-07-20 01:24:15.002 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgfsController:
	{GET [/api/cgfs/all]}: getAllCgfs()
	{GET [/api/cgfs/list]}: getCgfsPageList(CgfsPageQueryDTO)
2025-07-20 01:24:15.004 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgjhController:
	{POST [/api/cgjh/save]}: save(CgjhSaveDTO)
	{GET [/api/cgjh/commit/{jhbh}]}: commit(String)
	{GET [/api/cgjh/auditLog/{jhbh}]}: auditLog(String)
	{GET [/api/cgjh/callBack/{jhbh}]}: callBack(String)
	{GET [/api/cgjh/noAudit/{jhbh}]}: noAudit(String)
	{GET [/api/cgjh/list]}: getCgjhPageList(CgjhPageQueryDTO)
	{POST [/api/cgjh/modifyCgjh/{jhbh}]}: updateWithDetailedBudget(String,CgjhSaveDTO)
	{GET [/api/cgjh/queryByJhbh/{jhbh}]}: queryWithDetailedBudget(String)
	{GET [/api/cgjh/deleteByJhbh/{jhbh}]}: deleteWithDetailedBudget(String)
	{GET [/api/cgjh/checkAuthority/{jhbh}]}: isCheckedByAuthority(String)
	{POST [/api/cgjh/checkCgjh/{jhbh}]}: checkCgjh(String,AuditParam)
	{POST [/api/cgjh/checkCallBack/{jhbh}]}: checkCallBack(String,AuditParam)
	{POST [/api/cgjh/approved]}: getApprovedCgjhList(CgjhPageQueryDTO)
	{GET [/api/cgjh/cgjhlist]}: getCgjhList(CgjhPageQueryDTO)
	{GET [/api/cgjh/queryByMxxh/{mxxh}]}: queryByMxxh(String)
2025-07-20 01:24:15.004 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CglxController:
	{GET [/api/cglx/all]}: getAllCglx()
	{POST [/api/cglx/list]}: getCglxPageList(CglxPageQueryDTO)
2025-07-20 01:24:15.005 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgmlController:
	{POST [/api/cgml/list]}: pageQueryPost(CgmlPageQueryDTO)
2025-07-20 01:24:15.006 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgsqController:
	{POST [/api/cgsq/save]}: save(CgsqSaveDTO)
	{GET [/api/cgsq/checkAuthority/{sqbh}]}: checkAuthority(String)
	{GET [/api/cgsq/auditLog/{sqbh}]}: getAuditLog(String)
	{POST [/api/cgsq/modifyCgsq/{sqbh}]}: updateCgsq(String,CgsqSaveDTO)
	{GET [/api/cgsq/queryByDjbh/{sqbh}]}: getCgsqDetail(String)
	{GET [/api/cgsq/deleteByDjbh/{sqbh}]}: deleteCgsq(String)
	{GET [/api/cgsq/commit/{sqbh}]}: commitCgsq(String)
	{POST [/api/cgsq/checkCgsq/{sqbh}]}: auditCgsq(String,AuditParamSqbh)
	{GET [/api/cgsq/callback/{sqbh}]}: callBackCgsq(String)
	{POST [/api/cgsq/checkCallBack/{sqbh}]}: rejectCgsq(String,AuditParamSqbh)
	{GET [/api/cgsq/noAudit/{sqbh}]}: cancelCgsq(String)
	{GET [/api/cgsq/list]}: getCgsqPageList(CgsqPageQueryDTO)
	{POST [/api/cgsq/approved]}: getApprovedCgsqList(CgsqPageQueryDTO)
	{GET [/api/cgsq/queryByMxxh/{mxxh}]}: getCgsqByMxxh(String)
2025-07-20 01:24:15.008 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgysController:
	{POST [/api/cgys/save]}: save(CgysSaveDTO)
	{GET [/api/cgys/checkAuthority/{ysbh}]}: checkAuthority(String)
	{GET [/api/cgys/auditLog/{ysbh}]}: getAuditLog(String)
	{POST [/api/cgys/modifyCgys/{ysbh}]}: updateCgys(String,CgysSaveDTO)
	{GET [/api/cgys/queryByYsbh/{ysbh}]}: getCgysDetail(String)
	{GET [/api/cgys/deleteByYsbh/{ysbh}]}: deleteCgys(String)
	{GET [/api/cgys/commit/{ysbh}]}: commitCgys(String)
	{GET [/api/cgys/list]}: getCgysPageList(CgysPageQueryDTO)
	{POST [/api/cgys/checkCgys/{ysbh}]}: auditCgys(String,AuditParamYsbh)
	{GET [/api/cgys/callback/{ysbh}]}: callBackCgys(String)
	{POST [/api/cgys/checkCallBack/{ysbh}]}: rejectCgys(String,AuditParamYsbh)
	{GET [/api/cgys/noAudit/{ysbh}]}: cancelCgys(String)
	{GET [/api/cgys/queryByMxxh/{mxxh}]}: getCgysByMxxh(String)
	{GET [/api/cgys/cgyslist]}: getCgysList(CgysPageQueryDTO)
2025-07-20 01:24:15.009 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgzbsqController:
	{POST [/api/cgzbsq/save]}: save(CgzbsqSaveDTO)
	{GET [/api/cgzbsq/checkAuthority/{zbsqbh}]}: checkAuthority(String)
	{POST [/api/cgzbsq/checkCallBack/{zbsqbh}]}: rejectCgzbsq(String,AuditParamZbsqbh)
	{GET [/api/cgzbsq/noAudit/{zbsqbh}]}: cancelCgzbsq(String)
	{GET [/api/cgzbsq/list]}: getCgzbsqPageList(CgzbsqPageQueryDTO)
	{GET [/api/cgzbsq/queryByMxxh/{mxxh}]}: getCgzbsqBymxxh(String)
	{GET [/api/cgzbsq/auditLog/{zbsqbh}]}: getAuditLog(String)
	{POST [/api/cgzbsq/approved]}: getApprovedCgzbsqList(CgzbsqPageQueryDTO)
	{POST [/api/cgzbsq/modifyCgzbsq/{zbsqbh}]}: updateCgzbsq(String,CgzbsqSaveDTO)
	{GET [/api/cgzbsq/queryByZbsqbh/{zbsqbh}]}: getCgzbsqDetail(String)
	{GET [/api/cgzbsq/deleteByZbsqbh/{zbsqbh}]}: deleteCgzbsq(String)
	{GET [/api/cgzbsq/commit/{zbsqbh}]}: commitCgzbsq(String)
	{POST [/api/cgzbsq/checkCgzbsq/{zbsqbh}]}: auditCgzbsq(String,AuditParamZbsqbh)
	{GET [/api/cgzbsq/callback/{zbsqbh}]}: callBackCgzbsq(String)
2025-07-20 01:24:15.010 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgzjjgController:
	{GET [/api/cgzjjg/list]}: getCgzjjgPageList(CgzjjgPageQueryDTO)
2025-07-20 01:24:15.010 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgzjlyfsController:
	{GET [/api/cgzjlyfs/list]}: getCgzjlyfsPageList(CgzjlyfsPageQueryDTO)
2025-07-20 01:24:15.010 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.CgzzfsController:
	{GET [/api/cgzzfs/all]}: getAllCgzzfs()
	{GET [/api/cgzzfs/list]}: getCgzzfsPageList(CgzzfsPageQueryDTO)
2025-07-20 01:24:15.010 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.FlowTemplateController:
	{GET [/api/flow/template/getNodes]}: getNodes(Integer)
	{POST [/api/flow/template/saveNodes]}: saveNodes(CheckFlowTemplateBillVO)
	{POST [/api/flow/template/deleteNodes]}: deleteNodes(CheckFlowTemplateBillVO)
	{POST [/api/flow/template/cfl]}: checkFlowLegally(CheckFlowDto)
	{GET [/api/flow/template/getBillType]}: getBillType()
2025-07-20 01:24:15.011 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.IndexController:
	{GET [/api/index/qryIndex]}: qryIndex(IndexParams)
2025-07-20 01:24:15.011 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.LoginController:
	{GET [/api/loginOut]}: loginOut(HttpServletRequest,HttpServletResponse)
	{POST [/api/login]}: loginPost(LoginDTO,HttpServletRequest)
	{POST [/api/main]}: getSession()
2025-07-20 01:24:15.012 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.ModuleGivenController:
	{GET [/api/modelGiven/getAllPersonAndDeptList]}: getAllPersonAndDeptList(ModuleGivenDTO)
	{GET [/api/modelGiven/getIndexList]}: getIndexList(ModuleGivenDTO)
	{GET [/api/modelGiven/queryRolePermissions]}: queryRolePermissions(RoleDto)
	{POST [/api/modelGiven/saveRolePermissions]}: saveRolePermissions(RoleDto)
	{GET [/api/modelGiven/queryRoleUserList]}: queryRoleUserList(ModuleGivenDTO)
	{POST [/api/modelGiven/saveRoleUser]}: saveRoleUser(RoleDto)
	{POST [/api/modelGiven/deleteRoleUser]}: deleteRoleUser(RoleDto)
	{GET [/api/modelGiven/queryRoleIndexList]}: queryRoleIndexList(ModuleGivenDTO)
	{POST [/api/modelGiven/saveRoleIndex]}: saveRoleIndex(RoleDto)
	{POST [/api/modelGiven/deleteRoleIndex]}: deleteRoleIndex(RoleDto)
	{GET [/api/modelGiven/queryUserAuthorizeList]}: queryUserAuthorizeList()
2025-07-20 01:24:15.012 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.PubObjFlowController:
	{GET [/api/poft/list]}: getAuditNodeList()
2025-07-20 01:24:15.013 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.RoleController:
	{POST [/api/role/saveRole]}: saveRole(RoleDto)
	{GET [/api/role/getRoleList]}: getRoleList()
	{POST [/api/role/updateRoleList]}: upRoleList()
	{POST [/api/role/updateRole]}: updateRole(RoleDto)
	{POST [/api/role/deleteRole]}: deleteRole(RoleDto)
	{GET [/api/role/getShowPages]}: getShowPages()
2025-07-20 01:24:15.013 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.StatisticsController:
	{POST [/api/statistics/clear-cache]}: clearStatisticsCache()
	{GET [/api/statistics/bill-completion]}: getBillCompletionStatistics(BillCountStatisticsDTO)
2025-07-20 01:24:15.013 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.TodoController:
	{POST [/api/todo/myApplications]}: getMyApplications(TodoQueryDTO)
	{POST [/api/todo/myAudited]}: getMyAudited(TodoQueryDTO)
2025-07-20 01:24:15.013 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.YssjfsController:
	{GET [/api/yssjfs/list]}: getYssjfsPageList(YssjfsPageQueryDTO)
2025-07-20 01:24:15.014 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	c.g.g.h.c.ZYXXController:
	{POST [/api/zyxx/qryEmplyee]}: qrySalaryEmplyee(EmployeeDTO)
2025-07-20 01:24:15.015 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-20 01:24:15.020 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	o.s.w.a.OpenApiWebMvcResource:
	{GET [/api/v3/api-docs], produces [application/json]}: openapiJson(HttpServletRequest,String,Locale)
	{GET [/api/v3/api-docs.yaml], produces [application/vnd.oai.openapi]}: openapiYaml(HttpServletRequest,String,Locale)
2025-07-20 01:24:15.020 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	{GET [/swagger-ui.html]}: redirectToUi(HttpServletRequest)
2025-07-20 01:24:15.020 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 
	o.s.w.u.SwaggerConfigResource:
	{GET [/api/v3/api-docs/swagger-config], produces [application/json]}: openapiJson(HttpServletRequest)
2025-07-20 01:24:15.022 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 137 mappings in 'requestMappingHandlerMapping'
2025-07-20 01:24:15.042 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-07-20 01:24:15.067 [main] DEBUG _.s.web.servlet.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]], /static/**=ResourceHttpRequestHandler [classpath [static/]], /swagger-ui*/*swagger-initializer.js=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /swagger-ui*/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]]}
2025-07-20 01:24:15.084 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-20 01:24:15.110 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-20 01:24:15.117 [main] DEBUG c.g.x.k.s.configuration.Knife4jAutoConfiguration - Register Knife4jOpenApiCustomizer
2025-07-20 01:24:15.155 [main] DEBUG i.l.core.resource.AddressResolverGroupProvider - Starting with netty's non-blocking DNS resolver library
2025-07-20 01:24:15.157 [main] DEBUG io.lettuce.core.resource.KqueueProvider - Starting without optional kqueue library
2025-07-20 01:24:15.159 [main] DEBUG io.lettuce.core.resource.IOUringProvider - Starting without optional io_uring library
2025-07-20 01:24:15.159 [main] DEBUG io.lettuce.core.resource.EpollProvider - Starting without optional epoll library
2025-07-20 01:24:15.160 [main] DEBUG io.lettuce.core.resource.DefaultClientResources - -Dio.netty.eventLoopThreads: 8
2025-07-20 01:24:15.167 [main] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Creating executor io.netty.util.concurrent.DefaultEventExecutorGroup
2025-07-20 01:24:15.174 [main] DEBUG io.lettuce.core.event.jfr.EventRecorderHolder - Starting with JFR support
2025-07-20 01:24:15.283 [main] DEBUG c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-20 01:24:15.529 [main] DEBUG o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
2025-07-20 01:24:15.628 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-20 01:24:15.709 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8082 (http) with context path '/grp'
2025-07-20 01:24:15.724 [main] DEBUG o.s.b.a.l.ConditionEvaluationReportLoggingListener - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   AopAutoConfiguration matched:
      - @ConditionalOnProperty (spring.aop.auto=true) matched (OnPropertyCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:
      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   ApplicationAvailabilityAutoConfiguration#applicationAvailability matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.availability.ApplicationAvailability; SearchStrategy: all) did not find any beans (OnBeanCondition)

   AuditEventsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   BeansEndpointAutoConfiguration#beansEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.beans.BeansEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CacheMeterBinderProvidersConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.MeterBinder' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.JCacheCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.cache.jcache.JCacheCache', 'javax.cache.CacheManager' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.RedisCacheMeterBinderProviderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.cache.RedisCache' (OnClassCondition)

   CacheMetricsAutoConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) found bean 'cacheManager' (OnBeanCondition)

   CacheMetricsRegistrarConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.actuate.metrics.cache.CacheMeterBinderProvider,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'redissonCacheMeterBinderProvider', 'simpleMeterRegistry', 'redisCacheMeterBinderProvider', 'jCacheCacheMeterBinderProvider' (OnBeanCondition)

   CachesEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   CachesEndpointAutoConfiguration#cachesEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CompositeMeterRegistryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.composite.CompositeMeterRegistry' (OnClassCondition)

   ConditionsReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   ConditionsReportEndpointAutoConfiguration#conditionsReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.autoconfigure.condition.ConditionsReportEndpoint; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)
      - @ConditionalOnMissingBean (types: io.r2dbc.spi.ConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)
      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)

   DataSourceHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource; SearchStrategy: all) found bean 'dataSource' (OnBeanCondition)

   DataSourceHealthContributorAutoConfiguration#dbHealthContributor matched:
      - @ConditionalOnMissingBean (names: dbHealthIndicator,dbHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceInitializationConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer,org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: javax.sql.DataSource,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'simpleMeterRegistry', 'dataSource' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.DataSourcePoolMetadataMetricsConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.boot.jdbc.metadata.DataSourcePoolMetadataProvider; SearchStrategy: all) found bean 'hikariPoolDataSourceMetadataProvider' (OnBeanCondition)

   DataSourcePoolMetricsAutoConfiguration.HikariDataSourceMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DiskSpaceHealthContributorAutoConfiguration matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   DiskSpaceHealthContributorAutoConfiguration#diskSpaceHealthIndicator matched:
      - @ConditionalOnMissingBean (names: diskSpaceHealthIndicator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DynamicDataSourceAutoConfiguration matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dataSourceInitEvent matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.event.DataSourceInitEvent; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dsProcessor matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.processor.DsProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceAutoConfiguration#dynamicDatasourceAnnotationAdvisor matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.aop.enabled=true) matched (OnPropertyCondition)

   DynamicDataSourceAutoConfiguration#dynamicTransactionAdvisor matched:
      - @ConditionalOnProperty (spring.datasource.dynamic.seata=false) matched (OnPropertyCondition)

   DynamicDataSourceCreatorAutoConfiguration#dataSourceCreator matched:
      - @ConditionalOnMissingBean (types: com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DynamicDataSourceCreatorAutoConfiguration.DruidDataSourceCreatorConfiguration matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.HikariDataSourceCreatorConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnWarDeployment the application is not deployed as a WAR file. (OnWarDeploymentCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   EndpointAutoConfiguration#endpointCachingOperationInvokerAdvisor matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoker.cache.CachingOperationInvokerAdvisor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EndpointAutoConfiguration#endpointOperationParameterMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.invoke.ParameterValueMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   EnvironmentEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   GsonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)

   GsonAutoConfiguration#gson matched:
      - @ConditionalOnMissingBean (types: com.google.gson.Gson; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonAutoConfiguration#gsonBuilder matched:
      - @ConditionalOnMissingBean (types: com.google.gson.GsonBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GsonHttpMessageConvertersConfiguration matched:
      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)

   HealthContributorAutoConfiguration#pingHealthContributor matched:
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   HealthEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)

   HealthEndpointConfiguration#healthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthEndpointGroups matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointGroups; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthHttpCodeStatusMapper matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HttpCodeStatusMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointConfiguration#healthStatusAggregator matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.StatusAggregator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration#healthEndpointWebExtension matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.HealthEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HealthEndpointWebExtensionConfiguration.MvcAdditionalHealthEndpointPathsConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.web.exposure' property (OnAvailableEndpointCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   HeapDumpWebEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   HeapDumpWebEndpointAutoConfiguration#heapDumpWebEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.management.HeapDumpWebEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpClientMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (server.servlet.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpTraceEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   InfoEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   InfoEndpointAutoConfiguration#infoEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.info.InfoEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JCacheCacheConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)
      - Cache org.springframework.boot.autoconfigure.cache.JCacheCacheConfiguration automatic cache type (CacheCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter,org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxEndpointAutoConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxEndpointAutoConfiguration#endpointObjectNameFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.jmx.EndpointObjectNameFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxEndpointAutoConfiguration#jmxAnnotationEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.jmx.JmxEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxEndpointAutoConfiguration#jmxMBeanExporter matched:
      - @ConditionalOnSingleCandidate (types: javax.management.MBeanServer; SearchStrategy: all) found a single bean 'mbeanServer' (OnBeanCondition)

   JvmMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   JvmMetricsAutoConfiguration#classLoaderMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmGcMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmGcMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmHeapPressureMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmHeapPressureMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmMemoryMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JvmMetricsAutoConfiguration#jvmThreadMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   Knife4jAutoConfiguration matched:
      - @ConditionalOnProperty (knife4j.enable=true) matched (OnPropertyCondition)

   Knife4jAutoConfiguration#knife4jOpenApiCustomizer matched:
      - @ConditionalOnMissingBean (types: com.github.xiaoymin.knife4j.spring.extension.Knife4jOpenApiCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   Knife4jAutoConfiguration#knife4jOperationCustomizer matched:
      - @ConditionalOnMissingBean (types: com.github.xiaoymin.knife4j.spring.extension.Knife4jOperationCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LettuceConnectionConfiguration matched:
      - @ConditionalOnClass found required class 'io.lettuce.core.RedisClient' (OnClassCondition)
      - @ConditionalOnProperty (spring.redis.client-type=lettuce) matched (OnPropertyCondition)

   LettuceConnectionConfiguration#lettuceClientResources matched:
      - @ConditionalOnMissingBean (types: io.lettuce.core.resource.ClientResources; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LettuceMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.lettuce.core.RedisClient', 'io.lettuce.core.metrics.MicrometerCommandLatencyRecorder' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   LettuceMetricsAutoConfiguration#micrometerOptions matched:
      - @ConditionalOnMissingBean (types: io.lettuce.core.metrics.MicrometerOptions; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:
      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   LogFileWebEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   LogbackMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.MeterRegistry', 'ch.qos.logback.classic.LoggerContext', 'org.slf4j.LoggerFactory' (OnClassCondition)
      - LogbackLoggingCondition ILoggerFactory is a Logback LoggerContext (LogbackMetricsAutoConfiguration.LogbackLoggingCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   LogbackMetricsAutoConfiguration#logbackMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.logging.LogbackMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   LoggersEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   LoggersEndpointAutoConfiguration#loggersEndpoint matched:
      - Logging System enabled (LoggersEndpointAutoConfiguration.OnEnabledLoggingSystemCondition)
      - @ConditionalOnBean (types: org.springframework.boot.logging.LoggingSystem; SearchStrategy: all) found bean 'springBootLoggingSystem'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.logging.LoggersEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ManagementContextAutoConfiguration.SameManagementContextConfiguration matched:
      - Management Port actual port type (SAME) matched required type (OnManagementPortCondition)

   MappingsEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ServletWebConfiguration.SpringMvcConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   MetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)

   MetricsAutoConfiguration#micrometerClock matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MetricsEndpointAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.annotation.Timed' (OnClassCondition)
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   MetricsEndpointAutoConfiguration#metricsEndpoint matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.MetricsEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a single bean 'dataSource' (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisPlusLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NamedParameterJdbcTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a single bean 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   NettyAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.netty.util.NettyRuntime' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ReactiveHealthEndpointConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.actuate.health.HealthEndpoint; SearchStrategy: all) found bean 'healthEndpoint' (OnBeanCondition)

   ReactiveHealthEndpointConfiguration#reactiveHealthContributorRegistry matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.health.ReactiveHealthContributorRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - Cache org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration automatic cache type (CacheCondition)

   RedisHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory' (OnBeanCondition)

   RedisReactiveAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'org.springframework.data.redis.core.ReactiveRedisTemplate', 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisReactiveAutoConfiguration#reactiveRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (names: reactiveRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveAutoConfiguration#reactiveStringRedisTemplate matched:
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (names: reactiveStringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'reactor.core.publisher.Flux' (OnClassCondition)
      - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory' (OnBeanCondition)

   RedisReactiveHealthContributorAutoConfiguration#redisHealthContributor matched:
      - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedisRepositoriesAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)
      - @ConditionalOnProperty (spring.data.redis.repositories.enabled=true) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redissonConnectionFactory'; @ConditionalOnMissingBean (types: org.springframework.data.redis.repository.support.RedisRepositoryFactoryBean; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.redisson.Redisson', 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedissonAutoConfiguration#redisTemplate matched:
      - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redisson matched:
      - @ConditionalOnMissingBean (types: org.redisson.api.RedissonClient; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#redissonConnectionFactory matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonAutoConfiguration#stringRedisTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RedissonCacheStatisticsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.boot.actuate.metrics.cache.CacheMeterBinderProvider', 'org.redisson.spring.cache.RedissonCache' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) found bean 'cacheManager' (OnBeanCondition)

   RepositoryMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.repository.Repository' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#metricsRepositoryMethodInvocationListener matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.MetricsRepositoryMethodInvocationListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RepositoryMetricsAutoConfiguration#repositoryTagsProvider matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.data.RepositoryTagsProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on RestTemplateAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (RestTemplateAutoConfiguration.NotReactiveWebApplicationCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration#restTemplateBuilderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   RestTemplateMetricsConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) found bean 'restTemplateBuilder' (OnBeanCondition)

   RestTemplateMetricsConfiguration#restTemplateExchangeTagsProvider matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.web.client.RestTemplateExchangeTagsProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ScheduledTasksEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   ScheduledTasksEndpointAutoConfiguration#scheduledTasksEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.scheduling.ScheduledTasksEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SecurityAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityAutoConfiguration#authenticationEventPublisher matched:
      - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationEventPublisher; SearchStrategy: all) did not find any beans (OnBeanCondition)

   org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer', 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityFilterAutoConfiguration#securityFilterChainRegistration matched:
      - @ConditionalOnBean (names: springSecurityFilterChain; SearchStrategy: all) found bean 'springSecurityFilterChain' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath; SearchStrategy: all) found bean 'dispatcherServletRegistration' (OnBeanCondition)

   SecurityRequestMatchersManagementContextConfiguration.MvcRequestMatcherConfiguration#requestMatcherProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.security.servlet.RequestMatcherProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletEndpointManagementContextConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   ServletEndpointManagementContextConfiguration.WebMvcServletEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)

   ServletManagementContextAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.Servlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   SimpleMetricsExportAutoConfiguration matched:
      - @ConditionalOnEnabledMetricsExport management.metrics.export.defaults.enabled is considered true (OnMetricsExportEnabledCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.Clock; SearchStrategy: all) found bean 'micrometerClock'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SimpleMetricsExportAutoConfiguration#simpleConfig matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.simple.SimpleConfig; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringApplicationAdminJmxAutoConfiguration matched:
      - @ConditionalOnProperty (spring.application.admin.enabled=true) matched (OnPropertyCondition)

   SpringApplicationAdminJmxAutoConfiguration#springApplicationAdminRegistrar matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringBootWebSecurityConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   SpringBootWebSecurityConfiguration.ErrorPageSecurityFilterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.security.web.access.WebInvocationPrivilegeEvaluator' (OnClassCondition)
      - @ConditionalOnBean (types: org.springframework.security.web.access.WebInvocationPrivilegeEvaluator; SearchStrategy: all) found bean 'privilegeEvaluator' (OnBeanCondition)

   SpringDataWebAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#pageableCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDataWebAutoConfiguration#sortCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfigProperties matched:
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   SpringDocConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   SpringDocConfiguration#fileSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.FileSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#openAPIBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.OpenAPIService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#operationBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.OperationService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#parameterBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.GenericParameterService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#polymorphicModelConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.polymorphic-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PolymorphicModelConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#requestBodyBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.RequestBodyService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#responseSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.ResponseSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#schemaPropertyDeprecatingConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.deprecating-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SchemaPropertyDeprecatingConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#securityParser matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.SecurityService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.SpringDocCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocObjectMapperProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.ObjectMapperProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration#springDocProviders matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.SpringDocProviders; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocPageableConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Pageable' (OnClassCondition)

   SpringDocConfiguration.SpringDocPageableConfiguration#pageableOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.pageable-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.PageableOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocSortConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.data.domain.Sort' (OnClassCondition)

   SpringDocConfiguration.SpringDocSortConfiguration#delegatingMethodParameterCustomizer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DelegatingMethodParameterCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocSortConfiguration#sortOpenAPIConverter matched:
      - @ConditionalOnProperty (springdoc.model-converters.sort-converter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.SortOpenAPIConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocSpringDataWebPropertiesProvider#springDataWebPropertiesProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringDataWebPropertiesProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration matched:
      - @ConditionalOnClass found required class 'reactor.core.publisher.Flux' (OnClassCondition)

   SpringDocConfiguration.SpringDocWebFluxSupportConfiguration#webFluxSupportConverter matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.converters.WebFluxSupportConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocConfiguration.WebConversionServiceConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.autoconfigure.web.format.WebConversionService' (OnClassCondition)

   SpringDocUIConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnExpression (#{${springdoc.api-docs.enabled:true}}) resulted in true (OnExpressionCondition)
      - @ConditionalOnBean (types: org.springdoc.core.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.SpringDocConfiguration' (OnBeanCondition)

   SpringDocWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.SpringDocConfiguration' (OnBeanCondition)

   SpringDocWebMvcConfiguration#openApiResource matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.api.OpenApiWebMvcResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#requestBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.RequestService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#responseBuilder matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.GenericResponseService; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration#springWebProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringWebProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.function.RouterFunction' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcRouterConfiguration#routerFunctionProvider matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.core.RouterFunctionWebMvcProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SqlInitializationAutoConfiguration matched:
      - @ConditionalOnProperty (spring.sql.init.enabled) matched (OnPropertyCondition)
      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)

   StartupTimeMetricsListenerAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   StartupTimeMetricsListenerAutoConfiguration#startupTimeMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.startup.StartupTimeMetricsListener; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.SpringDocConfiguration' (OnBeanCondition)

   SwaggerConfig#indexPageTransformer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerIndexTransformer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerConfigResource matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerConfigResource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerResourceResolver matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerResourceResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerWebMvcConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerWebMvcConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerConfig#swaggerWelcome matched:
      - @ConditionalOnProperty (springdoc.use-management-port=false) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springdoc.webmvc.ui.SwaggerWelcomeWebMvc; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SwaggerUiConfigParameters matched:
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.SpringDocConfiguration' (OnBeanCondition)

   SwaggerUiConfigProperties matched:
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.SpringDocConfiguration' (OnBeanCondition)

   SwaggerUiOAuthProperties matched:
      - @ConditionalOnProperty (springdoc.swagger-ui.enabled) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: org.springdoc.core.SpringDocConfiguration; SearchStrategy: all) found bean 'org.springdoc.core.SpringDocConfiguration' (OnBeanCondition)

   SystemMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.MeterRegistry' (OnClassCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   SystemMetricsAutoConfiguration#diskSpaceMetrics matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.system.DiskSpaceMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#fileDescriptorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.FileDescriptorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#processorMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.ProcessorMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   SystemMetricsAutoConfiguration#uptimeMetrics matched:
      - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.system.UptimeMetrics; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutorMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics' (OnClassCondition)
      - @ConditionalOnBean (types: java.util.concurrent.Executor,io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans 'applicationTaskExecutor', 'simpleMeterRegistry' (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThreadDumpEndpointAutoConfiguration matched:
      - @ConditionalOnAvailableEndpoint marked as exposed by a 'management.endpoints.jmx.exposure' property (OnAvailableEndpointCondition)

   ThreadDumpEndpointAutoConfiguration#dumpEndpoint matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.management.ThreadDumpEndpoint; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TomcatMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'io.micrometer.core.instrument.binder.tomcat.TomcatMetrics', 'org.apache.catalina.Manager' (OnClassCondition)
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   TomcatMetricsAutoConfiguration#tomcatMetricsBinder matched:
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry'; @ConditionalOnMissingBean (types: io.micrometer.core.instrument.binder.tomcat.TomcatMetrics,org.springframework.boot.actuate.metrics.web.tomcat.TomcatMetricsBinder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a single bean 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration#controllerEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#endpointMediaTypes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.EndpointMediaTypes; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#pathMappedEndpoints matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.PathMappedEndpoints; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration#webEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)

   WebEndpointAutoConfiguration.WebEndpointServletConfiguration#servletEndpointDiscoverer matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:
      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:
      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:
      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet,org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier; SearchStrategy: all) found beans 'webEndpointDiscoverer', 'dispatcherServlet' (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#controllerEndpointHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.ControllerEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#webEndpointServletHandlerMapping matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcMetricsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found bean 'simpleMeterRegistry' (OnBeanCondition)

   WebMvcMetricsAutoConfiguration#webMvcMetricsFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcMetricsAutoConfiguration#webMvcTagsProvider matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.actuate.metrics.web.servlet.WebMvcTagsProvider; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   AopAutoConfiguration.ClassProxyingConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)

   AppOpticsMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.appoptics.AppOpticsMeterRegistry' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AtlasMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.atlas.AtlasMeterRegistry' (OnClassCondition)

   AuditAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnProperty (management.auditevents.enabled) matched (OnPropertyCondition)

   AuditEventsEndpointAutoConfiguration#auditEventsEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.audit.AuditEventRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.audit.AuditEventRepository (OnBeanCondition)

   AvailabilityHealthContributorAutoConfiguration#livenessStateHealthIndicator:
      Did not match:
         - @ConditionalOnProperty (management.health.livenessstate.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   AvailabilityHealthContributorAutoConfiguration#readinessStateHealthIndicator:
      Did not match:
         - @ConditionalOnProperty (management.health.readinessstate.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   AvailabilityProbesAutoConfiguration:
      Did not match:
         - Probes availability not running on a supported cloud platform (AvailabilityProbesAutoConfiguration.ProbesCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   Cache2kCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.cache2k.Cache2kBuilder' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (names: cacheResolver types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans of type 'org.springframework.cache.CacheManager' cacheManager (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CacheMeterBinderProvidersConfiguration.Cache2kCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.cache2k.Cache2kBuilder', 'org.cache2k.extra.spring.SpringCache2kCache', 'org.cache2k.extra.micrometer.Cache2kCacheMetrics' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.CaffeineCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Cache' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.EhCache2CacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'net.sf.ehcache.Ehcache' (OnClassCondition)

   CacheMeterBinderProvidersConfiguration.HazelcastCacheMeterBinderProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.spring.cache.HazelcastCache', 'com.hazelcast.core.Hazelcast' (OnClassCondition)

   CachesEndpointAutoConfiguration#cachesEndpointWebExtension:
      Did not match:
         - @ConditionalOnAvailableEndpoint no 'management.endpoints' property marked it as exposed (OnAvailableEndpointCondition)
      Matched:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.cache.CachesEndpoint; SearchStrategy: all) found bean 'cachesEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.cache.CachesEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Caffeine' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudFoundryActuatorAutoConfiguration:
      Did not match:
         - @ConditionalOnCloudPlatform did not find CLOUD_FOUNDRY (OnCloudPlatformCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
         - found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnProperty (management.cloudfoundry.enabled) matched (OnPropertyCondition)

   CodecsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CompositeMeterRegistryConfiguration:
      Did not match:
         - NoneNestedConditions 1 matched 1 did not; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.SingleInjectableMeterRegistry @ConditionalOnSingleCandidate (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found a single bean 'simpleMeterRegistry'; NestedCondition on CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition.NoMeterRegistryCondition @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' simpleMeterRegistry (CompositeMeterRegistryConfiguration.MultipleNonPrimaryMeterRegistriesCondition)

   ConfigurationPropertiesReportEndpointAutoConfiguration#configurationPropertiesReportEndpointWebExtension:
      Did not match:
         - @ConditionalOnAvailableEndpoint no 'management.endpoints' property marked it as exposed (OnAvailableEndpointCondition)
      Matched:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpoint; SearchStrategy: all) found bean 'configurationPropertiesReportEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.context.properties.ConfigurationPropertiesReportEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ConnectionFactoryHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   ConnectionPoolMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.pool.ConnectionPool' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'type' (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Generic:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)

   DataSourceConfiguration.OracleUcp:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DatadogMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.datadog.DatadogMeterRegistry' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidDynamicDataSourceConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.BeeCpDataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'cn.beecp.BeeDataSource' (OnClassCondition)

   DynamicDataSourceCreatorAutoConfiguration.Dbcp2DataSourceCreatorConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DynatraceMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.dynatrace.DynatraceMeterRegistry' (OnClassCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'net.sf.ehcache.Cache' (OnClassCondition)

   ElasticMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.elastic.ElasticMeterRegistry' (OnClassCondition)

   ElasticSearchReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient' (OnClassCondition)

   ElasticSearchRestHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClientBuilder' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClientSettings' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   EnvironmentEndpointAutoConfiguration#environmentEndpointWebExtension:
      Did not match:
         - @ConditionalOnAvailableEndpoint no 'management.endpoints' property marked it as exposed (OnAvailableEndpointCondition)
      Matched:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.env.EnvironmentEndpoint; SearchStrategy: all) found bean 'environmentEndpoint'; @ConditionalOnMissingBean (types: org.springframework.boot.actuate.env.EnvironmentEndpointWebExtension; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FlywayEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GangliaMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.ganglia.GangliaMeterRegistry' (OnClassCondition)

   GraphQlAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlRSocketAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQueryByExampleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlReactiveQuerydslAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebFluxSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphQlWebMvcSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   GraphiteMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.graphite.GraphiteMeterRegistry' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration.GsonHttpMessageConverterConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.JacksonJsonbUnavailable NoneNestedConditions 1 matched 1 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JsonbPreferred @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jsonb) did not find property 'spring.mvc.converters.preferred-json-mapper'; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JacksonAvailable @ConditionalOnBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) found bean 'mappingJackson2HttpMessageConverter'; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.GsonPreferred @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=gson) did not find property 'spring.mvc.converters.preferred-json-mapper' (GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJCacheCustomizationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HealthEndpointReactiveWebExtensionConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   HealthEndpointWebExtensionConfiguration.JerseyAdditionalHealthEndpointPathsConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HibernateMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManagerFactory' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HttpTraceAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.trace.http.HttpTraceRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.trace.http.HttpTraceRepository (OnBeanCondition)
      Matched:
         - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnProperty (management.trace.http.enabled) matched (OnPropertyCondition)

   HttpTraceAutoConfiguration.ReactiveTraceFilterConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)
         - Ancestor org.springframework.boot.actuate.autoconfigure.trace.http.HttpTraceAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   HttpTraceAutoConfiguration.ServletTraceFilterConfiguration:
      Did not match:
         - Ancestor org.springframework.boot.actuate.autoconfigure.trace.http.HttpTraceAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   HttpTraceEndpointAutoConfiguration#httpTraceEndpoint:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.boot.actuate.trace.http.HttpTraceRepository; SearchStrategy: all) did not find any beans of type org.springframework.boot.actuate.trace.http.HttpTraceRepository (OnBeanCondition)

   HumioMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.humio.HumioMeterRegistry' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)

   IdentifierGeneratorAutoConfiguration.InetUtilsAutoConfig:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.commons.util.InetUtils' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   InfluxDbHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   InfluxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.influx.InfluxMeterRegistry' (OnClassCondition)

   InfoContributorAutoConfiguration#buildInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.BuildProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#envInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.env.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#gitInfoContributor:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.boot.info.GitProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)
      Matched:
         - @ConditionalOnEnabledInfoContributor management.info.defaults.enabled is considered true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#javaInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.java.enabled is not true (OnEnabledInfoContributorCondition)

   InfoContributorAutoConfiguration#osInfoContributor:
      Did not match:
         - @ConditionalOnEnabledInfoContributor management.info.os.enabled is not true (OnEnabledInfoContributorCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   IntegrationGraphEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.graph.IntegrationGraphServer' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)

   JedisConnectionConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found beans of type 'org.springframework.data.redis.connection.RedisConnectionFactory' redissonConnectionFactory (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required classes 'org.apache.commons.pool2.impl.GenericObjectPool', 'org.springframework.data.redis.connection.jedis.JedisConnection', 'redis.clients.jedis.Jedis' (OnClassCondition)
         - @ConditionalOnProperty (spring.redis.client-type=jedis) matched (OnPropertyCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JerseySameManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JerseyServerMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.core.instrument.binder.jersey.server.MetricsApplicationEventListener' (OnClassCondition)

   JerseyWebEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   JettyMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.server.Server' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JmsHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   JmxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.jmx.JmxMeterRegistry' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JodConverterLocalAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (jodconverter.local.enabled=true) did not find property 'enabled' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.jodconverter.local.LocalConverter' (OnClassCondition)

   JodConverterRemoteAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jodconverter.remote.RemoteConverter' (OnClassCondition)

   JolokiaEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jolokia.http.AgentServlet' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   KafkaMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.ProducerFactory' (OnClassCondition)

   KairosMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.kairos.KairosMeterRegistry' (OnClassCondition)

   Knife4jAutoConfiguration#corsFilter:
      Did not match:
         - @ConditionalOnProperty (knife4j.cors=true) did not find property 'knife4j.cors' (OnPropertyCondition)

   Knife4jAutoConfiguration#productionSecurityFilter:
      Did not match:
         - @ConditionalOnProperty (knife4j.production=true) did not find property 'knife4j.production' (OnPropertyCondition)

   Knife4jAutoConfiguration#securityBasicAuthFilter:
      Did not match:
         - @ConditionalOnProperty (knife4j.basic.enable=true) found different value in property 'knife4j.basic.enable' (OnPropertyCondition)

   Knife4jInsightAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (knife4j.insight.enable=true) did not find property 'knife4j.insight.enable' (OnPropertyCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.LdapOperations' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LettuceConnectionConfiguration#redisConnectionFactory:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found beans of type 'org.springframework.data.redis.connection.RedisConnectionFactory' redissonConnectionFactory (OnBeanCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   LiquibaseEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.integration.spring.SpringLiquibase' (OnClassCondition)

   Log4J2MetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.logging.log4j.core.LoggerContext' (OnClassCondition)

   LogFileWebEndpointAutoConfiguration#logFileWebEndpoint:
      Did not match:
         - Log File did not find logging file (LogFileWebEndpointAutoConfiguration.LogFileCondition)

   MailHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.mail.javamail.JavaMailSenderImpl; SearchStrategy: all) did not find any beans of type org.springframework.mail.javamail.JavaMailSenderImpl (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnClassCondition)
         - @ConditionalOnEnabledHealthIndicator management.health.defaults.enabled is considered true (OnEnabledHealthIndicatorCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.mail.test-connection) did not find property 'test-connection' (OnPropertyCondition)

   ManagementContextAutoConfiguration.DifferentManagementContextConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   ManagementWebSecurityAutoConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter,org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter' securityConfig; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)

   MappingsEndpointAutoConfiguration.ReactiveWebConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MetricFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'javax.servlet.ServletRegistration' (OnClassCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.MongoTemplate' (OnClassCondition)

   MongoMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClientSettings' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.mongodb.core.ReactiveMongoTemplate' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MultipartAutoConfiguration#multipartConfigElement:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) found beans of type 'javax.servlet.MultipartConfigElement' multipartConfigElement (OnBeanCondition)

   MultipleOpenApiSupportConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 0 matched 2 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.GroupedOpenApi; SearchStrategy: all) did not find any beans of type org.springdoc.core.GroupedOpenApi (MultipleOpenApiSupportCondition)
      Matched:
         - found 'session' scope (OnWebApplicationCondition)
         - @ConditionalOnProperty (springdoc.api-docs.enabled) matched (OnPropertyCondition)

   MultipleOpenApiSupportConfiguration.SpringDocWebMvcActuatorDifferentConfiguration:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)
         - Ancestor org.springdoc.webmvc.core.MultipleOpenApiSupportConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisPlusLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)

   NewRelicMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.newrelic.NewRelicMeterRegistry' (OnClassCondition)

   NoOpMeterRegistryConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: io.micrometer.core.instrument.MeterRegistry; SearchStrategy: all) found beans of type 'io.micrometer.core.instrument.MeterRegistry' simpleMeterRegistry (OnBeanCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.BearerTokenAuthenticationToken' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   PrometheusMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.prometheus.PrometheusMeterRegistry' (OnClassCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   QuartzEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   R2dbcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)

   R2dbcInitializationConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)

   R2dbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)

   R2dbcTransactionManagerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)

   RSocketGraphQlClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'graphql.GraphQL' (OnClassCondition)

   RSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketRequesterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RSocketSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)

   RSocketServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)

   RSocketStrategiesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   RabbitHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.amqp.rabbit.core.RabbitTemplate' (OnClassCondition)

   RabbitMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.ConnectionFactory' (OnClassCondition)

   ReactiveCloudFoundryActuatorAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient' (OnClassCondition)

   ReactiveElasticsearchRestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)

   ReactiveManagementContextAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveManagementWebSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveMultipartAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.registration.ClientRegistration' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.ReactiveUserDetailsServiceCondition.ReactiveWebApplicationCondition did not find reactive web application classes; NestedCondition on ReactiveUserDetailsServiceAutoConfiguration.ReactiveUserDetailsServiceCondition.RSocketSecurityEnabledCondition @ConditionalOnBean (types: org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler; SearchStrategy: all) did not find any beans of type org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler (ReactiveUserDetailsServiceAutoConfiguration.ReactiveUserDetailsServiceCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   RedisAutoConfiguration#redisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) found beans named redisTemplate (OnBeanCondition)

   RedisAutoConfiguration#stringRedisTemplate:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) found beans of type 'org.springframework.data.redis.core.StringRedisTemplate' stringRedisTemplate (OnBeanCondition)

   RedisHealthContributorAutoConfiguration#redisHealthContributor:
      Did not match:
         - @ConditionalOnMissingBean (names: redisHealthIndicator,redisHealthContributor; SearchStrategy: all) found beans named redisHealthContributor (OnBeanCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   Saml2RelyingPartyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)

   SecurityDataConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.data.repository.query.SecurityEvaluationContextExtension' (OnClassCondition)

   org.springframework.boot.legacy.context.web.SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'javax.servlet.AsyncContext' (OnClassCondition)

   SecurityRequestMatchersManagementContextConfiguration.JerseyRequestMatcherConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletEndpointManagementContextConfiguration.JerseyServletEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.ResourceConfig' (OnClassCondition)

   ServletManagementContextAutoConfiguration.ApplicationContextFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (management.server.add-application-context-header=true) did not find property 'add-application-context-header' (OnPropertyCondition)

   ServletWebServerFactoryAutoConfiguration.ForwardedHeaderFilterConfiguration:
      Did not match:
         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SessionsEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.FindByIndexNameSessionRepository' (OnClassCondition)

   ShutdownEndpointAutoConfiguration:
      Did not match:
         - @ConditionalOnAvailableEndpoint no property management.endpoint.shutdown.enabled found so using endpoint default of false (OnAvailableEndpointCondition)

   SignalFxMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.signalfx.SignalFxMeterRegistry' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrHealthContributorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringBootWebSecurityConfiguration.SecurityFilterChainConfiguration:
      Did not match:
         - AllNestedConditions 1 matched 1 did not; NestedCondition on DefaultWebSecurityCondition.Beans @ConditionalOnMissingBean (types: org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter,org.springframework.security.web.SecurityFilterChain; SearchStrategy: all) found beans of type 'org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter' securityConfig; NestedCondition on DefaultWebSecurityCondition.Classes @ConditionalOnClass found required classes 'org.springframework.security.web.SecurityFilterChain', 'org.springframework.security.config.annotation.web.builders.HttpSecurity' (DefaultWebSecurityCondition)

   SpringBootWebSecurityConfiguration.WebSecurityEnablerConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (names: springSecurityFilterChain; SearchStrategy: all) found beans named springSecurityFilterChain (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   SpringDocConfiguration#propertiesResolverForSchema:
      Did not match:
         - @ConditionalOnProperty (springdoc.api-docs.resolve-schema-properties) did not find property 'springdoc.api-docs.resolve-schema-properties' (OnPropertyCondition)

   SpringDocConfiguration#propertyCustomizingConverter:
      Did not match:
         - @ConditionalOnBean (types: org.springdoc.core.customizers.PropertyCustomizer; SearchStrategy: all) did not find any beans of type org.springdoc.core.customizers.PropertyCustomizer (OnBeanCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor:
      Did not match:
         - AnyNestedCondition 0 matched 2 did not; NestedCondition on CacheOrGroupedOpenApiCondition.OnCacheDisabled found non-matching nested conditions @ConditionalOnProperty (springdoc.cache.disabled) did not find property 'springdoc.cache.disabled'; NestedCondition on CacheOrGroupedOpenApiCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 0 matched 2 did not; NestedCondition on MultipleOpenApiSupportCondition.OnActuatorDifferentPort found non-matching nested conditions Management Port actual port type (SAME) did not match required type (DIFFERENT), @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator'; NestedCondition on MultipleOpenApiSupportCondition.OnMultipleOpenApiSupportCondition AnyNestedCondition 0 matched 2 did not; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupConfigProperty @ConditionalOnProperty (springdoc.group-configs[0].group) did not find property 'springdoc.group-configs[0].group'; NestedCondition on MultipleOpenApiGroupsCondition.OnGroupedOpenApiBean @ConditionalOnBean (types: org.springdoc.core.GroupedOpenApi; SearchStrategy: all) did not find any beans of type org.springdoc.core.GroupedOpenApi (CacheOrGroupedOpenApiCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)

   SpringDocConfiguration#springdocBeanFactoryPostProcessor2:
      Did not match:
         - @ConditionalOnMissingClass found unwanted class 'org.springframework.boot.context.properties.bind.BindResult' (OnClassCondition)

   SpringDocConfiguration.SpringDocActuatorConfiguration:
      Did not match:
         - @ConditionalOnProperty (springdoc.show-actuator) did not find property 'springdoc.show-actuator' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties' (OnClassCondition)

   SpringDocConfiguration.SpringDocFunctionCatalogConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.function.web.function.FunctionEndpointInitializer' (OnClassCondition)

   SpringDocConfiguration.SpringDocPageableConfiguration#delegatingMethodParameterCustomizer:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.customizers.DelegatingMethodParameterCustomizer; SearchStrategy: all) found beans of type 'org.springdoc.core.customizers.DelegatingMethodParameterCustomizer' delegatingMethodParameterCustomizer (OnBeanCondition)

   SpringDocConfiguration.SpringDocRepositoryRestConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.core.config.RepositoryRestConfiguration' (OnClassCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#actuatorProvider:
      Did not match:
         - @ConditionalOnExpression (#{${springdoc.show-actuator:false} or ${springdoc.use-management-port:false}}) resulted in false (OnExpressionCondition)

   SpringDocWebMvcConfiguration.SpringDocWebMvcActuatorConfiguration#openApiActuatorResource:
      Did not match:
         - @ConditionalOnProperty (springdoc.use-management-port) did not find property 'springdoc.use-management-port' (OnPropertyCondition)

   StackdriverMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.stackdriver.StackdriverMeterRegistry' (OnClassCondition)

   StartupEndpointAutoConfiguration:
      Did not match:
         - ApplicationStartup configured applicationStartup is of type class org.springframework.core.metrics.DefaultApplicationStartup, expected BufferingApplicationStartup. (StartupEndpointAutoConfiguration.ApplicationStartupCondition)

   StatsdMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.micrometer.statsd.StatsdMeterRegistry' (OnClassCondition)

   SwaggerConfig#springWebProvider:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.providers.SpringWebProvider; SearchStrategy: all) found beans of type 'org.springdoc.core.providers.SpringWebProvider' springWebProvider (OnBeanCondition)

   SwaggerConfig#swaggerUiConfigParameters:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springdoc.core.SwaggerUiConfigParameters; SearchStrategy: all) found beans of type 'org.springdoc.core.SwaggerUiConfigParameters' org.springdoc.core.SwaggerUiConfigParameters (OnBeanCondition)

   SwaggerConfig#swaggerUiHome:
      Did not match:
         - @ConditionalOnProperty (springdoc.swagger-ui.use-root-path=true) did not find property 'springdoc.swagger-ui.use-root-path' (OnPropertyCondition)

   SwaggerConfig.SwaggerActuatorWelcomeConfiguration:
      Did not match:
         - @ConditionalOnProperty (springdoc.use-management-port) did not find property 'springdoc.use-management-port' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping' (OnClassCondition)

   TaskSchedulingAutoConfiguration#scheduledBeanLazyInitializationExcludeFilter:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.spring5.SpringTemplateEngine' (OnClassCondition)

   TransactionAutoConfiguration#transactionalOperator:
      Did not match:
         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.AspectJTransactionManagementConfiguration:
      Did not match:
         - @ConditionalOnBean did not find required type 'org.springframework.transaction.aspectj.AbstractTransactionAspect' (OnBeanCondition)
         - @ConditionalOnBean (types: org.springframework.transaction.aspectj.AbstractTransactionAspect; SearchStrategy: all) did not find any beans of type org.springframework.transaction.aspectj.AbstractTransactionAspect (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.security.authentication.AuthenticationManager,org.springframework.security.authentication.AuthenticationProvider,org.springframework.security.core.userdetails.UserDetailsService,org.springframework.security.authentication.AuthenticationManagerResolver,org.springframework.security.oauth2.jwt.JwtDecoder,org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector,org.springframework.security.oauth2.client.registration.ClientRegistrationRepository,org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository; SearchStrategy: all) found beans of type 'org.springframework.security.authentication.AuthenticationManager' authenticationManagerBean and found beans of type 'org.springframework.security.core.userdetails.UserDetailsService' userDetailsServiceImpl and found beans of type 'org.springframework.security.authentication.AuthenticationProvider' customAuthenticationProvider (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WavefrontMetricsExportAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.wavefront.sdk.common.WavefrontSender' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebClientMetricsConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebFluxEndpointManagementContextConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   WebFluxMetricsAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) did not find property 'enabled' (OnPropertyCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcEndpointManagementContextConfiguration#managementHealthEndpointWebMvcHandlerMapping:
      Did not match:
         - Management Port actual port type (SAME) did not match required type (DIFFERENT) (OnManagementPortCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.client.core.WebServiceTemplate' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSessionIdResolverAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.Jetty10WebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.websocket.javax.server.internal.JavaxWebSocketServerContainer', 'org.eclipse.jetty.websocket.server.JettyWebSocketServerContainer' (OnClassCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.availability.AvailabilityHealthContributorAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.info.InfoContributorAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    com.baomidou.mybatisplus.autoconfigure.IdentifierGeneratorAutoConfiguration

    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.metrics.integration.IntegrationMetricsAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration

    org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration

    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



2025-07-20 01:24:15.729 [main] DEBUG o.s.b.c.p.s.ConfigurationPropertySourcesPropertyResolver$DefaultResolver - Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-07-20 01:24:15.777 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：org.springdoc.webmvc.ui.SwaggerConfigResource
2025-07-20 01:24:15.786 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：org.springdoc.webmvc.api.OpenApiWebMvcResource
2025-07-20 01:24:15.790 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.ZYXXController
2025-07-20 01:24:15.792 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.YssjfsController
2025-07-20 01:24:15.795 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.TodoController
2025-07-20 01:24:15.797 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.StatisticsController
2025-07-20 01:24:15.799 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.RoleController
2025-07-20 01:24:15.805 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.PubObjFlowController
2025-07-20 01:24:15.808 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.ModuleGivenController
2025-07-20 01:24:15.812 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.LoginController
2025-07-20 01:24:15.814 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.IndexController
2025-07-20 01:24:15.820 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.FlowTemplateController
2025-07-20 01:24:15.822 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgzzfsController
2025-07-20 01:24:15.825 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgzjlyfsController
2025-07-20 01:24:15.826 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgzjjgController
2025-07-20 01:24:15.833 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgzbsqController
2025-07-20 01:24:15.839 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgysController
2025-07-20 01:24:15.846 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgsqController
2025-07-20 01:24:15.848 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgmlController
2025-07-20 01:24:15.849 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CglxController
2025-07-20 01:24:15.854 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgjhController
2025-07-20 01:24:15.856 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgfsController
2025-07-20 01:24:15.860 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.CgdjController
2025-07-20 01:24:15.862 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.BMXXController
2025-07-20 01:24:15.864 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.AuditController
2025-07-20 01:24:15.866 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.AttachmentController
2025-07-20 01:24:15.870 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.ApprovalReminderController
2025-07-20 01:24:15.872 [main] INFO  h.a.p.p.y.c.o.d.i.n.g - onTransformation：com.gg.grp.hkycg.controller.AcceptanceResultController
2025-07-20 01:24:16.879 [main] INFO  com.gg.grp.hkycg.HkyCgApplication - Started HkyCgApplication in 7.157 seconds (JVM running for 11.452)
2025-07-20 01:24:16.884 [main] DEBUG o.s.boot.availability.ApplicationAvailabilityBean - Application availability state LivenessState changed to CORRECT
2025-07-20 01:24:16.886 [main] DEBUG o.s.boot.availability.ApplicationAvailabilityBean - Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
2025-07-20 01:24:17.458 [RMI TCP Connection(2)-************] DEBUG c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-20 01:24:17.459 [RMI TCP Connection(4)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-20 01:24:17.459 [RMI TCP Connection(4)-************] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-20 01:24:17.459 [RMI TCP Connection(4)-************] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-20 01:24:17.460 [RMI TCP Connection(4)-************] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-20 01:24:17.463 [RMI TCP Connection(4)-************] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1ea934c7
2025-07-20 01:24:17.463 [RMI TCP Connection(4)-************] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@15475911
2025-07-20 01:24:17.463 [RMI TCP Connection(4)-************] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-20 01:24:17.463 [RMI TCP Connection(4)-************] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-20 01:24:17.992 [RMI TCP Connection(2)-************] DEBUG c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-20 01:24:18.049 [boundedElastic-1] DEBUG org.redisson.command.RedisExecutor - acquired connection for command (INFO) and params [server] from slot NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null] using node /127.0.0.1:6379... RedisConnection@********** [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x4fe8a53a, L:/127.0.0.1:59663 - R:/127.0.0.1:6379], currentCommand=null]
2025-07-20 01:24:18.053 [redisson-netty-2-9] DEBUG org.redisson.command.RedisExecutor - connection released for command (INFO) and params [server] from slot NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null] using connection RedisConnection@********** [redisClient=[addr=redis://127.0.0.1:6379], channel=[id: 0x4fe8a53a, L:/127.0.0.1:59663 - R:/127.0.0.1:6379], currentCommand=CommandData [promise=RedissonPromise [promise=ImmediateEventExecutor$ImmediatePromise@3099af42(success: {gcc_version=4.2.1, redis_build_id=27cd743c17552403, process_id=1293, io_threads_active=0, executable=/opt/homebrew/opt/redis/bin/redis-server, monotonic_clock=POSIX clock_gettime, server_time_usec=1752945858050807, os=Darwin 23.6.0 arm64, uptime_in_seconds=1092241, configured_hz=10, redis_git_dirty=1, redis_git_sha1=00000000, config_file=/opt/homebrew/etc/redis.conf, redis_mode=standalone, multiplexing_api=kqueue, atomicvar_api=c11-builtin, run_id=2c6713d2bbfca006c3b4f2460ac0936abeffeeaf, arch_bits=64, hz=10, redis_version=8.0.0, tcp_port=6379, lru_clock=8115394, uptime_in_days=12, process_supervised=no})], command=(INFO), params=[server], codec=org.redisson.client.codec.StringCodec]]
2025-07-20 01:24:18.326 [redisson-netty-2-19] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:18.327 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:23.328 [redisson-netty-2-22] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:23.330 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:28.331 [redisson-netty-2-23] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:28.333 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:33.335 [redisson-netty-2-24] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:33.336 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:38.337 [redisson-netty-2-25] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:38.338 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:43.339 [redisson-netty-2-26] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:43.340 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:48.341 [redisson-netty-2-27] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:48.342 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:53.343 [redisson-netty-2-28] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:53.345 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:24:58.346 [redisson-netty-2-29] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:24:58.348 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:03.349 [redisson-netty-2-30] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:03.351 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:08.352 [redisson-netty-2-31] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:08.352 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:13.354 [redisson-netty-2-1] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:13.356 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:18.356 [redisson-netty-2-32] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:18.356 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:23.358 [redisson-netty-2-2] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:23.360 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:28.361 [redisson-netty-2-3] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:28.362 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:33.363 [redisson-netty-2-4] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:33.363 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:38.364 [redisson-netty-2-5] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:38.365 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:43.366 [redisson-netty-2-6] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:43.367 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:48.369 [redisson-netty-2-7] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:48.370 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:53.371 [redisson-netty-2-8] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:53.372 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:25:58.373 [redisson-netty-2-9] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:25:58.373 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:03.373 [redisson-netty-2-10] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:03.374 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:08.376 [redisson-netty-2-11] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:08.376 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:13.377 [redisson-netty-2-12] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:13.379 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:18.380 [redisson-netty-2-13] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:18.380 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:23.381 [redisson-netty-2-16] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:23.382 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:28.383 [redisson-netty-2-14] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:28.384 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:33.385 [redisson-netty-2-17] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:33.386 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:38.388 [redisson-netty-2-15] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:38.389 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:43.390 [redisson-netty-2-18] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:43.391 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:48.393 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:48.393 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:53.394 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:53.395 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:26:58.396 [redisson-netty-2-19] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:26:58.397 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:03.398 [redisson-netty-2-22] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:03.400 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:08.400 [redisson-netty-2-23] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:08.401 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:13.410 [redisson-netty-2-24] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:13.412 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:18.414 [redisson-netty-2-25] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:18.415 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:23.416 [redisson-netty-2-26] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:23.417 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:28.418 [redisson-netty-2-27] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:28.420 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:33.421 [redisson-netty-2-28] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:33.423 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:38.424 [redisson-netty-2-29] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:38.424 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:43.425 [redisson-netty-2-30] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:43.426 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:48.426 [redisson-netty-2-31] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:48.428 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:53.430 [redisson-netty-2-1] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:53.431 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:27:58.431 [redisson-netty-2-32] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:27:58.432 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:03.433 [redisson-netty-2-2] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:03.434 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:08.436 [redisson-netty-2-3] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:08.436 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:13.437 [redisson-netty-2-4] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:13.437 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:18.438 [redisson-netty-2-5] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:18.439 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:23.440 [redisson-netty-2-6] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:23.441 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:28.442 [redisson-netty-2-7] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:28.442 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:33.444 [redisson-netty-2-8] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:33.445 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:38.446 [redisson-netty-2-9] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:38.448 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:43.448 [redisson-netty-2-10] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:43.449 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:48.451 [redisson-netty-2-11] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:48.453 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:53.454 [redisson-netty-2-12] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:53.455 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:28:58.456 [redisson-netty-2-13] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:28:58.457 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:03.458 [redisson-netty-2-16] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:03.460 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:08.461 [redisson-netty-2-14] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:08.461 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:13.462 [redisson-netty-2-17] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:13.463 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:18.464 [redisson-netty-2-15] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:18.464 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:23.465 [redisson-netty-2-18] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:23.466 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:28.467 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:28.469 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:33.471 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:33.471 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:38.473 [redisson-netty-2-19] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:38.474 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:43.475 [redisson-netty-2-22] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:43.476 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:48.477 [redisson-netty-2-23] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:48.478 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:53.479 [redisson-netty-2-24] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:53.481 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:29:58.482 [redisson-netty-2-25] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:29:58.483 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:03.484 [redisson-netty-2-26] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:03.486 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:08.488 [redisson-netty-2-27] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:08.489 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:13.491 [redisson-netty-2-28] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:13.493 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:18.494 [redisson-netty-2-29] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:18.496 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:23.496 [redisson-netty-2-30] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:23.497 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:28.497 [redisson-netty-2-31] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:28.499 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:33.500 [redisson-netty-2-1] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:33.501 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:38.502 [redisson-netty-2-32] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:38.507 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:43.508 [redisson-netty-2-2] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:43.509 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:48.511 [redisson-netty-2-3] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:48.512 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:53.513 [redisson-netty-2-4] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:53.514 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:30:58.515 [redisson-netty-2-5] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:30:58.516 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:03.517 [redisson-netty-2-6] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:03.519 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:08.520 [redisson-netty-2-7] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:08.521 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:13.523 [redisson-netty-2-8] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:13.525 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:18.525 [redisson-netty-2-9] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:18.526 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:23.527 [redisson-netty-2-10] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:23.529 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:28.529 [redisson-netty-2-11] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:28.530 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:33.531 [redisson-netty-2-12] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:33.531 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:38.532 [redisson-netty-2-13] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:38.532 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:43.534 [redisson-netty-2-16] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:43.535 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:48.536 [redisson-netty-2-14] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:48.537 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:53.538 [redisson-netty-2-17] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:53.539 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:31:58.540 [redisson-netty-2-15] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:31:58.541 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:03.542 [redisson-netty-2-18] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:03.542 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:08.543 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:08.543 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:13.544 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:13.544 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:18.545 [redisson-netty-2-19] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:18.547 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:23.549 [redisson-netty-2-22] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:23.549 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:28.550 [redisson-netty-2-23] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:28.551 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:33.552 [redisson-netty-2-24] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:33.553 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:38.554 [redisson-netty-2-25] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:38.554 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:43.555 [redisson-netty-2-26] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:43.556 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:48.557 [redisson-netty-2-27] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:48.558 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:53.559 [redisson-netty-2-28] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:53.560 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:32:58.562 [redisson-netty-2-29] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:32:58.562 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:03.563 [redisson-netty-2-30] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:03.564 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:08.565 [redisson-netty-2-31] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:08.567 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:13.568 [redisson-netty-2-1] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:13.569 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:18.570 [redisson-netty-2-32] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:18.572 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:23.573 [redisson-netty-2-2] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:23.574 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:28.575 [redisson-netty-2-3] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:28.576 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:33.577 [redisson-netty-2-4] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:33.578 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:38.579 [redisson-netty-2-5] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:38.581 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:43.581 [redisson-netty-2-6] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:43.582 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:48.583 [redisson-netty-2-7] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:48.584 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:53.585 [redisson-netty-2-8] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:53.587 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:33:58.588 [redisson-netty-2-9] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:33:58.589 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:03.591 [redisson-netty-2-10] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:03.592 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:08.593 [redisson-netty-2-11] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:08.593 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:13.594 [redisson-netty-2-12] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:13.596 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:18.597 [redisson-netty-2-13] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:18.601 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:23.602 [redisson-netty-2-16] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:23.602 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:28.603 [redisson-netty-2-14] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:28.605 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:33.606 [redisson-netty-2-17] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:33.608 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:38.609 [redisson-netty-2-15] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:38.611 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:43.613 [redisson-netty-2-18] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:43.614 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:48.615 [redisson-netty-2-20] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:48.617 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:53.618 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:53.619 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:34:58.620 [redisson-netty-2-19] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:34:58.621 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:03.622 [redisson-netty-2-22] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:03.623 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:08.624 [redisson-netty-2-23] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:08.624 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:13.625 [redisson-netty-2-24] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:13.626 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:18.628 [redisson-netty-2-25] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:18.636 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:23.652 [redisson-netty-2-26] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:23.654 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:28.656 [redisson-netty-2-27] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:28.656 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:33.657 [redisson-netty-2-28] DEBUG org.redisson.connection.DNSMonitor - Request sent to resolve ip address for master host: 127.0.0.1
2025-07-20 01:35:33.659 [redisson-netty-2-21] DEBUG org.redisson.connection.DNSMonitor - Resolved ip: /127.0.0.1 for master host: 127.0.0.1
2025-07-20 01:35:34.793 [SpringApplicationShutdownHook] DEBUG o.s.boot.availability.ApplicationAvailabilityBean - Application availability state ReadinessState changed from ACCEPTING_TRAFFIC to REFUSING_TRAFFIC
2025-07-20 01:35:34.794 [SpringApplicationShutdownHook] DEBUG o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@16b2bb0c, started on Sun Jul 20 01:24:10 CST 2025
2025-07-20 01:35:34.794 [SpringApplicationShutdownHook] DEBUG o.s.b.c.p.s.ConfigurationPropertySourcesPropertyResolver$DefaultResolver - Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-07-20 01:35:34.828 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain
2025-07-20 01:35:34.828 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain
2025-07-20 01:35:34.828 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain
2025-07-20 01:35:34.828 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain
2025-07-20 01:35:34.828 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG o.s.boot.actuate.endpoint.jmx.JmxEndpointExporter - Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain
2025-07-20 01:35:34.829 [SpringApplicationShutdownHook] DEBUG io.lettuce.core.resource.DefaultClientResources - Initiate shutdown (0, 2, SECONDS)
2025-07-20 01:35:34.831 [SpringApplicationShutdownHook] DEBUG i.l.core.resource.DefaultEventLoopGroupProvider - Initiate shutdown (0, 2, SECONDS)
2025-07-20 01:35:34.856 [redisson-netty-2-8] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-8
2025-07-20 01:35:34.856 [redisson-netty-2-11] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-11
2025-07-20 01:35:34.857 [redisson-netty-2-7] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-7
2025-07-20 01:35:34.857 [redisson-netty-2-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-3
2025-07-20 01:35:34.857 [redisson-netty-2-30] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-30
2025-07-20 01:35:34.858 [redisson-netty-2-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-2
2025-07-20 01:35:34.858 [redisson-netty-2-28] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-28
2025-07-20 01:35:34.859 [redisson-netty-2-32] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-32
2025-07-20 01:35:34.859 [redisson-netty-2-9] DEBUG io.netty.buffer.PoolThreadCache - Freed 5 thread-local buffer(s) from thread: redisson-netty-2-9
2025-07-20 01:35:34.860 [redisson-netty-2-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-6
2025-07-20 01:35:34.859 [redisson-netty-2-26] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-26
2025-07-20 01:35:34.860 [redisson-netty-2-10] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-10
2025-07-20 01:35:34.860 [redisson-netty-2-24] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-24
2025-07-20 01:35:34.860 [redisson-netty-2-25] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-25
2025-07-20 01:35:34.860 [redisson-netty-2-12] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-12
2025-07-20 01:35:34.860 [redisson-netty-2-31] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-31
2025-07-20 01:35:34.860 [redisson-netty-2-29] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-29
2025-07-20 01:35:34.860 [redisson-netty-2-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-5
2025-07-20 01:35:34.860 [redisson-netty-2-27] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-27
2025-07-20 01:35:34.861 [redisson-netty-2-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: redisson-netty-2-4
2025-07-20 01:35:34.874 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-20 01:35:34.893 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-07-20 01:35:34.894 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-07-20 01:35:34.894 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-20 01:35:34.894 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-20 01:35:34.895 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-07-20 01:35:34.895 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-07-20 01:35:34.895 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-20 01:35:34.897 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-20 01:35:34.897 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
